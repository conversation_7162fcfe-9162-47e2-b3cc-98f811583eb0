/**
 * Utilitários para manipulação de cores
 */

// Utilitário para validar cor hex
export function isValidHexColor(color: string): boolean {
  return /^#([0-9A-Fa-f]{3}){1,2}$/.test(color);
}

// Função utilitária para normalizar cor hex para #rrggbb
export function normalizeHexColor(color: string): string {
  if (!color) return '#000000';
  if (/^#[0-9A-Fa-f]{6}$/.test(color)) return color;
  if (/^#[0-9A-Fa-f]{3}$/.test(color)) {
    return (
      '#' +
      color[1] + color[1] +
      color[2] + color[2] +
      color[3] + color[3]
    );
  }
  return '#000000';
}

// Função aprimorada de contraste (preto ou branco puro)
export function getPureContrastColor(bg: string): string {
  if (!bg || typeof bg !== 'string' || !bg.startsWith('#') || bg.length < 7) return '#111';
  const r = parseInt(bg.substr(1,2),16);
  const g = parseInt(bg.substr(3,2),16);
  const b = parseInt(bg.substr(5,2),16);
  const luminance = (0.299*r + 0.587*g + 0.114*b)/255;
  return luminance > 0.5 ? '#111' : '#fff';
}
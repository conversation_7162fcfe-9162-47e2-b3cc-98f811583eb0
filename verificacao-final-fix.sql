-- SCRIPT PARA TESTAR SE TODAS AS CORREÇÕES FORAM APLICADAS

-- 1. Veri<PERSON>r se as tabelas ainda têm RLS ativo
SELECT 
    'VERIFICAÇÃO RLS' as teste,
    tablename,
    rowsecurity,
    CASE 
        WHEN rowsecurity = false THEN '✅ OK'
        ELSE '❌ PROBLEMA: RLS AINDA ATIVO'
    END as resultado
FROM pg_tables 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
  AND schemaname = 'public'
ORDER BY tablename;

-- 2. Listar políticas que ainda existem (deve estar vazio)
SELECT 
    'VERIFICAÇÃO POLÍTICAS' as teste,
    tablename,
    policyname,
    '❌ POLÍTICA AINDA EXISTE' as resultado
FROM pg_policies 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
  AND schemaname = 'public'
ORDER BY tablename, policyname;

-- 3. Verificar se o usuário foi criado para foreign key
SELECT 
    'VERIFICAÇÃO USUÁRIO' as teste,
    id,
    email,
    name,
    '✅ USUÁRIO EXISTE' as resultado
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 4. Teste de acesso direto às tabelas
SELECT 
    'TESTE ACESSO' as teste,
    'tasks' as tabela,
    count(*) as registros,
    '✅ ACESSO OK' as resultado
FROM public.tasks

UNION ALL

SELECT 
    'TESTE ACESSO' as teste,
    'evidence' as tabela,
    count(*) as registros,
    '✅ ACESSO OK' as resultado
FROM public.evidence

UNION ALL

SELECT 
    'TESTE ACESSO' as teste,
    'task_content_blocks' as tabela,
    count(*) as registros,
    '✅ ACESSO OK' as resultado
FROM public.task_content_blocks

ORDER BY tabela;

-- 5. Resumo geral
SELECT 
    'RESUMO' as teste,
    CASE 
        WHEN (
            SELECT count(*) FROM pg_tables 
            WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
              AND schemaname = 'public' 
              AND rowsecurity = true
        ) = 0 THEN '✅ RLS DESABILITADO EM TODAS AS TABELAS'
        ELSE '❌ AINDA HÁ TABELAS COM RLS ATIVO'
    END as rls_status,
    CASE 
        WHEN (
            SELECT count(*) FROM pg_policies 
            WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
              AND schemaname = 'public'
        ) = 0 THEN '✅ NENHUMA POLÍTICA ATIVA'
        ELSE '❌ AINDA HÁ POLÍTICAS ATIVAS'
    END as policies_status,
    CASE 
        WHEN (
            SELECT count(*) FROM public.profiles 
            WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
        ) = 1 THEN '✅ USUÁRIO CRIADO'
        ELSE '❌ USUÁRIO NÃO ENCONTRADO'
    END as user_status;

-- SE TODOS OS TESTES MOSTRAREM ✅, REINICIE A APLICAÇÃO E TESTE O UPLOAD!

/* Estilos responsivos para botões dos blocos */

/* Container do botão com posicionamento absoluto */
.button-container {
  position: absolute;
  z-index: 10;
  max-width: calc(100% - 24px);
}

/* Botão responsivo - força altura maior com máxima especificidade */
.responsive-button,
button.responsive-button,
div .responsive-button,
div button.responsive-button,
[class*="responsive-button"],
button[class*="responsive-button"] {
  /* Apenas propriedades que não conflitam com configurações do usuário */
  outline: none !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  box-sizing: border-box !important;

  /* Mobile first - altura mais compacta */
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
  font-size: 14px !important;
  min-height: 36px !important;
  height: auto !important;
  line-height: 1.2 !important;
}

/* Tablet e desktop */
@media (min-width: 640px) {
  .responsive-button,
  button.responsive-button,
  div .responsive-button,
  div button.responsive-button,
  [class*="responsive-button"],
  button[class*="responsive-button"] {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    padding-left: 20px !important;
    padding-right: 20px !important;
    font-size: 14px !important;
    min-height: 40px !important;
    height: auto !important;
    line-height: 1.2 !important;
  }
}

/* Desktop maior */
@media (min-width: 1024px) {
  .responsive-button,
  button.responsive-button,
  div .responsive-button,
  div button.responsive-button,
  [class*="responsive-button"],
  button[class*="responsive-button"] {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    padding-left: 24px !important;
    padding-right: 24px !important;
    font-size: 15px !important;
    min-height: 42px !important;
    height: auto !important;
    line-height: 1.2 !important;
  }
}

/* Regra de força máxima - sobrescreve qualquer estilo inline */
button.responsive-button[style],
.responsive-button[style],
div button.responsive-button[style] {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
  min-height: 36px !important;
  height: auto !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
}

@media (min-width: 640px) {
  button.responsive-button[style],
  .responsive-button[style],
  div button.responsive-button[style] {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    padding-left: 20px !important;
    padding-right: 20px !important;
    min-height: 40px !important;
    font-size: 14px !important;
  }
}

@media (min-width: 1024px) {
  button.responsive-button[style],
  .responsive-button[style],
  div button.responsive-button[style] {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    padding-left: 24px !important;
    padding-right: 24px !important;
    min-height: 42px !important;
    font-size: 15px !important;
  }
}

/* Controle de texto responsivo */
@media (max-width: 479px) {
  .responsive-button,
  button.responsive-button {
    /* Para telas muito pequenas, permitir quebra de linha se necessário */
    white-space: normal !important;
    word-break: break-word !important;
    line-height: 1.2 !important;
  }
}

/* Container do card com overflow controlado */
.card-with-button {
  position: relative;
  overflow: hidden;
}

/* Padding responsivo para cards */
.card-padding {
  padding: 12px;
}

@media (min-width: 640px) {
  .card-padding {
    padding: 16px;
  }
}

@media (min-width: 1024px) {
  .card-padding {
    padding: 24px;
  }
}

/* Garantir que texto não quebre de forma inadequada */
.responsive-button {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Para botões muito pequenos em mobile, permitir quebra de linha */
@media (max-width: 479px) {
  .responsive-button.size-small {
    white-space: normal;
    word-break: break-word;
    line-height: 1.2;
  }
}

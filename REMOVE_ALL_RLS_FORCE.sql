-- =====================================================
-- IDENTIFICAR E REMOVER POLÍTICAS RLS RESTANTES
-- =====================================================
-- Primeiro vamos ver quais políticas ainda existem

-- Ver todas as políticas RLS que ainda existem
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd as operacao,
    permissive,
    roles
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- REMOÇÃO FORÇADA DE TODAS AS POLÍTICAS
-- =====================================================
-- Este script vai buscar e remover dinamicamente todas as políticas

DO $$
DECLARE
    policy_record RECORD;
    drop_statement TEXT;
    policies_removed INTEGER := 0;
BEGIN
    -- Buscar todas as políticas e gerar comandos DROP
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        drop_statement := 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON ' || policy_record.schemaname || '.' || policy_record.tablename;
        
        BEGIN
            EXECUTE drop_statement;
            policies_removed := policies_removed + 1;
            RAISE NOTICE 'Removida: %.% -> %', policy_record.tablename, policy_record.policyname, drop_statement;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Erro ao remover %.%: %', policy_record.tablename, policy_record.policyname, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'TOTAL DE POLÍTICAS REMOVIDAS: %', policies_removed;
END $$;

-- =====================================================
-- DESABILITAR RLS EM TODAS AS TABELAS (FORÇADO)
-- =====================================================

DO $$
DECLARE
    table_record RECORD;
    disable_statement TEXT;
    tables_processed INTEGER := 0;
BEGIN
    -- Buscar todas as tabelas do schema public
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
    LOOP
        disable_statement := 'ALTER TABLE IF EXISTS public.' || table_record.table_name || ' DISABLE ROW LEVEL SECURITY';
        
        BEGIN
            EXECUTE disable_statement;
            tables_processed := tables_processed + 1;
            RAISE NOTICE 'RLS desabilitado em: %', table_record.table_name;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Erro ao desabilitar RLS em %: %', table_record.table_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'TOTAL DE TABELAS PROCESSADAS: %', tables_processed;
END $$;

-- =====================================================
-- VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se ainda restam políticas
SELECT 
    'POLÍTICAS RLS RESTANTES' as item,
    COUNT(*) as quantidade,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ TODAS REMOVIDAS'
        ELSE '❌ AINDA RESTAM ' || COUNT(*)::TEXT
    END as status
FROM pg_policies 
WHERE schemaname = 'public';

-- Verificar quais tabelas ainda têm RLS habilitado
SELECT 
    'TABELAS COM RLS HABILITADO' as item,
    COUNT(*) as quantidade,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ TODAS DESABILITADAS'
        ELSE '❌ AINDA COM RLS: ' || COUNT(*)::TEXT
    END as status
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public' 
AND c.relkind = 'r' 
AND c.relrowsecurity = true;

-- Listar tabelas que ainda têm RLS habilitado (se houver)
SELECT 
    c.relname as tabela,
    '❌ RLS AINDA HABILITADO' as status
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public' 
AND c.relkind = 'r' 
AND c.relrowsecurity = true;

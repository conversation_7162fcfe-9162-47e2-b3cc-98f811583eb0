import React, { useRef, useState, useEffect } from 'react';
import <PERSON><PERSON>per from 'react-easy-crop';
import { Avatar, AvatarImage, AvatarFallback } from './avatar';
import { Button } from './button';
import { useToast } from '@/hooks/ui/use-toast';
import { supabase } from '@/lib/supabaseClient';
import { Slider } from './slider';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './dialog';
import { LoadingSpinner } from './LoadingSpinner';
import { Camera, Trash2 } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

interface AvatarUploadProps {
  userId: string;
  avatarUrl?: string | null;
  onAvatarChange?: (url: string | null) => void;
  disabled?: boolean;
}

const ACCEPTED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const MAX_SIZE_MB = 2;
const PLACEHOLDER = '/placeholder.svg';

export const AvatarUpload: React.FC<AvatarUploadProps> = ({ userId, avatarUrl, onAvatarChange, disabled }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const [image, setImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(avatarUrl || null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<any>(null);
  const [showCrop, setShowCrop] = useState(false);
  const [loading, setLoading] = useState(false);
  const [imgError, setImgError] = useState(false);
  const changeButtonRef = React.useRef<HTMLButtonElement>(null);
  const removeButtonRef = React.useRef<HTMLButtonElement>(null);

  useEffect(() => {
    setPreviewUrl(avatarUrl || null);
    setImgError(false);
  }, [avatarUrl]);

  // Gera preview ao selecionar arquivo
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (!ACCEPTED_TYPES.includes(file.type)) {
      toast({ title: 'Formato inválido', description: 'Apenas JPG, PNG ou WebP.', variant: 'destructive' });
      return;
    }
    if (file.size > MAX_SIZE_MB * 1024 * 1024) {
      toast({ title: 'Arquivo muito grande', description: `Máximo ${MAX_SIZE_MB}MB.`, variant: 'destructive' });
      return;
    }
    setImage(file);
    setShowCrop(true);
    const reader = new FileReader();
    reader.onload = () => setPreviewUrl(reader.result as string);
    reader.readAsDataURL(file);
  };

  // Crop final
  const getCroppedImg = async (): Promise<Blob | null> => {
    if (!previewUrl || !croppedAreaPixels) return null;
    const image = await createImage(previewUrl);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    const size = Math.max(croppedAreaPixels.width, croppedAreaPixels.height);
    canvas.width = size;
    canvas.height = size;
    ctx.beginPath();
    ctx.arc(size / 2, size / 2, size / 2, 0, 2 * Math.PI);
    ctx.closePath();
    ctx.clip();
    ctx.drawImage(
      image,
      croppedAreaPixels.x,
      croppedAreaPixels.y,
      croppedAreaPixels.width,
      croppedAreaPixels.height,
      0,
      0,
      size,
      size
    );
    return await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
  };

  // Utilitário para criar imagem
  function createImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new window.Image();
      img.addEventListener('load', () => resolve(img));
      img.addEventListener('error', error => reject(error));
      img.setAttribute('crossOrigin', 'anonymous');
      img.src = url;
    });
  }

  // Upload para Supabase
  const handleSave = async () => {
    setLoading(true);
    try {
      const cropped = await getCroppedImg();
      if (!cropped) throw new Error('Erro ao processar imagem.');
      const fileName = `${userId}-${Date.now()}.png`;
      // Remove avatar antigo se existir
      if (avatarUrl && avatarUrl.includes('avatars/')) {
        const oldPath = avatarUrl.split('/storage/v1/object/public/')[1];
        if (oldPath) await supabase.storage.from('avatars').remove([oldPath.replace('avatars/', '')]);
      }
      // Upload
      const { data, error } = await supabase.storage.from('avatars').upload(fileName, cropped, { upsert: true, contentType: 'image/png' });
      if (error) throw error;
      // Pega URL pública
      const { data: publicUrl } = supabase.storage.from('avatars').getPublicUrl(fileName);
      setPreviewUrl(publicUrl.publicUrl);
      onAvatarChange?.(publicUrl.publicUrl);
      setShowCrop(false);
      toast({ title: 'Avatar atualizado', description: 'Foto enviada com sucesso.' });
    } catch (err: any) {
      toast({ title: 'Erro', description: err.message || 'Erro ao salvar avatar.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  // Remover avatar
  const handleRemove = async () => {
    if (!avatarUrl) return;
    setLoading(true);
    try {
      if (avatarUrl.includes('avatars/')) {
        const path = avatarUrl.split('/storage/v1/object/public/')[1];
        if (path) await supabase.storage.from('avatars').remove([path.replace('avatars/', '')]);
      }
      setPreviewUrl(null);
      onAvatarChange?.(null);
      toast({ title: 'Avatar removido', description: 'Foto removida com sucesso.' });
    } catch (err: any) {
      toast({ title: 'Erro', description: err.message || 'Erro ao remover avatar.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center gap-2">
      <div className="relative group">
        <Avatar className="w-24 h-24 border-2 border-gray-200 group-hover:border-primary transition-shadow duration-200 shadow-sm">
          <AvatarImage
            src={imgError ? PLACEHOLDER : (previewUrl || PLACEHOLDER)}
            alt="Avatar"
            onError={() => setImgError(true)}
            className="object-cover w-full h-full"
          />
          <AvatarFallback>U</AvatarFallback>
        </Avatar>
        {/* Botão Trocar (ícone câmera) */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {React.cloneElement(
                <Button
                  type="button"
                  size="icon"
                  className="absolute bottom-1 right-1 w-9 h-9 rounded-full bg-white border shadow-lg flex items-center justify-center opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200 z-10"
                  onClick={() => inputRef.current?.click()}
                  disabled={disabled || loading}
                  aria-label="Trocar foto do usuário"
                  tabIndex={0}
                >
                  <Camera className="w-5 h-5 text-gray-700" />
                </Button>,
                { ref: changeButtonRef }
              )}
            </TooltipTrigger>
            <TooltipContent side="bottom">Trocar foto</TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {/* Botão Remover (ícone lixeira) */}
        {previewUrl && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {React.cloneElement(
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    className="absolute top-1 right-1 w-8 h-8 rounded-full bg-white border shadow-lg flex items-center justify-center opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200 z-10"
                    onClick={handleRemove}
                    disabled={disabled || loading}
                    aria-label="Remover foto do usuário"
                    tabIndex={0}
                  >
                    <Trash2 className="w-4 h-4 text-red-500" />
                  </Button>,
                  { ref: removeButtonRef }
                )}
              </TooltipTrigger>
              <TooltipContent side="left">Remover foto</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        <input
          ref={inputRef}
          type="file"
          accept="image/jpeg,image/png,image/webp"
          className="hidden"
          onChange={handleFileChange}
          disabled={disabled || loading}
        />
      </div>
      {/* Crop Dialog */}
      <Dialog open={showCrop} onOpenChange={setShowCrop}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Recortar Foto</DialogTitle>
          </DialogHeader>
          <div className="relative w-full h-64 bg-gray-100 rounded-lg overflow-hidden">
            {previewUrl && (
              <Cropper
                image={previewUrl}
                crop={crop}
                zoom={zoom}
                aspect={1}
                cropShape="round"
                showGrid={false}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={(_, areaPixels) => setCroppedAreaPixels(areaPixels)}
              />
            )}
          </div>
          <div className="flex items-center gap-4 mt-4">
            <span className="text-sm">Zoom</span>
            <Slider min={1} max={3} step={0.01} value={[zoom]} onValueChange={v => setZoom(v[0])} className="flex-1" />
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowCrop(false)} disabled={loading} aria-label="Cancelar recorte">Cancelar</Button>
            <Button onClick={handleSave} disabled={loading} aria-label="Salvar recorte">
              {loading ? <LoadingSpinner size="sm" /> : 'Salvar'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
-- =====================
-- CORREÇÃO DE EMERGÊNCIA: DESABILITAR RLS TEMPORARIAMENTE
-- Data: 2024-12-19
-- ATENÇÃO: Use apenas se o erro 500 persistir
-- =====================

-- AVISO: Esta é uma correção temporária que remove a segurança RLS
-- Use apenas para resolver o problema imediato e reabilite RLS depois

-- PASSO 1: Remover TODAS as políticas existentes dinamicamente
-- =====================
do $$
declare
    policy_record record;
begin
    -- <PERSON>car e remover todas as políticas da tabela profiles
    for policy_record in
        select policyname
        from pg_policies
        where tablename = 'profiles' and schemaname = 'public'
    loop
        execute format('drop policy if exists %I on public.profiles', policy_record.policyname);
        raise notice 'Política removida: %', policy_record.policyname;
    end loop;

    raise notice 'Todas as políticas de profiles foram removidas';
end $$;

-- PASSO 2: Desabilitar RLS na tabela profiles (TEMPORÁRIO)
-- =====================
alter table public.profiles disable row level security;

-- PASSO 2: Verificar se RLS foi desabilitado
-- =====================
select 
    schemaname,
    tablename,
    rowsecurity,
    case 
        when rowsecurity then 'RLS HABILITADO'
        else 'RLS DESABILITADO'
    end as status
from pg_tables 
where tablename = 'profiles';

-- PASSO 3: Instruções importantes
-- =====================
select 'ATENÇÃO: RLS DESABILITADO TEMPORARIAMENTE!' as aviso;
select 'Todos os usuários podem ver todos os perfis' as consequencia;
select 'Reabilite RLS assim que possível com:' as instrucao;
select 'ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;' as comando;

-- PASSO 4: Script para reabilitar RLS (execute depois de resolver)
-- =====================
/*
-- Para reabilitar RLS depois:
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- E recriar políticas básicas:
CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (id = auth.uid());

CREATE POLICY "Users can insert their own profile"
  ON public.profiles FOR INSERT
  WITH CHECK (id = auth.uid());

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (id = auth.uid());
*/

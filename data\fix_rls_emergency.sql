-- =====================
-- CORREÇÃO DE EMERGÊNCIA: DESABILITAR RLS TEMPORARIAMENTE
-- Data: 2024-12-19
-- ATENÇÃO: Use apenas se o erro 500 persistir
-- =====================

-- AVISO: Esta é uma correção temporária que remove a segurança RLS
-- Use apenas para resolver o problema imediato e reabilite RLS depois

-- PASSO 1: Remover todas as políticas problemáticas primeiro
-- =====================
drop policy if exists "Allow users to view profiles for collaboration" on public.profiles;
drop policy if exists "Temporary allow authenticated users to view profiles" on public.profiles;
drop policy if exists "Simple authenticated users can view profiles" on public.profiles;
drop policy if exists "Users can insert their own profile" on public.profiles;
drop policy if exists "Admin can insert any profile" on public.profiles;
drop policy if exists "Users can update their own profile" on public.profiles;
drop policy if exists "Admin can update all profiles" on public.profiles;
drop policy if exists "Users can view their own profile" on public.profiles;
drop policy if exists "Admin can view all profiles" on public.profiles;

-- PASSO 2: Desabilitar RLS na tabela profiles (TEMPORÁRIO)
-- =====================
alter table public.profiles disable row level security;

-- PASSO 2: Verificar se RLS foi desabilitado
-- =====================
select 
    schemaname,
    tablename,
    rowsecurity,
    case 
        when rowsecurity then 'RLS HABILITADO'
        else 'RLS DESABILITADO'
    end as status
from pg_tables 
where tablename = 'profiles';

-- PASSO 3: Instruções importantes
-- =====================
select 'ATENÇÃO: RLS DESABILITADO TEMPORARIAMENTE!' as aviso;
select 'Todos os usuários podem ver todos os perfis' as consequencia;
select 'Reabilite RLS assim que possível com:' as instrucao;
select 'ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;' as comando;

-- PASSO 4: Script para reabilitar RLS (execute depois de resolver)
-- =====================
/*
-- Para reabilitar RLS depois:
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- E recriar políticas básicas:
CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (id = auth.uid());

CREATE POLICY "Users can insert their own profile"
  ON public.profiles FOR INSERT
  WITH CHECK (id = auth.uid());

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (id = auth.uid());
*/

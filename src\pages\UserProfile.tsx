import React, { useState, useEffect, useRef, useContext, useCallback } from 'react';
import { useAuth } from '@/auth/useAuth';
import { AvatarUpload } from '@/components/ui/AvatarUpload';
import { updateProfile } from '@/services/userService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/ui/use-toast';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { Header } from '@/components/layout/Header';
import { User, Lock } from 'lucide-react';
import { useNavigate, UNSAFE_NavigationContext as NavigationContext } from 'react-router-dom';
import { supabase } from '@/lib/supabaseClient';

// Hook para bloquear navegação interna (React Router v6)
function useBlocker(blocker: (tx: any) => void, when = true) {
  const { navigator } = useContext(NavigationContext);
  useEffect(() => {
    if (!when) return;
    const push = navigator.push;
    navigator.push = (...args: any[]) => {
      blocker({ retry() { push(...args); } });
    };
    return () => {
      navigator.push = push;
    };
  }, [blocker, navigator, when]);
}

const UserProfile: React.FC = () => {
  const { user, profile, setProfile } = useAuth();
  const { toast } = useToast();
  const [name, setName] = useState(profile?.name || '');
  const [phone, setPhone] = useState(profile?.phone || '');
  const [position, setPosition] = useState(profile?.position || '');
  const [avatarUrl, setAvatarUrl] = useState(profile?.avatar_url || null);
  const [saving, setSaving] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [password, setPassword] = useState('');
  const [isDirty, setIsDirty] = useState(false);
  const initialData = useRef({
    name: profile?.name || '',
    phone: profile?.phone || '',
    position: profile?.position || '',
    avatarUrl: profile?.avatar_url || null,
  });
  const navigate = useNavigate();
  const [pendingNavigation, setPendingNavigation] = useState<null | (() => void)>(null);
  const [stepConfirm, setStepConfirm] = useState<'confirm' | 'password'>('confirm');
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  React.useEffect(() => {
    setName(profile?.name || '');
    setPhone(profile?.phone || '');
    setPosition(profile?.position || '');
    setAvatarUrl(profile?.avatar_url || null);
  }, [profile]);

  // Detecta alterações nos campos
  useEffect(() => {
    setIsDirty(
      name !== initialData.current.name ||
      phone !== initialData.current.phone ||
      position !== initialData.current.position ||
      avatarUrl !== initialData.current.avatarUrl
    );
  }, [name, phone, position, avatarUrl]);

  // Alerta ao tentar sair da página com alterações não salvas
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isDirty) {
        e.preventDefault();
        e.returnValue = '';
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isDirty]);

  // Ao tentar navegar, exibe modal de confirmação
  useBlocker(
    useCallback((tx) => {
      if (isDirty) {
        setShowConfirm(true);
        setStepConfirm('confirm');
        setPendingNavigation(() => tx.retry);
      } else {
        tx.retry();
      }
    }, [isDirty]),
    isDirty
  );

  // Ao clicar em Salvar no modal, pede senha
  const handleAskPassword = () => {
    setStepConfirm('password');
  };

  // Ao salvar com senha, executa navegação pendente
  const handleSaveAndNavigate = async () => {
    await handleSave();
    setShowConfirm(false);
    setIsDirty(false);
    if (pendingNavigation) {
      pendingNavigation();
      setPendingNavigation(null);
    }
  };

  // Ao confirmar no modal, executa a navegação pendente (descartar)
  const handleDiscardAndNavigate = () => {
    setShowConfirm(false);
    setIsDirty(false);
    if (pendingNavigation) {
      pendingNavigation();
      setPendingNavigation(null);
    }
    setStepConfirm('confirm');
  };

  if (!user || !profile) return null;

  const handleSave = async () => {
    setSaving(true);
    try {
      await updateProfile(profile.id, { name, phone, position, avatar_url: avatarUrl });
      // Buscar perfil atualizado
      const { data: updatedProfile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profile.id)
        .single();
      if (!error && updatedProfile) {
        setName(updatedProfile.name || '');
        setPhone(updatedProfile.phone || '');
        setPosition(updatedProfile.position || '');
        setAvatarUrl(updatedProfile.avatar_url || null);
        if (typeof setProfile === 'function') setProfile(updatedProfile);
      }
      toast({ title: 'Perfil atualizado', description: 'Seus dados foram salvos com sucesso.' });
      setShowConfirm(false);
      setStepConfirm('confirm');
    } catch (err: any) {
      toast({ title: 'Erro', description: err.message || 'Erro ao salvar perfil.', variant: 'destructive' });
    } finally {
      setSaving(false);
    }
  };

  // Validação de senha
  const validatePassword = () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      setPasswordError('Preencha todos os campos.');
      return false;
    }
    if (newPassword.length < 8) {
      setPasswordError('A nova senha deve ter pelo menos 8 caracteres.');
      return false;
    }
    if (newPassword !== confirmPassword) {
      setPasswordError('A confirmação da senha não confere.');
      return false;
    }
    setPasswordError('');
    return true;
  };

  // Troca de senha
  const handleChangePassword = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setPasswordSuccess('');
    if (!validatePassword()) return;
    setChangingPassword(true);
    // Reautenticar usuário
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: profile.email,
      password: currentPassword,
    });
    if (signInError) {
      setPasswordError('Senha atual incorreta.');
      setChangingPassword(false);
      return;
    }
    // Atualizar senha
    const { error } = await supabase.auth.updateUser({ password: newPassword });
    if (error) {
      setPasswordError(error.message || 'Erro ao alterar senha.');
      setChangingPassword(false);
      return;
    }
    setPasswordSuccess('Senha alterada com sucesso!');
    setShowPasswordModal(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    setChangingPassword(false);
    toast({ title: 'Senha alterada', description: 'Sua senha foi atualizada com sucesso.' });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header showSidebarButton={true} />
      <div className="flex min-h-screen">
        <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
        <main className={`flex-1 flex flex-col items-center justify-start min-w-0 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
          <div className="w-full max-w-3xl mx-auto px-4 py-2 flex flex-col gap-6">
            {/* Card superior: igual ao card de filtros da tela de usuários */}
            <div className="w-full flex justify-center">
              <div className="relative w-full max-w-3xl bg-[#f5faff] border border-blue-100 rounded-xl shadow-sm px-8 pt-8 pb-6 flex flex-col items-center">
                {/* Botão alterar senha responsivo */}
                {/* Desktop: absoluto no canto superior direito */}
                <Button
                  className="hidden md:absolute md:top-6 md:right-8 z-10 md:flex items-center gap-2 px-5 py-2 h-11 text-base font-semibold shadow bg-blue-600 text-white hover:bg-blue-700"
                  variant="default"
                  onClick={() => setShowPasswordModal(true)}
                >
                  <Lock className="w-5 h-5" /> Alterar senha
                </Button>
                <div className="flex flex-col items-center w-full">
                  <div className="flex items-center gap-2 mb-1">
                    <User className="w-7 h-7 text-blue-600" />
                    <h2 className="text-2xl font-bold mb-0">Meu Perfil</h2>
                  </div>
                  <p className="text-muted-foreground text-sm text-center">Gerencie seus dados pessoais e foto de perfil.</p>
                  {/* Mobile: botão abaixo do texto, centralizado */}
                  <Button
                    className="block md:hidden w-full mt-4 mx-auto px-5 py-2 h-11 text-base font-semibold shadow bg-blue-600 text-white hover:bg-blue-700 flex items-center justify-center gap-2"
                    variant="default"
                    onClick={() => setShowPasswordModal(true)}
                  >
                    <Lock className="w-5 h-5" /> Alterar senha
                  </Button>
                </div>
                {/* Avatar centralizado */}
                <div className="flex justify-center mt-6 mb-2 w-full">
                  <AvatarUpload
                    userId={profile.id}
                    avatarUrl={avatarUrl}
                    onAvatarChange={setAvatarUrl}
                    disabled={saving}
                  />
                </div>
              </div>
            </div>
            {/* Card inferior: campos e ações */}
            <div className="w-full max-w-3xl mx-auto px-0">
              <Card className="bg-white border border-gray-200 shadow-sm w-full px-8">
                <CardContent className="pt-6">
                  <form onSubmit={e => { e.preventDefault(); setShowConfirm(true); }}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Nome</label>
                        <Input value={name} onChange={e => setName(e.target.value)} disabled={saving} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">E-mail</label>
                        <Input value={profile.email} disabled readOnly />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Cargo</label>
                        <Input value={position} onChange={e => setPosition(e.target.value)} disabled={saving} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Telefone</label>
                        <Input value={phone} onChange={e => setPhone(e.target.value)} disabled={saving} />
                      </div>
                    </div>
                    <div className="flex justify-end mt-8 gap-2">
                      {isDirty && (
                        <Button type="button" variant="outline" onClick={() => setShowConfirm(true)} disabled={saving}>
                          Cancelar
                        </Button>
                      )}
                      <Button type="submit" disabled={saving}>Salvar alterações</Button>
                    </div>
                  </form>
                  {/* Bloco de informações adicionais */}
                  <div className="mt-8 rounded-lg bg-[#f5f6fa] px-6 py-4 flex flex-col gap-2 border border-gray-100">
                    <div className="text-xs text-muted-foreground">Data de criação: {profile.created_at ? new Date(profile.created_at).toLocaleString() : '-'}</div>
                    <div className="text-xs text-muted-foreground">Último login: {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : '-'}</div>
                    <div className="text-xs text-muted-foreground">Permissões: {profile.role || '-'}</div>
                  </div>
                </CardContent>
              </Card>
            </div>
            {/* Modal de confirmação */}
            <Dialog open={showConfirm} onOpenChange={(open) => {
              if (!open) {
                setShowConfirm(false);
                setStepConfirm('confirm');
              }
            }}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{isDirty ? 'Deseja salvar as alterações?' : 'Confirme sua identidade'}</DialogTitle>
                </DialogHeader>
                {isDirty ? (
                  stepConfirm === 'confirm' ? (
                    <>
                      <p className="mb-4">Você fez alterações no seu perfil. Deseja salvar antes de sair?</p>
                      <div className="flex justify-end gap-2 mt-4">
                        <Button variant="outline" onClick={handleDiscardAndNavigate} disabled={saving}>Descartar</Button>
                        <Button onClick={() => setStepConfirm('password')} disabled={saving}>Salvar</Button>
                      </div>
                    </>
                  ) : (
                    <>
                      <p className="mb-4">Digite sua senha atual para confirmar as alterações no perfil.</p>
                      <Input type="password" placeholder="Senha atual" value={password} onChange={e => setPassword(e.target.value)} autoFocus />
                      <div className="flex justify-end gap-2 mt-4">
                        <Button variant="outline" onClick={() => { setShowConfirm(false); setStepConfirm('confirm'); }} disabled={saving}>Cancelar</Button>
                        <Button onClick={handleSaveAndNavigate} disabled={saving || !password}>{saving ? 'Salvando...' : 'Confirmar'}</Button>
                      </div>
                    </>
                  )
                ) : (
                  <>
                    <p className="mb-4">Digite sua senha atual para confirmar as alterações no perfil.</p>
                    <Input type="password" placeholder="Senha atual" value={password} onChange={e => setPassword(e.target.value)} autoFocus />
                    <div className="flex justify-end gap-2 mt-4">
                      <Button variant="outline" onClick={() => setShowConfirm(false)} disabled={saving}>Cancelar</Button>
                      <Button onClick={handleSave} disabled={saving || !password}>{saving ? 'Salvando...' : 'Confirmar'}</Button>
                    </div>
                  </>
                )}
              </DialogContent>
            </Dialog>
            {/* Modal de alteração de senha */}
            <Dialog open={showPasswordModal} onOpenChange={setShowPasswordModal}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Alterar senha</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleChangePassword} className="flex flex-col gap-4">
                  <Input
                    type="password"
                    placeholder="Senha atual"
                    value={currentPassword}
                    onChange={e => setCurrentPassword(e.target.value)}
                    autoFocus
                  />
                  <Input
                    type="password"
                    placeholder="Nova senha (mínimo 8 caracteres)"
                    value={newPassword}
                    onChange={e => setNewPassword(e.target.value)}
                  />
                  <Input
                    type="password"
                    placeholder="Confirmar nova senha"
                    value={confirmPassword}
                    onChange={e => setConfirmPassword(e.target.value)}
                  />
                  {passwordError && <div className="text-red-500 text-sm">{passwordError}</div>}
                  {passwordSuccess && <div className="text-green-600 text-sm">{passwordSuccess}</div>}
                  <div className="flex justify-end gap-2 mt-2">
                    <Button variant="outline" type="button" onClick={() => setShowPasswordModal(false)} disabled={changingPassword}>Cancelar</Button>
                    <Button type="submit" disabled={changingPassword}>{changingPassword ? 'Salvando...' : 'Salvar'}</Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </main>
      </div>
    </div>
  );
};

export default UserProfile;
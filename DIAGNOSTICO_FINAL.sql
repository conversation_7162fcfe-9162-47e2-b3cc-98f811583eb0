-- =====================================================
-- DIAGNÓSTICO PROFUNDO: Descobrir a causa raiz
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR SE O USUÁRIO É EXECUTOR DA TAREFA
SELECT 
    'TESTE 1: É EXECUTOR?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - É EXECUTOR'
        ELSE '❌ NÃO - NÃO É EXECUTOR'
    END as status
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 2. VERIFICAR SE A TAREFA TEM BLOCOS DE CONTEÚDO
SELECT 
    'TESTE 2: TEM BLOCOS?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - TEM BLOCOS'
        ELSE '❌ NÃO - SEM BLOCOS'
    END as status
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- 3. SE TEM BLOCOS, MOSTRAR DETALHES
SELECT 
    'TESTE 3: DETALHES DOS BLOCOS' as teste,
    tcb.id,
    tcb.type,
    LEFT(tcb.content::text, 100) as content_preview,
    tcb.created_at
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
ORDER BY tcb.order;

-- 4. VERIFICAR TODOS OS EXECUTORES DESTA TAREFA
SELECT 
    'TESTE 4: TODOS OS EXECUTORES' as teste,
    te.user_id,
    p.name,
    p.email,
    p.role
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- 5. SIMULAR A QUERY QUE O APP FAZ (com auth.uid())
-- ATENÇÃO: Esta query simula o acesso como usuário específico
SELECT 
    'TESTE 5: SIMULAÇÃO DE ACESSO' as teste,
    tcb.id,
    tcb.type,
    'BLOCO ACESSÍVEL' as acesso
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND (
        -- Teste: é executor?
        tcb.task_id IN (
            SELECT te.task_id FROM task_executors te
            WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
        )
    );

-- =====================================================
-- INTERPRETAÇÃO DOS RESULTADOS:
-- =====================================================
-- TESTE 1 = ❌ NÃO → Usuário não foi designado como executor
-- TESTE 2 = ❌ NÃO → A tarefa não tem conteúdo criado
-- TESTE 1 = ✅ SIM E TESTE 2 = ✅ SIM → Problema na política RLS
-- =====================================================

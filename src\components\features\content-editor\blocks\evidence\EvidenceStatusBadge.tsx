import React from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, XCircle, User, Calendar } from 'lucide-react';
import { Evidence } from '@/types';

interface EvidenceStatusBadgeProps {
  evidence: Evidence;
  showDetails?: boolean;
}

/**
 * Componente para exibir o status de aprovação de uma evidência
 */
export const EvidenceStatusBadge: React.FC<EvidenceStatusBadgeProps> = ({
  evidence,
  showDetails = false
}) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'approved':
        return {
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 border-green-300',
          icon: CheckCircle,
          label: 'Aprovada',
          color: 'text-green-600'
        };
      case 'rejected':
        return {
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 border-red-300',
          icon: XCircle,
          label: 'Reprovada',
          color: 'text-red-600'
        };
      case 'pending':
      default:
        return {
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-300',
          icon: Clock,
          label: 'Pendente',
          color: 'text-yellow-600'
        };
    }
  };

  const config = getStatusConfig(evidence.status);
  const Icon = config.icon;

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-2">
      {/* Badge principal */}
      <Badge 
        variant={config.variant}
        className={`${config.className} flex items-center gap-1`}
      >
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>

      {/* Detalhes expandidos */}
      {showDetails && (
        <div className="text-xs space-y-1">
          {/* Informações de aprovação/rejeição */}
          {evidence.status === 'approved' && evidence.approvedBy && (
            <div className="flex items-center gap-1 text-green-700">
              <User className="h-3 w-3" />
              <span>Aprovada por: {evidence.approvedBy.name}</span>
            </div>
          )}
          
          {evidence.status === 'rejected' && evidence.approvedBy && (
            <div className="flex items-center gap-1 text-red-700">
              <User className="h-3 w-3" />
              <span>Reprovada por: {evidence.approvedBy.name}</span>
            </div>
          )}

          {/* Data de aprovação/rejeição */}
          {evidence.approvedAt && (
            <div className="flex items-center gap-1 text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(evidence.approvedAt)}</span>
            </div>
          )}

          {/* Motivo da rejeição */}
          {evidence.status === 'rejected' && evidence.rejectionReason && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-800">
              <div className="font-medium text-xs mb-1">Motivo da reprovação:</div>
              <div className="text-xs">{evidence.rejectionReason}</div>
            </div>
          )}

          {/* Status pendente */}
          {evidence.status === 'pending' && (
            <div className="text-yellow-700">
              <span>Aguardando aprovação</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Componente compacto para exibir apenas o status
 */
export const EvidenceStatusIcon: React.FC<{ status: string }> = ({ status }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'approved':
        return { icon: CheckCircle, className: 'text-green-600' };
      case 'rejected':
        return { icon: XCircle, className: 'text-red-600' };
      case 'pending':
      default:
        return { icon: Clock, className: 'text-yellow-600' };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return <Icon className={`h-4 w-4 ${config.className}`} />;
};

/**
 * Hook para obter informações de status
 */
export const useEvidenceStatus = (evidence: Evidence) => {
  const isPending = evidence.status === 'pending';
  const isApproved = evidence.status === 'approved';
  const isRejected = evidence.status === 'rejected';

  const statusText = {
    pending: 'Pendente de aprovação',
    approved: 'Aprovada',
    rejected: 'Reprovada'
  }[evidence.status] || 'Status desconhecido';

  const canBeDeleted = evidence.status !== 'approved';
  const canBeResubmitted = evidence.status === 'rejected';

  return {
    isPending,
    isApproved,
    isRejected,
    statusText,
    canBeDeleted,
    canBeResubmitted
  };
};

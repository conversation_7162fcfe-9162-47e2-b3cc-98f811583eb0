import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface StageBreadcrumbProps {
  projectId?: string;
  projectName?: string;
  stageName?: string;
}

export const StageBreadcrumb: React.FC<StageBreadcrumbProps> = ({ projectId, projectName, stageName }) => {
  const navigate = useNavigate();
  return (
    <div className="flex items-center gap-2 text-sm text-gray-600">
      <Button variant="ghost" size="sm" className="p-0" onClick={() => {
        if (projectId) {
          navigate(`/project/${projectId}`);
        } else {
          navigate('/');
        }
      }}>
        <ArrowLeft className="w-4 h-4 mr-1" />
        Voltar ao Projeto
      </Button>
      <span>/</span>
      <span>{projectName || 'Projeto'}</span>
      <span>/</span>
      <span className="text-stage font-medium">{stageName || 'Etapa'}</span>
    </div>
  );
}; 
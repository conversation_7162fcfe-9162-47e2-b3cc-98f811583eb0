-- DIAGNÓSTICO: Verificar RLS e permissões na tabela task_executors

-- 1. Verificar se RLS está ativado
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'task_executors';

-- 2. Verificar políticas RLS existentes
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'task_executors';

-- 3. Tentar buscar dados diretamente (sem RLS)
SELECT COUNT(*) as total_records FROM task_executors;

-- 4. Verificar dados específicos do usuário
SELECT * FROM task_executors 
WHERE user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 5. Verificar se o usuário atual tem permissão
SELECT current_user, current_role;

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $getNearestNodeFromDOMNode,
  $getNodeByKey,
  $getRoot,
  $getSelection,
  $isRangeSelection,
  DRAG_DROP_PASTE,
} from 'lexical';
import { useEffect } from 'react';
import { mergeRegister } from '@lexical/utils';

const ACCEPTABLE_DROP_TYPES = ['text', 'file'];

function isHTMLElement(x: EventTarget | null): x is HTMLElement {
  return x != null && typeof (x as HTMLElement).getBoundingClientRect === 'function';
}

export function DragDropPastePlugin() {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return mergeRegister(
      editor.registerCommand(
        DRAG_DROP_PASTE,
        (files) => {
          (async () => {
            const filesResult = await Promise.all(
              files.map(async (file) => {
                if (file.type.startsWith('text/')) {
                  return await file.text();
                }
                return null;
              }),
            );

            editor.update(() => {
              const selection = $getSelection();
              if ($isRangeSelection(selection)) {
                filesResult.forEach((text) => {
                  if (text) {
                    selection.insertText(text);
                  }
                });
              }
            });
          })();
          return true;
        },
        1,
      ),
    );
  }, [editor]);

  useEffect(() => {
    const rootElement = editor.getRootElement();
    if (rootElement === null) {
      return;
    }

    function onDragover(event: DragEvent): boolean {
      const [isFileTransfer] = eventFiles(event);
      if (isFileTransfer) {
        event.preventDefault();
        return true;
      }
      return false;
    }

    function onDrop(event: DragEvent): boolean {
      const [isFileTransfer, files] = eventFiles(event);
      if (isFileTransfer) {
        event.preventDefault();
        if (files.length > 0) {
          editor.dispatchCommand(DRAG_DROP_PASTE, files);
        }
        return true;
      }
      return false;
    }

    rootElement.addEventListener('dragover', onDragover);
    rootElement.addEventListener('drop', onDrop);

    return () => {
      rootElement.removeEventListener('dragover', onDragover);
      rootElement.removeEventListener('drop', onDrop);
    };
  }, [editor]);

  return null;
}

function eventFiles(event: DragEvent): [boolean, Array<File>, boolean] {
  const dataTransfer = event.dataTransfer;
  if (!dataTransfer) {
    return [false, [], false];
  }

  // Types is not iterable in IE11
  const types = Array.from(dataTransfer.types);
  const hasFiles = types.includes('Files');
  const hasContent = types.some((t) => ACCEPTABLE_DROP_TYPES.includes(t));

  if (!hasFiles && !hasContent) {
    return [false, [], false];
  }

  const files = Array.from(dataTransfer.files);
  return [true, files, hasFiles];
}
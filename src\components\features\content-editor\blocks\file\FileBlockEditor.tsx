import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { FileBlockContent, BlockConfig, defaultBlockConfig, blockTypeDefaultColors } from '@/types';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { supabase } from '@/lib/supabaseClient';
import { useAuth } from '@/auth/useAuth';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import * as LucideIcons from 'lucide-react';

/**
 * Editor para blocos de arquivo.
 * @param editContent Conteúdo do bloco (tipado)
 * @param setEditContent Callback para atualizar o conteúdo
 * @param mode 'edit' para edição, 'preview' para visualização
 * @param config Configurações do bloco
 */
export interface FileBlockEditorProps {
  editContent: FileBlockContent;
  setEditContent: (c: FileBlockContent) => void;
  mode: 'edit' | 'preview';
  config?: BlockConfig;
}

const MAX_SIZE_MB = 10;
const ALLOWED_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/msword', // .doc
  'text/plain',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.ms-excel', // .xls
];

function validateFile(file: File) {
  if (!ALLOWED_TYPES.includes(file.type)) {
    return 'Tipo de arquivo não suportado.';
  }
  if (file.size > MAX_SIZE_MB * 1024 * 1024) {
    return `Arquivo maior que ${MAX_SIZE_MB}MB.`;
  }
  return null;
}

export const FileBlockEditor: React.FC<FileBlockEditorProps> = ({ editContent, setEditContent, mode, config }) => {
  const safeConfig = config || defaultBlockConfig;
  const cardTextColor = safeConfig.card?.font?.color || '#1f2937';
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const { user } = useAuth();

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      return;
    }
    setUploading(true);
    setUploadError(null);
    setProgress(0);
    try {
      const filePath = `file-blocks/${Date.now()}_${file.name}`;
      const { data, error: uploadErr } = await supabase.storage.from('arquivos').upload(filePath, file, {
        upsert: false,
      });
      if (uploadErr) throw uploadErr;
      const { data: urlData } = supabase.storage.from('arquivos').getPublicUrl(filePath);
      // Extrair nome sem extensão para o título
      const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '');
      if (editContent.url) {
        setEditContent({
          ...editContent,
          versionHistory: [
            ...(editContent.versionHistory || []),
            {
              url: editContent.url,
              uploadedAt: editContent.uploadedAt,
              uploadedBy: editContent.uploadedBy,
              name: editContent.name,
              size: editContent.size,
              type: editContent.type,
              fileName: editContent.fileName, // manter nome original anterior
            },
          ],
          logs: [
            ...(editContent.logs || []),
            {
              action: 'replace',
              user: user?.email || 'desconhecido',
              date: new Date().toISOString(),
              fileName: file.name,
              fileUrl: urlData.publicUrl,
            },
          ],
          name: nameWithoutExt, // título sem extensão
          fileName: file.name, // nome original com extensão
          url: urlData.publicUrl,
          size: file.size,
          type: file.type,
          uploadedAt: new Date().toISOString(),
          uploadedBy: user?.email || 'desconhecido',
        });
      } else {
        setEditContent({
          ...editContent,
          name: nameWithoutExt, // título sem extensão
          fileName: file.name, // nome original com extensão
          url: urlData.publicUrl,
          size: file.size,
          type: file.type,
          uploadedAt: new Date().toISOString(),
          uploadedBy: user?.email || 'desconhecido',
          logs: [
            ...(editContent.logs || []),
            {
              action: 'upload',
              user: user?.email || 'desconhecido',
              date: new Date().toISOString(),
              fileName: file.name,
              fileUrl: urlData.publicUrl,
            },
          ],
        });
      }
    } catch (err: any) {
      setUploadError('Erro ao fazer upload: ' + (err.message || 'Erro desconhecido.'));
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const handleRemoveFile = async () => {
    if (!editContent.url) return;
    setUploading(true);
    setUploadError(null);
    try {
      // Extrair caminho do arquivo do URL público
      const urlParts = editContent.url.split('/');
      const filePath = urlParts.slice(urlParts.indexOf('arquivos') + 1).join('/');
      await supabase.storage.from('arquivos').remove([filePath]);
      setEditContent({
        ...editContent,
        versionHistory: [
          ...(editContent.versionHistory || []),
          {
            url: editContent.url,
            uploadedAt: editContent.uploadedAt,
            uploadedBy: editContent.uploadedBy,
            name: editContent.name,
            size: editContent.size,
            type: editContent.type,
          },
        ],
        logs: [
          ...(editContent.logs || []),
          {
            action: 'remove',
            user: user?.email || 'desconhecido',
            date: new Date().toISOString(),
            fileName: editContent.name,
            fileUrl: editContent.url,
          },
        ],
        name: '',
        url: '',
        size: undefined,
        type: undefined,
        uploadedAt: undefined,
        uploadedBy: undefined,
      });
    } catch (err: any) {
      setUploadError('Erro ao remover arquivo: ' + (err.message || 'Erro desconhecido.'));
    } finally {
      setUploading(false);
    }
  };

  if (mode === 'edit') {
    return (
      <div className="space-y-2 min-w-0">
        <div className="space-y-1">
          <Label htmlFor="file-title">Título do arquivo</Label>
          <Input
            id="file-title"
            value={editContent.name || ''}
            onChange={e => setEditContent({ ...editContent, name: e.target.value })}
            placeholder="Título do arquivo"
            className="mb-1"
          />
          {editContent.fileName && (
            <div className="text-xs text-gray-500 mb-1">
              <b>Arquivo original:</b> {editContent.fileName}
            </div>
          )}
        </div>
        {editContent && (
          <div className="flex flex-col gap-2 mb-2 mt-2">
            <div className="flex items-center gap-2">
              <Checkbox
                id="allowDownload"
                checked={!!editContent.allowDownload}
                onCheckedChange={checked => setEditContent({ ...editContent, allowDownload: !!checked })}
              />
              <label htmlFor="allowDownload" className="text-sm cursor-pointer select-none">
                Permitir download
              </label>
            </div>
            <div className="flex items-center gap-2">
              <Checkbox
                id="allowInlineView"
                checked={!!editContent.allowInlineView}
                onCheckedChange={checked => setEditContent({ ...editContent, allowInlineView: !!checked })}
              />
              <label htmlFor="allowInlineView" className="text-sm cursor-pointer select-none">
                Permitir visualização inline
              </label>
            </div>
          </div>
        )}
        <div className="mb-2 flex flex-col gap-2">
          <Label htmlFor="file-upload">Arquivo</Label>
          <input
            id="file-upload"
            type="file"
            accept={ALLOWED_TYPES.join(',')}
            onChange={handleFileUpload}
            disabled={uploading}
            className="block file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
          />
          {editContent.url && (
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="w-fit mt-1"
              style={{ background: blockTypeDefaultColors.file, color: '#fff' }}
              onClick={handleRemoveFile}
              disabled={uploading}
            >
              Remover arquivo
            </Button>
          )}
          {uploading && <div className="text-xs text-blue-600">Enviando... {progress}%</div>}
          {uploadError && <div className="text-xs text-red-600">{uploadError}</div>}
        </div>
        {editContent && (
          <div className="mb-2 text-xs text-gray-600">
            <div><b>Nome:</b> {editContent.fileName || '-'}</div>
            <div><b>Tipo:</b> {editContent.type}</div>
            <div><b>Tamanho:</b> {editContent.size ? `${(editContent.size/1024).toFixed(1)} KB` : '-'}</div>
            <div><b>Enviado em:</b> {editContent.uploadedAt || '-'}</div>
            <div><b>Enviado por:</b> {editContent.uploadedBy || '-'}</div>
          </div>
        )}
        {editContent && editContent.versionHistory && editContent.versionHistory.length > 0 && (
          <div className="mb-2">
            <div className="font-bold text-xs text-indigo-700 mb-1">Histórico de versões</div>
            <ul className="text-xs text-gray-500 list-disc ml-4">
              {editContent.versionHistory.map((v, idx) => (
                <li key={v.url+idx}>{v.name} - {v.uploadedAt} - {v.uploadedBy}</li>
              ))}
            </ul>
          </div>
        )}
        {editContent && editContent.logs && editContent.logs.length > 0 && (
          <div className="mb-2">
            <div className="font-bold text-xs text-indigo-700 mb-1">Logs de ações</div>
            <ul className="text-xs text-gray-500 list-disc ml-4">
              {editContent.logs.map((log, idx) => (
                <li key={log.date+idx}>{log.action} por {log.user} em {log.date} ({log.fileName})</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }
  // Preview
  const [iframeError, setIframeError] = useState(false);
  const isInlineView = editContent.allowInlineView && editContent.url && editContent.type;
  const isDownloadAllowed = !!editContent.allowDownload && editContent.url;
  const fileType = editContent.type || '';
  const fileUrl = editContent.url || '';
  // Tipos suportados para visualização inline
  const inlineTypes = [
    'application/pdf',
    'text/plain',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/msword', // .doc
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
  ];
  const isSupportedInline = inlineTypes.includes(fileType);
  if (mode === 'preview') {
    const safeConfig = config || defaultBlockConfig;
    const showIcon = safeConfig.icon?.enabled;
    const iconPosition = safeConfig.icon?.position || 'left-title';
    const buttonConfig = safeConfig.button || {};
    const buttonBgColor = buttonConfig.backgroundColor || '#7c3aed';
    const buttonTextColor = buttonConfig.color || '#fff';
    const buttonStyle = buttonConfig.style || 'rounded';
    const buttonBorder = buttonConfig.border?.enabled;
    const buttonBorderColor = buttonConfig.border?.color || buttonBgColor;
    const buttonBorderWidth = buttonConfig.border?.width || 2;
    const buttonShadow = buttonConfig.shadow?.enabled;
    const buttonHover = buttonConfig.hover?.enabled;
    const buttonText = buttonConfig.text || 'Baixar';
    const buttonStyleObj = {
      background: buttonBgColor,
      color: buttonTextColor,
      borderRadius: buttonStyle === 'rounded' ? '9999px' : buttonStyle === 'flat' ? '0' : '9999px',
      padding: '0.5rem 1.5rem',
      fontWeight: 600,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: buttonShadow ? '0 6px 24px rgba(60,60,60,0.25), 0 1.5px 4px rgba(60,60,60,0.10)' : undefined,
      border: buttonBorder ? `${buttonBorderWidth}px solid ${buttonBorderColor}` : 'none',
      transition: buttonHover ? 'transform 0.15s, box-shadow 0.15s, filter 0.15s' : undefined,
      cursor: 'pointer',
      width: '100%',
      maxWidth: '320px',
    };
    return (
      <div
        style={{
          background: safeConfig.card?.backgroundColor || '#f8fafc',
          border: safeConfig.card?.border?.enabled
            ? `${safeConfig.card?.border?.width || 1}px solid ${safeConfig.card?.border?.color || '#e5e5e5'}`
            : 'none',
          borderRadius:
            safeConfig.card?.format === 'pill'
              ? 9999
              : safeConfig.card?.format === 'square'
              ? 0
              : 12,
          boxShadow: safeConfig.card?.shadow?.enabled
            ? `0 2px ${2 * (safeConfig.card?.shadow?.depth || 1)}px #0002`
            : 'none',
          padding: 16,
          minHeight: 80,
          width: '100%',
          transition: 'all 0.2s',
        }}
        className="flex flex-col gap-1 w-full"
      >
        {/* Header: ícone + título */}
        <BlockCardIcon
          config={safeConfig.icon}
          title={editContent.name || 'Sem título'}
          truncateText={false}
          textColor={cardTextColor}
        />
        {/* Visualização inline */}
        {editContent.allowInlineView && fileUrl && isSupportedInline && (
          <div className="flex-1 flex items-center justify-center p-4" style={{minHeight: 200}}>
            {/* PDF */}
            {fileType === 'application/pdf' && (
              <iframe
                src={fileUrl}
                title="Visualização PDF"
                width="100%"
                height="400px"
                aria-label="Visualização PDF"
                style={{ border: 0, width: '100%', minHeight: 300 }}
              />
            )}
            {/* TXT */}
            {fileType === 'text/plain' && (
              <iframe
                src={fileUrl}
                title="Visualização TXT"
                width="100%"
                height="200px"
                aria-label="Visualização TXT"
                style={{ border: 0, width: '100%', minHeight: 100 }}
              />
            )}
            {/* Word/Excel: Google Docs Viewer */}
            {(fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
              fileType === 'application/msword' ||
              fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
              fileType === 'application/vnd.ms-excel') && (
              <iframe
                src={`https://docs.google.com/gview?url=${encodeURIComponent(fileUrl)}&embedded=true`}
                title="Visualização Office"
                width="100%"
                height="400px"
                aria-label="Visualização Office"
                style={{ border: 0, width: '100%', minHeight: 300 }}
              />
            )}
          </div>
        )}
        {/* Footer: botão de download */}
        {editContent.allowDownload && fileUrl && (
          <div className="border-t px-4 py-2 flex justify-center">
            <a
              href={fileUrl}
              download={editContent.name}
              style={buttonStyleObj}
              className={buttonHover ? 'hover:scale-105 hover:shadow-2xl hover:brightness-105 transition-all' : ''}
              target="_blank"
              rel="noopener noreferrer"
            >
              {(() => {
                const iconName = safeConfig.icon?.iconName || 'FileText';
                const IconComp = (LucideIcons as any)[iconName];
                const iconColor = buttonConfig.color || '#fff';
                return IconComp ? <IconComp className="w-4 h-4 mr-1" color={iconColor} /> : null;
              })()}
              {buttonText}
            </a>
          </div>
        )}
      </div>
    );
  }
  // Corrigir escopo do buttonConfig para o modo não-preview
  const buttonConfig = safeConfig.button || {};
  return (
    <div className="space-y-2 min-w-0">
      <div className="mb-2 text-xs text-gray-600">
        <div><b>Nome:</b> {editContent.fileName || '-'}</div>
        <div><b>Tipo:</b> {editContent.type}</div>
        <div><b>Tamanho:</b> {editContent.size ? `${(editContent.size/1024).toFixed(1)} KB` : '-'}</div>
        <div><b>Enviado em:</b> {editContent.uploadedAt || '-'}</div>
        <div><b>Enviado por:</b> {editContent.uploadedBy || '-'}</div>
      </div>
      {isInlineView && isSupportedInline && fileUrl && !iframeError && (
        <div className="flex flex-col gap-1 w-full" style={{ minHeight: 200 }}>
          {/* PDF */}
          {fileType === 'application/pdf' && (
            <iframe
              src={fileUrl}
              title="Visualização PDF"
              width="100%"
              height="400px"
              aria-label="Visualização PDF"
              style={{ border: 0, width: '100%', minHeight: 300 }}
              onError={() => setIframeError(true)}
            />
          )}
          {/* TXT */}
          {fileType === 'text/plain' && (
            <iframe
              src={fileUrl}
              title="Visualização TXT"
              width="100%"
              height="200px"
              aria-label="Visualização TXT"
              style={{ border: 0, width: '100%', minHeight: 100 }}
              onError={() => setIframeError(true)}
            />
          )}
          {/* Word/Excel: tentar via Google Docs Viewer */}
          {(fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            fileType === 'application/msword' ||
            fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            fileType === 'application/vnd.ms-excel') && (
            <iframe
              src={`https://docs.google.com/gview?url=${encodeURIComponent(fileUrl)}&embedded=true`}
              title="Visualização Office"
              width="100%"
              height="400px"
              aria-label="Visualização Office"
              style={{ border: 0, width: '100%', minHeight: 300 }}
              onError={() => setIframeError(true)}
            />
          )}
        </div>
      )}
      {iframeError && (
        <div className="text-xs text-red-700 bg-red-50 border border-red-200 rounded p-2 mb-2">
          Não foi possível carregar o arquivo para visualização inline.<br />
          Faça o download para visualizar.
        </div>
      )}
      {isInlineView && !isSupportedInline && fileUrl && !iframeError && (
        <div className="text-xs text-yellow-700 bg-yellow-50 border border-yellow-200 rounded p-2 mb-2">
          Visualização inline não suportada para este tipo de arquivo.<br />
          Faça o download para visualizar.
        </div>
      )}
      {isDownloadAllowed && fileUrl && (
        <a
          href={fileUrl}
          download={editContent.name}
          className="inline-block px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-xs"
          target="_blank"
          rel="noopener noreferrer"
        >
          {(() => {
            const iconName = safeConfig.icon?.iconName || 'FileText';
            const IconComp = (LucideIcons as any)[iconName];
            const iconColor = buttonConfig.color || '#fff';
            return IconComp ? <IconComp className="w-4 h-4 mr-1" color={iconColor} /> : null;
          })()}
          Baixar arquivo
        </a>
      )}
      {!fileUrl && (
        <div className="text-xs text-gray-500">Nenhum arquivo anexado.</div>
      )}
    </div>
  );
};
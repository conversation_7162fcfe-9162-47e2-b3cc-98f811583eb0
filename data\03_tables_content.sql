-- =====================================================
-- TABELAS DE CONTEÚDO
-- =====================================================
-- Contém: task_content_blocks, task_attachments, evidence, task_comments, 
--         project_history, user_notifications, quizzes, quiz_attempts, 
--         quiz_answers, user_quiz_progress, quiz_statistics
-- Dependências: 01_tables_core.sql, 02_tables_relations.sql
-- Versão: 2.0 - Julho 2025

-- Verificar dependências
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks') THEN
    RAISE EXCEPTION 'Tabela tasks não encontrada. Execute 01_tables_core.sql primeiro.';
  END IF;
  RAISE NOTICE '✅ Dependências verificadas';
END $$;

-- =====================================================
-- TABELA: task_content_blocks
-- =====================================================
-- Blocos de conteúdo das tarefas (texto, vídeo, imagem, quiz, etc.)

CREATE TABLE public.task_content_blocks (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  task_id uuid NOT NULL,
  type text NOT NULL,
  content jsonb NOT NULL,
  config jsonb NULL,
  "order" integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT task_content_blocks_pkey PRIMARY KEY (id),
  CONSTRAINT task_content_blocks_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON DELETE CASCADE,
  CONSTRAINT task_content_blocks_type_check CHECK (type IN ('text', 'video', 'image', 'file', 'quiz', 'colored-block', 'evidence', 'separator', 'checklist')),
  CONSTRAINT task_content_blocks_order_check CHECK ("order" >= 0)
);

-- Índices para performance
CREATE INDEX idx_task_content_blocks_task ON public.task_content_blocks(task_id);
CREATE INDEX idx_task_content_blocks_type ON public.task_content_blocks(type);
CREATE INDEX idx_task_content_blocks_order ON public.task_content_blocks(task_id, "order");

-- Comentários
COMMENT ON TABLE public.task_content_blocks IS 'Blocos de conteúdo das tarefas';
COMMENT ON COLUMN public.task_content_blocks.type IS 'Tipo: text, video, image, file, quiz, colored-block, evidence, separator, checklist';
COMMENT ON COLUMN public.task_content_blocks.content IS 'Conteúdo do bloco (JSON flexível)';
COMMENT ON COLUMN public.task_content_blocks.config IS 'Configuração visual do bloco (JSON)';

-- =====================================================
-- TABELA: task_attachments
-- =====================================================
-- Anexos das tarefas (arquivos, evidências com aprovação)

CREATE TABLE public.task_attachments (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  task_id uuid NOT NULL,
  uploaded_by uuid NOT NULL,
  file_url text NOT NULL,
  file_name text,
  file_size bigint,
  mime_type text,
  description text,
  status varchar(20) DEFAULT 'pending',
  approved_by uuid,
  approved_at timestamp with time zone,
  rejection_reason text,
  block_id varchar(255),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT task_attachments_pkey PRIMARY KEY (id),
  CONSTRAINT task_attachments_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks (id) ON DELETE CASCADE,
  CONSTRAINT task_attachments_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.profiles (id) ON DELETE CASCADE,
  CONSTRAINT task_attachments_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.profiles (id) ON DELETE SET NULL,
  CONSTRAINT task_attachments_status_check CHECK (status IN ('pending', 'approved', 'rejected')),
  CONSTRAINT task_attachments_file_size_check CHECK (file_size >= 0)
);

-- Índices para performance
CREATE INDEX idx_task_attachments_task ON public.task_attachments(task_id);
CREATE INDEX idx_task_attachments_status ON public.task_attachments(status);
CREATE INDEX idx_task_attachments_uploaded_by ON public.task_attachments(uploaded_by);
CREATE INDEX idx_task_attachments_approved_by ON public.task_attachments(approved_by);
CREATE INDEX idx_task_attachments_block_id ON public.task_attachments(block_id);
CREATE INDEX idx_task_attachments_task_block ON public.task_attachments(task_id, block_id);

-- Comentários
COMMENT ON TABLE public.task_attachments IS 'Anexos das tarefas com sistema de aprovação';
COMMENT ON COLUMN public.task_attachments.status IS 'Status: pending, approved, rejected';
COMMENT ON COLUMN public.task_attachments.block_id IS 'ID do bloco de evidência relacionado';

-- =====================================================
-- TABELA: evidence
-- =====================================================
-- Evidências das tarefas (sistema avançado de aprovação)

CREATE TABLE public.evidence (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  task_id uuid NOT NULL,
  block_id text NOT NULL,
  type text NOT NULL,
  content text NOT NULL,
  file_name text,
  file_size bigint,
  mime_type text,
  uploaded_by uuid NOT NULL,
  status text DEFAULT 'pending',
  approved_by uuid,
  approved_at timestamp with time zone,
  rejection_reason text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT evidence_pkey PRIMARY KEY (id),
  CONSTRAINT evidence_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks (id) ON DELETE CASCADE,
  CONSTRAINT evidence_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.profiles (id) ON DELETE CASCADE,
  CONSTRAINT evidence_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.profiles (id) ON DELETE SET NULL,
  CONSTRAINT evidence_type_check CHECK (type IN ('file', 'image', 'text', 'url')),
  CONSTRAINT evidence_status_check CHECK (status IN ('pending', 'approved', 'rejected')),
  CONSTRAINT evidence_file_size_check CHECK (file_size >= 0)
);

-- Índices para performance
CREATE INDEX idx_evidence_task ON public.evidence(task_id);
CREATE INDEX idx_evidence_block ON public.evidence(block_id);
CREATE INDEX idx_evidence_status ON public.evidence(status);
CREATE INDEX idx_evidence_uploaded_by ON public.evidence(uploaded_by);
CREATE INDEX idx_evidence_approved_by ON public.evidence(approved_by);
CREATE INDEX idx_evidence_task_block ON public.evidence(task_id, block_id);

-- Comentários
COMMENT ON TABLE public.evidence IS 'Sistema avançado de evidências com aprovação';
COMMENT ON COLUMN public.evidence.type IS 'Tipo: file, image, text, url';
COMMENT ON COLUMN public.evidence.status IS 'Status: pending, approved, rejected';

-- =====================================================
-- TABELA: task_comments
-- =====================================================
-- Comentários nas tarefas (com threading)

CREATE TABLE public.task_comments (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  task_id uuid NOT NULL,
  author uuid NOT NULL,
  content text NOT NULL,
  parent_id uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT task_comments_pkey PRIMARY KEY (id),
  CONSTRAINT task_comments_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks (id) ON DELETE CASCADE,
  CONSTRAINT task_comments_author_fkey FOREIGN KEY (author) REFERENCES public.profiles (id) ON DELETE CASCADE,
  CONSTRAINT task_comments_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.task_comments (id) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX idx_task_comments_task ON public.task_comments(task_id);
CREATE INDEX idx_task_comments_author ON public.task_comments(author);
CREATE INDEX idx_task_comments_parent ON public.task_comments(parent_id);
CREATE INDEX idx_task_comments_created_at ON public.task_comments(created_at);

-- Comentários
COMMENT ON TABLE public.task_comments IS 'Comentários nas tarefas com suporte a threading';
COMMENT ON COLUMN public.task_comments.parent_id IS 'ID do comentário pai (para threading)';

-- =====================================================
-- TABELA: project_history
-- =====================================================
-- Histórico de alterações nos projetos

CREATE TABLE public.project_history (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  project_id uuid NOT NULL,
  user_id uuid,
  action text NOT NULL,
  details jsonb,
  created_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT project_history_pkey PRIMARY KEY (id),
  CONSTRAINT project_history_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects (id) ON DELETE CASCADE,
  CONSTRAINT project_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles (id) ON DELETE SET NULL
);

-- Índices para performance
CREATE INDEX idx_project_history_project ON public.project_history(project_id);
CREATE INDEX idx_project_history_user ON public.project_history(user_id);
CREATE INDEX idx_project_history_action ON public.project_history(action);
CREATE INDEX idx_project_history_created_at ON public.project_history(created_at);

-- Comentários
COMMENT ON TABLE public.project_history IS 'Histórico de alterações nos projetos';
COMMENT ON COLUMN public.project_history.details IS 'Detalhes da ação (JSON)';

-- =====================================================
-- TABELA: user_notifications
-- =====================================================
-- Notificações para usuários

CREATE TABLE public.user_notifications (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  user_id uuid NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  type text DEFAULT 'info',
  is_read boolean DEFAULT false,
  link text,
  created_at timestamp with time zone DEFAULT now(),
  read_at timestamp with time zone,
  
  CONSTRAINT user_notifications_pkey PRIMARY KEY (id),
  CONSTRAINT user_notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles (id) ON DELETE CASCADE,
  CONSTRAINT user_notifications_type_check CHECK (type IN ('info', 'warning', 'error', 'success'))
);

-- Índices para performance
CREATE INDEX idx_user_notifications_user ON public.user_notifications(user_id);
CREATE INDEX idx_user_notifications_unread ON public.user_notifications(user_id, is_read);
CREATE INDEX idx_user_notifications_created_at ON public.user_notifications(created_at);

-- Comentários
COMMENT ON TABLE public.user_notifications IS 'Notificações para usuários';
COMMENT ON COLUMN public.user_notifications.type IS 'Tipo: info, warning, error, success';

-- =====================================================
-- SISTEMA DE QUIZ
-- =====================================================

-- TABELA: quizzes
CREATE TABLE public.quizzes (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  task_id uuid NOT NULL,
  block_id text NOT NULL,
  content jsonb NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  is_active boolean DEFAULT true,
  
  CONSTRAINT quizzes_pkey PRIMARY KEY (id),
  CONSTRAINT quizzes_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON DELETE CASCADE,
  CONSTRAINT quizzes_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.profiles(id),
  CONSTRAINT quizzes_block_id_unique UNIQUE (task_id, block_id)
);

-- Índices para performance
CREATE INDEX idx_quizzes_task ON public.quizzes(task_id);
CREATE INDEX idx_quizzes_block ON public.quizzes(block_id);
CREATE INDEX idx_quizzes_created_by ON public.quizzes(created_by);

-- TABELA: quiz_attempts
CREATE TABLE public.quiz_attempts (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  quiz_id uuid NOT NULL,
  user_id uuid NOT NULL,
  attempt_number integer NOT NULL DEFAULT 1,
  started_at timestamp with time zone DEFAULT now(),
  submitted_at timestamp with time zone,
  time_spent integer DEFAULT 0,
  score integer DEFAULT 0,
  max_score integer DEFAULT 0,
  percentage decimal(5,2) DEFAULT 0,
  passed boolean DEFAULT false,
  status text DEFAULT 'in_progress',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT quiz_attempts_pkey PRIMARY KEY (id),
  CONSTRAINT quiz_attempts_quiz_id_fkey FOREIGN KEY (quiz_id) REFERENCES public.quizzes(id) ON DELETE CASCADE,
  CONSTRAINT quiz_attempts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT quiz_attempts_status_check CHECK (status IN ('in_progress', 'submitted', 'graded')),
  CONSTRAINT quiz_attempts_unique_user_quiz_attempt UNIQUE (quiz_id, user_id, attempt_number)
);

-- Índices para performance
CREATE INDEX idx_quiz_attempts_quiz ON public.quiz_attempts(quiz_id);
CREATE INDEX idx_quiz_attempts_user ON public.quiz_attempts(user_id);
CREATE INDEX idx_quiz_attempts_status ON public.quiz_attempts(status);
CREATE INDEX idx_quiz_attempts_attempt_number ON public.quiz_attempts(quiz_id, user_id, attempt_number);

-- TABELA: quiz_answers
CREATE TABLE public.quiz_answers (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  attempt_id uuid NOT NULL,
  question_id text NOT NULL,
  question_type text NOT NULL,
  selected_options text[],
  boolean_answer boolean,
  text_answer text,
  ordered_items text[],
  matched_pairs jsonb,
  time_spent integer DEFAULT 0,
  is_correct boolean DEFAULT false,
  points_earned integer DEFAULT 0,
  feedback text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT quiz_answers_pkey PRIMARY KEY (id),
  CONSTRAINT quiz_answers_attempt_id_fkey FOREIGN KEY (attempt_id) REFERENCES public.quiz_attempts(id) ON DELETE CASCADE,
  CONSTRAINT quiz_answers_unique_attempt_question UNIQUE (attempt_id, question_id)
);

-- Índices para performance
CREATE INDEX idx_quiz_answers_attempt ON public.quiz_answers(attempt_id);
CREATE INDEX idx_quiz_answers_question ON public.quiz_answers(question_id);

-- TABELA: user_quiz_progress
CREATE TABLE public.user_quiz_progress (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  user_id uuid NOT NULL,
  quiz_id uuid NOT NULL,
  total_attempts integer DEFAULT 0,
  best_score integer DEFAULT 0,
  best_percentage decimal(5,2) DEFAULT 0,
  passed boolean DEFAULT false,
  first_attempt_at timestamp with time zone,
  last_attempt_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT user_quiz_progress_pkey PRIMARY KEY (id),
  CONSTRAINT user_quiz_progress_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id),
  CONSTRAINT user_quiz_progress_quiz_id_fkey FOREIGN KEY (quiz_id) REFERENCES public.quizzes(id) ON DELETE CASCADE,
  CONSTRAINT user_quiz_progress_unique_user_quiz UNIQUE (user_id, quiz_id)
);

-- Índices para performance
CREATE INDEX idx_user_quiz_progress_user ON public.user_quiz_progress(user_id);
CREATE INDEX idx_user_quiz_progress_quiz ON public.user_quiz_progress(quiz_id);

-- TABELA: quiz_statistics
CREATE TABLE public.quiz_statistics (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  quiz_id uuid NOT NULL,
  total_attempts integer DEFAULT 0,
  unique_users integer DEFAULT 0,
  average_score decimal(5,2) DEFAULT 0,
  pass_rate decimal(5,2) DEFAULT 0,
  average_time_spent integer DEFAULT 0,
  question_stats jsonb DEFAULT '[]',
  last_calculated_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT quiz_statistics_pkey PRIMARY KEY (id),
  CONSTRAINT quiz_statistics_quiz_id_fkey FOREIGN KEY (quiz_id) REFERENCES public.quizzes(id) ON DELETE CASCADE,
  CONSTRAINT quiz_statistics_unique_quiz_stats UNIQUE (quiz_id)
);

-- Índices para performance
CREATE INDEX idx_quiz_statistics_quiz ON public.quiz_statistics(quiz_id);

-- Comentários do sistema de quiz
COMMENT ON TABLE public.quizzes IS 'Quizzes das tarefas';
COMMENT ON TABLE public.quiz_attempts IS 'Tentativas de quiz pelos usuários';
COMMENT ON TABLE public.quiz_answers IS 'Respostas individuais dos quizzes';
COMMENT ON TABLE public.user_quiz_progress IS 'Progresso dos usuários nos quizzes';
COMMENT ON TABLE public.quiz_statistics IS 'Estatísticas dos quizzes';

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE '✅ Tabelas de conteúdo criadas com sucesso!';
  RAISE NOTICE '📝 Criadas: task_content_blocks, task_attachments, evidence, task_comments';
  RAISE NOTICE '📊 Criadas: project_history, user_notifications';
  RAISE NOTICE '🧩 Sistema de quiz completo criado!';
END $$;

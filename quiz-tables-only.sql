-- =====================
-- APENAS TABELAS DO SISTEMA DE QUIZ
-- Execute este script no SQL Editor do Supabase se você só quer as tabelas do Quiz
-- =====================

-- Remover tabelas do quiz se existirem (ordem reversa de dependência)
DROP TABLE IF EXISTS public.quiz_statistics CASCADE;
DROP TABLE IF EXISTS public.user_quiz_progress CASCADE;
DROP TABLE IF EXISTS public.quiz_answers CASCADE;
DROP TABLE IF EXISTS public.quiz_attempts CASCADE;
DROP TABLE IF EXISTS public.quizzes CASCADE;

-- <PERSON>bela principal de quizzes
CREATE TABLE public.quizzes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL, -- Referência para tasks (assumindo que existe)
  block_id TEXT NOT NULL, -- ID do bloco de conteúdo
  content JSONB NOT NULL, -- Quiz<PERSON>ontent completo
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID, -- Referência para usuário (assumindo que existe)
  is_active BOOLEAN DEFAULT true,
  
  -- Índices para performance
  CONSTRAINT unique_task_block UNIQUE(task_id, block_id)
);

-- Tabela de tentativas de quiz
CREATE TABLE public.quiz_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  quiz_id UUID NOT NULL REFERENCES public.quizzes(id) ON DELETE CASCADE,
  user_id UUID NOT NULL, -- Referência para usuário
  attempt_number INTEGER NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  submitted_at TIMESTAMP WITH TIME ZONE,
  time_spent INTEGER DEFAULT 0, -- em segundos
  score DECIMAL(5,2) DEFAULT 0,
  max_score DECIMAL(5,2) NOT NULL,
  percentage DECIMAL(5,2) DEFAULT 0,
  passed BOOLEAN DEFAULT false,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'graded')),
  metadata JSONB DEFAULT '{}', -- dados adicionais como IP, user agent, etc.
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Índices para performance
  CONSTRAINT unique_user_quiz_attempt UNIQUE(quiz_id, user_id, attempt_number)
);

-- Tabela de respostas individuais
CREATE TABLE public.quiz_answers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  attempt_id UUID NOT NULL REFERENCES public.quiz_attempts(id) ON DELETE CASCADE,
  question_id TEXT NOT NULL,
  question_type TEXT NOT NULL,
  
  -- Campos para diferentes tipos de resposta
  selected_options TEXT[], -- para múltipla/única escolha
  boolean_answer BOOLEAN, -- para verdadeiro/falso
  text_answer TEXT, -- para resposta aberta
  ordered_items TEXT[], -- para ordenação
  matched_pairs JSONB, -- para correspondência
  
  -- Metadados da resposta
  time_spent INTEGER DEFAULT 0,
  is_correct BOOLEAN DEFAULT false,
  points_earned DECIMAL(5,2) DEFAULT 0,
  feedback TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Índices para performance
  CONSTRAINT unique_attempt_question UNIQUE(attempt_id, question_id)
);

-- Tabela de progresso do usuário por quiz
CREATE TABLE public.user_quiz_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL, -- Referência para usuário
  quiz_id UUID NOT NULL REFERENCES public.quizzes(id) ON DELETE CASCADE,
  
  total_attempts INTEGER DEFAULT 0,
  best_score DECIMAL(5,2) DEFAULT 0,
  best_percentage DECIMAL(5,2) DEFAULT 0,
  passed BOOLEAN DEFAULT false,
  first_attempt_at TIMESTAMP WITH TIME ZONE,
  last_attempt_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Índices para performance
  CONSTRAINT unique_user_quiz UNIQUE(user_id, quiz_id)
);

-- Tabela de estatísticas agregadas (para performance)
CREATE TABLE public.quiz_statistics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  quiz_id UUID NOT NULL REFERENCES public.quizzes(id) ON DELETE CASCADE,
  
  total_attempts INTEGER DEFAULT 0,
  unique_users INTEGER DEFAULT 0,
  average_score DECIMAL(5,2) DEFAULT 0,
  pass_rate DECIMAL(5,2) DEFAULT 0,
  average_time_spent INTEGER DEFAULT 0,
  
  question_stats JSONB DEFAULT '[]', -- estatísticas por pergunta
  
  last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT unique_quiz_stats UNIQUE(quiz_id)
);

-- Índices para otimização de queries
CREATE INDEX IF NOT EXISTS idx_quizzes_task_id ON public.quizzes(task_id);
CREATE INDEX IF NOT EXISTS idx_quizzes_created_by ON public.quizzes(created_by);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_quiz_id ON public.quiz_attempts(quiz_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_user_id ON public.quiz_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_status ON public.quiz_attempts(status);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_attempt_id ON public.quiz_answers(attempt_id);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_question_id ON public.quiz_answers(question_id);
CREATE INDEX IF NOT EXISTS idx_user_quiz_progress_user_id ON public.user_quiz_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_quiz_progress_quiz_id ON public.user_quiz_progress(quiz_id);

-- Triggers para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

CREATE TRIGGER update_quizzes_updated_at BEFORE UPDATE ON public.quizzes
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quiz_attempts_updated_at BEFORE UPDATE ON public.quiz_attempts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quiz_answers_updated_at BEFORE UPDATE ON public.quiz_answers
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_quiz_progress_updated_at BEFORE UPDATE ON public.user_quiz_progress
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quiz_statistics_updated_at BEFORE UPDATE ON public.quiz_statistics
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para calcular estatísticas do quiz
CREATE OR REPLACE FUNCTION calculate_quiz_statistics(quiz_uuid UUID)
RETURNS VOID AS $$
DECLARE
  stats_record RECORD;
BEGIN
  -- Calcular estatísticas agregadas
  SELECT 
    COUNT(*) as total_attempts,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(score) as average_score,
    (COUNT(*) FILTER (WHERE passed = true)::DECIMAL / COUNT(*) * 100) as pass_rate,
    AVG(time_spent) as average_time_spent
  INTO stats_record
  FROM public.quiz_attempts 
  WHERE quiz_id = quiz_uuid AND status = 'graded';
  
  -- Inserir ou atualizar estatísticas
  INSERT INTO public.quiz_statistics (
    quiz_id, 
    total_attempts, 
    unique_users, 
    average_score, 
    pass_rate, 
    average_time_spent,
    last_calculated_at
  ) VALUES (
    quiz_uuid,
    COALESCE(stats_record.total_attempts, 0),
    COALESCE(stats_record.unique_users, 0),
    COALESCE(stats_record.average_score, 0),
    COALESCE(stats_record.pass_rate, 0),
    COALESCE(stats_record.average_time_spent, 0),
    NOW()
  )
  ON CONFLICT (quiz_id) 
  DO UPDATE SET
    total_attempts = EXCLUDED.total_attempts,
    unique_users = EXCLUDED.unique_users,
    average_score = EXCLUDED.average_score,
    pass_rate = EXCLUDED.pass_rate,
    average_time_spent = EXCLUDED.average_time_spent,
    last_calculated_at = EXCLUDED.last_calculated_at;
END;
$$ LANGUAGE plpgsql;

-- Trigger para recalcular estatísticas quando uma tentativa é finalizada
CREATE OR REPLACE FUNCTION trigger_calculate_quiz_statistics()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'graded' AND (OLD.status IS NULL OR OLD.status != 'graded') THEN
    PERFORM calculate_quiz_statistics(NEW.quiz_id);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER quiz_attempt_statistics_trigger
  AFTER INSERT OR UPDATE ON public.quiz_attempts
  FOR EACH ROW
  EXECUTE FUNCTION trigger_calculate_quiz_statistics();

-- =====================
-- RLS (Row Level Security) policies básicas
-- =====================
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_quiz_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_statistics ENABLE ROW LEVEL SECURITY;

-- Políticas básicas (ajuste conforme sua estrutura de usuários)
-- NOTA: Estas políticas assumem que auth.uid() retorna o ID do usuário logado

-- Políticas para quizzes (todos podem ver)
CREATE POLICY "Anyone can view quizzes" ON public.quizzes
  FOR SELECT USING (true);

CREATE POLICY "Anyone can insert quizzes" ON public.quizzes
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update quizzes" ON public.quizzes
  FOR UPDATE USING (true);

-- Políticas para quiz_attempts (usuários podem gerenciar suas próprias tentativas)
CREATE POLICY "Users can manage their own quiz attempts" ON public.quiz_attempts
  FOR ALL USING (user_id = auth.uid());

-- Políticas para quiz_answers (usuários podem gerenciar suas próprias respostas)
CREATE POLICY "Users can manage their own quiz answers" ON public.quiz_answers
  FOR ALL USING (
    attempt_id IN (SELECT id FROM public.quiz_attempts WHERE user_id = auth.uid())
  );

-- Políticas para user_quiz_progress (usuários podem ver seu próprio progresso)
CREATE POLICY "Users can view their own quiz progress" ON public.user_quiz_progress
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own quiz progress" ON public.user_quiz_progress
  FOR ALL USING (user_id = auth.uid());

-- Políticas para quiz_statistics (todos podem ver)
CREATE POLICY "Anyone can view quiz statistics" ON public.quiz_statistics
  FOR SELECT USING (true);

-- Comentários para documentação
COMMENT ON TABLE public.quizzes IS 'Armazena configurações e conteúdo dos quizzes vinculados a tarefas';
COMMENT ON TABLE public.quiz_attempts IS 'Registra tentativas de execução de quizzes pelos usuários';
COMMENT ON TABLE public.quiz_answers IS 'Armazena respostas individuais para cada pergunta de um quiz';
COMMENT ON TABLE public.user_quiz_progress IS 'Acompanha progresso geral do usuário em cada quiz';
COMMENT ON TABLE public.quiz_statistics IS 'Estatísticas agregadas para performance e relatórios de quiz';

-- =====================
-- Verificação final
-- =====================
-- Execute esta query para verificar se as tabelas foram criadas:
-- SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'quiz%';

# Sistema de Quiz Avançado

## Visão Geral

O sistema de Quiz foi completamente reformulado para oferecer funcionalidades avançadas de criação, execução e análise de questionários. Mantém compatibilidade total com o formato antigo enquanto oferece recursos profissionais.

**🆕 NOVIDADE: Suporte a Pesquisas de Opinião!**

O sistema agora suporta dois modos principais:

### 🎯 Modo Avaliação (Assessment) - Padrão
- Correção automática com respostas certas/erradas
- Sistema de pontuação e nota mínima para aprovação
- Feedback específico por resposta
- Bloqueio de progresso se obrigatório e não aprovado
- Relatórios detalhados de performance

### 📊 Modo Pesquisa (Survey) - Novo!
- Coleta de opiniões sem correção automática
- Sempre considerado "completo" (nunca bloqueia progresso)
- Feedback neutro de agradecimento
- Estatísticas agregadas e distribuição de respostas
- Foco em coleta de dados e análise estatística

## Funcionalidades Principais

### 🎯 Tipos de Pergunta Suportados

1. **Escolha Única** - Radio buttons para uma resposta correta
2. **Múltipla Escolha** - Checkboxes para múltiplas respostas corretas
3. **Verdadeiro/Falso** - Pergunta binária simples
4. **Resposta Aberta** - Campo de texto com correção por palavras-chave
5. **Ordenação** - Arrastar itens para ordem correta (estrutura pronta)
6. **Correspondência** - Conectar itens de duas colunas (estrutura pronta)

### 📊 Sistema de Correção Automática

- **Pontuação configurável** por pergunta
- **Nota mínima** para aprovação
- **Feedback personalizado** para respostas corretas/incorretas
- **Correção em tempo real** durante execução
- **Múltiplas tentativas** configuráveis

### 🔒 Sistema de Validação

- **Quiz obrigatório** com bloqueio de progresso
- **Controle de tentativas** por usuário
- **Timer configurável** por pergunta ou quiz total
- **Verificação de permissões** e status

### 📈 Relatórios e Analytics

- **Dashboard de estatísticas** completo
- **Análise por pergunta** detalhada
- **Progresso por usuário** individual
- **Exportação de dados** (preparado)

## Estrutura de Arquivos

```
quiz/
├── QuizBlockEditor.tsx          # Editor principal (modo edição)
├── QuizExecutionBlock.tsx       # Componente de execução
├── components/
│   ├── QuizConfigPanel.tsx      # Painel de configurações
│   ├── QuizPreview.tsx          # Preview do quiz
│   ├── QuizReports.tsx          # Relatórios e analytics
│   ├── QuestionEditor.tsx       # Editor de perguntas
│   └── QuestionBank.tsx         # Banco de perguntas
└── README.md                    # Esta documentação
```

## Banco de Dados (Supabase)

### Tabelas Criadas

1. **quizzes** - Configurações e conteúdo dos quizzes
2. **quiz_attempts** - Tentativas de execução pelos usuários
3. **quiz_answers** - Respostas individuais para cada pergunta
4. **user_quiz_progress** - Progresso geral do usuário em cada quiz
5. **quiz_statistics** - Estatísticas agregadas para performance

### Schema SQL

Execute o arquivo `src/database/quiz-schema.sql` no seu projeto Supabase para criar todas as tabelas necessárias.

## Como Usar

### 1. Criando um Quiz

```typescript
// No editor de conteúdo, adicione um bloco de Quiz
// O novo editor será exibido automaticamente com:

// Aba "Perguntas" - Adicionar e gerenciar perguntas
// Aba "Correção" - Configurar pontuação e feedback
// Aba "Validação" - Definir requisitos e tempo
// Aba "Relatórios" - Visualizar estatísticas
```

### 2. Configurando Perguntas

```typescript
// Clique nos botões visuais para adicionar perguntas:
// - Escolha Única: Para uma resposta correta
// - Múltipla Escolha: Para múltiplas respostas corretas
// - Verdadeiro/Falso: Para perguntas binárias
// - Resposta Aberta: Para texto livre
// - Ordenação: Para sequência correta
// - Correspondência: Para conectar itens
```

### 3. Executando um Quiz

```typescript
// Na tela de execução da tarefa:
// 1. Quiz aparece automaticamente no conteúdo
// 2. Usuário clica "Iniciar Quiz"
// 3. Navega entre perguntas
// 4. Respostas são salvas automaticamente
// 5. Finaliza e recebe resultado
```

### 4. Usando o QuizService

```typescript
import { QuizService } from '@/services/quizService';

// Carregar quiz
const quiz = await QuizService.loadQuiz(taskId, blockId);

// Iniciar tentativa
const attempt = await QuizService.startAttempt(taskId, blockId, userId);

// Salvar resposta
await QuizService.saveAnswer(attemptId, answer);

// Finalizar tentativa
const result = await QuizService.submitAttempt(attemptId, answers, timeSpent);

// Carregar progresso
const progress = await QuizService.getUserProgress(taskId, blockId, userId);
```

### 5. Usando os Novos Modos (Assessment vs Survey)

```typescript
import { QuizUtils } from '@/types/quiz';

// Criar template de avaliação tradicional
const assessmentQuiz = QuizUtils.createAssessmentTemplate();
// Resultado: modo 'assessment', correção automática, nota mínima, etc.

// Criar template de pesquisa de opinião
const surveyQuiz = QuizUtils.createSurveyTemplate();
// Resultado: modo 'survey', sem correção, estatísticas agregadas

// Verificar modo de um quiz existente
const isAssessment = QuizUtils.isAssessmentMode(quizConfig);
const isSurvey = QuizUtils.isSurveyMode(quizConfig);

// Garantir compatibilidade com quizzes antigos
const compatibleConfig = QuizUtils.ensureCompatibility(oldQuizConfig);
// Resultado: adiciona campos padrão, mode='assessment' se não especificado
```

#### Diferenças Práticas:

**Modo Assessment (Avaliação):**
```typescript
const assessmentConfig = {
  mode: 'assessment',
  passingScore: 70,
  showScore: true,
  showCorrectAnswers: true,
  blockProgressUntilPassed: true // Pode bloquear progresso
};

// Opções com respostas corretas
const assessmentOptions = [
  { id: '1', text: 'Brasília', isCorrect: true },
  { id: '2', text: 'São Paulo', isCorrect: false }
];
```

**Modo Survey (Pesquisa):**
```typescript
const surveyConfig = {
  mode: 'survey',
  showScore: false,
  showCorrectAnswers: false,
  blockProgressUntilPassed: false, // Nunca bloqueia progresso
  surveySettings: {
    showAggregatedResults: true,
    allowAnonymous: false,
    showOthersResponses: false
  }
};

// Opções sem correção (isCorrect opcional)
const surveyOptions = [
  { id: '1', text: 'Excelente', surveyValue: 5 },
  { id: '2', text: 'Bom', surveyValue: 4 },
  { id: '3', text: 'Regular', surveyValue: 3 }
];
```

## Configurações Disponíveis

### Quiz Config

```typescript
interface QuizConfig {
  title: string;                    // Título do quiz
  description?: string;             // Descrição opcional
  instructions?: string;            // Instruções para usuários
  
  maxAttempts: number;              // Máximo de tentativas (-1 = ilimitado)
  allowRetry: boolean;              // Permitir nova tentativa após aprovação
  
  passingScore: number;             // Nota mínima para aprovação (%)
  showScore: boolean;               // Mostrar pontuação final
  showCorrectAnswers: boolean;      // Mostrar respostas corretas
  showFeedback: boolean;            // Mostrar feedback das perguntas
  
  timeLimit?: number;               // Tempo limite total (segundos)
  showTimer: boolean;               // Mostrar cronômetro
  
  isRequired: boolean;              // Quiz obrigatório
  blockProgressUntilPassed: boolean; // Bloquear progresso até aprovação
  
  shuffleQuestions: boolean;        // Embaralhar perguntas
  shuffleOptions: boolean;          // Embaralhar opções
  showProgressBar: boolean;         // Mostrar barra de progresso
  allowSaveDraft: boolean;          // Permitir salvar rascunho
  
  enableAnalytics: boolean;         // Habilitar analytics
  showResultsToUser: boolean;       // Mostrar resultados ao usuário
}
```

### Question Config

```typescript
interface QuizQuestion {
  id: string;
  type: QuizQuestionType;           // Tipo da pergunta
  title: string;                    // Título da pergunta
  description?: string;             // Descrição opcional
  points: number;                   // Pontuação da pergunta
  required: boolean;                // Pergunta obrigatória
  timeLimit?: number;               // Tempo limite (segundos)
  
  // Campos específicos por tipo
  options?: QuizOption[];           // Para múltipla/única escolha
  correctAnswer?: boolean;          // Para verdadeiro/falso
  openTextAnswer?: string;          // Para resposta aberta
  openTextKeywords?: string[];      // Palavras-chave para correção
  orderingItems?: QuizOrderingItem[]; // Para ordenação
  matchingPairs?: QuizMatchingPair[]; // Para correspondência
  
  // Feedback
  correctFeedback?: string;         // Feedback para resposta correta
  incorrectFeedback?: string;       // Feedback para resposta incorreta
  explanation?: string;             // Explicação detalhada
}
```

## Estados do Quiz

### Durante Execução

1. **loading** - Carregando dados do quiz
2. **ready** - Pronto para iniciar
3. **executing** - Quiz em execução
4. **completed** - Quiz finalizado
5. **blocked** - Bloqueado (sem tentativas restantes)

### Estados de Tentativa

1. **draft** - Rascunho (em andamento)
2. **submitted** - Submetido para correção
3. **graded** - Corrigido e finalizado

## Compatibilidade

### Formato Antigo

O sistema mantém total compatibilidade com quizzes criados no formato antigo:

```typescript
// Formato antigo (ainda funciona)
{
  question: "Qual é a capital do Brasil?",
  options: ["Brasília", "São Paulo", "Rio de Janeiro"]
}

// É automaticamente convertido para o novo formato
```

### Migração Automática

- Quizzes antigos são convertidos automaticamente
- Primeira opção é considerada correta por padrão
- Configurações padrão são aplicadas
- Nenhuma perda de dados

## Próximos Passos

### Funcionalidades Planejadas

1. **Drag & Drop** para ordenação real
2. **Interface de correspondência** interativa
3. **Mais tipos de pergunta** (escala, matriz, etc.)
4. **Exportação real** para CSV/Excel
5. **Analytics avançados** com gráficos
6. **Banco de perguntas** compartilhado
7. **Templates de quiz** pré-definidos

### Melhorias de UX

1. **Animações** de transição
2. **Feedback visual** melhorado
3. **Modo escuro** para execução
4. **Acessibilidade** aprimorada
5. **PWA** para execução offline

## Suporte

Para dúvidas ou problemas:

1. Verifique os logs do console para erros
2. Confirme se as tabelas do Supabase foram criadas
3. Verifique as variáveis de ambiente do Supabase
4. Teste com um quiz simples primeiro

## Contribuição

Para contribuir com o sistema de Quiz:

1. Mantenha compatibilidade com formato antigo
2. Adicione testes para novas funcionalidades
3. Documente mudanças na API
4. Siga os padrões de código estabelecidos

import React, { useEffect } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $isCodeNode, getDefaultCodeLanguage, getCodeLanguages, CodeNode } from '@lexical/code';
import {
  $getNodeByKey,
  NodeKey,
} from 'lexical';
import { createRoot } from 'react-dom/client';
import CodeBlockComponent from './CodeBlockComponent';

// Função para destacar código com base na linguagem
function getHighlightedCode(code: string, language: string): string {
  // Lista de palavras-chave para diferentes linguagens
  const keywords: { [key: string]: string[] } = {
    javascript: ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'class', 'extends', 'import', 'export', 'default', 'async', 'await', 'try', 'catch', 'throw', 'new'],
    typescript: ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'class', 'extends', 'import', 'export', 'default', 'async', 'await', 'try', 'catch', 'throw', 'new', 'interface', 'type', 'enum', 'public', 'private', 'protected'],
    python: ['def', 'class', 'if', 'elif', 'else', 'for', 'while', 'import', 'from', 'as', 'return', 'try', 'except', 'finally', 'with', 'lambda', 'and', 'or', 'not', 'in', 'is'],
    java: ['public', 'private', 'protected', 'class', 'interface', 'extends', 'implements', 'import', 'package', 'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'default', 'try', 'catch', 'finally', 'throw', 'throws', 'new', 'return'],
    html: ['html', 'head', 'body', 'div', 'span', 'p', 'a', 'img', 'ul', 'ol', 'li', 'table', 'tr', 'td', 'th', 'form', 'input', 'button'],
    css: ['color', 'background', 'margin', 'padding', 'border', 'width', 'height', 'display', 'position', 'top', 'left', 'right', 'bottom', 'font', 'text']
  };

  const languageKeywords = keywords[language.toLowerCase()] || [];
  let highlightedCode = code;

  // Destacar palavras-chave
  languageKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'g');
    highlightedCode = highlightedCode.replace(regex, `<span class="code-keyword">${keyword}</span>`);
  });

  // Destacar strings
  highlightedCode = highlightedCode.replace(/(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g, '<span class="code-string">$&</span>');

  // Destacar comentários
  if (['javascript', 'typescript', 'java', 'css'].includes(language.toLowerCase())) {
    highlightedCode = highlightedCode.replace(/\/\/.*$/gm, '<span class="code-comment">$&</span>');
    highlightedCode = highlightedCode.replace(/\/\*[\s\S]*?\*\//g, '<span class="code-comment">$&</span>');
  } else if (language.toLowerCase() === 'python') {
    highlightedCode = highlightedCode.replace(/#.*$/gm, '<span class="code-comment">$&</span>');
  } else if (language.toLowerCase() === 'html') {
    highlightedCode = highlightedCode.replace(/<!--[\s\S]*?-->/g, '<span class="code-comment">$&</span>');
  } else if (language.toLowerCase() === 'css') {
    highlightedCode = highlightedCode.replace(/\/\*[\s\S]*?\*\//g, '<span class="code-comment">$&</span>');
  }

  // Destacar números
  highlightedCode = highlightedCode.replace(/\b\d+(\.\d+)?\b/g, '<span class="code-number">$&</span>');

  return highlightedCode;
}

export default function CodeHighlightPlugin(): JSX.Element | null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const removeListener = editor.registerNodeTransform(CodeNode, (node: CodeNode) => {
      const language = node.getLanguage() || getDefaultCodeLanguage();
      const code = node.getTextContent();
      
      // Aplicar destaque de sintaxe diretamente no conteúdo do nó
      if (code) {
        const highlightedCode = getHighlightedCode(code, language);
        
        // Atualizar o elemento DOM do nó
        setTimeout(() => {
          const nodeKey = node.getKey();
          const codeElement = editor.getElementByKey(nodeKey);
          if (codeElement && codeElement.tagName === 'CODE') {
            // Aplicar classes CSS para estilização
            codeElement.className = 'code-block-content';
            codeElement.setAttribute('data-language', language);
            
            // Criar wrapper se não existir
            let wrapper = codeElement.parentElement;
            if (!wrapper || !wrapper.classList.contains('code-block-wrapper')) {
              wrapper = document.createElement('div');
              wrapper.className = 'code-block-wrapper';
              codeElement.parentNode?.insertBefore(wrapper, codeElement);
              wrapper.appendChild(codeElement);
              
              // Adicionar cabeçalho
              const header = document.createElement('div');
              header.className = 'code-block-header';
              header.innerHTML = `
                <span class="code-block-language">${language}</span>
                <button class="code-block-copy" onclick="navigator.clipboard.writeText('${code.replace(/'/g, "\\'")}')">Copiar</button>
              `;
              wrapper.insertBefore(header, codeElement);
              
              // Adicionar container de conteúdo
              const contentDiv = document.createElement('div');
              contentDiv.className = 'code-block-content';
              
              // Adicionar numeração de linhas
              const lines = code.split('\n');
              const lineNumbers = document.createElement('div');
              lineNumbers.className = 'code-block-lines';
              lineNumbers.textContent = lines.map((_, i) => i + 1).join('\n');
              
              const codeDiv = document.createElement('div');
              codeDiv.className = 'code-block-code';
              
              contentDiv.appendChild(lineNumbers);
              contentDiv.appendChild(codeDiv);
              codeDiv.appendChild(codeElement);
              wrapper.appendChild(contentDiv);
            }
            
            // Aplicar destaque de sintaxe
            codeElement.innerHTML = highlightedCode;
          }
        }, 0);
      }
    });

    return removeListener;
  }, [editor]);

  return null;
}

// Exportar também as linguagens disponíveis
export const SUPPORTED_LANGUAGES = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'sql', label: 'SQL' },
  { value: 'bash', label: 'Bash' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'cpp', label: 'C++' },
  { value: 'csharp', label: 'C#' },
];
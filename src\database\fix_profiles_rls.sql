-- Correção das políticas RLS da tabela profiles
-- Data: 2024-12-19
-- Problema: Erro 403 ao tentar listar usuários

-- 1. Verificar se a função is_admin existe
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND role = 'admin'
  );
$$;

-- 2. Política para permitir que usuários vejam outros usuários em contextos específicos
-- (necessário para autocomplete de usuários, atribuição de tarefas, etc.)
DROP POLICY IF EXISTS "Users can view other users for collaboration" ON public.profiles;
CREATE POLICY "Users can view other users for collaboration"
  ON public.profiles
  FOR SELECT
  USING (
    -- Admin pode ver todos
    public.is_admin(auth.uid()) OR
    -- Usu<PERSON>rio pode ver próprio perfil
    id = auth.uid() OR
    -- Usuários podem ver outros usuários se estão no mesmo projeto
    EXISTS (
      SELECT 1 FROM public.project_members pm1
      JOIN public.project_members pm2 ON pm1.project_id = pm2.project_id
      WHERE pm1.user_id = auth.uid() AND pm2.user_id = profiles.id
    ) OR
    -- Usuários podem ver outros usuários se são donos de projetos onde o outro é membro
    EXISTS (
      SELECT 1 FROM public.projects p
      JOIN public.project_members pm ON p.id = pm.project_id
      WHERE p.owner_id = auth.uid() AND pm.user_id = profiles.id
    ) OR
    -- Usuários podem ver donos de projetos onde são membros
    EXISTS (
      SELECT 1 FROM public.projects p
      JOIN public.project_members pm ON p.id = pm.project_id
      WHERE pm.user_id = auth.uid() AND p.owner_id = profiles.id
    )
  );

-- 3. Manter política existente para admin ver todos
DROP POLICY IF EXISTS "Admin can view all profiles" ON public.profiles;
CREATE POLICY "Admin can view all profiles"
  ON public.profiles
  FOR SELECT
  USING (public.is_admin(auth.uid()));

-- 4. Manter política para usuário ver próprio perfil
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
CREATE POLICY "Users can view their own profile"
  ON public.profiles
  FOR SELECT
  USING (id = auth.uid());

-- 5. Política para permitir inserção de perfis (necessário para cadastro)
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
CREATE POLICY "Users can insert their own profile"
  ON public.profiles
  FOR INSERT
  WITH CHECK (id = auth.uid());

-- 6. Política para admin inserir qualquer perfil
DROP POLICY IF EXISTS "Admin can insert any profile" ON public.profiles;
CREATE POLICY "Admin can insert any profile"
  ON public.profiles
  FOR INSERT
  WITH CHECK (public.is_admin(auth.uid()));

-- 7. Política para atualização (manter existentes)
DROP POLICY IF EXISTS "Admin can update all profiles" ON public.profiles;
CREATE POLICY "Admin can update all profiles"
  ON public.profiles
  FOR UPDATE
  USING (public.is_admin(auth.uid()));

DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
CREATE POLICY "Users can update their own profile"
  ON public.profiles
  FOR UPDATE
  USING (id = auth.uid());

-- 8. Garantir que RLS está ativado
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 9. Verificar políticas criadas
SELECT 
    policyname,
    cmd,
    permissive,
    roles
FROM pg_policies 
WHERE tablename = 'profiles'
ORDER BY policyname;

# Correção de Erro 400 - Relacionamento Tasks → Projects

## 🐛 **Problema Identificado**

Erro 400 no Supabase quando tentando verificar permissões de upload de evidências:

```
GET https://...supabase.co/rest/v1/tasks?select=id%2Cproject_id%2Cexecutors%3Atask_executors%28user_id%29%2Cproject%3Aprojects%28id%2Cmembers%3Aproject_members%28user_id%2Crole%29%29&id=eq.7c606667-9391-4660-933d-90d6bd276e88 400 (Bad Request)

{code: 'PGRST200', details: "Searched for a foreign key relationship between 'tasks' and 'projects' in the schema 'public', but no matches were found.", hint: "Perhaps you meant 'profiles' instead of 'projects'.", message: "Could not find a relationship between 'tasks' and 'projects' in the schema cache"}
```

## 🔍 **Causa <PERSON>z**

A consulta Supabase em `evidenceApprovalService.ts` tentava fazer um relacionamento direto entre `tasks` e `projects`, mas a estrutura do banco é:

```
tasks → stage_id → stages → project_id → projects
```

**❌ Consulta Incorreta (anterior):**
```typescript
.from('tasks')
.select(`
  id,
  project_id,                    // ❌ tasks não tem project_id direto
  executors:task_executors(user_id),
  project:projects(             // ❌ relacionamento inexistente
    id,
    members:project_members(user_id, role)
  )
`)
```

**✅ Consulta Correta (nova):**
```typescript
.from('tasks')
.select(`
  id,
  stage_id,
  executors:task_executors(user_id),
  stage:stages(                 // ✅ relacionamento correto tasks → stages
    id,
    project_id,
    project:projects(           // ✅ relacionamento correto stages → projects
      id,
      members:project_members(user_id, role)
    )
  )
`)
```

## 🔧 **Correção Implementada**

### **Arquivo**: `src/services/evidenceApprovalService.ts`
### **Função**: `canUploadEvidence()`

**Antes:**
```typescript
// ❌ Consulta com relacionamento incorreto
const { data: taskData, error } = await supabase
  .from('tasks')
  .select(`
    id,
    project_id,  // ❌ Campo inexistente
    executors:task_executors(user_id),
    project:projects(  // ❌ Relacionamento direto inexistente
      id,
      members:project_members(user_id, role)
    )
  `)

// ❌ Lógica de verificação inadequada para arrays
const isProjectMember = taskData.project && Array.isArray(taskData.project) && taskData.project.length > 0 
  ? taskData.project[0].members?.some((member: any) => member.user_id === userId)
  : false;
```

**Depois:**
```typescript
// ✅ Consulta com relacionamento correto através de stages
const { data: taskData, error } = await supabase
  .from('tasks')
  .select(`
    id,
    stage_id,
    executors:task_executors(user_id),
    stage:stages(  // ✅ Relacionamento tasks → stages
      id,
      project_id,
      project:projects(  // ✅ Relacionamento stages → projects
        id,
        members:project_members(user_id, role)
      )
    )
  `)

// ✅ Lógica de verificação robusta para qualquer estrutura
const stage = Array.isArray(taskData.stage) ? taskData.stage[0] : taskData.stage;
const project = Array.isArray(stage?.project) ? stage.project[0] : stage?.project;
const isProjectMember = project?.members?.some((member: any) => member.user_id === userId) || false;
```

## 🎯 **Benefícios da Correção**

### ✅ **Correção de Relacionamentos**
- Agora segue a estrutura correta do banco: `tasks → stages → projects`
- Elimina o erro 400 do Supabase

### ✅ **Robustez na Verificação**
- Trata tanto objetos únicos quanto arrays retornados pelo Supabase
- Proteção contra `undefined` com optional chaining

### ✅ **Debug Melhorado**
- Console logs adicionados para rastreamento de permissões
- Visibilidade da estrutura de dados retornada

### ✅ **Manutenção da Funcionalidade**
- Preserva toda a lógica de permissões existente
- Usuários admin continuam funcionando normalmente
- Upload de evidências para membros/executores mantido

## 🧪 **Teste da Correção**

### **Antes da Correção:**
- ❌ Erro 400 para usuários admin
- ❌ Falha na verificação de permissões
- ❌ Upload de evidências não funcionava

### **Depois da Correção:**
- ✅ Consulta Supabase executada com sucesso
- ✅ Verificação de permissões funcionando
- ✅ Upload de evidências habilitado para usuários apropriados

## 📋 **Validação**

Para validar a correção:

1. **Acesse**: http://localhost:5174/
2. **Login como admin**: Não deve aparecer erro 400 no console
3. **Vá para uma tarefa**: Tab "Executar Tarefa" deve carregar normalmente
4. **Upload de evidências**: Botão "Adicionar" deve aparecer se tiver permissões

### **Console Logs Esperados:**
```
🔍 Carregando evidências para tarefa: [taskId] bloco: [blockId]
✅ Evidências com aprovação carregadas: [array]
📊 Total de evidências encontradas: [number]
🔐 Permissão para upload: true/false
🔍 Verificação de permissões: { userId, isExecutor, isProjectMember, stage, project, members }
```

## 🔗 **Estrutura de Banco Correta**

```sql
-- Relacionamentos corretos:
tasks.stage_id → stages.id
stages.project_id → projects.id
projects.id ← project_members.project_id
tasks.id ← task_executors.task_id
```

A correção agora segue esta estrutura corretamente, eliminando o erro 400 e restaurando toda a funcionalidade de upload de evidências.

---
**Status**: ✅ **CORRIGIDO E TESTADO**  
**Servidor**: Rodando em http://localhost:5174/  
**Data**: 17/07/2025  

# Funcionalidades do Configurador de Blocos

Este documento lista todas as funcionalidades existentes no configurador de blocos do editor de conteúdo, detalhando as opções de personalização disponíveis para cada tipo de bloco. Use o checklist para validar a implementação e funcionamento de cada opção.

---

## Sumário
- [Alert](#alert)
- [Colored Block](#colored-block)
- [File](#file)
- [Image](#image)
- [Quiz](#quiz)
- [Text](#text)
- [Video](#video)

---

## <a name="alert"></a>Alert
**Fontes:**
- `src/components/features/content-editor/blocks/alert/AlertBlockGallery.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/CardAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/IconAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/ButtonConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/alert/presets.ts`

### Aparência
- [ ] Cor de fundo do card
- [ ] Cor do texto do card
- [ ] Formato do card (rounded, square, pill)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do card

### Ícone
- [ ] Ativar/desativar ícone (Ao desativar, ocultar todas as opções)
- [ ] Posição (esquerda do título, direita do título, esquerda do título e descrição, direita do título e descrição, esquerda do conteúdo, direita do conteúdo, topo centralizado, rodapé centralizado)
- [ ] Seletor de Tipo (predefinido, personalizado)
- [ ] Botão para escolher ícone (Abrir modal com lista de ícones predefinida (Lucide), quando o Seletor de Tipo for igual a predefinido)
- [ ] Imput para Informar URL quando o Seletor de Tipo for igual a personalizado
- [ ] Cor de fundo do icone
- [ ] Cor do icone
- [ ] Formato do icone (Circular, Quadrado)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do icone

### Botão
- [ ] Cor de fundo do botão
- [ ] Cor do texto do botão
- [ ] Estilo (rounded, flat, filled, outlined, text)
- [ ] Tamanho (small, medium, large)
- [ ] Posição (top-left, top-right, top-center, bottom-left, bottom-right, bottom-center)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Ícone do botão (usar o mesmo do card)
- [ ] Botão restaurar padrão do botão

### Preset
- [ ] Seleção de preset visual (success, info, warning, error, variações de layout)
- [ ] Aplicação automática de cor, ícone e layout ao selecionar preset

---

## <a name="colored-block"></a>Colored Block
**Fontes:**
- `src/components/features/content-editor/blocks/colored-block/ColoredBlockEditor.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/CardAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/IconAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/ButtonConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/colored/presets.ts`

### Aparência
- [ ] Cor de fundo do card
- [ ] Cor do texto do card
- [ ] Formato do card (rounded, square, pill)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do card

### Ícone
- [ ] Ativar/desativar ícone (Ao desativar, ocultar todas as opções)
- [ ] Posição (esquerda do título, direita do título, esquerda do título e descrição, direita do título e descrição, esquerda do conteúdo, direita do conteúdo, topo centralizado, rodapé centralizado)
- [ ] Seletor de Tipo (predefinido, personalizado)
- [ ] Botão para escolher ícone (Abrir modal com lista de ícones predefinida (Lucide), quando o Seletor de Tipo for igual a predefinido)
- [ ] Imput para Informar URL quando o Seletor de Tipo for igual a personalizado
- [ ] Cor de fundo do icone
- [ ] Cor do icone
- [ ] Formato do icone (Circular, Quadrado)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do icone

### Botão
- [ ] Cor de fundo do botão
- [ ] Cor do texto do botão
- [ ] Estilo (rounded, flat, filled, outlined, text)
- [ ] Tamanho (small, medium, large)
- [ ] Posição (top-left, top-right, top-center, bottom-left, bottom-right, bottom-center)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Ícone do botão (usar o mesmo do card)
- [ ] Botão restaurar padrão do botão

### Preset
- [ ] Seleção de variante (info, warning, error, success)
- [ ] Aplicação automática de cores e ícone

---

## <a name="file"></a>File
**Fontes:**
- `src/components/features/content-editor/blocks/file/FileBlockEditor.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/CardAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/IconAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/ButtonConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/file/presets.ts`

### Aparência
- [ ] Cor de fundo do card
- [ ] Cor do texto do card
- [ ] Formato do card (rounded, square, pill)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do card

### Ícone
- [ ] Ativar/desativar ícone (Ao desativar, ocultar todas as opções)
- [ ] Posição (esquerda do título, direita do título, esquerda do título e descrição, direita do título e descrição, esquerda do conteúdo, direita do conteúdo, topo centralizado, rodapé centralizado)
- [ ] Seletor de Tipo (predefinido, personalizado)
- [ ] Botão para escolher ícone (Abrir modal com lista de ícones predefinida (Lucide), quando o Seletor de Tipo for igual a predefinido)
- [ ] Imput para Informar URL quando o Seletor de Tipo for igual a personalizado
- [ ] Cor de fundo do icone
- [ ] Cor do icone
- [ ] Formato do icone (Circular, Quadrado)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do icone

### Botão
- [ ] Cor de fundo do botão
- [ ] Cor do texto do botão
- [ ] Estilo (rounded, flat, filled, outlined, text)
- [ ] Tamanho (small, medium, large)
- [ ] Posição (top-left, top-right, top-center, bottom-left, bottom-right, bottom-center)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Ícone do botão (usar o mesmo do card)
- [ ] Botão restaurar padrão do botão

### Preset
- [ ] Seleção de preset (default, pdf, image, document)

---

## <a name="image"></a>Image
**Fontes:**
- `src/components/features/content-editor/blocks/image/ImageBlockEditor.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/CardAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/IconAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/ButtonConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/image/presets.ts`

### Aparência
- [ ] Cor de fundo do card
- [ ] Cor do texto do card
- [ ] Formato do card (rounded, square, pill)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do card

### Ícone
- [ ] Ativar/desativar ícone (Ao desativar, ocultar todas as opções)
- [ ] Posição (esquerda do título, direita do título, esquerda do título e descrição, direita do título e descrição, esquerda do conteúdo, direita do conteúdo, topo centralizado, rodapé centralizado)
- [ ] Seletor de Tipo (predefinido, personalizado)
- [ ] Botão para escolher ícone (Abrir modal com lista de ícones predefinida (Lucide), quando o Seletor de Tipo for igual a predefinido)
- [ ] Imput para Informar URL quando o Seletor de Tipo for igual a personalizado
- [ ] Cor de fundo do icone
- [ ] Cor do icone
- [ ] Formato do icone (Circular, Quadrado)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do icone

### Botão
- [ ] Cor de fundo do botão
- [ ] Cor do texto do botão
- [ ] Estilo (rounded, flat, filled, outlined, text)
- [ ] Tamanho (small, medium, large)
- [ ] Posição (top-left, top-right, top-center, bottom-left, bottom-right, bottom-center)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Ícone do botão (usar o mesmo do card)
- [ ] Botão restaurar padrão do botão

### Preset
- [ ] Seleção de preset (default, gallery, hero, thumbnail, avatar, banner)

---

## <a name="quiz"></a>Quiz
**Fontes:**
- `src/components/features/content-editor/blocks/quiz/QuizBlockEditor.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/CardAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/IconAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/ButtonConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/quiz/presets.ts`

### Aparência
- [ ] Cor de fundo do card
- [ ] Cor do texto do card
- [ ] Formato do card (rounded, square, pill)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do card

### Ícone
- [ ] Ativar/desativar ícone (Ao desativar, ocultar todas as opções)
- [ ] Posição (esquerda do título, direita do título, esquerda do título e descrição, direita do título e descrição, esquerda do conteúdo, direita do conteúdo, topo centralizado, rodapé centralizado)
- [ ] Seletor de Tipo (predefinido, personalizado)
- [ ] Botão para escolher ícone (Abrir modal com lista de ícones predefinida (Lucide), quando o Seletor de Tipo for igual a predefinido)
- [ ] Imput para Informar URL quando o Seletor de Tipo for igual a personalizado
- [ ] Cor de fundo do icone
- [ ] Cor do icone
- [ ] Formato do icone (Circular, Quadrado)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do icone

### Botão
- [ ] Cor de fundo do botão
- [ ] Cor do texto do botão
- [ ] Estilo (rounded, flat, filled, outlined, text)
- [ ] Tamanho (small, medium, large)
- [ ] Posição (top-left, top-right, top-center, bottom-left, bottom-right, bottom-center)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Ícone do botão (usar o mesmo do card)
- [ ] Botão restaurar padrão do botão

### Preset
- [ ] Seleção de preset (default, multipleChoice, trueFalse, openEnded)

---

## <a name="text"></a>Text
**Fontes:**
- `src/components/features/content-editor/blocks/text/TextBlockEditor.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/CardAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/IconAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/text/presets.ts`

### Aparência
- [ ] Cor de fundo do card
- [ ] Cor do texto do card
- [ ] Formato do card (rounded, square, pill)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do card

### Ícone
- [ ] Ativar/desativar ícone (Ao desativar, ocultar todas as opções)
- [ ] Posição (esquerda do título, direita do título, esquerda do título e descrição, direita do título e descrição, esquerda do conteúdo, direita do conteúdo, topo centralizado, rodapé centralizado)
- [ ] Seletor de Tipo (predefinido, personalizado)
- [ ] Botão para escolher ícone (Abrir modal com lista de ícones predefinida (Lucide), quando o Seletor de Tipo for igual a predefinido)
- [ ] Imput para Informar URL quando o Seletor de Tipo for igual a personalizado
- [ ] Cor de fundo do icone
- [ ] Cor do icone
- [ ] Formato do icone (Circular, Quadrado)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do icone

### Preset
- [ ] Seleção de preset (default, minimal)

---

## <a name="video"></a>Video
**Fontes:**
- `src/components/features/content-editor/blocks/video/VideoBlockEditor.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/CardAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/IconAppearanceConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/components/ButtonConfig.tsx`
- `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/video/presets.ts`

### Aparência
- [ ] Cor de fundo do card
- [ ] Cor do texto do card
- [ ] Formato do card (rounded, square, pill)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do card

### Ícone
- [ ] Ativar/desativar ícone (Ao desativar, ocultar todas as opções)
- [ ] Posição (esquerda do título, direita do título, esquerda do título e descrição, direita do título e descrição, esquerda do conteúdo, direita do conteúdo, topo centralizado, rodapé centralizado)
- [ ] Seletor de Tipo (predefinido, personalizado)
- [ ] Botão para escolher ícone (Abrir modal com lista de ícones predefinida (Lucide), quando o Seletor de Tipo for igual a predefinido)
- [ ] Imput para Informar URL quando o Seletor de Tipo for igual a personalizado
- [ ] Cor de fundo do icone
- [ ] Cor do icone
- [ ] Formato do icone (Circular, Quadrado)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Botão restaurar padrão do icone

### Botão
- [ ] Cor de fundo do botão
- [ ] Cor do texto do botão
- [ ] Estilo (rounded, flat, filled, outlined, text)
- [ ] Tamanho (small, medium, large)
- [ ] Posição (top-left, top-right, top-center, bottom-left, bottom-right, bottom-center)
- [ ] Borda (cor, largura, ativar/desativar)
- [ ] Sombra (profundidade, ativar/desativar)
- [ ] Hover (sombra extra, ativar/desativar)
- [ ] Ícone do botão (usar o mesmo do card)
- [ ] Botão restaurar padrão do botão

### Preset
- [ ] Seleção de preset (default, youtube, vimeo)

---

## Checklist Global
- [ ] Todas as opções acima estão visíveis e funcionais no painel de configuração de cada bloco.
- [ ] Alterações são refletidas em tempo real no preview do bloco.
- [ ] Presets aplicam corretamente todas as opções relacionadas.
- [ ] Respeito aos limites de tamanho, cor e formato definidos nos presets.
- [ ] Não há opções duplicadas ou conflitantes entre blocos. 
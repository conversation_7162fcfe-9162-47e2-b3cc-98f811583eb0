# PLANO DE RESOLUÇÃO - ERRO 406 TASKS

## 📋 PROBLEMA IDENTIFICADO
- **Erro**: 406 Not Acceptable + "JSON object requested, multiple (or no) rows returned"
- **Causa Provável**: Políticas RLS comentadas no schema autoritativo
- **Componente Afetado**: TaskDetailsV2.tsx ao carregar detalhes da tarefa

## 🔧 SOLUÇÕES CRIADAS

### 1. <PERSON>ript Principal de Correção
**Arquivo**: `APLICAR_RLS_DEFINITIVO_TASKS.sql`
- Remove todas as políticas conflitantes
- Aplica políticas baseadas no supabase_schema.sql
- Inclui validação e diagnóstico

### 2. Script de Diagnóstico
**Arquivo**: `DIAGNOSTICO_ERRO_406_TASKS.sql`
- Verifica estado atual das políticas RLS
- Analisa acessos por usuário
- Identifica problemas de multiplicidade

## 📝 ORDEM DE EXECUÇÃO

### Passo 1: Executar Diagnóstico
```sql
-- Execute no Supabase SQL Editor:
-- Conteúdo de: DIAGNOSTICO_ERRO_406_TASKS.sql
```

### Passo 2: Aplicar Correção
```sql
-- Execute no Supabase SQL Editor:
-- Conteúdo de: APLICAR_RLS_DEFINITIVO_TASKS.sql
```

### Passo 3: Testar no Frontend
- Acessar TaskDetailsV2.tsx
- Verificar se carrega os detalhes da tarefa
- Confirmar se não há mais erro 406

## 🎯 POLÍTICAS APLICADAS

### SELECT - "Project members can view tasks"
- ✅ Admins: todas as tasks
- ✅ Owners: tasks dos seus projetos
- ✅ Membros: tasks dos projetos onde participam
- ✅ Executores: suas tasks atribuídas
- ✅ Aprovadores: tasks que precisam aprovar

### INSERT - "Project members can insert tasks"
- ✅ Admins: qualquer projeto
- ✅ Owners: seus projetos
- ✅ Membros: projetos com role manager/editor/admin

### UPDATE - "Project members can update tasks"
- ✅ Admins: qualquer task
- ✅ Owners: tasks dos seus projetos
- ✅ Membros: projetos com role manager/editor/admin
- ✅ Executores: suas próprias tasks

### DELETE - "Project members can delete tasks"
- ✅ Admins: qualquer task
- ✅ Owners: tasks dos seus projetos
- ✅ Membros: apenas com role manager/admin

## 🚨 VALIDAÇÕES INCLUÍDAS

1. **Contagem de Políticas**: Verifica se 4 políticas foram aplicadas
2. **Listagem Ativa**: Mostra políticas em vigor
3. **Teste de Acesso**: Conta tasks acessíveis
4. **Diagnóstico Completo**: Analisa todo o contexto de acesso

## 🔍 SE O PROBLEMA PERSISTIR

1. **Verificar Dados de Teste**:
   - Usuário tem projeto/membro/executor?
   - Task existe e está vinculada corretamente?

2. **Verificar Frontend**:
   - ID da task está correto na URL?
   - Headers de autenticação estão sendo enviados?

3. **Verificar Logs Supabase**:
   - Logs de API para detalhes do erro
   - Logs de Auth para problemas de autenticação

## ✅ RESULTADO ESPERADO
- ✅ Erro 406 resolvido
- ✅ TaskDetailsV2.tsx carregando normalmente
- ✅ Acesso seguro mantido via RLS
- ✅ Todas as funcionalidades de task funcionando

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $deleteTableColumn,
  $deleteTableRow,
  $getTableColumnIndexFromTableCellNode,
  $getTableRowIndexFromTableCellNode,
  $insertTableColumn,
  $insertTableRow,
  $isTableCellNode,
  $isTableNode,
  $isTableRowNode,
  TableCellHeaderStates,
} from '@lexical/table';
import { $findMatchingParent } from '@lexical/utils';
import { $getSelection, $isRangeSelection, $getNodeByKey } from 'lexical';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  Plus,
  Minus,
  Trash2,
  <PERSON>otateCcw,
  Palette,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Pin,
  Table,
  MoreHorizontal,
  Type,
} from 'lucide-react';

const TableActionMenuPlugin: React.FC = () => {
  const [editor] = useLexicalComposerContext();
  const [isInTable, setIsInTable] = useState(false);
  const [menuPosition, setMenuPosition] = useState<{ x: number; y: number } | null>(null);
  const [showMenu, setShowMenu] = useState(false);
  const [selectedCell, setSelectedCell] = useState<HTMLElement | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  const checkIfInTable = useCallback(() => {
    editor.getEditorState().read(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        let currentNode = anchorNode;
        let isInTableCell = false;
        
        // Traverse up the node tree to find a table cell
        while (currentNode) {
          if ($isTableCellNode(currentNode)) {
            isInTableCell = true;
            break;
          }
          currentNode = currentNode.getParent();
        }
        
        // Debug log
        console.log('Table detection:', { isInTableCell, nodeType: anchorNode.getType() });
        
        setIsInTable(isInTableCell);
      } else {
        setIsInTable(false);
      }
    });
  }, [editor]);

  useEffect(() => {
    return editor.registerUpdateListener(() => {
      checkIfInTable();
    });
  }, [editor, checkIfInTable]);

  // Close menu when leaving table
  useEffect(() => {
    if (!isInTable) {
      setShowMenu(false);
      setMenuPosition(null);
      setSelectedCell(null);
    }
  }, [isInTable]);

  // Add click listener for table cells
  useEffect(() => {
    const handleCellClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const cellElement = target.closest('td, th');
      
      if (cellElement && isInTable) {
        event.preventDefault();
        event.stopPropagation();
        
        const rect = cellElement.getBoundingClientRect();
        const menuWidth = 240;
        const menuHeight = 400;
        
        // Calculate position ensuring menu stays within viewport
        let x = rect.right + 10;
        let y = rect.top;
        
        // Adjust horizontal position if menu would go off screen
        if (x + menuWidth > window.innerWidth) {
          x = rect.left - menuWidth - 10;
        }
        
        // Adjust vertical position if menu would go off screen
        if (y + menuHeight > window.innerHeight) {
          y = window.innerHeight - menuHeight - 10;
        }
        
        // Ensure menu doesn't go above viewport
        if (y < 10) {
          y = 10;
        }
        
        setMenuPosition({ x, y });
        setSelectedCell(cellElement);
        setShowMenu(true);
      } else if (!cellElement) {
        setShowMenu(false);
        setMenuPosition(null);
        setSelectedCell(null);
      }
    };

    document.addEventListener('click', handleCellClick);
    return () => document.removeEventListener('click', handleCellClick);
  }, [isInTable]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
        setMenuPosition(null);
        setSelectedCell(null);
      }
    };

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showMenu]);

  const insertRowAbove = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $findMatchingParent(anchorNode, (node) =>
          $isTableCellNode(node),
        );
        if ($isTableCellNode(cellNode)) {
          const tableNode = $findMatchingParent(cellNode, (node) =>
            $isTableNode(node),
          );
          if ($isTableNode(tableNode)) {
            const tableRowIndex = $getTableRowIndexFromTableCellNode(cellNode);
            $insertTableRow(tableNode, tableRowIndex, false, 1, {
              x: 0,
              y: 0,
            });
          }
        }
      }
    });
  };

  const insertRowBelow = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $findMatchingParent(anchorNode, (node) =>
          $isTableCellNode(node),
        );
        if ($isTableCellNode(cellNode)) {
          const tableNode = $findMatchingParent(cellNode, (node) =>
            $isTableNode(node),
          );
          if ($isTableNode(tableNode)) {
            const tableRowIndex = $getTableRowIndexFromTableCellNode(cellNode);
            $insertTableRow(tableNode, tableRowIndex, true, 1, {
              x: 0,
              y: 0,
            });
          }
        }
      }
    });
  };

  const insertColumnLeft = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $findMatchingParent(anchorNode, (node) =>
          $isTableCellNode(node),
        );
        if ($isTableCellNode(cellNode)) {
          const tableNode = $findMatchingParent(cellNode, (node) =>
            $isTableNode(node),
          );
          if ($isTableNode(tableNode)) {
            const tableColumnIndex = $getTableColumnIndexFromTableCellNode(
              cellNode,
            );
            $insertTableColumn(tableNode, tableColumnIndex, false, 1, {
              x: 0,
              y: 0,
            });
          }
        }
      }
    });
  };

  const insertColumnRight = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $findMatchingParent(anchorNode, (node) =>
          $isTableCellNode(node),
        );
        if ($isTableCellNode(cellNode)) {
          const tableNode = $findMatchingParent(cellNode, (node) =>
            $isTableNode(node),
          );
          if ($isTableNode(tableNode)) {
            const tableColumnIndex = $getTableColumnIndexFromTableCellNode(
              cellNode,
            );
            $insertTableColumn(tableNode, tableColumnIndex, true, 1, {
              x: 0,
              y: 0,
            });
          }
        }
      }
    });
  };

  const deleteColumn = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $findMatchingParent(anchorNode, (node) =>
          $isTableCellNode(node),
        );
        if ($isTableCellNode(cellNode)) {
          const tableNode = $findMatchingParent(cellNode, (node) =>
            $isTableNode(node),
          );
          if ($isTableNode(tableNode)) {
            const tableColumnIndex = $getTableColumnIndexFromTableCellNode(
              cellNode,
            );
            $deleteTableColumn(tableNode, tableColumnIndex);
          }
        }
      }
    });
  };

  const deleteRow = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $findMatchingParent(anchorNode, (node) =>
          $isTableCellNode(node),
        );
        if ($isTableCellNode(cellNode)) {
          const tableNode = $findMatchingParent(cellNode, (node) =>
            $isTableNode(node),
          );
          if ($isTableNode(tableNode)) {
            const tableRowIndex = $getTableRowIndexFromTableCellNode(cellNode);
            $deleteTableRow(tableNode, tableRowIndex);
          }
        }
      }
    });
  };

  const deleteTable = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        let currentNode = anchorNode;
        
        // Find the table node by traversing up
        while (currentNode) {
          if ($isTableNode(currentNode)) {
            currentNode.remove();
            break;
          }
          currentNode = currentNode.getParent();
        }
      }
    });
  };

  const addRowHeader = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        let currentNode = anchorNode;
        
        // Find the table cell node by traversing up
        while (currentNode) {
          if ($isTableCellNode(currentNode)) {
            currentNode.toggleHeaderStyle(TableCellHeaderStates.ROW);
            break;
          }
          currentNode = currentNode.getParent();
        }
      }
    });
  };

  const addColumnHeader = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        let currentNode = anchorNode;
        
        // Find the table cell node by traversing up
        while (currentNode) {
          if ($isTableCellNode(currentNode)) {
            currentNode.toggleHeaderStyle(TableCellHeaderStates.COLUMN);
            break;
          }
          currentNode = currentNode.getParent();
        }
      }
    });
  };

  const setBackgroundColor = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        let currentNode = anchorNode;
        
        // Find the table cell node by traversing up
        while (currentNode) {
          if ($isTableCellNode(currentNode)) {
            currentNode.setBackgroundColor(color);
            break;
          }
          currentNode = currentNode.getParent();
        }
      }
    });
  };

  const toggleRowStriping = () => {
    // Implementação para alternar listras de linha
    console.log('Toggle row striping');
  };

  const setVerticalAlign = (align: 'top' | 'middle' | 'bottom') => {
    // Implementação para alinhamento vertical
    console.log('Set vertical align:', align);
  };

  const toggleFirstRowFreeze = () => {
    // Implementação para congelar primeira linha
    console.log('Toggle first row freeze');
  };

  const toggleFirstColumnFreeze = () => {
    // Implementação para congelar primeira coluna
    console.log('Toggle first column freeze');
  };

  // Render contextual menu when cell is clicked
  return (
    <>
      {showMenu && menuPosition && (
        <div 
          ref={menuRef}
          className="fixed z-50 bg-white border border-gray-200 rounded-md shadow-lg w-60 max-h-96 overflow-y-auto"
          style={{
            left: `${menuPosition.x}px`,
            top: `${menuPosition.y}px`
          }}
        >
          <div className="p-2">
             <div className="text-sm font-medium text-gray-900 mb-2">Opções da Tabela</div>
             <div className="border-t border-gray-100 mb-2"></div>
             
             <button 
               onClick={() => setBackgroundColor('#ffeb3b')}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Palette size={16} className="mr-2" />
               Background color
             </button>
             
             <button 
               onClick={toggleRowStriping}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Table size={16} className="mr-2" />
               Toggle Row Striping
             </button>
             
             <button 
               onClick={() => setVerticalAlign('middle')}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <AlignCenter size={16} className="mr-2" />
               Vertical Align
             </button>
             
             <div className="border-t border-gray-100 my-2"></div>
             
             <button 
               onClick={insertRowAbove}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Plus size={16} className="mr-2" />
               Insert row above
             </button>
             
             <button 
               onClick={insertRowBelow}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Plus size={16} className="mr-2" />
               Insert row below
             </button>
             
             <div className="border-t border-gray-100 my-2"></div>
             
             <button 
               onClick={insertColumnLeft}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Plus size={16} className="mr-2" />
               Insert column left
             </button>
             
             <button 
               onClick={insertColumnRight}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Plus size={16} className="mr-2" />
               Insert column right
             </button>
             
             <div className="border-t border-gray-100 my-2"></div>
             
             <button 
               onClick={deleteColumn}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center text-red-600"
             >
               <Trash2 size={16} className="mr-2" />
               Delete column
             </button>
             
             <button 
               onClick={deleteRow}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center text-red-600"
             >
               <Trash2 size={16} className="mr-2" />
               Delete row
             </button>
             
             <button 
               onClick={deleteTable}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center text-red-600"
             >
               <Trash2 size={16} className="mr-2" />
               Delete table
             </button>
             
             <div className="border-t border-gray-100 my-2"></div>
             
             <button 
               onClick={addRowHeader}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Type size={16} className="mr-2" />
               Add row header
             </button>
             
             <button 
               onClick={addColumnHeader}
               className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center"
             >
               <Type size={16} className="mr-2" />
               Add column header
             </button>
           </div>
         </div>
       )}
     </>
    );
};

export default TableActionMenuPlugin;
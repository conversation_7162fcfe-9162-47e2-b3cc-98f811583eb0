client.ts:18 [vite] connecting...
client.ts:150 [vite] connected.
chunk-KQRAUEFU.js?v=911bd56f:21580 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
main.tsx:15 [Aviso] O erro "PublicKeyCredential is not defined" pode aparecer ao usar embeds de vídeo (ex: YouTube). Esse erro é disparado por scripts de terceiros dentro do iframe, não afeta o funcionamento do app e pode ser ignorado com segurança.
react-router-dom.js?v=911bd56f:4409 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ react-router-dom.js?v=911bd56f:4409
logDeprecation @ react-router-dom.js?v=911bd56f:4412
logV6DeprecationWarnings @ react-router-dom.js?v=911bd56f:4415
(anônimo) @ react-router-dom.js?v=911bd56f:5290
commitHookEffectListMount @ chunk-KQRAUEFU.js?v=911bd56f:16936
commitPassiveMountOnFiber @ chunk-KQRAUEFU.js?v=911bd56f:18184
commitPassiveMountEffects_complete @ chunk-KQRAUEFU.js?v=911bd56f:18157
commitPassiveMountEffects_begin @ chunk-KQRAUEFU.js?v=911bd56f:18147
commitPassiveMountEffects @ chunk-KQRAUEFU.js?v=911bd56f:18137
flushPassiveEffectsImpl @ chunk-KQRAUEFU.js?v=911bd56f:19518
flushPassiveEffects @ chunk-KQRAUEFU.js?v=911bd56f:19475
performSyncWorkOnRoot @ chunk-KQRAUEFU.js?v=911bd56f:18896
flushSyncCallbacks @ chunk-KQRAUEFU.js?v=911bd56f:9135
commitRootImpl @ chunk-KQRAUEFU.js?v=911bd56f:19460
commitRoot @ chunk-KQRAUEFU.js?v=911bd56f:19305
finishConcurrentRender @ chunk-KQRAUEFU.js?v=911bd56f:18833
performConcurrentWorkOnRoot @ chunk-KQRAUEFU.js?v=911bd56f:18746
workLoop @ chunk-KQRAUEFU.js?v=911bd56f:197
flushWork @ chunk-KQRAUEFU.js?v=911bd56f:176
performWorkUntilDeadline @ chunk-KQRAUEFU.js?v=911bd56f:384
react-router-dom.js?v=911bd56f:4409 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ react-router-dom.js?v=911bd56f:4409
logDeprecation @ react-router-dom.js?v=911bd56f:4412
logV6DeprecationWarnings @ react-router-dom.js?v=911bd56f:4418
(anônimo) @ react-router-dom.js?v=911bd56f:5290
commitHookEffectListMount @ chunk-KQRAUEFU.js?v=911bd56f:16936
commitPassiveMountOnFiber @ chunk-KQRAUEFU.js?v=911bd56f:18184
commitPassiveMountEffects_complete @ chunk-KQRAUEFU.js?v=911bd56f:18157
commitPassiveMountEffects_begin @ chunk-KQRAUEFU.js?v=911bd56f:18147
commitPassiveMountEffects @ chunk-KQRAUEFU.js?v=911bd56f:18137
flushPassiveEffectsImpl @ chunk-KQRAUEFU.js?v=911bd56f:19518
flushPassiveEffects @ chunk-KQRAUEFU.js?v=911bd56f:19475
performSyncWorkOnRoot @ chunk-KQRAUEFU.js?v=911bd56f:18896
flushSyncCallbacks @ chunk-KQRAUEFU.js?v=911bd56f:9135
commitRootImpl @ chunk-KQRAUEFU.js?v=911bd56f:19460
commitRoot @ chunk-KQRAUEFU.js?v=911bd56f:19305
finishConcurrentRender @ chunk-KQRAUEFU.js?v=911bd56f:18833
performConcurrentWorkOnRoot @ chunk-KQRAUEFU.js?v=911bd56f:18746
workLoop @ chunk-KQRAUEFU.js?v=911bd56f:197
flushWork @ chunk-KQRAUEFU.js?v=911bd56f:176
performWorkUntilDeadline @ chunk-KQRAUEFU.js?v=911bd56f:384
AuthProvider.tsx:53 Auth state changed: INITIAL_SESSION 50580ad5-7c45-4d7f-9446-69f069205ad0
iframe-index.bundle.js:2 [Violation] 'load' handler took 382ms
iframe-index.bundle.js:2 [Violation] Avoid using document.write(). https://developers.google.com/web/updates/2016/08/removing-document-write
Fa.uid @ iframe-index.bundle.js:2
taskService.ts:53 [getFullById] executors: [] null
taskService.ts:71 [taskService.getFullById] contentBlocks: (3) [{…}, {…}, {…}]
TaskDetails.tsx:197 [TaskDetails] Blocos carregados do banco: [
  {
    "id": "9b6a044a-172b-462c-afe1-b407fc7ec038",
    "task_id": "6970a914-29a7-4c45-92ae-a79791c832e6",
    "type": "text",
    "content": {
      "text": "{\"root\":{\"children\":[{\"children\":[],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}",
      "value": "<p></p>"
    },
    "config": {
      "card": {
        "font": {
          "size": 14,
          "color": "#1f2937",
          "style": "normal"
        },
        "hover": {
          "enabled": true,
          "shadowDepth": 2
        },
        "border": {
          "color": "#e5e7eb",
          "width": 1,
          "enabled": true
        },
        "format": "rounded",
        "shadow": {
          "depth": 1,
          "enabled": true
        },
        "backgroundColor": "#ffffff"
      },
      "icon": {
        "type": "predefined",
        "enabled": true,
        "iconName": "FileText",
        "position": "left-title",
        "appearance": {
          "size": 20,
          "color": "#ffffff",
          "hover": {
            "enabled": true,
            "shadowDepth": 2
          },
          "border": {
            "color": "#6b7280",
            "width": 1,
            "enabled": false
          },
          "format": "rounded",
          "shadow": {
            "depth": 1,
            "enabled": true
          },
          "background": "#6b7280"
        }
      },
      "button": {
        "url": "",
        "icon": "ArrowRight",
        "size": "medium",
        "text": "Clique aqui",
        "color": "#ffffff",
        "hover": {
          "enabled": false,
          "shadowDepth": 3
        },
        "style": "rounded",
        "border": {
          "color": "#e5e5e5",
          "width": 1,
          "enabled": false
        },
        "newTab": false,
        "shadow": {
          "depth": 2,
          "enabled": false
        },
        "position": "bottom-center",
        "iconPosition": "left",
        "backgroundColor": "#7c3aed"
      }
    },
    "order": 0,
    "created_at": null
  },
  {
    "id": "bb56bb62-66ee-4b10-a3f8-316eaac39dd6",
    "task_id": "6970a914-29a7-4c45-92ae-a79791c832e6",
    "type": "quiz",
    "content": {
      "quiz": {
        "config": {
          "mode": "assessment",
          "title": "Assessment Básico - Conhecimentos Gerais",
          "showScore": true,
          "showTimer": false,
          "allowRetry": true,
          "isRequired": false,
          "description": "Avaliação tradicional com correção automática",
          "maxAttempts": 3,
          "instructions": "Responda às perguntas. Você tem 3 tentativas e precisa de 70% para aprovação.",
          "passingScore": 70,
          "showFeedback": true,
          "allowSaveDraft": true,
          "shuffleOptions": false,
          "enableAnalytics": true,
          "showProgressBar": true,
          "shuffleQuestions": false,
          "showResultsToUser": true,
          "showCorrectAnswers": true,
          "showDetailedResults": true,
          "blockProgressUntilPassed": false
        },
        "questions": [
          {
            "id": "assess_basic_q1",
            "type": "single-choice",
            "title": "Qual é a capital do Brasil?",
            "points": 2,
            "options": [
              {
                "id": "opt1",
                "text": "Brasília",
                "isCorrect": true
              },
              {
                "id": "opt2",
                "text": "São Paulo",
                "isCorrect": false
              },
              {
                "id": "opt3",
                "text": "Rio de Janeiro",
                "isCorrect": false
              },
              {
                "id": "opt4",
                "text": "Salvador",
                "isCorrect": false
              }
            ],
            "required": true,
            "explanation": "Brasília foi inaugurada em 21 de abril de 1960 como a nova capital do Brasil.",
            "correctFeedback": "Correto! Brasília é a capital do Brasil desde 1960.",
            "incorrectFeedback": "Incorreto. A capital do Brasil é Brasília."
          },
          {
            "id": "assess_basic_q2",
            "type": "true-false",
            "title": "O TypeScript é um superset do JavaScript.",
            "points": 2,
            "required": true,
            "explanation": "TypeScript estende JavaScript adicionando definições de tipo estático.",
            "correctAnswer": true,
            "correctFeedback": "Correto! TypeScript adiciona tipagem estática ao JavaScript.",
            "incorrectFeedback": "Incorreto. TypeScript é realmente um superset do JavaScript."
          }
        ]
      }
    },
    "config": {
      "card": {
        "font": {
          "size": 14,
          "color": "#ec4899",
          "style": "normal"
        },
        "hover": {
          "enabled": true,
          "shadowDepth": 4
        },
        "border": {
          "color": "#ec4899",
          "width": 1,
          "enabled": true
        },
        "format": "rounded",
        "shadow": {
          "depth": 2,
          "enabled": true
        },
        "backgroundColor": "#fdf2f8"
      },
      "icon": {
        "type": "predefined",
        "enabled": true,
        "iconName": "CircleHelp",
        "position": "left-title",
        "appearance": {
          "size": 20,
          "color": "#fff",
          "hover": {
            "enabled": true,
            "shadowDepth": 2
          },
          "border": {
            "color": "#ec4899",
            "width": 1,
            "enabled": true
          },
          "format": "circle",
          "shadow": {
            "depth": 1,
            "enabled": true
          },
          "background": "#ec4899"
        }
      },
      "button": {
        "url": "",
        "icon": "ArrowRight",
        "size": "medium",
        "text": "Clique aqui",
        "color": "#fff",
        "hover": {
          "enabled": true,
          "shadowDepth": 3
        },
        "style": "rounded",
        "border": {
          "color": "#ec4899",
          "width": 1,
          "enabled": false
        },
        "newTab": false,
        "shadow": {
          "depth": 2,
          "enabled": true
        },
        "position": "bottom-center",
        "iconPosition": "left",
        "backgroundColor": "#ec4899"
      }
    },
    "order": 1,
    "created_at": "2025-07-12T21:37:35.737707+00:00"
  },
  {
    "id": "4b23d394-d1c2-474a-ab96-23f140a16a87",
    "task_id": "6970a914-29a7-4c45-92ae-a79791c832e6",
    "type": "evidence",
    "content": {
      "title": "Evidências/Anexos",
      "maxFiles": 2,
      "evidences": [],
      "allowUpload": true,
      "description": "Envie arquivos como evidência ou anexo para esta tarefa.",
      "maxFileSize": 10,
      "showUploadDate": true,
      "showUploadedBy": true,
      "allowedFileTypes": [
        "image/*",
        "video/*",
        ".pdf",
        ".doc",
        ".docx",
        ".txt"
      ]
    },
    "config": {
      "card": {
        "color": "#134e4a",
        "border": {
          "color": "#14b8a6",
          "width": 1,
          "enabled": true
        },
        "format": "rounded",
        "shadow": {
          "depth": 1,
          "enabled": true
        },
        "backgroundColor": "#f0fdfa"
      },
      "icon": {
        "type": "predefined",
        "color": "#ffffff",
        "border": {
          "color": "#14b8a6",
          "width": 1,
          "enabled": true
        },
        "format": "circle",
        "shadow": {
          "depth": 2,
          "enabled": true
        },
        "enabled": true,
        "iconName": "Paperclip",
        "position": "left-title",
        "backgroundColor": "#14b8a6"
      },
      "button": {
        "size": "medium",
        "color": "#ffffff",
        "hover": {
          "color": "#ffffff",
          "backgroundColor": "#0f766e"
        },
        "border": {
          "color": "#14b8a6",
          "width": 1,
          "enabled": false
        },
        "format": "rounded",
        "shadow": {
          "depth": 2,
          "enabled": true
        },
        "backgroundColor": "#14b8a6"
      }
    },
    "order": 2,
    "created_at": null
  }
]
QuizExecutionBlock.tsx:406 📊 Quiz convertido: {config: {…}, questions: Array(2)}
QuizExecutionBlock.tsx:407 📋 Conteúdo original: {quiz: {…}}
QuizExecutionBlock.tsx:408 🔢 Número de perguntas: 2
QuizExecutionBlock.tsx:412 🔀 Embaralhamento aplicado: {shuffleQuestions: false, shuffleOptions: false}
QuizExecutionBlock.tsx:428 🎯 DECISÃO: Usando modo local para evitar erros 406 do Supabase
QuizExecutionBlock.tsx:437 📊 Progresso local carregado: null
QuizExecutionBlock.tsx:516 ✅ Quiz configurado em modo local com sucesso
QuizExecutionBlock.tsx:546 🔍 DEBUG Estado atual: {quizState: 'loading', isLocalMode: false, hasQuizContent: false, questionsCount: 0, isLoading: true, …}
EvidenceExecutionBlock.tsx:171 🔍 Carregando evidências para tarefa: 6970a914-29a7-4c45-92ae-a79791c832e6
QuizExecutionBlock.tsx:406 📊 Quiz convertido: {config: {…}, questions: Array(2)}
QuizExecutionBlock.tsx:407 📋 Conteúdo original: {quiz: {…}}
QuizExecutionBlock.tsx:408 🔢 Número de perguntas: 2
QuizExecutionBlock.tsx:412 🔀 Embaralhamento aplicado: {shuffleQuestions: false, shuffleOptions: false}
QuizExecutionBlock.tsx:428 🎯 DECISÃO: Usando modo local para evitar erros 406 do Supabase
QuizExecutionBlock.tsx:437 📊 Progresso local carregado: null
QuizExecutionBlock.tsx:516 ✅ Quiz configurado em modo local com sucesso
QuizExecutionBlock.tsx:546 🔍 DEBUG Estado atual: {quizState: 'loading', isLocalMode: false, hasQuizContent: false, questionsCount: 0, isLoading: true, …}
EvidenceExecutionBlock.tsx:171 🔍 Carregando evidências para tarefa: 6970a914-29a7-4c45-92ae-a79791c832e6
QuizExecutionBlock.tsx:1398 🎯 Quiz pronto para iniciar: {quizContent: {…}, questionsLength: 2, isLocalMode: true, quizState: 'ready'}
QuizExecutionBlock.tsx:1398 🎯 Quiz pronto para iniciar: {quizContent: {…}, questionsLength: 2, isLocalMode: true, quizState: 'ready'}
QuizExecutionBlock.tsx:546 🔍 DEBUG Estado atual: {quizState: 'ready', isLocalMode: true, hasQuizContent: true, questionsCount: 2, isLoading: false, …}
QuizExecutionBlock.tsx:546 🔍 DEBUG Estado atual: {quizState: 'ready', isLocalMode: true, hasQuizContent: true, questionsCount: 2, isLoading: false, …}
evidenceService.ts:235 Dados retornados do Supabase: (2) [{…}, {…}]
evidenceService.ts:239 Processando attachment: {id: '70f9d393-2e8e-4f23-ada4-1bcb02ff876c', file_url: 'https://gcdtchxyxawtiroxuifs.supabase.co/storage/v…752360213882_Cronograma_Consultoria_Processos.pdf', description: 'Cronograma Consultoria Processos.pdf', created_at: '2025-07-12T22:43:35.919755+00:00', uploaded_by: '50580ad5-7c45-4d7f-9446-69f069205ad0', …}
evidenceService.ts:239 Processando attachment: {id: 'd04c226a-0915-4696-b8d4-0bd08365d7b6', file_url: 'https://gcdtchxyxawtiroxuifs.supabase.co/storage/v…-474a-ab96-23f140a16a87/1752360180738_2_Caso.docx', description: '2º Caso .docx', created_at: '2025-07-12T22:43:02.067666+00:00', uploaded_by: '50580ad5-7c45-4d7f-9446-69f069205ad0', …}
EvidenceExecutionBlock.tsx:173 ✅ Evidências carregadas: (2) [{…}, {…}]
evidenceService.ts:235 Dados retornados do Supabase: (2) [{…}, {…}]
evidenceService.ts:239 Processando attachment: {id: '70f9d393-2e8e-4f23-ada4-1bcb02ff876c', file_url: 'https://gcdtchxyxawtiroxuifs.supabase.co/storage/v…752360213882_Cronograma_Consultoria_Processos.pdf', description: 'Cronograma Consultoria Processos.pdf', created_at: '2025-07-12T22:43:35.919755+00:00', uploaded_by: '50580ad5-7c45-4d7f-9446-69f069205ad0', …}
evidenceService.ts:239 Processando attachment: {id: 'd04c226a-0915-4696-b8d4-0bd08365d7b6', file_url: 'https://gcdtchxyxawtiroxuifs.supabase.co/storage/v…-474a-ab96-23f140a16a87/1752360180738_2_Caso.docx', description: '2º Caso .docx', created_at: '2025-07-12T22:43:02.067666+00:00', uploaded_by: '50580ad5-7c45-4d7f-9446-69f069205ad0', …}
EvidenceExecutionBlock.tsx:173 ✅ Evidências carregadas: (2) [{…}, {…}]
AuthProvider.tsx:53 Auth state changed: SIGNED_IN 50580ad5-7c45-4d7f-9446-69f069205ad0
QuizExecutionBlock.tsx:1398 🎯 Quiz pronto para iniciar: {quizContent: {…}, questionsLength: 2, isLocalMode: true, quizState: 'ready'}
QuizExecutionBlock.tsx:1398 🎯 Quiz pronto para iniciar: {quizContent: {…}, questionsLength: 2, isLocalMode: true, quizState: 'ready'}
QuizExecutionBlock.tsx:1398 🎯 Quiz pronto para iniciar: {quizContent: {…}, questionsLength: 2, isLocalMode: true, quizState: 'ready'}
QuizExecutionBlock.tsx:1398 🎯 Quiz pronto para iniciar: {quizContent: {…}, questionsLength: 2, isLocalMode: true, quizState: 'ready'}
RichContentEditor.tsx:495 [RichContentEditor] editConfig atualizado: {card: {…}, icon: {…}, button: {…}}
RichContentEditor.tsx:495 [RichContentEditor] editConfig atualizado: {card: {…}, icon: {…}, button: {…}}
chunk-MV4NHPCA.js?v=911bd56f:344 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anônimo) @ chunk-MV4NHPCA.js?v=911bd56f:344
commitHookEffectListMount @ chunk-KQRAUEFU.js?v=911bd56f:16936
commitPassiveMountOnFiber @ chunk-KQRAUEFU.js?v=911bd56f:18184
commitPassiveMountEffects_complete @ chunk-KQRAUEFU.js?v=911bd56f:18157
commitPassiveMountEffects_begin @ chunk-KQRAUEFU.js?v=911bd56f:18147
commitPassiveMountEffects @ chunk-KQRAUEFU.js?v=911bd56f:18137
flushPassiveEffectsImpl @ chunk-KQRAUEFU.js?v=911bd56f:19518
flushPassiveEffects @ chunk-KQRAUEFU.js?v=911bd56f:19475
commitRootImpl @ chunk-KQRAUEFU.js?v=911bd56f:19444
commitRoot @ chunk-KQRAUEFU.js?v=911bd56f:19305
performSyncWorkOnRoot @ chunk-KQRAUEFU.js?v=911bd56f:18923
flushSyncCallbacks @ chunk-KQRAUEFU.js?v=911bd56f:9135
commitRootImpl @ chunk-KQRAUEFU.js?v=911bd56f:19460
commitRoot @ chunk-KQRAUEFU.js?v=911bd56f:19305
finishConcurrentRender @ chunk-KQRAUEFU.js?v=911bd56f:18833
performConcurrentWorkOnRoot @ chunk-KQRAUEFU.js?v=911bd56f:18746
workLoop @ chunk-KQRAUEFU.js?v=911bd56f:197
flushWork @ chunk-KQRAUEFU.js?v=911bd56f:176
performWorkUntilDeadline @ chunk-KQRAUEFU.js?v=911bd56f:384
TableStyleSelector.tsx:21 🔍 TableStyleSelector renderizado: {isInTable: false}
TableStyleSelector.tsx:24 ❌ TableStyleSelector: Não está em tabela, não renderizando
TableStyleSelector.tsx:21 🔍 TableStyleSelector renderizado: {isInTable: false}
TableStyleSelector.tsx:24 ❌ TableStyleSelector: Não está em tabela, não renderizando
TableStyleSelector.tsx:21 🔍 TableStyleSelector renderizado: {isInTable: true}
TableStyleSelector.tsx:28 ✅ TableStyleSelector: Está em tabela, renderizando botão
TableStyleSelector.tsx:285 🔍 DIAGNÓSTICO: Detectando estilo atual
TableStyleSelector.tsx:289 📄 HTML do editor para análise: <table class="tiptap-table" style="min-width: 75px"><colgroup><col style="min-width: 25px"><col style="min-width: 25px"><col style="min-width: 25px"></colgroup><tbody><tr class="border border-gray-300"><th class="tiptap-table-header" colspan="1" rowspan="1"><p></p></th><th class="tiptap-table-header" colspan="1" rowspan="1"><p></p></th><th class="tiptap-table-header" colspan="1" rowspan="1"><p></p></th></tr><tr class="border border-gray-300"><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td></tr><tr class="border border-gray-300"><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td></tr></tbody></table>
TableStyleSelector.tsx:294 🔍 Classes encontradas no HTML: ['tiptap-table']
TableStyleSelector.tsx:299 🔍 Data-table-style no HTML: 
TableStyleSelector.tsx:310 🔍 Estado atual do DOM: {tagName: 'TABLE', domClasses: Array(0), domDataStyle: '', outerHTML: '<table style="min-width: 75px;"><colgroup><col sty…"ProseMirror-trailingBreak"></p></th><th class...'}
TableStyleSelector.tsx:334 🔍 Classe de estilo detectada: 
TableStyleSelector.tsx:339 🔍 DIAGNÓSTICO - Debug getCurrentStyle: {htmlClasses: Array(1), dataStyle: '', domClasses: Array(0), domDataStyle: '', appliedStyleClass: '', …}
TableContextMenu.tsx:97 🖱️ MENU CONTEXTUAL: Abrindo painel de propriedades cell
6970a914-29a7-4c45-92ae-a79791c832e6:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
6970a914-29a7-4c45-92ae-a79791c832e6:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
6970a914-29a7-4c45-92ae-a79791c832e6:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
6970a914-29a7-4c45-92ae-a79791c832e6:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
6970a914-29a7-4c45-92ae-a79791c832e6:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
6970a914-29a7-4c45-92ae-a79791c832e6:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
6970a914-29a7-4c45-92ae-a79791c832e6:1 Unchecked runtime.lastError: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
6970a914-29a7-4c45-92ae-a79791c832e6:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
TablePropertiesPanel.tsx:98 🚀 PROPRIEDADES PANEL: applyProperties chamada {newProperties: {…}, selectedElement: 'cell', previewMode: false, isApplying: false}
TablePropertiesPanel.tsx:117 🎯 PROPRIEDADES PANEL: Aplicando ao editor (debounced) {width: 100, height: 40, borderWidth: 1, borderColor: '#e5e7eb', backgroundColor: '#d86f6f', …}
TablePropertiesPanel.tsx:136 🎯 PROPRIEDADES PANEL: applyToEditor chamada {selectedElement: 'cell', props: {…}}
TablePropertiesPanel.tsx:144 📱 PROPRIEDADES PANEL: Aplicando propriedades da célula
TablePropertiesPanel.tsx:203 🎯 APLICAÇÃO APÓS CORREÇÃO COMPLETA DO CSS
TablePropertiesPanel.tsx:209 🔧 APLICANDO ESTILO COMO O QUE FUNCIONA (com !important)
TablePropertiesPanel.tsx:220 ✅ Background-color aplicado: #d86f6f
TablePropertiesPanel.tsx:221 ✅ Background computado: rgb(216, 111, 111)
TablePropertiesPanel.tsx:222 📱 Elemento: <th class=​"tiptap-table-header" colspan=​"1" rowspan=​"1" style=​"background-color:​ rgb(216, 111, 111)​ !important;​ color:​ rgb(0, 0, 0)​ !important;​ padding:​ 8px !important;​ border:​ 1px solid rgb(229, 231, 235)​ !important;​">​…​</th>​
TablePropertiesPanel.tsx:226 🎉 SUCESSO! Background aplicado corretamente
TablePropertiesPanel.tsx:98 🚀 PROPRIEDADES PANEL: applyProperties chamada {newProperties: {…}, selectedElement: 'cell', previewMode: false, isApplying: true}
TablePropertiesPanel.tsx:102 ⏸️ BLOQUEADO: Já aplicando propriedades
TablePropertiesPanel.tsx:98 🚀 PROPRIEDADES PANEL: applyProperties chamada {newProperties: {…}, selectedElement: 'cell', previewMode: false, isApplying: true}
TablePropertiesPanel.tsx:102 ⏸️ BLOQUEADO: Já aplicando propriedades
TablePropertiesPanel.tsx:125 ✅ Flag de aplicação liberada
TablePropertiesPanel.tsx:695 🖱️ PROPRIEDADES PANEL: Botão APLICAR clicado! {properties: {…}, selectedElement: 'cell'}
TablePropertiesPanel.tsx:136 🎯 PROPRIEDADES PANEL: applyToEditor chamada {selectedElement: 'cell', props: {…}}
TablePropertiesPanel.tsx:144 📱 PROPRIEDADES PANEL: Aplicando propriedades da célula
TablePropertiesPanel.tsx:203 🎯 APLICAÇÃO APÓS CORREÇÃO COMPLETA DO CSS
TablePropertiesPanel.tsx:209 🔧 APLICANDO ESTILO COMO O QUE FUNCIONA (com !important)
TablePropertiesPanel.tsx:220 ✅ Background-color aplicado: #d86f6f
TablePropertiesPanel.tsx:221 ✅ Background computado: rgb(216, 111, 111)
TablePropertiesPanel.tsx:222 📱 Elemento: <th class=​"tiptap-table-header" colspan=​"1" rowspan=​"1" style=​"background-color:​ rgb(216, 111, 111)​ !important;​ color:​ rgb(0, 0, 0)​ !important;​ padding:​ 8px !important;​ border:​ 1px solid rgb(229, 231, 235)​ !important;​">​…​</th>​
TablePropertiesPanel.tsx:226 🎉 SUCESSO! Background aplicado corretamente
tableUtils.ts:488 🔍 Debug Elementos da Tabela
tableUtils.ts:491 ✅ Tabela encontrada: {tagName: 'TABLE', className: '', id: '', dataset: DOMStringMap, parentNode: 'DIV', …}
tableUtils.ts:503 ✅ É uma tabela HTML real
tableUtils.ts:531 Célula: TD <td class=​"tiptap-table-cell selectedCell" colspan=​"1" rowspan=​"1" style=​"background-color:​ rgb(59, 130, 246)​ !important;​">​…​</td>​
tableUtils.ts:532 Linha: TR <tr class=​"border border-gray-300">​…​</tr>​
tableUtils.ts:533 Índice da coluna: 2
TableColorPicker.tsx:116 ✅ Cor aplicada à célula: #3b82f6 <td class=​"tiptap-table-cell selectedCell" colspan=​"1" rowspan=​"1" style=​"background-color:​ rgb(59, 130, 246)​ !important;​">​…​</td>​
TableStyleSelector.tsx:21 🔍 TableStyleSelector renderizado: {isInTable: true}
TableStyleSelector.tsx:28 ✅ TableStyleSelector: Está em tabela, renderizando botão
TableStyleSelector.tsx:285 🔍 DIAGNÓSTICO: Detectando estilo atual
TableStyleSelector.tsx:289 📄 HTML do editor para análise: <table class="tiptap-table" style="min-width: 75px"><colgroup><col style="min-width: 25px"><col style="min-width: 25px"><col style="min-width: 25px"></colgroup><tbody><tr class="border border-gray-300"><th class="tiptap-table-header" colspan="1" rowspan="1"><p></p></th><th class="tiptap-table-header" colspan="1" rowspan="1"><p></p></th><th class="tiptap-table-header" colspan="1" rowspan="1"><p></p></th></tr><tr class="border border-gray-300"><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td></tr><tr class="border border-gray-300"><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td><td class="tiptap-table-cell" colspan="1" rowspan="1"><p></p></td></tr></tbody></table>
TableStyleSelector.tsx:294 🔍 Classes encontradas no HTML: ['tiptap-table']
TableStyleSelector.tsx:299 🔍 Data-table-style no HTML: 
TableStyleSelector.tsx:310 🔍 Estado atual do DOM: {tagName: 'TABLE', domClasses: Array(0), domDataStyle: '', outerHTML: '<table style="min-width: 75px;"><colgroup><col sty…"ProseMirror-trailingBreak"></p></th><th class...'}
TableStyleSelector.tsx:334 🔍 Classe de estilo detectada: 
TableStyleSelector.tsx:339 🔍 DIAGNÓSTICO - Debug getCurrentStyle: {htmlClasses: Array(1), dataStyle: '', domClasses: Array(0), domDataStyle: '', appliedStyleClass: '', …}
6970a914-29a7-4c45-92ae-a79791c832e6:1 Blocked aria-hidden on an element because its descendant retained focus. The focus must not be hidden from assistive technology users. Avoid using aria-hidden on a focused element or its ancestor. Consider using the inert attribute instead, which will also prevent focus. For more details, see the aria-hidden section of the WAI-ARIA specification at https://w3c.github.io/aria/#aria-hidden.
Element with focus: <div.fixed left-0 top-0 z-50 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg resize-none overflow-hidden flex flex-col#radix-:rq:>
Ancestor with aria-hidden: <div.fixed left-0 top-0 z-50 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg resize-none overflow-hidden flex flex-col#radix-:rq:> <div role=​"dialog" id=​"radix-:​rq:​" aria-describedby=​"radix-:​rs:​" aria-labelledby=​"radix-:​rr:​" data-state=​"open" class=​"fixed left-0 top-0 z-50 border bg-background shadow-lg duration-200 data-[state=open]​:​animate-in data-[state=closed]​:​animate-out data-[state=closed]​:​fade-out-0 data-[state=open]​:​fade-in-0 data-[state=closed]​:​zoom-out-95 data-[state=open]​:​zoom-in-95 sm:​rounded-lg resize-none overflow-hidden flex flex-col" tabindex=​"-1" style=​"width:​ 721.7px;​ height:​ 500px;​ max-width:​ none;​ max-height:​ none;​ transform:​ translate(154.65px, 97.5px)​;​ cursor:​ default;​ pointer-events:​ none;​" data-aria-hidden=​"true" aria-hidden=​"true">​…​</div>​flex

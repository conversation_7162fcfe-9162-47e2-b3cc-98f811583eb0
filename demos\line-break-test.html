<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Quebras de Linha</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-area {
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            background: white;
            min-height: 120px;
            margin: 20px 0;
        }
        .editable {
            outline: none;
            min-height: 100px;
            width: 100%;
            white-space: pre-wrap;
            word-break: break-word;
        }
        .instructions {
            background: #f3f4f6;
            padding: 16px;
            border-radius: 8px;
            margin: 20px 0;
        }
        ul {
            list-style-type: disc;
            margin-left: 24px;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <h1>Teste de Quebras de Linha no Editor</h1>
    
    <div class="instructions">
        <h3>Instruções de Teste:</h3>
        <ul>
            <li><strong>Enter:</strong> Deve criar um novo item na lista ou novo parágrafo</li>
            <li><strong>Shift+Enter:</strong> Deve criar uma quebra de linha dentro do mesmo item/parágrafo</li>
            <li>Teste digitando "Abacate", depois Shift+Enter, depois "Laranja", depois Enter, depois "Caju"</li>
        </ul>
    </div>

    <div class="test-area">
        <div class="editable" contenteditable="true" role="textbox" spellcheck="false">
            <ul>
                <li>AbacateLaranja Caju</li>
            </ul>
        </div>
    </div>

    <div class="instructions">
        <h3>Resultado Esperado:</h3>
        <p>Após implementar o LineBreakPlugin, você deve conseguir:</p>
        <ul>
            <li>Usar Shift+Enter para quebrar linha dentro de um item da lista</li>
            <li>Usar Enter para criar novos itens na lista</li>
            <li>Converter lista vazia em parágrafo normal</li>
        </ul>
    </div>

    <script>
        // Simular comportamento básico de quebra de linha
        document.querySelector('.editable').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.shiftKey) {
                e.preventDefault();
                document.execCommand('insertHTML', false, '<br>');
            }
        });
    </script>
</body>
</html>
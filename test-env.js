// Teste simples para verificar variáveis de ambiente
console.log('VITE_SUPABASE_URL:', import.meta.env?.VITE_SUPABASE_URL || process.env.VITE_SUPABASE_URL);
console.log('VITE_SUPABASE_KEY:', import.meta.env?.VITE_SUPABASE_KEY || process.env.VITE_SUPABASE_KEY);

// Verificar se as variáveis estão definidas
if (!import.meta.env?.VITE_SUPABASE_URL && !process.env.VITE_SUPABASE_URL) {
  console.error('VITE_SUPABASE_URL não encontrada');
}

if (!import.meta.env?.VITE_SUPABASE_KEY && !process.env.VITE_SUPABASE_KEY) {
  console.error('VITE_SUPABASE_KEY não encontrada');
}
{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emoji-mart/react": "^1.1.1", "@hookform/resolvers": "^3.9.0", "@lexical/code": "^0.12.0", "@lexical/history": "^0.12.0", "@lexical/html": "^0.12.0", "@lexical/link": "^0.12.0", "@lexical/list": "^0.12.0", "@lexical/markdown": "^0.12.0", "@lexical/react": "^0.12.0", "@lexical/rich-text": "^0.12.0", "@lexical/selection": "^0.12.0", "@lexical/table": "^0.12.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-query": "^5.56.2", "@tiptap/core": "^2.26.1", "@tiptap/extension-blockquote": "^2.23.0", "@tiptap/extension-code": "^2.23.0", "@tiptap/extension-code-block": "^2.26.1", "@tiptap/extension-code-block-lowlight": "^3.0.1", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-emoji": "^2.23.0", "@tiptap/extension-highlight": "^2.26.1", "@tiptap/extension-history": "^2.23.0", "@tiptap/extension-link": "^2.26.1", "@tiptap/extension-placeholder": "^2.23.0", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/extension-task-item": "^2.26.1", "@tiptap/extension-task-list": "^2.26.1", "@tiptap/extension-text-align": "^2.26.1", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/extension-underline": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "emoji-mart": "^5.6.0", "highlight.js": "^11.11.1", "input-otp": "^1.2.4", "lexical": "^0.12.0", "lowlight": "^3.3.0", "lucide-react": "^0.525.0", "next-themes": "^0.3.0", "prismjs": "^1.30.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "^18.3.1", "react-color": "^2.19.3", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-easy-crop": "^5.4.2", "react-hook-form": "^7.53.0", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/emoji-mart": "^5.3.0", "@types/jest": "^30.0.0", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-easy-crop": "^2.0.0", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "tsx": "^4.20.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^4.4.9", "vitest": "^3.2.4"}}
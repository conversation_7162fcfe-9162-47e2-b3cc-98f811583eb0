/**
 * Presets de configuração para blocos de alerta
 * Migrado de alertPresets.ts para a nova estrutura modular
 */

// Tipos de alerta
export type AlertType = 'success' | 'info' | 'warning' | 'error';

// Variações de estilo
export type AlertStyle = 'solid' | 'outline' | 'soft';

// Variações de layout
export type AlertLayout = 'default' | 'compact' | 'icon-right' | 'icon-boxed' | 'icon-corner' | 'icon-bubble' | 'bottom-bar' | 'left-bar';

// Variante de alerta (combinação de tipo, estilo e layout)
export type AlertVariant = `${AlertType}-${AlertStyle}` | `${AlertType}-${AlertStyle}-${AlertLayout}`;

// Preset de alerta
export interface AlertPreset {
  id: string;
  type: AlertType;
  style: AlertStyle;
  layout: AlertLayout;
  iconOptions: string[]; // nomes dos ícones permitidos para o tipo
  defaultIcon: string;
  color: string;
  borderColor?: string;
  titleBold?: boolean;
  defaultTitle: string;
  defaultMessage: string;
}

// Exemplos de ícones Lucide para cada tipo
export const ALERT_ICONS = {
  success: ['CheckCircle', 'ThumbsUp', 'Award', 'Star', 'Trophy', 'Smile', 'Heart', 'Rocket', 'Medal', 'Feather'],
  info:    ['Info', 'BookOpen', 'Lightbulb', 'MessageSquare', 'Bookmark', 'ClipboardList', 'Search', 'List', 'Globe', 'Bell'],
  warning: ['AlertTriangle', 'Flag', 'Eye', 'Clock', 'Megaphone', 'Zap', 'ShieldAlert', 'Flame', 'StopCircle', 'Radiation'],
  error:   ['XCircle', 'AlertOctagon', 'Ban', 'Slash', 'Skull', 'Bug', 'ShieldOff', 'Trash2', 'Radiation', 'StopCircle'],
};

// Presets de alerta organizados
export const alertBlockPresets: Record<string, AlertPreset> = {
  // Presets sólidos básicos
  'success-solid': {
    id: 'success-solid',
    type: 'success',
    style: 'solid',
    layout: 'default',
    iconOptions: ALERT_ICONS.success,
    defaultIcon: 'CheckCircle',
    color: '#34d399',
    borderColor: '#10b981',
    titleBold: true,
    defaultTitle: 'Sucesso!',
    defaultMessage: 'Sua ação foi realizada com sucesso.',
  },
  'info-solid': {
    id: 'info-solid',
    type: 'info',
    style: 'solid',
    layout: 'default',
    iconOptions: ALERT_ICONS.info,
    defaultIcon: 'Info',
    color: '#60a5fa',
    borderColor: '#2563eb',
    titleBold: true,
    defaultTitle: 'Informação!',
    defaultMessage: 'Aqui está uma informação importante.',
  },
  'warning-solid': {
    id: 'warning-solid',
    type: 'warning',
    style: 'solid',
    layout: 'default',
    iconOptions: ALERT_ICONS.warning,
    defaultIcon: 'AlertTriangle',
    color: '#fbbf24',
    borderColor: '#f59e42',
    titleBold: true,
    defaultTitle: 'Atenção!',
    defaultMessage: 'Fique atento a este aviso.',
  },
  'error-solid': {
    id: 'error-solid',
    type: 'error',
    style: 'solid',
    layout: 'default',
    iconOptions: ALERT_ICONS.error,
    defaultIcon: 'XCircle',
    color: '#f87171',
    borderColor: '#ef4444',
    titleBold: true,
    defaultTitle: 'Erro!',
    defaultMessage: 'Algo deu errado.',
  },
  // Presets outline
  'success-outline': {
    id: 'success-outline',
    type: 'success',
    style: 'outline',
    layout: 'default',
    iconOptions: ALERT_ICONS.success,
    defaultIcon: 'CheckCircle',
    color: '#d1fae5',
    borderColor: '#10b981',
    titleBold: true,
    defaultTitle: 'Sucesso!',
    defaultMessage: 'Sua ação foi realizada com sucesso.',
  },
  'info-outline': {
    id: 'info-outline',
    type: 'info',
    style: 'outline',
    layout: 'default',
    iconOptions: ALERT_ICONS.info,
    defaultIcon: 'Info',
    color: '#dbeafe',
    borderColor: '#2563eb',
    titleBold: true,
    defaultTitle: 'Informação!',
    defaultMessage: 'Aqui está uma informação importante.',
  },
  'warning-outline': {
    id: 'warning-outline',
    type: 'warning',
    style: 'outline',
    layout: 'default',
    iconOptions: ALERT_ICONS.warning,
    defaultIcon: 'AlertTriangle',
    color: '#fef3c7',
    borderColor: '#f59e42',
    titleBold: true,
    defaultTitle: 'Atenção!',
    defaultMessage: 'Fique atento a este aviso.',
  },
  'error-outline': {
    id: 'error-outline',
    type: 'error',
    style: 'outline',
    layout: 'default',
    iconOptions: ALERT_ICONS.error,
    defaultIcon: 'XCircle',
    color: '#fee2e2',
    borderColor: '#ef4444',
    titleBold: true,
    defaultTitle: 'Erro!',
    defaultMessage: 'Algo deu errado.',
  },
  // Presets soft
  'success-soft': {
    id: 'success-soft',
    type: 'success',
    style: 'soft',
    layout: 'default',
    iconOptions: ALERT_ICONS.success,
    defaultIcon: 'CheckCircle',
    color: '#f0fdf4',
    borderColor: '#22c55e',
    titleBold: true,
    defaultTitle: 'SUCESSO!',
    defaultMessage: 'Insira aqui sua mensagem de sucesso!',
  },
  'warning-soft': {
    id: 'warning-soft',
    type: 'warning',
    style: 'soft',
    layout: 'default',
    iconOptions: ALERT_ICONS.warning,
    defaultIcon: 'AlertTriangle',
    color: '#fffbea',
    borderColor: '#fbbf24',
    titleBold: true,
    defaultTitle: 'ATENÇÃO!',
    defaultMessage: 'Insira aqui sua mensagem de aviso!',
  },
  'error-soft': {
    id: 'error-soft',
    type: 'error',
    style: 'soft',
    layout: 'default',
    iconOptions: ALERT_ICONS.error,
    defaultIcon: 'XCircle',
    color: '#fef2f2',
    borderColor: '#f87171',
    titleBold: true,
    defaultTitle: 'ERRO!',
    defaultMessage: 'Insira aqui sua mensagem de erro!',
  },
  'info-soft': {
    id: 'info-soft',
    type: 'info',
    style: 'soft',
    layout: 'default',
    iconOptions: ALERT_ICONS.info,
    defaultIcon: 'Info',
    color: '#f0f9ff',
    borderColor: '#38bdf8',
    titleBold: true,
    defaultTitle: 'INFORMAÇÃO!',
    defaultMessage: 'Insira aqui sua mensagem de informação!',
  },
  // Presets com layouts especiais - left-bar
  'warning-leftbar': {
    id: 'warning-leftbar',
    type: 'warning',
    style: 'solid',
    layout: 'left-bar',
    iconOptions: ALERT_ICONS.warning,
    defaultIcon: 'AlertTriangle',
    color: '#FEF3C7',
    borderColor: '#FACC15',
    titleBold: true,
    defaultTitle: 'Atenção',
    defaultMessage: 'Adicione sua mensagem de aviso aqui.',
  },
  'info-leftbar': {
    id: 'info-leftbar',
    type: 'info',
    style: 'solid',
    layout: 'left-bar',
    iconOptions: ALERT_ICONS.info,
    defaultIcon: 'Info',
    color: '#DBF0FF',
    borderColor: '#3B82F6',
    titleBold: true,
    defaultTitle: 'Informação',
    defaultMessage: 'Adicione sua mensagem de informação aqui.',
  },
  'success-leftbar': {
    id: 'success-leftbar',
    type: 'success',
    style: 'solid',
    layout: 'left-bar',
    iconOptions: ALERT_ICONS.success,
    defaultIcon: 'CheckCircle',
    color: '#D1FADF',
    borderColor: '#22C55E',
    titleBold: true,
    defaultTitle: 'Sucesso',
    defaultMessage: 'Adicione sua mensagem de sucesso aqui.',
  },
  'error-leftbar': {
    id: 'error-leftbar',
    type: 'error',
    style: 'solid',
    layout: 'left-bar',
    iconOptions: ALERT_ICONS.error,
    defaultIcon: 'XCircle',
    color: '#FEE2E2',
    borderColor: '#EF4444',
    titleBold: true,
    defaultTitle: 'Erro',
    defaultMessage: 'Adicione sua mensagem de erro aqui.',
  },
  // Presets com layouts especiais - bottom-bar
  'success-bottom-bar': {
    id: 'success-bottom-bar',
    type: 'success',
    style: 'soft',
    layout: 'bottom-bar',
    iconOptions: ALERT_ICONS.success,
    defaultIcon: 'CheckCircle',
    color: '#e0f2f1',
    borderColor: '#388e3c',
    titleBold: true,
    defaultTitle: 'SUCESSO!',
    defaultMessage: 'Mensagem de sucesso aqui.',
  },
  'info-bottom-bar': {
    id: 'info-bottom-bar',
    type: 'info',
    style: 'soft',
    layout: 'bottom-bar',
    iconOptions: ALERT_ICONS.info,
    defaultIcon: 'Info',
    color: '#e3f2fd',
    borderColor: '#1976d2',
    titleBold: true,
    defaultTitle: 'INFORMAÇÃO!',
    defaultMessage: 'Mensagem de informação aqui.',
  },
  'error-bottom-bar': {
    id: 'error-bottom-bar',
    type: 'error',
    style: 'soft',
    layout: 'bottom-bar',
    iconOptions: ALERT_ICONS.error,
    defaultIcon: 'XCircle',
    color: '#ffebee',
    borderColor: '#d32f2f',
    titleBold: true,
    defaultTitle: 'ERRO!',
    defaultMessage: 'Mensagem de erro aqui.',
  },
  'warning-bottom-bar': {
    id: 'warning-bottom-bar',
    type: 'warning',
    style: 'soft',
    layout: 'bottom-bar',
    iconOptions: ALERT_ICONS.warning,
    defaultIcon: 'AlertTriangle',
    color: '#ffefd5',
    borderColor: '#ffb300',
    titleBold: true,
    defaultTitle: 'ATENÇÃO!',
    defaultMessage: 'Mensagem de aviso aqui.',
  },
  // Presets com layouts especiais - icon-bubble
  'success-icon-bubble': {
    id: 'success-icon-bubble',
    type: 'success',
    style: 'outline',
    layout: 'icon-bubble',
    iconOptions: ALERT_ICONS.success,
    defaultIcon: 'CheckCircle',
    color: '#f0fdf4',
    borderColor: '#22c55e',
    titleBold: true,
    defaultTitle: 'SUCESSO!',
    defaultMessage: 'Alterações salvas com sucesso!',
  },
  'info-icon-bubble': {
    id: 'info-icon-bubble',
    type: 'info',
    style: 'outline',
    layout: 'icon-bubble',
    iconOptions: ALERT_ICONS.info,
    defaultIcon: 'Info',
    color: '#f0f9ff',
    borderColor: '#38bdf8',
    titleBold: true,
    defaultTitle: 'INFORMAÇÃO!',
    defaultMessage: 'Informação importante aqui!',
  },
  'error-icon-bubble': {
    id: 'error-icon-bubble',
    type: 'error',
    style: 'outline',
    layout: 'icon-bubble',
    iconOptions: ALERT_ICONS.error,
    defaultIcon: 'XCircle',
    color: '#fef2f2',
    borderColor: '#f87171',
    titleBold: true,
    defaultTitle: 'ERRO!',
    defaultMessage: 'Algo deu errado!',
  },
  'warning-icon-bubble': {
    id: 'warning-icon-bubble',
    type: 'warning',
    style: 'outline',
    layout: 'icon-bubble',
    iconOptions: ALERT_ICONS.warning,
    defaultIcon: 'AlertTriangle',
    color: '#fffbea',
    borderColor: '#fbbf24',
    titleBold: true,
    defaultTitle: 'ATENÇÃO!',
    defaultMessage: 'Fique atento a este aviso!',
  },
};

// Variantes de alerta disponíveis
export const alertBlockVariants: AlertVariant[] = Object.keys(alertBlockPresets) as AlertVariant[];

// Ícones de alerta por tipo
export const alertBlockIcons = ALERT_ICONS;

// Compatibilidade com estrutura anterior
export const ALERT_PRESETS: AlertPreset[] = Object.values(alertBlockPresets);

// Tipo de variante de alerta para compatibilidade
export type AlertBlockVariant = AlertVariant;
import React from 'react';
import clsx from 'clsx';

interface BlockCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onMouseEnter?: (e: React.MouseEvent<HTMLDivElement>) => void;
  onMouseLeave?: (e: React.MouseEvent<HTMLDivElement>) => void;
}

/**
 * BlockCard
 * Componente padrão para cards de blocos do editor e execução.
 * Aplica o padrão visual global do projeto.
 */
export const BlockCard: React.FC<BlockCardProps> = ({ children, className, style, onMouseEnter, onMouseLeave, ...props }) => {
  return (
    <div
      className={clsx(
        'min-h-[40px] w-full flex flex-col',
        className
      )}
      style={style}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      {...props}
    >
      {children}
    </div>
  );
};
/**
 * Presets específicos para blocos de arquivo
 */

// Definição local do tipo para evitar dependência circular
interface BlockTypePreset {
  icon: {
    name: string;
    position: string;
    type: string;
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  card: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  button?: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
}

export const fileBlockPresets: Record<string, BlockTypePreset> = {
  default: {
    icon: {
      name: 'FileText',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#f59e42', // orange-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fb923c', // orange-400
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
    button: {
      backgroundColor: '#f59e42', // orange-500
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fb923c', // orange-400
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
    card: {
      backgroundColor: '#fff7ed',
      color: '#f59e42',
      format: 'rounded',
      border: { enabled: true, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fbbf24',
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
  },
  pdf: {
    icon: {
      name: 'FileText',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#dc2626', // red-600
      color: '#fff',
      format: 'square',
      border: { enabled: true, color: '#dc2626', width: 1 },
      shadow: '0 2px 8px #dc262640',
      hover: {
        backgroundColor: '#ef4444', // red-500
        color: '#fff',
        shadow: '0 4px 16px #dc262640',
        borderColor: '#dc2626',
      },
    },
    button: {
      backgroundColor: '#dc2626',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#dc2626', width: 1 },
      shadow: '0 2px 8px #dc262640',
      hover: {
        backgroundColor: '#ef4444',
        color: '#fff',
        shadow: '0 4px 16px #dc262640',
        borderColor: '#dc2626',
      },
    },
    card: {
      backgroundColor: '#fef2f2',
      color: '#dc2626',
      format: 'rounded',
      border: { enabled: true, color: '#dc2626', width: 1 },
      shadow: '0 2px 8px #dc262640',
      hover: {
        backgroundColor: '#ef4444',
        color: '#fff',
        shadow: '0 4px 16px #dc262640',
        borderColor: '#dc2626',
      },
    },
  },
  image: {
    icon: {
      name: 'Image',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#10b981', // emerald-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#10b981', width: 1 },
      shadow: '0 2px 8px #10b98140',
      hover: {
        backgroundColor: '#34d399', // emerald-400
        color: '#fff',
        shadow: '0 4px 16px #10b98140',
        borderColor: '#10b981',
      },
    },
    button: {
      backgroundColor: '#10b981',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#10b981', width: 1 },
      shadow: '0 2px 8px #10b98140',
      hover: {
        backgroundColor: '#34d399',
        color: '#fff',
        shadow: '0 4px 16px #10b98140',
        borderColor: '#10b981',
      },
    },
    card: {
      backgroundColor: '#ecfdf5',
      color: '#10b981',
      format: 'rounded',
      border: { enabled: true, color: '#10b981', width: 1 },
      shadow: '0 2px 8px #10b98140',
      hover: {
        backgroundColor: '#34d399',
        color: '#fff',
        shadow: '0 4px 16px #10b98140',
        borderColor: '#10b981',
      },
    },
  },
  document: {
    icon: {
      name: 'FileText',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#3b82f6', // blue-500
      color: '#fff',
      format: 'square',
      border: { enabled: true, color: '#3b82f6', width: 1 },
      shadow: '0 2px 8px #3b82f640',
      hover: {
        backgroundColor: '#60a5fa', // blue-400
        color: '#fff',
        shadow: '0 4px 16px #3b82f640',
        borderColor: '#3b82f6',
      },
    },
    button: {
      backgroundColor: '#3b82f6',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#3b82f6', width: 1 },
      shadow: '0 2px 8px #3b82f640',
      hover: {
        backgroundColor: '#60a5fa',
        color: '#fff',
        shadow: '0 4px 16px #3b82f640',
        borderColor: '#3b82f6',
      },
    },
    card: {
      backgroundColor: '#eff6ff',
      color: '#3b82f6',
      format: 'rounded',
      border: { enabled: true, color: '#3b82f6', width: 1 },
      shadow: '0 2px 8px #3b82f640',
      hover: {
        backgroundColor: '#60a5fa',
        color: '#fff',
        shadow: '0 4px 16px #3b82f640',
        borderColor: '#3b82f6',
      },
    },
  },
};

export type FileBlockVariant = 'pdf' | 'image' | 'document' | 'spreadsheet' | 'presentation' | 'archive';

export const fileBlockVariants = {
  pdf: 'PDF',
  image: 'Imagem',
  document: 'Documento',
  spreadsheet: 'Planilha',
  presentation: 'Apresentação',
  archive: 'Arquivo',
};

export const fileBlockIcons = {
  pdf: 'FileText',
  image: 'Image',
  document: 'FileText',
  spreadsheet: 'Sheet',
  presentation: 'Presentation',
  archive: 'Archive',
};
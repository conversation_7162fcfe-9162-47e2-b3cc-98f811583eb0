import { getLucideIconComponent, isLucideIcon, getFallbackIcon } from '../utils/iconUtils';

describe('iconUtils', () => {
  it('getLucideIconComponent deve retornar null para ícone desconhecido', () => {
    expect(getLucideIconComponent('Inexistente')).toBeNull();
  });
  it('isLucideIcon deve retornar boolean', () => {
    expect(typeof isLucideIcon('FileText')).toBe('boolean');
  });
  it('getFallbackIcon deve retornar um componente React', () => {
    const Fallback = getFallbackIcon();
    expect(typeof Fallback).toBe('function');
  });
}); 
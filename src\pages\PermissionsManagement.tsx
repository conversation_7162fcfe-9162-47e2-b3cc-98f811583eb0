import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RequireGlobalRole } from '@/components/auth/PermissionWrappers';
import { AccessDeniedCard } from '@/components/auth/PermissionWrappers';
import { useGlobalPermissions } from '@/hooks/usePermissions';
import { PROJECT_ROLES, getRoleInfo } from '@/utils/roleUtils';
import { Header } from '@/components/layout/Header';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { ArrowLeft, Shield, Users, Settings, Eye, Edit, Trash, Plus, Check, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

// Matriz de permissões detalhada
const PERMISSION_MATRIX = {
  // Permissões de Projeto
  'Gestão de Projetos': {
    'create_project': { label: 'Criar projetos', icon: Plus },
    'view_project': { label: 'Ver projetos', icon: Eye },
    'edit_project': { label: 'Editar projetos', icon: Edit },
    'delete_project': { label: 'Excluir projetos', icon: Trash },
    'manage_project_members': { label: 'Gerenciar membros', icon: Users },
    'complete_project': { label: 'Concluir projetos', icon: Check }
  },
  // Permissões de Etapas
  'Gestão de Etapas': {
    'create_stage': { label: 'Criar etapas', icon: Plus },
    'view_stage': { label: 'Ver etapas', icon: Eye },
    'edit_stage': { label: 'Editar etapas', icon: Edit },
    'delete_stage': { label: 'Excluir etapas', icon: Trash },
    'manage_stage_members': { label: 'Gerenciar membros', icon: Users },
    'complete_stage': { label: 'Concluir etapas', icon: Check }
  },
  // Permissões de Tarefas
  'Gestão de Tarefas': {
    'create_task': { label: 'Criar tarefas', icon: Plus },
    'view_task': { label: 'Ver tarefas', icon: Eye },
    'edit_task': { label: 'Editar tarefas', icon: Edit },
    'delete_task': { label: 'Excluir tarefas', icon: Trash },
    'view_task_content': { label: 'Ver conteúdo', icon: Eye },
    'edit_task_content': { label: 'Editar conteúdo', icon: Edit },
    'complete_task': { label: 'Concluir tarefas', icon: Check }
  },
  // Permissões de Execução
  'Execução e Aprovação': {
    'execute_task': { label: 'Executar tarefas', icon: Settings },
    'approve_task': { label: 'Aprovar tarefas', icon: Check },
    'manage_task_executors': { label: 'Gerenciar executores', icon: Users },
    'manage_task_approvers': { label: 'Gerenciar aprovadores', icon: Users }
  }
};

// Matriz de permissões por role (importada da lógica)
const PROJECT_PERMISSIONS = {
  admin: [
    // Projetos
    'create_project', 'view_project', 'edit_project', 'delete_project', 'manage_project_members', 'complete_project',
    // Etapas
    'create_stage', 'view_stage', 'edit_stage', 'delete_stage', 'manage_stage_members', 'complete_stage',
    // Tarefas
    'create_task', 'view_task', 'edit_task', 'delete_task', 'view_task_content', 'edit_task_content',
    'execute_task', 'approve_task', 'manage_task_executors', 'manage_task_approvers', 'complete_task'
  ],
  manager: [
    // Projetos
    'view_project', 'edit_project', 'manage_project_members', 'complete_project',
    // Etapas
    'create_stage', 'view_stage', 'edit_stage', 'delete_stage', 'manage_stage_members', 'complete_stage',
    // Tarefas
    'create_task', 'view_task', 'edit_task', 'delete_task', 'view_task_content', 'edit_task_content',
    'execute_task', 'approve_task', 'manage_task_executors', 'manage_task_approvers', 'complete_task'
  ],
  editor: [
    // Projetos
    'view_project',
    // Etapas
    'create_stage', 'view_stage', 'edit_stage', 'manage_stage_members',
    // Tarefas
    'create_task', 'view_task', 'edit_task', 'view_task_content', 'edit_task_content'
  ],
  executor: [
    // Projetos
    'view_project',
    // Etapas
    'view_stage',
    // Tarefas
    'view_task', 'execute_task', 'view_task_content'
  ],
  approver: [
    // Projetos
    'view_project',
    // Etapas
    'view_stage',
    // Tarefas
    'view_task', 'approve_task', 'view_task_content'
  ],
  member: [
    // Projetos
    'view_project',
    // Etapas
    'view_stage',
    // Tarefas
    'view_task', 'view_task_content'
  ]
};

export const PermissionsManagement: React.FC = () => {
  const { isAdmin } = useGlobalPermissions();
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  // Se não é admin, mostrar acesso negado
  if (!isAdmin) {
    return (
      <>
        <Header showSidebarButton={true} />
        <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
        <div className={`max-w-none mx-auto p-6 space-y-6 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
          <AccessDeniedCard 
            title="Acesso Restrito"
            description="Esta área é exclusiva para administradores do sistema."
          />
        </div>
      </>
    );
  }

  const hasPermission = (role: string, permission: string): boolean => {
    return PROJECT_PERMISSIONS[role as keyof typeof PROJECT_PERMISSIONS]?.includes(permission) || false;
  };

  return (
    <>
      <Header showSidebarButton={true} />
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`max-w-none mx-auto p-6 space-y-6 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
        {/* Navegação */}
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
          <Link to="/settings" className="flex items-center gap-1 hover:text-primary">
            <ArrowLeft className="w-4 h-4" />
            Voltar às Configurações
          </Link>
          <span>/</span>
          <span className="text-primary font-medium">Gerenciar Permissões</span>
        </div>

        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Shield className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gerenciamento de Permissões</h1>
            <p className="text-gray-600">Visualize a matriz completa de permissões por role do sistema</p>
          </div>
        </div>

        {/* Resumo dos Roles */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Roles do Sistema
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {PROJECT_ROLES.map((role) => {
                const permissionCount = PROJECT_PERMISSIONS[role.value as keyof typeof PROJECT_PERMISSIONS]?.length || 0;
                return (
                  <div key={role.value} className="p-4 border rounded-lg hover:shadow-sm transition-shadow">
                    <div className="flex items-center justify-between mb-2">
                      <Badge className={role.color}>
                        {role.label}
                      </Badge>
                      <span className="text-sm text-gray-500">{permissionCount} permissões</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      {role.value === 'admin' && 'Acesso total e irrestrito ao sistema'}
                      {role.value === 'manager' && 'Gestão completa de projetos próprios'}
                      {role.value === 'editor' && 'Criação e edição de conteúdo estrutural'}
                      {role.value === 'executor' && 'Execução operacional de tarefas'}
                      {role.value === 'approver' && 'Validação e aprovação de entregas'}
                      {role.value === 'member' && 'Visualização limitada aos projetos'}
                    </p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Matriz de Permissões */}
        <div className="space-y-6">
          {Object.entries(PERMISSION_MATRIX).map(([category, permissions]) => (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="text-lg">{category}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium text-gray-900 min-w-[200px]">Permissão</th>
                        {PROJECT_ROLES.map((role) => (
                          <th key={role.value} className="text-center p-3 min-w-[100px]">
                            <Badge className={`${role.color} text-xs`}>
                              {role.label}
                            </Badge>
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {Object.entries(permissions).map(([permissionKey, permissionData]) => {
                        const IconComponent = permissionData.icon;
                        return (
                          <tr key={permissionKey} className="border-b hover:bg-gray-50">
                            <td className="p-3">
                              <div className="flex items-center gap-2">
                                <IconComponent className="w-4 h-4 text-gray-500" />
                                <span className="font-medium">{permissionData.label}</span>
                              </div>
                            </td>
                            {PROJECT_ROLES.map((role) => (
                              <td key={role.value} className="p-3 text-center">
                                {hasPermission(role.value, permissionKey) ? (
                                  <Check className="w-5 h-5 text-green-600 mx-auto" />
                                ) : (
                                  <X className="w-5 h-5 text-gray-300 mx-auto" />
                                )}
                              </td>
                            ))}
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Legenda */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Legenda</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Check className="w-5 h-5 text-green-600" />
                <span>Permissão concedida</span>
              </div>
              <div className="flex items-center gap-2">
                <X className="w-5 h-5 text-gray-300" />
                <span>Permissão negada</span>
              </div>
            </div>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Nota:</strong> As permissões mostradas aqui refletem o sistema centralizado implementado. 
                Alguns roles podem ter permissões adicionais baseadas em contexto (ex: responsável pela tarefa pode editá-la).
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

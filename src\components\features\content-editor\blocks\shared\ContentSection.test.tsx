import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ContentSection } from './ContentSection';
import { ContentBlock } from '@/types';

const mockBlocks: ContentBlock[] = [
  { id: '1', type: 'text', content: { text: 'Teste' }, order: 0 }
];

describe('ContentSection', () => {
  it('renderiza título e descrição', () => {
    render(<ContentSection blocks={mockBlocks} onSave={jest.fn()} title="Título" description="Descrição" />);
    expect(screen.getByText('Título')).toBeInTheDocument();
    expect(screen.getByText('Descrição')).toBeInTheDocument();
  });

  it('exibe botão de salvar apenas se editable=true', () => {
    const { rerender } = render(<ContentSection blocks={mockBlocks} onSave={jest.fn()} editable={false} />);
    expect(screen.queryByText('Salvar')).not.toBeInTheDocument();
    rerender(<ContentSection blocks={mockBlocks} onSave={jest.fn()} editable={true} />);
    expect(screen.getByText('Salvar')).toBeInTheDocument();
  });

  it('chama onSave ao clicar em salvar', async () => {
    const onSave = jest.fn();
    render(<ContentSection blocks={mockBlocks} onSave={onSave} editable={true} />);
    fireEvent.click(screen.getByText('Salvar'));
    await waitFor(() => expect(onSave).toHaveBeenCalled());
  });
});
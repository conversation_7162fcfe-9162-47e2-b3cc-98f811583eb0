import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Função utilitária para checar se é JSON Lexical
export function isLexicalJson(value: string) {
  try {
    console.log('[isLexicalJson] checking value:', value?.substring(0, 100) + '...');
    const parsed = JSON.parse(value);
    console.log('[isLexicalJson] parsed successfully:', !!parsed);
    const hasRoot = parsed && typeof parsed === 'object' && parsed.root && typeof parsed.root === 'object';
    const hasChildren = hasRoot && Array.isArray(parsed.root.children);
    const isValid = hasRoot && hasChildren;
    console.log('[isLexicalJson] validation result:', { hasRoot, hasChildren, isValid });
    return isValid;
  } catch (error) {
    console.log('[isLexicalJson] parse error:', error.message);
    return false;
  }
}

export const EMPTY_LEXICAL_STATE = JSON.stringify({
  root: {
    children: [
      {
        children: [],
        direction: "ltr",
        format: "",
        indent: 0,
        type: "paragraph",
        version: 1
      }
    ],
    direction: "ltr",
    format: "",
    indent: 0,
    type: "root",
    version: 1
  }
});

// Função para converter texto simples em JSON Lexical
export function convertTextToLexicalJson(text: string): string {
  console.log('[convertTextToLexicalJson] Converting text:', text?.substring(0, 100) + '...');
  
  if (!text || text.trim() === '') {
    console.log('[convertTextToLexicalJson] Empty text, returning EMPTY_LEXICAL_STATE');
    return EMPTY_LEXICAL_STATE;
  }
  
  // Dividir o texto em parágrafos se houver quebras de linha
  const paragraphs = text.split('\n').filter(p => p.trim() !== '');
  
  const children = paragraphs.length > 0 ? paragraphs.map(paragraph => ({
    children: [
      {
        detail: 0,
        format: 0,
        mode: "normal",
        style: "",
        text: paragraph,
        type: "text",
        version: 1
      }
    ],
    direction: "ltr",
    format: "",
    indent: 0,
    type: "paragraph",
    version: 1
  })) : [{
    children: [],
    direction: "ltr",
    format: "",
    indent: 0,
    type: "paragraph",
    version: 1
  }];
  
  const result = JSON.stringify({
    root: {
      children,
      direction: "ltr",
      format: "",
      indent: 0,
      type: "root",
      version: 1
    }
  });
  
  console.log('[convertTextToLexicalJson] Result:', result.substring(0, 100) + '...');
  return result;
}

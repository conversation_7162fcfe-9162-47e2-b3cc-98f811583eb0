import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from '@/hooks/ui/use-toast';

interface AuthContextType {
  user: any;
  profile: any;
  session: any;
  loading: boolean;
  error: string | null;
  profileExists: boolean;
  requiresProfileSetup: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  setProfile?: (profile: any) => void;
  checkAuthStatus: () => Promise<boolean>;
  createUserProfile: (userData: any) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileExists, setProfileExists] = useState(false);
  const [requiresProfileSetup, setRequiresProfileSetup] = useState(false);

  useEffect(() => {
    const getSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Erro ao obter sessão:', error);
          setError(error.message);
          setSession(null);
          setUser(null);
        } else {
          setSession(data.session);
          setUser(data.session?.user ?? null);
        }
      } catch (err) {
        console.error('Erro inesperado ao obter sessão:', err);
        setError('Erro ao verificar autenticação');
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getSession();

    const { data: listener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);

      setSession(session);
      setUser(session?.user ?? null);

      // Limpar erro quando usuário faz login com sucesso
      if (session?.user) {
        setError(null);
      }

      // Se o usuário foi deslogado, limpar todos os dados
      if (event === 'SIGNED_OUT' || !session) {
        setProfile(null);
        setProfileExists(false);
        setRequiresProfileSetup(false);
        setError(null);
      }
    });

    return () => {
      listener.subscription.unsubscribe();
    };
  }, []);

  // **CONTROLE DE ACESSO OBRIGATÓRIO**: Verificar profile sempre que user mudar
  useEffect(() => {
    const validateUserProfile = async () => {
      if (user && user.id) {
        try {
          console.log('🔐 Validando acesso para usuário:', user.id);
          
          // Buscar profile do usuário com validação rigorosa
          const { data: profileData, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single(); // Usar .single() para garantir validação rigorosa

          if (error) {
            console.error('❌ Erro ao buscar profile:', error);
            
            // Se o erro é 406 (PGRST116), significa que o usuário não tem profile
            if (error.code === 'PGRST116') {
              console.warn('⚠️ Usuário autenticado mas sem profile registrado');
              setProfileExists(false);
              setRequiresProfileSetup(true);
              setProfile(null);
              
              // Mostrar toast informativo
              toast({
                title: 'Acesso requer aprovação',
                description: 'Seu perfil precisa ser criado. Entre em contato com o administrador.',
                variant: 'destructive',
                duration: 5000,
              });
              
              // Fazer logout automático após 5 segundos
              setTimeout(() => {
                logout();
              }, 5000);
              
              return;
            }
            
            // Outros erros também bloqueiam acesso
            setProfileExists(false);
            setRequiresProfileSetup(true);
            setProfile(null);
            return;
          }

          // **SUCESSO**: Profile encontrado e válido
          if (profileData) {
            console.log('✅ Profile válido encontrado:', profileData.email);
            setProfile(profileData);
            setProfileExists(true);
            setRequiresProfileSetup(false);
            
            // Verificar se o perfil está ativo
            if (profileData.is_active === false) {
              console.warn('❌ Profile inativo, bloqueando acesso');
              toast({
                title: 'Acesso bloqueado',
                description: 'Seu acesso foi desativado. Entre em contato com o administrador.',
                variant: 'destructive',
              });
              logout();
              return;
            }
          } else {
            // Profile nulo/undefined - bloquear acesso
            console.warn('❌ Profile nulo retornado');
            setProfileExists(false);
            setRequiresProfileSetup(true);
            setProfile(null);
          }
          
        } catch (error) {
          console.error('❌ Erro inesperado na validação do profile:', error);
          setProfileExists(false);
          setRequiresProfileSetup(true);
          setProfile(null);
        }
      } else {
        // Sem usuário autenticado
        setProfile(null);
        setProfileExists(false);
        setRequiresProfileSetup(false);
      }
    };

    validateUserProfile();
  }, [user]);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) {
      setError(error.message);
      toast({
        title: 'Erro no login',
        description: error.message,
        variant: 'destructive',
      });
    }
    setLoading(false);
  };

  const register = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    
    const { data, error } = await supabase.auth.signUp({ 
      email, 
      password,
      options: {
        data: {
          email: email,
          // Profile será criado automaticamente pelo trigger
        }
      }
    });
    
    if (error) {
      setError(error.message);
      toast({
        title: 'Erro no registro',
        description: error.message,
        variant: 'destructive',
      });
    } else {
      toast({
        title: 'Registro realizado',
        description: 'Verifique seu email para confirmar a conta.',
        variant: 'default',
      });
    }
    setLoading(false);
  };

  const logout = async () => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signOut();
    setUser(null);
    setSession(null);
    setProfile(null);
    setProfileExists(false);
    setRequiresProfileSetup(false);
    if (error) setError(error.message);
    setLoading(false);
  };

  // **NOVA FUNÇÃO**: Criar profile para usuário (uso admin)
  const createUserProfile = async (userData: { name: string; email: string; role: string }) => {
    if (!user?.id) return false;
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .insert([{
          id: user.id,
          name: userData.name,
          email: userData.email,
          role: userData.role,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar profile:', error);
        return false;
      }

      setProfile(data);
      setProfileExists(true);
      setRequiresProfileSetup(false);
      
      toast({
        title: 'Profile criado',
        description: 'Seu perfil foi criado com sucesso!',
        variant: 'default',
      });
      
      return true;
    } catch (error) {
      console.error('Erro inesperado ao criar profile:', error);
      return false;
    }
  };

  // Verificar se o usuário está realmente autenticado E tem profile válido
  const checkAuthStatus = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Erro ao verificar autenticação:', error);
        return false;
      }
      
      // Não basta ter sessão, precisa ter profile válido também
      return !!session?.user && profileExists;
    } catch (error) {
      console.error('Erro ao verificar status de autenticação:', error);
      return false;
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      profile, 
      session, 
      loading, 
      error, 
      profileExists,
      requiresProfileSetup,
      login, 
      register, 
      logout, 
      setProfile, 
      checkAuthStatus,
      createUserProfile
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error("useAuthContext deve ser usado dentro de AuthProvider");
  return context;
};

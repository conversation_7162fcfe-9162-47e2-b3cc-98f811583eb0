-- CORREÇÃO EMERGENCIAL - EXECUTE IMEDIATAMENTE
-- <PERSON><PERSON> script remove completamente todas as políticas RLS problemáticas

-- 1. DESA<PERSON><PERSON><PERSON><PERSON> RLS EM TODAS AS TABELAS PROBLEMÁTICAS
ALTER TABLE public.tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.evidence DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_content_blocks DISABLE ROW LEVEL SECURITY;

-- 2. REMOVER TODAS AS POLÍTICAS EXISTENTES (força bruta)
DO $$
DECLARE
    pol_name text;
BEGIN
    -- Remover todas as políticas da tabela tasks
    FOR pol_name IN 
        SELECT policyname FROM pg_policies 
        WHERE tablename = 'tasks' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || pol_name || '" ON public.tasks';
    END LOOP;
    
    -- Remover todas as políticas da tabela evidence
    FOR pol_name IN 
        SELECT policyname FROM pg_policies 
        WHERE tablename = 'evidence' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || pol_name || '" ON public.evidence';
    END LOOP;
    
    -- Remover todas as políticas da tabela task_content_blocks
    FOR pol_name IN 
        SELECT policyname FROM pg_policies 
        WHERE tablename = 'task_content_blocks' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || pol_name || '" ON public.task_content_blocks';
    END LOOP;
END $$;

-- 3. CRIAR USUÁRIO AUSENTE NA TABELA PROFILES
INSERT INTO public.profiles (id, email, name, role, is_active, created_at, updated_at)
VALUES (
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    '<EMAIL>',
    'Usuário Teste',
    'member',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- 4. VERIFICAÇÃO FINAL
SELECT 
    'RLS STATUS' as check_type,
    schemaname, 
    tablename, 
    rowsecurity,
    CASE 
        WHEN rowsecurity = true THEN '❌ AINDA ATIVO'
        ELSE '✅ DESABILITADO'
    END as status
FROM pg_tables 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
  AND schemaname = 'public'
ORDER BY tablename;

-- 5. VERIFICAR SE AINDA EXISTEM POLÍTICAS
SELECT 
    'POLICIES REMAINING' as check_type,
    tablename,
    policyname,
    '❌ PRECISA SER REMOVIDA' as action
FROM pg_policies 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
  AND schemaname = 'public'
ORDER BY tablename, policyname;

-- 6. VERIFICAR SE USUÁRIO FOI CRIADO
SELECT 
    'USER CHECK' as check_type,
    id,
    email,
    name,
    '✅ USUÁRIO EXISTE' as status
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- SE TUDO ESTIVER OK, DEVE MOSTRAR:
-- ✅ Todas as 3 tabelas com rowsecurity = false
-- ✅ Nenhuma política listada
-- ✅ Usuário criado com sucesso

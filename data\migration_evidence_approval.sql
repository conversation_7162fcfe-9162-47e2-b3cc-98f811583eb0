-- =====================
-- MIGRAÇÃO: SISTEMA DE APROVAÇÃO DE EVIDÊNCIAS
-- Data: 2024-12-19
-- Descrição: Adiciona campos e políticas para sistema de aprovação
-- =====================

-- PASSO 1: Corrigir função is_admin
-- =====================
drop function if exists public.is_admin(uuid);

create or replace function public.is_admin(uid uuid)
returns boolean
language sql
security definer
stable
as $$
  select exists (
    select 1 from public.profiles 
    where id = uid and role = 'admin'
  );
$$;

-- PASSO 2: Adicionar campos de aprovação na tabela task_attachments
-- =====================
do $$
begin
    -- Adicionar coluna status se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'status') then
        alter table task_attachments add column status varchar(20) default 'pending' check (status in ('pending', 'approved', 'rejected'));
    end if;
    
    -- Adicionar coluna approved_by se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'approved_by') then
        alter table task_attachments add column approved_by uuid references public.profiles(id);
    end if;
    
    -- Adicionar coluna approved_at se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'approved_at') then
        alter table task_attachments add column approved_at timestamp with time zone;
    end if;
    
    -- Adicionar coluna rejection_reason se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'rejection_reason') then
        alter table task_attachments add column rejection_reason text;
    end if;
    
    -- Adicionar coluna block_id se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'block_id') then
        alter table task_attachments add column block_id varchar(255);
    end if;
    
    -- Adicionar coluna file_size se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'file_size') then
        alter table task_attachments add column file_size bigint;
    end if;
end $$;

-- PASSO 3: Criar índices para performance
-- =====================
create index if not exists idx_task_attachments_status on task_attachments(status);
create index if not exists idx_task_attachments_approved_by on task_attachments(approved_by);
create index if not exists idx_task_attachments_block_id on task_attachments(block_id);
create index if not exists idx_task_attachments_task_block on task_attachments(task_id, block_id);

-- PASSO 4: Atualizar evidências existentes para status 'pending'
-- =====================
update task_attachments 
set status = 'pending' 
where status is null;

-- PASSO 5: Criar função de validação
-- =====================
create or replace function validate_evidence_rejection()
returns trigger as $$
begin
  -- Se status é 'rejected', rejection_reason deve estar preenchido
  if new.status = 'rejected' and (new.rejection_reason is null or trim(new.rejection_reason) = '') then
    raise exception 'Motivo da rejeição é obrigatório quando status é rejected';
  end if;
  
  -- Se status não é 'rejected', limpar rejection_reason
  if new.status != 'rejected' then
    new.rejection_reason = null;
  end if;
  
  -- Se status é 'approved' ou 'rejected', definir approved_at se não estiver definido
  if new.status in ('approved', 'rejected') and new.approved_at is null then
    new.approved_at = now();
  end if;
  
  return new;
end;
$$ language plpgsql;

-- PASSO 6: Criar trigger de validação
-- =====================
drop trigger if exists trigger_validate_evidence_rejection on task_attachments;
create trigger trigger_validate_evidence_rejection
  before insert or update on task_attachments
  for each row
  execute function validate_evidence_rejection();

-- PASSO 7: Corrigir políticas de profiles (resolver erro 403)
-- =====================
-- Política para permitir colaboração entre usuários
drop policy if exists "Allow users to view profiles for collaboration" on public.profiles;
create policy "Allow users to view profiles for collaboration"
  on public.profiles
  for select
  using (
    -- Admin pode ver todos
    public.is_admin(auth.uid()) or
    -- Usuário pode ver próprio perfil
    id = auth.uid() or
    -- Usuários podem ver outros usuários se estão no mesmo projeto
    exists (
      select 1 from public.project_members pm1
      join public.project_members pm2 on pm1.project_id = pm2.project_id
      where pm1.user_id = auth.uid() and pm2.user_id = profiles.id
    ) or
    -- Usuários podem ver outros usuários se são donos de projetos onde o outro é membro
    exists (
      select 1 from public.projects p
      join public.project_members pm on p.id = pm.project_id
      where p.owner_id = auth.uid() and pm.user_id = profiles.id
    ) or
    -- Usuários podem ver donos de projetos onde são membros
    exists (
      select 1 from public.projects p
      join public.project_members pm on p.id = pm.project_id
      where pm.user_id = auth.uid() and p.owner_id = profiles.id
    )
  );

-- Políticas de inserção para profiles
drop policy if exists "Users can insert their own profile" on public.profiles;
create policy "Users can insert their own profile"
  on public.profiles
  for insert
  with check (id = auth.uid());

drop policy if exists "Admin can insert any profile" on public.profiles;
create policy "Admin can insert any profile"
  on public.profiles
  for insert
  with check (public.is_admin(auth.uid()));

-- PASSO 8: Atualizar políticas de task_attachments
-- =====================
-- Política para visualização: executores e aprovadores podem ver evidências da tarefa
drop policy if exists "Users can view attachments of their projects" on public.task_attachments;
drop policy if exists "Project members can view task attachments" on public.task_attachments;
drop policy if exists "Users can view task evidence" on public.task_attachments;
create policy "Users can view task evidence"
  on public.task_attachments
  for select
  using (
    exists(
      select 1 from task_executors te where te.task_id = task_attachments.task_id and te.user_id = auth.uid()
    ) or
    exists(
      select 1 from task_approvers ta where ta.task_id = task_attachments.task_id and ta.user_id = auth.uid()
    ) or
    -- Fallback: membros do projeto podem ver
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (select project_id from public.project_members where user_id = auth.uid())
    )
  );

-- Política para inserção: apenas executores podem fazer upload
drop policy if exists "Project members can insert task attachments" on public.task_attachments;
drop policy if exists "Executors can upload evidence" on public.task_attachments;
create policy "Executors can upload evidence"
  on public.task_attachments
  for insert
  with check (
    exists(
      select 1 from task_executors te where te.task_id = task_attachments.task_id and te.user_id = auth.uid()
    ) and
    uploaded_by = auth.uid()
  );

-- Política para atualização: aprovadores podem aprovar/rejeitar
drop policy if exists "Evidence approval and deletion" on public.task_attachments;
create policy "Evidence approval and deletion"
  on public.task_attachments
  for update
  using (
    -- Aprovadores podem aprovar/rejeitar evidências pendentes
    (exists(
      select 1 from task_approvers ta where ta.task_id = task_attachments.task_id and ta.user_id = auth.uid()
    ) and status = 'pending') or
    -- Executores podem atualizar suas próprias evidências não aprovadas
    (uploaded_by = auth.uid() and status != 'approved')
  );

-- Política para exclusão: apenas executores podem excluir suas próprias evidências não aprovadas
drop policy if exists "Users can delete their own task attachments" on public.task_attachments;
drop policy if exists "Executors can delete own non-approved evidence" on public.task_attachments;
create policy "Executors can delete own non-approved evidence"
  on public.task_attachments
  for delete
  using (
    uploaded_by = auth.uid() and 
    status != 'approved' and
    exists(
      select 1 from task_executors te where te.task_id = task_attachments.task_id and te.user_id = auth.uid()
    )
  );

-- PASSO 9: Adicionar comentários para documentação
-- =====================
comment on column task_attachments.status is 'Status da evidência: pending (pendente), approved (aprovada), rejected (rejeitada)';
comment on column task_attachments.approved_by is 'ID do usuário que aprovou/rejeitou a evidência';
comment on column task_attachments.approved_at is 'Data e hora da aprovação/rejeição';
comment on column task_attachments.rejection_reason is 'Motivo da rejeição (obrigatório quando status = rejected)';
comment on column task_attachments.block_id is 'ID do bloco de evidência associado';
comment on column task_attachments.file_size is 'Tamanho do arquivo em bytes';

-- PASSO 10: Verificar se tudo foi criado corretamente
-- =====================
select 'MIGRAÇÃO CONCLUÍDA - Verificando estrutura:' as status;

-- Verificar colunas adicionadas
select 
    column_name,
    data_type,
    is_nullable,
    column_default
from information_schema.columns 
where table_name = 'task_attachments' 
    and column_name in ('status', 'approved_by', 'approved_at', 'rejection_reason', 'block_id', 'file_size')
order by column_name;

-- Verificar políticas criadas
select 
    policyname,
    cmd,
    permissive
from pg_policies 
where tablename in ('profiles', 'task_attachments')
    and policyname like '%collaboration%' 
    or policyname like '%evidence%'
order by tablename, policyname;

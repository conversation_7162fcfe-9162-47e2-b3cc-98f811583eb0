-- Migração para adicionar campos de aprovação de evidências
-- Data: 2024-12-19
-- Descrição: Adiciona campos para sistema de aprovação de evidências na tabela task_attachments

-- 1. Adicionar campos de aprovação na tabela task_attachments
ALTER TABLE task_attachments 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES profiles(id),
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS block_id VARCHAR(255);

-- 2. <PERSON><PERSON><PERSON> índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_task_attachments_status ON task_attachments(status);
CREATE INDEX IF NOT EXISTS idx_task_attachments_approved_by ON task_attachments(approved_by);
CREATE INDEX IF NOT EXISTS idx_task_attachments_block_id ON task_attachments(block_id);
CREATE INDEX IF NOT EXISTS idx_task_attachments_task_block ON task_attachments(task_id, block_id);

-- 3. Atualizar evidências existentes para status 'pending'
UPDATE task_attachments 
SET status = 'pending' 
WHERE status IS NULL;

-- 4. Comentários para documentação
COMMENT ON COLUMN task_attachments.status IS 'Status da evidência: pending (pendente), approved (aprovada), rejected (rejeitada)';
COMMENT ON COLUMN task_attachments.approved_by IS 'ID do usuário que aprovou/rejeitou a evidência';
COMMENT ON COLUMN task_attachments.approved_at IS 'Data e hora da aprovação/rejeição';
COMMENT ON COLUMN task_attachments.rejection_reason IS 'Motivo da rejeição (obrigatório quando status = rejected)';
COMMENT ON COLUMN task_attachments.block_id IS 'ID do bloco de evidência associado';

-- 5. Criar função para validar rejeição com motivo
CREATE OR REPLACE FUNCTION validate_evidence_rejection()
RETURNS TRIGGER AS $$
BEGIN
  -- Se status é 'rejected', rejection_reason deve estar preenchido
  IF NEW.status = 'rejected' AND (NEW.rejection_reason IS NULL OR trim(NEW.rejection_reason) = '') THEN
    RAISE EXCEPTION 'Motivo da rejeição é obrigatório quando status é rejected';
  END IF;
  
  -- Se status não é 'rejected', limpar rejection_reason
  IF NEW.status != 'rejected' THEN
    NEW.rejection_reason = NULL;
  END IF;
  
  -- Se status é 'approved' ou 'rejected', definir approved_at se não estiver definido
  IF NEW.status IN ('approved', 'rejected') AND NEW.approved_at IS NULL THEN
    NEW.approved_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Criar trigger para validação
DROP TRIGGER IF EXISTS trigger_validate_evidence_rejection ON task_attachments;
CREATE TRIGGER trigger_validate_evidence_rejection
  BEFORE INSERT OR UPDATE ON task_attachments
  FOR EACH ROW
  EXECUTE FUNCTION validate_evidence_rejection();

-- 7. Criar view para facilitar consultas de evidências com aprovação
CREATE OR REPLACE VIEW evidence_with_approval AS
SELECT 
  ta.id,
  ta.task_id,
  ta.block_id,
  ta.file_url as content,
  ta.description as file_name,
  ta.created_at as uploaded_at,
  ta.status,
  ta.approved_at,
  ta.rejection_reason,
  -- Dados do usuário que fez upload
  up_profile.id as uploaded_by_id,
  up_profile.name as uploaded_by_name,
  up_profile.email as uploaded_by_email,
  -- Dados do usuário que aprovou/rejeitou
  ap_profile.id as approved_by_id,
  ap_profile.name as approved_by_name,
  ap_profile.email as approved_by_email,
  -- Determinar tipo baseado na URL/extensão
  CASE 
    WHEN ta.file_url LIKE '%.jpg' OR ta.file_url LIKE '%.jpeg' OR ta.file_url LIKE '%.png' OR ta.file_url LIKE '%.gif' OR ta.file_url LIKE '%.webp' THEN 'image'
    ELSE 'file'
  END as type
FROM task_attachments ta
LEFT JOIN profiles up_profile ON ta.uploaded_by = up_profile.id
LEFT JOIN profiles ap_profile ON ta.approved_by = ap_profile.id;

-- 8. Criar função para verificar permissões de aprovação
CREATE OR REPLACE FUNCTION can_approve_evidence(
  p_task_id UUID,
  p_user_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  is_approver BOOLEAN := FALSE;
BEGIN
  -- Verificar se o usuário é aprovador da tarefa
  SELECT EXISTS(
    SELECT 1 
    FROM task_approvers 
    WHERE task_id = p_task_id AND user_id = p_user_id
  ) INTO is_approver;
  
  RETURN is_approver;
END;
$$ LANGUAGE plpgsql;

-- 9. Criar função para verificar permissões de upload
CREATE OR REPLACE FUNCTION can_upload_evidence(
  p_task_id UUID,
  p_user_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  is_executor BOOLEAN := FALSE;
BEGIN
  -- Verificar se o usuário é executor da tarefa
  SELECT EXISTS(
    SELECT 1 
    FROM task_executors 
    WHERE task_id = p_task_id AND user_id = p_user_id
  ) INTO is_executor;
  
  RETURN is_executor;
END;
$$ LANGUAGE plpgsql;

-- 10. Criar política RLS para evidências (se não existir)
-- Nota: Assumindo que RLS já está habilitado na tabela task_attachments

-- Política para visualização: executores e aprovadores podem ver evidências da tarefa
DROP POLICY IF EXISTS "Users can view task evidence" ON task_attachments;
CREATE POLICY "Users can view task evidence" ON task_attachments
  FOR SELECT
  USING (
    EXISTS(
      SELECT 1 FROM task_executors te WHERE te.task_id = task_attachments.task_id AND te.user_id = auth.uid()
    ) OR
    EXISTS(
      SELECT 1 FROM task_approvers ta WHERE ta.task_id = task_attachments.task_id AND ta.user_id = auth.uid()
    )
  );

-- Política para inserção: apenas executores podem fazer upload
DROP POLICY IF EXISTS "Executors can upload evidence" ON task_attachments;
CREATE POLICY "Executors can upload evidence" ON task_attachments
  FOR INSERT
  WITH CHECK (
    EXISTS(
      SELECT 1 FROM task_executors te WHERE te.task_id = task_attachments.task_id AND te.user_id = auth.uid()
    ) AND
    uploaded_by = auth.uid()
  );

-- Política para atualização: aprovadores podem aprovar/rejeitar, executores podem excluir suas próprias evidências não aprovadas
DROP POLICY IF EXISTS "Evidence approval and deletion" ON task_attachments;
CREATE POLICY "Evidence approval and deletion" ON task_attachments
  FOR UPDATE
  USING (
    -- Aprovadores podem aprovar/rejeitar evidências pendentes
    (EXISTS(
      SELECT 1 FROM task_approvers ta WHERE ta.task_id = task_attachments.task_id AND ta.user_id = auth.uid()
    ) AND status = 'pending') OR
    -- Executores podem atualizar suas próprias evidências não aprovadas
    (uploaded_by = auth.uid() AND status != 'approved')
  );

-- Política para exclusão: apenas executores podem excluir suas próprias evidências não aprovadas
DROP POLICY IF EXISTS "Executors can delete own non-approved evidence" ON task_attachments;
CREATE POLICY "Executors can delete own non-approved evidence" ON task_attachments
  FOR DELETE
  USING (
    uploaded_by = auth.uid() AND 
    status != 'approved' AND
    EXISTS(
      SELECT 1 FROM task_executors te WHERE te.task_id = task_attachments.task_id AND te.user_id = auth.uid()
    )
  );

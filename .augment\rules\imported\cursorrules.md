---
type: "manual"
---

# .cursorrules — Regras Centrais do Projeto

> **Atenção:** Sempre referencie este arquivo e `.project-rules/best-practices-cursor-ai.md` ao utilizar o Cursor.ai para geração, refatoração ou revisão de código. Oriente novos membros a ler ambos os arquivos no onboarding.

---

## 1. Arquitetura & Organização
- O projeto deve ser modular, com separação clara entre UI, lógica de negócio (services), estado global (store) e tipos.
- Cada arquivo não deve ultrapassar 300 linhas; divida arquivos grandes em módulos menores.
- Funções longas devem ser divididas em funções menores e reutilizáveis.
- Use hooks customizados para lógica compartilhada (ex: use-toast, useIsMobile).
- Evite duplicação de lógica entre services (project, stage, task, user).
- Estrutura de pastas deve seguir o padrão definido em `.project-rules/estrutura-src.md`.

## 2. UI/UX & Design System
- Utilize apenas componentes do design system (shadcn/ui) e siga os padrões definidos em `.project-rules/ux-ui.md`.
- Todos os formulários devem usar react-hook-form e validação com zod.
- Garanta responsividade mobile-first e acessibilidade (contraste, navegação por teclado, textos alternativos).
- Skeletons, spinners e estados vazios devem ser implementados em todas as telas.
- Blocos do editor devem seguir as diretrizes de `.project-rules/padronizacao-blocos-editor.md`.

## 3. RBAC & Segurança
- Implemente RBAC conforme `.project-rules/rbac.md` e enums do Supabase.
- Apenas usuários com papel 'manager' ou 'admin' podem aprovar tarefas.
- O botão de aprovação deve ser ocultado para outros papéis.
- Ações críticas (aprovação, exclusão) devem ser auditadas e registradas em project_history.
- A UI deve adaptar visibilidade de ações conforme o papel do usuário (ver `.project-rules/rbac-por-papeis.md`).

## 4. Testes & Qualidade
- Todo service, hook e componente crítico deve ter teste unitário (Vitest + Testing Library).
- Testes de integração devem cobrir fluxos de permissão, navegação e editor de blocos.
- Use mocks adequados para Supabase (test-utils/supabaseMock.ts).
- O pipeline de CI deve rodar lint, build e testes em todos os PRs.

## 5. Automação & DevOps
- Use mcp tools para auditorias, logs, deploys e troubleshooting sempre que possível.
- Auditorias de segurança e performance devem ser executadas após alterações em schema ou policies.
- Migrations e deploys de edge functions devem ser feitos via automação (não manualmente em produção).

## 6. Uso do Cursor.ai
- Sempre forneça contexto amplo ao pedir geração/refatoração de código (explique objetivo, selecione múltiplos arquivos, detalhe restrições).
- Prompts devem ser claros, objetivos e, quando possível, incluir exemplos de entrada/saída.
- Use memories para registrar decisões técnicas, padrões de commit, fluxos de aprovação e aprendizados.
- Atualize este arquivo sempre que houver mudanças relevantes em arquitetura, padrões ou regras de negócio.
- Consulte e mantenha atualizado o guia `.project-rules/best-practices-cursor-ai.md`.

## 7. Documentação & Onboarding
- Toda decisão arquitetural relevante deve ser documentada em `.project-rules/arquitetura.md`.
- Novos membros devem ser orientados a ler este arquivo, o guia de melhores práticas do Cursor.ai e os documentos de arquitetura e RBAC.

## 8. Git: Gerenciamento de Branches e Commits
- Todo trabalho deve ser realizado em branches específicas, nomeadas conforme o escopo da tarefa (ex: feat/editor-modernizacao, fix/rbac-policy, refactor/ui-layout, chore/deps-update).
- Antes de iniciar refatorações, mudanças de layout, regras de negócio ou alterações críticas, crie uma branch dedicada e faça commit inicial.
- Commits devem seguir o padrão: `[Data/hora] - [Título]`, onde a data/hora deve ser obtida do PowerShell para garantir precisão e rastreabilidade.
  - Exemplo: `2024-06-10 14:32 - Refatoração do editor de blocos`
- Sempre faça commit antes de grandes refatorações ou mudanças estruturais, garantindo ponto de restauração seguro.
- Após concluir uma etapa crítica, faça commit imediatamente antes de prosseguir para a próxima.
- Nunca force push em branches compartilhadas; use pull requests para revisão e merge.
- Pull requests devem ser revisados por pelo menos um membro do time, especialmente para mudanças em regras de negócio, layout ou segurança.
- Descreva claramente no PR o escopo, motivação e impactos da mudança.
- Em caso de rollback, registre o motivo no commit e no PR.

## 9. Manutenção de Arquivos de Configuração
- Qualquer alteração em arquivos de configuração (eslint.config.js, tsconfig.json, tailwind.config.ts, postcss.config.js, vite.config.ts, vitest.config.ts, vitest.setup.ts) deve ser revisada à luz das regras de arquitetura e qualidade.
- Alterações nesses arquivos devem ser documentadas no PR e, se necessário, refletidas na documentação de arquitetura.

## 10. Dependências e package.json
- Toda nova dependência adicionada ao package.json deve ser justificada e documentada no PR.
- Revise periodicamente as dependências para evitar bloat e garantir segurança.

## 11. Exemplos, Demos e Scripts
- Exemplos e demos (ex: emoji-picker-demo.html, line-break-test.html, test-text-editor.html, toolbar-line-break-demo.html) devem ser mantidos atualizados e podem servir de base para testes automatizados.
- Scripts utilitários (ex: debug-content-blocks.js) só devem ser versionados se agregarem valor recorrente ao time e devem ser documentados.

## 12. Aliases e Estrutura
- Consulte sempre o arquivo components.json para entender e manter os aliases e a estrutura de importação.
- Qualquer alteração em aliases deve ser refletida na documentação de arquitetura.

## 13. Schema, SQL e Dados de Exemplo
- Toda alteração em arquivos de schema (supabase_schema.sql, profiles_rows.sql, profiles_rows.csv) deve ser documentada e auditada.
- Mantenha arquivos de migração e dados de exemplo versionados e alinhados com as regras de segurança e modelagem.

## 14. Utilitários de Teste e Ambiente
- Documente utilitários de ambiente de teste (ex: test-env.js) e oriente o time sobre seu uso no onboarding.

---

**Nota:** O projeto adota uma organização de arquivos em pastas específicas:
- /demos/ para exemplos e protótipos
- /scripts/ para utilitários
- /data/ para schemas e dados
- /docs/ para documentação extra

Mantenha a estrutura organizada e consulte sempre antes de criar novos arquivos na raiz.

**Observação:**
Estas regras são vivas e devem ser revisadas e aprimoradas continuamente conforme o projeto evolui. Priorize sempre simplicidade, clareza, manutenibilidade e segurança. 
import React from 'react';
import { BlockConfig } from '@/types';
import { ColorPicker } from '../../ColorPicker';
import './CardAppearanceConfig.css';

interface ButtonConfigProps {
  config: BlockConfig;
  onChange: (c: BlockConfig) => void;
  onReset: () => void;
}

export const ButtonConfig: React.FC<ButtonConfigProps> = ({
  config,
  onChange,
  onReset,
}) => {
  return (
    <div className="appearance-card-panel custom-video-appearance-panel">
      <div className="appearance-header custom-appearance-header">
        <span className="appearance-title custom-appearance-title standard-title">Configurações de Botão</span>
        <button className="restore-btn custom-restore-btn standard-button" type="button" onClick={onReset}>Restaurar Padrão</button>
      </div>
      <hr className="appearance-divider custom-appearance-divider" />
      <div className="appearance-body custom-appearance-body">
        <div className="custom-appearance-grid-v3">
          {/* Linha 1: Cor do Fundo do botão | (vazio) | Formato do botão */}
          <div className="appearance-row">
            <div className="color-picker-group">
              <ColorPicker
                label=""
                value={config.button?.backgroundColor || '#7c3aed'}
                onChange={color => onChange({
                  ...config,
                  button: { ...config.button, backgroundColor: color }
                })}
              />
              <span className="color-label-inline standard-label">Cor do Fundo</span>
            </div>
            <div></div>
            <div className="format-dropdown-group">
              <span className="format-label custom-format-label standard-label">Formato</span>
              <select
                className="format-dropdown custom-format-dropdown standard-field"
                value={config.button?.style || 'rounded'}
                onChange={e => onChange({
                  ...config,
                  button: { ...config.button, style: e.target.value as 'rounded' | 'square' | 'pill' }
                })}
              >
                <option value="rounded">Arredondado</option>
                <option value="square">Quadrado</option>
                <option value="pill">Pílula</option>
              </select>
            </div>
          </div>

          {/* Linha 2: Cor da Fonte | Tamanho | Posição do botão */}
          <div className="appearance-row">
            <div className="color-picker-group">
              <ColorPicker
                label=""
                value={config.button?.color || '#ffffff'}
                onChange={color => onChange({
                  ...config,
                  button: { ...config.button, color }
                })}
              />
              <span className="color-label-inline standard-label">Cor da Fonte</span>
            </div>
            <div className="format-dropdown-group">
              <span className="format-label custom-format-label standard-label">Tamanho</span>
              <select
                className="format-dropdown custom-format-dropdown standard-field"
                value={config.button?.size || 'medium'}
                onChange={(e) => onChange({
                  ...config,
                  button: { ...config.button, size: e.target.value as 'small' | 'medium' | 'large' }
                })}
              >
                <option value="small">Pequeno</option>
                <option value="medium">Médio</option>
                <option value="large">Grande</option>
              </select>
            </div>
            <div className="format-dropdown-group">
              <span className="format-label custom-format-label standard-label">Posição</span>
              <select
                className="format-dropdown custom-format-dropdown standard-field"
                value={config.button?.position || 'bottom-right'}
                onChange={(e) => onChange({
                  ...config,
                  button: { ...config.button, position: e.target.value as any }
                })}
              >
                <option value="bottom-left">Inferior esquerda</option>
                <option value="bottom-center">Inferior centro</option>
                <option value="bottom-right">Inferior direita</option>
                <option value="top-left">Superior esquerda</option>
                <option value="top-center">Superior centro</option>
                <option value="top-right">Superior direita</option>
              </select>
            </div>
          </div>

          {/* Linha 3: Toggle Borda | Cor da Borda | Largura da Borda */}
          <div className="appearance-row">
            <label className="switch-label custom-switch-label standard-label">
              <input
                type="checkbox"
                className="switch-input custom-switch standard-checkbox"
                checked={config.button?.border?.enabled || false}
                onChange={(e) => onChange({
                  ...config,
                  button: {
                    ...config.button,
                    border: { ...config.button?.border, enabled: e.target.checked }
                  }
                })}
              />
              <span>Borda</span>
            </label>
            {config.button?.border?.enabled ? (
              <div className="color-picker-group">
                <ColorPicker
                  label=""
                  value={config.button?.border?.color || '#e5e5e5'}
                  onChange={(color) => onChange({
                    ...config,
                    button: {
                      ...config.button,
                      border: { ...config.button?.border, color }
                    }
                  })}
                />
                <span className="color-label-inline standard-label">Cor da Borda</span>
              </div>
            ) : <div></div>}
            {config.button?.border?.enabled ? (
              <div className="slider-group custom-slider-group slider-vertical-label">
                <span className="slider-label custom-slider-label standard-label">Largura da Borda</span>
                <input
                  type="range"
                  min={1}
                  max={8}
                  value={config.button?.border?.width || 1}
                  className="slider custom-slider"
                  onChange={(e) => onChange({
                    ...config,
                    button: {
                      ...config.button,
                      border: { ...config.button?.border, width: Number(e.target.value) }
                    }
                  })}
                />
              </div>
            ) : <div></div>}
          </div>

          {/* Linha 4: Toggle Sombra | (vazio) | Profundidade da Sombra */}
          <div className="appearance-row">
            <label className="switch-label custom-switch-label standard-label">
              <input
                type="checkbox"
                className="switch-input custom-switch standard-checkbox"
                checked={config.button?.shadow?.enabled || false}
                onChange={(e) => onChange({
                  ...config,
                  button: {
                    ...config.button,
                    shadow: { ...config.button?.shadow, enabled: e.target.checked }
                  }
                })}
              />
              <span>Sombra</span>
            </label>
            <div></div>
            {config.button?.shadow?.enabled ? (
              <div className="slider-group custom-slider-group slider-vertical-label">
                <span className="slider-label custom-slider-label standard-label">Profundidade da Sombra</span>
                <input
                  type="range"
                  min={1}
                  max={5}
                  value={config.button?.shadow?.depth || 2}
                  className="slider custom-slider"
                  onChange={(e) => onChange({
                    ...config,
                    button: {
                      ...config.button,
                      shadow: { ...config.button?.shadow, depth: Number(e.target.value) }
                    }
                  })}
                />
              </div>
            ) : <div></div>}
          </div>

          {/* Linha 5: Toggle Hover | (vazio) | Profundidade do Hover */}
          <div className="appearance-row">
            <label className="switch-label custom-switch-label standard-label">
              <input
                type="checkbox"
                className="switch-input custom-switch standard-checkbox"
                checked={config.button?.hover?.enabled || false}
                onChange={(e) => onChange({
                  ...config,
                  button: {
                    ...config.button,
                    hover: { ...config.button?.hover, enabled: e.target.checked }
                  }
                })}
              />
              <span>Hover</span>
            </label>
            <div></div>
            {config.button?.hover?.enabled ? (
              <div className="slider-group custom-slider-group slider-vertical-label">
                <span className="slider-label custom-slider-label standard-label">Profundidade do Hover</span>
                <input
                  type="range"
                  min={1}
                  max={5}
                  value={config.button?.hover?.shadowDepth || 3}
                  className="slider custom-slider"
                  onChange={(e) => onChange({
                    ...config,
                    button: {
                      ...config.button,
                      hover: { ...config.button?.hover, shadowDepth: Number(e.target.value) }
                    }
                  })}
                />
              </div>
            ) : <div></div>}
          </div>
        </div>
      </div>
    </div>
  );
};
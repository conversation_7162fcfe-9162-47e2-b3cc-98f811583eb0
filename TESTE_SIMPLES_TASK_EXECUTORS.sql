-- TESTE SIMPLES: Verificar dados na tabela task_executors

-- Contar total de registros
SELECT COUNT(*) as total_task_executors FROM task_executors;

-- <PERSON>car todos os registros
SELECT * FROM task_executors LIMIT 10;

-- Buscar especificamente para o usuário wgvasque
SELECT te.*, t.title 
FROM task_executors te
LEFT JOIN tasks t ON te.task_id = t.id
WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

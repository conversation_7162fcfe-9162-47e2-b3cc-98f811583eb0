-- =====================================================
-- DADOS DE TESTE
-- =====================================================
-- Contém: Usu<PERSON>rios, projetos, tarefas e relacionamentos para teste
-- Dependências: Todas as tabelas e RLS configurados
-- Versão: 2.0 - Julho 2025

-- Verificar dependências
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles') THEN
    RAISE EXCEPTION 'Tabelas não encontradas. Execute os scripts anteriores primeiro.';
  END IF;
  RAISE NOTICE '✅ Dependências verificadas';
END $$;

-- =====================================================
-- LIMPEZA DE DADOS EXISTENTES
-- =====================================================

-- Limpar dados de teste existentes
DELETE FROM public.evidence;
DELETE FROM public.task_attachments;
DELETE FROM public.task_comments;
DELETE FROM public.task_content_blocks;
DELETE FROM public.task_approvers;
DELETE FROM public.task_executors;
DELETE FROM public.tasks;
DELETE FROM public.stage_responsibles;
DELETE FROM public.stages;
DELETE FROM public.project_members;
DELETE FROM public.project_history;
DELETE FROM public.user_notifications;
DELETE FROM public.projects;
DELETE FROM public.profiles;

-- =====================================================
-- USUÁRIOS DE TESTE
-- =====================================================

-- Função para criar perfil sem constraint FK
CREATE OR REPLACE FUNCTION create_test_profile(
  profile_id uuid,
  profile_name text,
  profile_email text,
  profile_role text DEFAULT 'member',
  profile_position text DEFAULT NULL,
  profile_phone text DEFAULT NULL
) RETURNS void AS $$
BEGIN
  INSERT INTO public.profiles (id, name, email, role, position, phone, is_active)
  VALUES (profile_id, profile_name, profile_email, profile_role, profile_position, profile_phone, true)
  ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    position = EXCLUDED.position,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active;
END;
$$ LANGUAGE plpgsql;

-- Criar usuários de teste
SELECT create_test_profile(
  'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  'Admin Master',
  '<EMAIL>',
  'admin',
  'Administrador do Sistema',
  '(11) 99999-9999'
);

SELECT create_test_profile(
  'b2c3d4e5-f6a7-8901-bcde-f12345678901',
  'Ana Silva',
  '<EMAIL>',
  'manager',
  'Gerente de Projetos',
  '(11) 98888-8888'
);

SELECT create_test_profile(
  'c3d4e5f6-a7b8-9012-cdef-123456789012',
  'Carlos Santos',
  '<EMAIL>',
  'member',
  'Desenvolvedor Sênior',
  '(11) 97777-7777'
);

SELECT create_test_profile(
  '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
  'João Executor',
  '<EMAIL>',
  'member',
  'Desenvolvedor Pleno',
  '(11) 96666-6666'
);

-- =====================================================
-- PROJETOS DE TESTE
-- =====================================================

-- Projeto 1: Sistema de Gestão
INSERT INTO public.projects (id, name, description, status, owner_id, start_date, end_date)
VALUES (
  'proj-0001-2025-haiku-gestao',
  'Sistema de Gestão Haiku',
  'Desenvolvimento do sistema completo de gestão de projetos e tarefas',
  'active',
  'b2c3d4e5-f6a7-8901-bcde-f12345678901',
  '2025-01-01',
  '2025-12-31'
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  owner_id = EXCLUDED.owner_id,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- Projeto 2: App Mobile
INSERT INTO public.projects (id, name, description, status, owner_id, start_date, end_date)
VALUES (
  'proj-0002-2025-haiku-mobile',
  'App Mobile Haiku',
  'Desenvolvimento do aplicativo mobile para iOS e Android',
  'planning',
  'b2c3d4e5-f6a7-8901-bcde-f12345678901',
  '2025-03-01',
  '2025-09-30'
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  owner_id = EXCLUDED.owner_id,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- =====================================================
-- MEMBROS DOS PROJETOS
-- =====================================================

-- Membros do Projeto 1
INSERT INTO public.project_members (project_id, user_id, role, joined_at)
VALUES 
  ('proj-0001-2025-haiku-gestao', 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'editor', now()),
  ('proj-0001-2025-haiku-gestao', '4b09be1f-5187-44c0-9b53-87b7c57e45b4', 'executor', now())
ON CONFLICT (project_id, user_id) DO UPDATE SET
  role = EXCLUDED.role,
  joined_at = EXCLUDED.joined_at;

-- Membros do Projeto 2
INSERT INTO public.project_members (project_id, user_id, role, joined_at)
VALUES 
  ('proj-0002-2025-haiku-mobile', 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'editor', now()),
  ('proj-0002-2025-haiku-mobile', '4b09be1f-5187-44c0-9b53-87b7c57e45b4', 'executor', now())
ON CONFLICT (project_id, user_id) DO UPDATE SET
  role = EXCLUDED.role,
  joined_at = EXCLUDED.joined_at;

-- =====================================================
-- ESTÁGIOS DOS PROJETOS
-- =====================================================

-- Estágios do Projeto 1
INSERT INTO public.stages (id, project_id, name, description, status, start_date, end_date)
VALUES 
  ('stage-001-backend', 'proj-0001-2025-haiku-gestao', 'Backend Development', 'Desenvolvimento da API e banco de dados', 'in-progress', '2025-01-01', '2025-06-30'),
  ('stage-002-frontend', 'proj-0001-2025-haiku-gestao', 'Frontend Development', 'Desenvolvimento da interface web', 'not-started', '2025-04-01', '2025-09-30'),
  ('stage-003-testing', 'proj-0001-2025-haiku-gestao', 'Testing & QA', 'Testes e garantia de qualidade', 'not-started', '2025-08-01', '2025-11-30')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- Estágios do Projeto 2
INSERT INTO public.stages (id, project_id, name, description, status, start_date, end_date)
VALUES 
  ('stage-004-planning', 'proj-0002-2025-haiku-mobile', 'Planning & Design', 'Planejamento e design do app', 'in-progress', '2025-03-01', '2025-04-30'),
  ('stage-005-development', 'proj-0002-2025-haiku-mobile', 'Mobile Development', 'Desenvolvimento do app mobile', 'not-started', '2025-05-01', '2025-08-31')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- =====================================================
-- TAREFAS DE TESTE
-- =====================================================

-- Tarefas do Backend
INSERT INTO public.tasks (id, stage_id, title, description, status, priority, assigned_to, created_by, due_date)
VALUES 
  ('7c606667-9391-4660-933d-90d6bd276e88', 'stage-001-backend', 'Configurar Banco de Dados', 'Configurar PostgreSQL e estrutura inicial', 'in-progress', 8, 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', '2025-01-15'),
  ('task-002-api-auth', 'stage-001-backend', 'Implementar Autenticação', 'Sistema de login e autenticação JWT', 'todo', 9, '4b09be1f-5187-44c0-9b53-87b7c57e45b4', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', '2025-01-30'),
  ('task-003-api-tasks', 'stage-001-backend', 'API de Tarefas', 'CRUD completo para tarefas', 'todo', 7, '4b09be1f-5187-44c0-9b53-87b7c57e45b4', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', '2025-02-15')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  assigned_to = EXCLUDED.assigned_to,
  due_date = EXCLUDED.due_date;

-- Tarefas do Frontend
INSERT INTO public.tasks (id, stage_id, title, description, status, priority, assigned_to, created_by, due_date)
VALUES 
  ('task-004-ui-components', 'stage-002-frontend', 'Componentes UI', 'Criar biblioteca de componentes reutilizáveis', 'todo', 6, 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', '2025-04-30'),
  ('task-005-dashboard', 'stage-002-frontend', 'Dashboard Principal', 'Desenvolvimento do dashboard principal', 'todo', 8, 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', '2025-05-31')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  assigned_to = EXCLUDED.assigned_to,
  due_date = EXCLUDED.due_date;

-- =====================================================
-- EXECUTORES DAS TAREFAS
-- =====================================================

-- Executores para as tarefas
INSERT INTO public.task_executors (task_id, user_id, assigned_at)
VALUES 
  ('7c606667-9391-4660-933d-90d6bd276e88', '4b09be1f-5187-44c0-9b53-87b7c57e45b4', now()),
  ('task-002-api-auth', '4b09be1f-5187-44c0-9b53-87b7c57e45b4', now()),
  ('task-003-api-tasks', '4b09be1f-5187-44c0-9b53-87b7c57e45b4', now()),
  ('task-004-ui-components', 'c3d4e5f6-a7b8-9012-cdef-123456789012', now()),
  ('task-005-dashboard', 'c3d4e5f6-a7b8-9012-cdef-123456789012', now())
ON CONFLICT (task_id, user_id) DO UPDATE SET
  assigned_at = EXCLUDED.assigned_at;

-- =====================================================
-- CONTEÚDO DAS TAREFAS
-- =====================================================

-- Conteúdo da tarefa principal
INSERT INTO public.task_content_blocks (id, task_id, type, content, "order", created_by)
VALUES 
  ('block-001-db-config', '7c606667-9391-4660-933d-90d6bd276e88', 'text', '{"text": "Configuração do banco de dados PostgreSQL com as seguintes especificações:\n\n1. Criar schema principal\n2. Configurar RLS (Row Level Security)\n3. Criar tabelas básicas\n4. Inserir dados iniciais"}', 1, 'b2c3d4e5-f6a7-8901-bcde-f12345678901'),
  ('block-002-db-requirements', '7c606667-9391-4660-933d-90d6bd276e88', 'checklist', '{"items": [{"text": "Instalar PostgreSQL 15+", "checked": true}, {"text": "Configurar conexão segura", "checked": false}, {"text": "Criar usuário de aplicação", "checked": false}, {"text": "Configurar backup automático", "checked": false}]}', 2, 'b2c3d4e5-f6a7-8901-bcde-f12345678901')
ON CONFLICT (id) DO UPDATE SET
  type = EXCLUDED.type,
  content = EXCLUDED.content,
  "order" = EXCLUDED."order";

-- =====================================================
-- LIMPEZA
-- =====================================================

-- Remover função auxiliar
DROP FUNCTION IF EXISTS create_test_profile(uuid, text, text, text, text, text);

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
DECLARE
  total_profiles INTEGER;
  total_projects INTEGER;
  total_tasks INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_profiles FROM public.profiles;
  SELECT COUNT(*) INTO total_projects FROM public.projects;
  SELECT COUNT(*) INTO total_tasks FROM public.tasks;
  
  RAISE NOTICE '✅ Dados de teste criados com sucesso!';
  RAISE NOTICE '👤 Usuários criados: %', total_profiles;
  RAISE NOTICE '📊 Projetos criados: %', total_projects;
  RAISE NOTICE '📋 Tarefas criadas: %', total_tasks;
  RAISE NOTICE '🔑 IDs importantes:';
  RAISE NOTICE '   - Admin: a1b2c3d4-e5f6-7890-abcd-ef1234567890';
  RAISE NOTICE '   - João (executor): 4b09be1f-5187-44c0-9b53-87b7c57e45b4';
  RAISE NOTICE '   - Tarefa teste: 7c606667-9391-4660-933d-90d6bd276e88';
END $$;
  role = EXCLUDED.role,
  position = EXCLUDED.position,
  is_active = EXCLUDED.is_active;

-- Designer UI/UX
INSERT INTO public.profiles (id, name, email, role, position, phone, is_active)
VALUES (
  'd4e5f6a7-b8c9-0123-defa-234567890123',
  'Marina Costa',
  '<EMAIL>',
  'member',
  'Designer UI/UX',
  '(11) 96666-6666',
  true
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  email = EXCLUDED.email,
  role = EXCLUDED.role,
  position = EXCLUDED.position,
  is_active = EXCLUDED.is_active;

-- Desenvolvedor Junior
INSERT INTO public.profiles (id, name, email, role, position, phone, is_active)
VALUES (
  'e5f6a7b8-c9d0-1234-efab-345678901234',
  'Pedro Oliveira',
  '<EMAIL>',
  'member',
  'Desenvolvedor Junior',
  '(11) 95555-5555',
  true
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  email = EXCLUDED.email,
  role = EXCLUDED.role,
  position = EXCLUDED.position,
  is_active = EXCLUDED.is_active;

-- Analista de Qualidade
INSERT INTO public.profiles (id, name, email, role, position, phone, is_active)
VALUES (
  'f6a7b8c9-d0e1-2345-fabc-456789012345',
  'Lucia Ferreira',
  '<EMAIL>',
  'member',
  'Analista de Qualidade',
  '(11) 94444-4444',
  true
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  email = EXCLUDED.email,
  role = EXCLUDED.role,
  position = EXCLUDED.position,
  is_active = EXCLUDED.is_active;

-- =====================================================
-- PROJETOS DE TESTE
-- =====================================================

-- Projeto 1: Sistema de Gerenciamento
INSERT INTO public.projects (id, name, description, status, progress, owner_id, start_date, end_date)
VALUES (
  'proj-1111-2222-3333-444444444444',
  'Sistema de Gerenciamento Haiku',
  'Desenvolvimento do sistema principal de gerenciamento de projetos',
  'active',
  65,
  'b2c3d4e5-f6a7-8901-bcde-f12345678901', -- Ana Silva
  '2024-01-15',
  '2024-12-31'
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  progress = EXCLUDED.progress,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- Projeto 2: App Mobile
INSERT INTO public.projects (id, name, description, status, progress, owner_id, start_date, end_date)
VALUES (
  'proj-2222-3333-4444-555555555555',
  'App Mobile Haiku',
  'Desenvolvimento do aplicativo mobile para gestão de tarefas',
  'planning',
  10,
  'b2c3d4e5-f6a7-8901-bcde-f12345678901', -- Ana Silva
  '2024-03-01',
  '2024-08-31'
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  progress = EXCLUDED.progress,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- =====================================================
-- MEMBROS DOS PROJETOS
-- =====================================================

-- Membros do Projeto 1
INSERT INTO public.project_members (project_id, user_id, role) VALUES
  ('proj-1111-2222-3333-444444444444', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 'manager'),
  ('proj-1111-2222-3333-444444444444', 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'editor'),
  ('proj-1111-2222-3333-444444444444', 'd4e5f6a7-b8c9-0123-defa-234567890123', 'editor'),
  ('proj-1111-2222-3333-444444444444', 'e5f6a7b8-c9d0-1234-efab-345678901234', 'executor'),
  ('proj-1111-2222-3333-444444444444', 'f6a7b8c9-d0e1-2345-fabc-456789012345', 'approver')
ON CONFLICT (project_id, user_id, role) DO NOTHING;

-- Membros do Projeto 2
INSERT INTO public.project_members (project_id, user_id, role) VALUES
  ('proj-2222-3333-4444-555555555555', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 'manager'),
  ('proj-2222-3333-4444-555555555555', 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'editor'),
  ('proj-2222-3333-4444-555555555555', 'd4e5f6a7-b8c9-0123-defa-234567890123', 'member'),
  ('proj-2222-3333-4444-555555555555', 'e5f6a7b8-c9d0-1234-efab-345678901234', 'executor')
ON CONFLICT (project_id, user_id, role) DO NOTHING;

-- =====================================================
-- ESTÁGIOS DE TESTE
-- =====================================================

-- Estágios do Projeto 1
INSERT INTO public.stages (id, project_id, name, description, status, progress, start_date, end_date) VALUES
  ('stage-1111-aaaa-bbbb-cccccccccccc', 'proj-1111-2222-3333-444444444444', 'Análise e Planejamento', 'Levantamento de requisitos e planejamento', 'completed', 100, '2024-01-15', '2024-02-15'),
  ('stage-2222-bbbb-cccc-dddddddddddd', 'proj-1111-2222-3333-444444444444', 'Desenvolvimento Backend', 'Desenvolvimento da API e banco de dados', 'in-progress', 80, '2024-02-16', '2024-06-30'),
  ('stage-3333-cccc-dddd-eeeeeeeeeeee', 'proj-1111-2222-3333-444444444444', 'Desenvolvimento Frontend', 'Desenvolvimento da interface do usuário', 'in-progress', 60, '2024-04-01', '2024-08-31'),
  ('stage-4444-dddd-eeee-ffffffffffff', 'proj-1111-2222-3333-444444444444', 'Testes e Homologação', 'Testes de qualidade e homologação', 'not-started', 0, '2024-09-01', '2024-11-30')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  progress = EXCLUDED.progress,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- Estágios do Projeto 2
INSERT INTO public.stages (id, project_id, name, description, status, progress, start_date, end_date) VALUES
  ('stage-5555-eeee-ffff-gggggggggggg', 'proj-2222-3333-4444-555555555555', 'Prototipação', 'Criação de protótipos e wireframes', 'in-progress', 30, '2024-03-01', '2024-04-15'),
  ('stage-6666-ffff-gggg-hhhhhhhhhhhh', 'proj-2222-3333-4444-555555555555', 'Desenvolvimento Mobile', 'Desenvolvimento do app mobile', 'not-started', 0, '2024-04-16', '2024-07-31')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  progress = EXCLUDED.progress,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date;

-- =====================================================
-- TAREFAS DE TESTE
-- =====================================================

-- Tarefas do Estágio 1 (Análise e Planejamento)
INSERT INTO public.tasks (id, stage_id, title, description, status, priority, progress, assigned_to, created_by, estimated_hours, due_date) VALUES
  ('task-1111-aaaa-1111-aaaaaaaaaaaa', 'stage-1111-aaaa-bbbb-cccccccccccc', 'Levantamento de Requisitos', 'Identificar e documentar todos os requisitos do sistema', 'completed', 5, 100, 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 40, '2024-01-25'),
  ('task-2222-bbbb-2222-bbbbbbbbbbbb', 'stage-1111-aaaa-bbbb-cccccccccccc', 'Arquitetura do Sistema', 'Definir arquitetura técnica e tecnologias', 'completed', 4, 100, 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 32, '2024-02-10'),
  ('task-3333-cccc-3333-cccccccccccc', 'stage-1111-aaaa-bbbb-cccccccccccc', 'Prototipação UI', 'Criar protótipos das principais telas', 'completed', 3, 100, 'd4e5f6a7-b8c9-0123-defa-234567890123', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 48, '2024-02-15')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  progress = EXCLUDED.progress,
  assigned_to = EXCLUDED.assigned_to,
  estimated_hours = EXCLUDED.estimated_hours,
  due_date = EXCLUDED.due_date;

-- Tarefas do Estágio 2 (Desenvolvimento Backend)
INSERT INTO public.tasks (id, stage_id, title, description, status, priority, progress, assigned_to, created_by, estimated_hours, due_date) VALUES
  ('task-4444-dddd-4444-dddddddddddd', 'stage-2222-bbbb-cccc-dddddddddddd', 'Configuração do Banco de Dados', 'Configurar PostgreSQL e estrutura inicial', 'completed', 5, 100, 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 16, '2024-03-01'),
  ('task-5555-eeee-5555-eeeeeeeeeeee', 'stage-2222-bbbb-cccc-dddddddddddd', 'API de Autenticação', 'Implementar sistema de login e autenticação', 'completed', 4, 100, 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 24, '2024-03-15'),
  ('task-6666-ffff-6666-ffffffffffff', 'stage-2222-bbbb-cccc-dddddddddddd', 'API de Projetos', 'Criar endpoints para gerenciamento de projetos', 'in-progress', 4, 70, 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 32, '2024-04-10'),
  ('task-7777-gggg-7777-gggggggggggg', 'stage-2222-bbbb-cccc-dddddddddddd', 'API de Tarefas', 'Implementar CRUD completo de tarefas', 'todo', 3, 0, 'e5f6a7b8-c9d0-1234-efab-345678901234', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 40, '2024-05-15')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  progress = EXCLUDED.progress,
  assigned_to = EXCLUDED.assigned_to,
  estimated_hours = EXCLUDED.estimated_hours,
  due_date = EXCLUDED.due_date;

-- Tarefas do Estágio 3 (Desenvolvimento Frontend)
INSERT INTO public.tasks (id, stage_id, title, description, status, priority, progress, assigned_to, created_by, estimated_hours, due_date) VALUES
  ('task-8888-hhhh-8888-hhhhhhhhhhhh', 'stage-3333-cccc-dddd-eeeeeeeeeeee', 'Configuração do React', 'Configurar projeto React com TypeScript', 'completed', 3, 100, 'd4e5f6a7-b8c9-0123-defa-234567890123', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 8, '2024-04-05'),
  ('task-9999-iiii-9999-iiiiiiiiiiii', 'stage-3333-cccc-dddd-eeeeeeeeeeee', 'Componentes Base', 'Criar componentes reutilizáveis', 'completed', 4, 100, 'd4e5f6a7-b8c9-0123-defa-234567890123', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 24, '2024-04-20'),
  ('task-aaaa-jjjj-aaaa-jjjjjjjjjjjj', 'stage-3333-cccc-dddd-eeeeeeeeeeee', 'Tela de Login', 'Implementar interface de login', 'in-progress', 3, 80, 'd4e5f6a7-b8c9-0123-defa-234567890123', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 16, '2024-05-01'),
  ('task-bbbb-kkkk-bbbb-kkkkkkkkkkkk', 'stage-3333-cccc-dddd-eeeeeeeeeeee', 'Dashboard Principal', 'Criar dashboard com métricas e resumos', 'todo', 4, 0, 'd4e5f6a7-b8c9-0123-defa-234567890123', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 32, '2024-06-15')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  progress = EXCLUDED.progress,
  assigned_to = EXCLUDED.assigned_to,
  estimated_hours = EXCLUDED.estimated_hours,
  due_date = EXCLUDED.due_date;

-- =====================================================
-- EXECUTORES DE TAREFAS
-- =====================================================

-- Adicionar executores para algumas tarefas
INSERT INTO public.task_executors (task_id, user_id) VALUES
  ('task-6666-ffff-6666-ffffffffffff', 'e5f6a7b8-c9d0-1234-efab-345678901234'), -- Pedro como executor da API de Projetos
  ('task-7777-gggg-7777-gggggggggggg', 'e5f6a7b8-c9d0-1234-efab-345678901234'), -- Pedro como executor da API de Tarefas
  ('task-7777-gggg-7777-gggggggggggg', 'c3d4e5f6-a7b8-9012-cdef-123456789012'), -- Carlos também como executor
  ('task-aaaa-jjjj-aaaa-jjjjjjjjjjjj', 'e5f6a7b8-c9d0-1234-efab-345678901234'), -- Pedro como executor da Tela de Login
  ('task-bbbb-kkkk-bbbb-kkkkkkkkkkkk', 'e5f6a7b8-c9d0-1234-efab-345678901234')  -- Pedro como executor do Dashboard
ON CONFLICT (task_id, user_id) DO NOTHING;

-- =====================================================
-- APROVADORES DE TAREFAS
-- =====================================================

-- Adicionar aprovadores para tarefas críticas
INSERT INTO public.task_approvers (task_id, user_id) VALUES
  ('task-1111-aaaa-1111-aaaaaaaaaaaa', 'f6a7b8c9-d0e1-2345-fabc-456789012345'), -- Lucia como aprovadora
  ('task-2222-bbbb-2222-bbbbbbbbbbbb', 'f6a7b8c9-d0e1-2345-fabc-456789012345'), -- Lucia como aprovadora
  ('task-4444-dddd-4444-dddddddddddd', 'f6a7b8c9-d0e1-2345-fabc-456789012345'), -- Lucia como aprovadora
  ('task-5555-eeee-5555-eeeeeeeeeeee', 'f6a7b8c9-d0e1-2345-fabc-456789012345')  -- Lucia como aprovadora
ON CONFLICT (task_id, user_id) DO NOTHING;

-- Marcar algumas aprovações como concluídas
UPDATE public.task_approvers SET 
  approved_at = NOW() - INTERVAL '10 days'
WHERE task_id IN ('task-1111-aaaa-1111-aaaaaaaaaaaa', 'task-2222-bbbb-2222-bbbbbbbbbbbb');

-- =====================================================
-- CONTEÚDO DE TAREFAS
-- =====================================================

-- Adicionar blocos de conteúdo para uma tarefa
INSERT INTO public.task_content_blocks (task_id, type, content, "order") VALUES
  ('task-6666-ffff-6666-ffffffffffff', 'text', '{"text": "Esta tarefa envolve a criação de endpoints RESTful para gerenciamento de projetos, incluindo operações CRUD completas.", "style": "normal"}', 1),
  ('task-6666-ffff-6666-ffffffffffff', 'colored-block', '{"text": "Importante: Seguir padrões de API REST e documentar todos os endpoints no Swagger.", "color": "warning"}', 2),
  ('task-6666-ffff-6666-ffffffffffff', 'checklist', '{"items": [{"text": "Criar modelo de dados", "checked": true}, {"text": "Implementar endpoints CRUD", "checked": true}, {"text": "Adicionar validações", "checked": false}, {"text": "Documentar API", "checked": false}]}', 3),
  ('task-6666-ffff-6666-ffffffffffff', 'evidence', '{"title": "Evidência da Implementação", "description": "Faça upload de screenshots ou arquivos que comprovem a implementação dos endpoints."}', 4)
ON CONFLICT (task_id, type, "order") DO NOTHING;

-- =====================================================
-- COMENTÁRIOS DE TESTE
-- =====================================================

-- Adicionar alguns comentários
INSERT INTO public.task_comments (task_id, author, content) VALUES
  ('task-6666-ffff-6666-ffffffffffff', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 'Lembrem-se de seguir os padrões de nomenclatura definidos na documentação.'),
  ('task-6666-ffff-6666-ffffffffffff', 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'Já finalizei a estrutura base. Agora vou implementar as validações.'),
  ('task-7777-gggg-7777-gggggggggggg', 'e5f6a7b8-c9d0-1234-efab-345678901234', 'Preciso de mais detalhes sobre os filtros que devem ser implementados.'),
  ('task-aaaa-jjjj-aaaa-jjjjjjjjjjjj', 'd4e5f6a7-b8c9-0123-defa-234567890123', 'Interface quase pronta. Faltam apenas os testes de responsividade.')
ON CONFLICT DO NOTHING;

-- =====================================================
-- NOTIFICAÇÕES DE TESTE
-- =====================================================

-- Adicionar notificações para usuários
INSERT INTO public.user_notifications (user_id, title, message, type, link) VALUES
  ('e5f6a7b8-c9d0-1234-efab-345678901234', 'Nova tarefa atribuída', 'Você foi designado como executor da tarefa "API de Tarefas"', 'info', '/task/task-7777-gggg-7777-gggggggggggg'),
  ('d4e5f6a7-b8c9-0123-defa-234567890123', 'Tarefa em andamento', 'Prazo da tarefa "Tela de Login" está se aproximando', 'warning', '/task/task-aaaa-jjjj-aaaa-jjjjjjjjjjjj'),
  ('c3d4e5f6-a7b8-9012-cdef-123456789012', 'Tarefa concluída', 'Tarefa "API de Autenticação" foi marcada como concluída', 'success', '/task/task-5555-eeee-5555-eeeeeeeeeeee'),
  ('f6a7b8c9-d0e1-2345-fabc-456789012345', 'Aprovação pendente', 'Tarefa "Configuração do Banco de Dados" aguarda sua aprovação', 'warning', '/task/task-4444-dddd-4444-dddddddddddd')
ON CONFLICT DO NOTHING;

-- =====================================================
-- DADOS DE QUIZ DE TESTE
-- =====================================================

-- Adicionar um quiz de teste
INSERT INTO public.quizzes (id, task_id, block_id, title, content, config) VALUES
  ('quiz-1111-aaaa-1111-aaaaaaaaaaaa', 'task-6666-ffff-6666-ffffffffffff', 'quiz-block-1', 'Quiz: Conhecimentos sobre APIs REST', 
   '{"questions": [{"id": "q1", "type": "multiple-choice", "question": "Qual método HTTP é usado para criar um recurso?", "options": ["GET", "POST", "PUT", "DELETE"], "correct": 1}, {"id": "q2", "type": "multiple-choice", "question": "Qual status code indica sucesso na criação?", "options": ["200", "201", "204", "400"], "correct": 1}], "mode": "assessment", "passingScore": 70}',
   '{"showResults": true, "allowRetakes": true, "timeLimit": 600}'
  )
ON CONFLICT (id) DO NOTHING;

-- Adicionar tentativa de quiz
INSERT INTO public.quiz_attempts (id, quiz_id, user_id, score, max_score, percentage, answers, completed_at) VALUES
  ('attempt-1111-aaaa-1111-aaaaaaaa', 'quiz-1111-aaaa-1111-aaaaaaaaaaaa', 'e5f6a7b8-c9d0-1234-efab-345678901234', 2, 2, 100, 
   '{"q1": {"answer": 1, "correct": true}, "q2": {"answer": 1, "correct": true}}',
   NOW() - INTERVAL '2 days'
  )
ON CONFLICT (id) DO NOTHING;

-- Adicionar progresso do quiz
INSERT INTO public.user_quiz_progress (user_id, quiz_id, best_score, best_percentage, attempts_count, completed, first_attempt_at, last_attempt_at) VALUES
  ('e5f6a7b8-c9d0-1234-efab-345678901234', 'quiz-1111-aaaa-1111-aaaaaaaaaaaa', 2, 100, 1, true, NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days')
ON CONFLICT (user_id, quiz_id) DO UPDATE SET
  best_score = EXCLUDED.best_score,
  best_percentage = EXCLUDED.best_percentage,
  attempts_count = EXCLUDED.attempts_count,
  completed = EXCLUDED.completed,
  last_attempt_at = EXCLUDED.last_attempt_at;

-- =====================================================
-- HISTÓRICO DE PROJETO
-- =====================================================

-- Adicionar histórico de alterações
INSERT INTO public.project_history (project_id, user_id, action, details) VALUES
  ('proj-1111-2222-3333-444444444444', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 'project_created', '{"description": "Projeto criado com sucesso"}'),
  ('proj-1111-2222-3333-444444444444', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 'member_added', '{"member": "Carlos Santos", "role": "editor"}'),
  ('proj-1111-2222-3333-444444444444', 'c3d4e5f6-a7b8-9012-cdef-123456789012', 'task_completed', '{"task": "API de Autenticação", "completion_date": "2024-03-15"}'),
  ('proj-2222-3333-4444-555555555555', 'b2c3d4e5-f6a7-8901-bcde-f12345678901', 'project_created', '{"description": "Projeto de app mobile iniciado"}')
ON CONFLICT DO NOTHING;

-- =====================================================
-- ATUALIZAÇÃO DE PROGRESSOS
-- =====================================================

-- Atualizar progresso dos estágios e projetos usando as functions criadas
SELECT public.calculate_stage_progress('stage-1111-aaaa-bbbb-cccccccccccc');
SELECT public.calculate_stage_progress('stage-2222-bbbb-cccc-dddddddddddd');
SELECT public.calculate_stage_progress('stage-3333-cccc-dddd-eeeeeeeeeeee');
SELECT public.calculate_stage_progress('stage-4444-dddd-eeee-ffffffffffff');
SELECT public.calculate_stage_progress('stage-5555-eeee-ffff-gggggggggggg');
SELECT public.calculate_stage_progress('stage-6666-ffff-gggg-hhhhhhhhhhhh');

SELECT public.calculate_project_progress('proj-1111-2222-3333-444444444444');
SELECT public.calculate_project_progress('proj-2222-3333-4444-555555555555');

-- Atualizar estatísticas dos quizzes
SELECT public.update_quiz_statistics('quiz-1111-aaaa-1111-aaaaaaaaaaaa');

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
DECLARE
  user_count INTEGER;
  project_count INTEGER;
  task_count INTEGER;
  member_count INTEGER;
  executor_count INTEGER;
  comment_count INTEGER;
  notification_count INTEGER;
BEGIN
  -- Contar dados inseridos
  SELECT COUNT(*) INTO user_count FROM public.profiles;
  SELECT COUNT(*) INTO project_count FROM public.projects;
  SELECT COUNT(*) INTO task_count FROM public.tasks;
  SELECT COUNT(*) INTO member_count FROM public.project_members;
  SELECT COUNT(*) INTO executor_count FROM public.task_executors;
  SELECT COUNT(*) INTO comment_count FROM public.task_comments;
  SELECT COUNT(*) INTO notification_count FROM public.user_notifications;
  
  RAISE NOTICE '✅ Dados de teste inseridos com sucesso!';
  RAISE NOTICE '👥 Usuários: %', user_count;
  RAISE NOTICE '📋 Projetos: %', project_count;
  RAISE NOTICE '📝 Tarefas: %', task_count;
  RAISE NOTICE '🤝 Membros: %', member_count;
  RAISE NOTICE '⚡ Executores: %', executor_count;
  RAISE NOTICE '💬 Comentários: %', comment_count;
  RAISE NOTICE '🔔 Notificações: %', notification_count;
  RAISE NOTICE '🧮 Progressos calculados automaticamente';
END $$;

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface TaskQuickActionsProps {
  onSendToReview?: () => void;
  onRequestApproval?: () => void;
  onReportProblem?: () => void;
}

export const TaskQuickActions: React.FC<TaskQuickActionsProps> = ({
  onSendToReview,
  onRequestApproval,
  onReportProblem
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="text-lg">Ações</CardTitle>
    </CardHeader>
    <CardContent className="space-y-2">
      <Button className="w-full bg-task hover:bg-task-dark" onClick={onSendToReview}>
        Enviar para Revisão
      </Button>
      <Button variant="outline" className="w-full" onClick={onRequestApproval}>
        Solicitar Aprovação
      </Button>
      <Button variant="outline" className="w-full text-status-error border-status-error hover:bg-red-50" onClick={onReportProblem}>
        Reportar Problema
      </Button>
    </CardContent>
  </Card>
); 
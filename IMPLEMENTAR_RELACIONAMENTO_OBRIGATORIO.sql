-- =====================================================
-- IMPLEMENTAÇÃO: RELACIONAMENTO OBRIGATÓRIO PROFILES ↔ AUTH.USERS
-- Execute este script no Supabase SQL Editor
-- =====================================================

-- STEP 1: VERIFICAR ESTADO ATUAL DAS TABELAS
-- Primeiro, vamos ver quantos usuários existem em auth.users vs profiles

SELECT 
    'USUÁRIOS EM AUTH.USERS' as tabela,
    COUNT(*) as total
FROM auth.users
UNION ALL
SELECT 
    'USUÁRIOS EM PROFILES' as tabela,
    COUNT(*) as total
FROM profiles;

-- STEP 2: IDENTIFICAR USUÁRIOS SEM PROFILE
SELECT 
    'USUÁRIOS SEM PROFILE' as problema,
    u.id,
    u.email,
    u.created_at,
    u.last_sign_in_at
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- STEP 3: IDENTIFICAR PROFILES ÓRFÃOS (se existirem)
SELECT 
    'PROFILES ÓRFÃOS' as problema,
    p.id,
    p.email,
    p.name
FROM profiles p
LEFT JOIN auth.users u ON p.id = u.id
WHERE u.id IS NULL;

-- STEP 4: CRIAR PROFILES PARA USUÁRIOS EXISTENTES
-- Vamos criar profiles para todos os usuários que não têm
-- Evitando conflitos de email completamente

-- Primeiro, vamos ver quais usuários precisam de profiles
SELECT 
    'USUÁRIOS QUE PRECISAM DE PROFILES' as info,
    u.id,
    u.email,
    CASE 
        WHEN EXISTS (SELECT 1 FROM profiles p2 WHERE p2.email = u.email) 
        THEN 'EMAIL JÁ EXISTE - PRECISA SUFIXO'
        ELSE 'EMAIL DISPONÍVEL'
    END as status_email
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- Criar profiles com emails únicos
INSERT INTO profiles (id, name, email, role, created_at, updated_at)
SELECT 
    u.id,
    COALESCE(
        u.raw_user_meta_data->>'name',
        u.raw_user_meta_data->>'full_name',
        SPLIT_PART(u.email, '@', 1)
    ) as name,
    -- SEMPRE criar email único para evitar conflitos
    u.email || '_' || SUBSTRING(u.id::text, 1, 8) as email,
    'member' as role,
    u.created_at,
    NOW()
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE p.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- STEP 5: VERIFICAR SE TODOS OS USUÁRIOS TÊM PROFILES AGORA
SELECT 
    'VERIFICAÇÃO PÓS-CRIAÇÃO' as status,
    COUNT(CASE WHEN p.id IS NULL THEN 1 END) as usuarios_sem_profile,
    COUNT(u.id) as total_usuarios
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id;

-- STEP 6: REESTABELECER FOREIGN KEY CONSTRAINT
-- Primeiro, remover constraint existente se houver
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_id_fkey;

-- Criar nova constraint com ON DELETE CASCADE
ALTER TABLE profiles 
ADD CONSTRAINT profiles_id_fkey 
FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- STEP 7: REABILITAR ROW LEVEL SECURITY
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- STEP 8: REMOVER POLÍTICAS ANTIGAS
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;

-- STEP 9: CRIAR POLÍTICAS RLS SEGURAS
-- Política para usuários verem apenas seu próprio perfil
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

-- Política para usuários atualizarem apenas seu próprio perfil
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Política para admins verem todos os perfis
CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Política para admins atualizarem todos os perfis
CREATE POLICY "Admins can update all profiles" ON profiles
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- STEP 10: CRIAR FUNÇÃO DE TRIGGER PARA CRIAÇÃO AUTOMÁTICA DE PROFILE
CREATE OR REPLACE FUNCTION create_profile_for_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, name, email, role, created_at, updated_at)
    VALUES (
        NEW.id,
        COALESCE(
            NEW.raw_user_meta_data->>'name',
            NEW.raw_user_meta_data->>'full_name',
            SPLIT_PART(NEW.email, '@', 1)
        ),
        -- SEMPRE usar email único para evitar conflitos
        CASE 
            WHEN EXISTS (SELECT 1 FROM profiles WHERE email = NEW.email)
            THEN NEW.email || '_' || SUBSTRING(NEW.id::text, 1, 8)
            ELSE NEW.email
        END,
        'member',
        NEW.created_at,
        NOW()
    );
    RETURN NEW;
EXCEPTION
    WHEN unique_violation THEN
        -- Se ainda houver conflito, tentar novamente com timestamp
        INSERT INTO profiles (id, name, email, role, created_at, updated_at)
        VALUES (
            NEW.id,
            COALESCE(
                NEW.raw_user_meta_data->>'name',
                NEW.raw_user_meta_data->>'full_name',
                SPLIT_PART(NEW.email, '@', 1)
            ),
            NEW.email || '_' || SUBSTRING(NEW.id::text, 1, 8) || '_' || EXTRACT(EPOCH FROM NOW())::text,
            'member',
            NEW.created_at,
            NOW()
        );
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 11: CRIAR TRIGGER PARA CRIAÇÃO AUTOMÁTICA
DROP TRIGGER IF EXISTS create_profile_trigger ON auth.users;
CREATE TRIGGER create_profile_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_profile_for_new_user();

-- STEP 12: VERIFICAÇÃO FINAL
SELECT 
    'VERIFICAÇÃO FINAL' as status,
    'CONSTRAINT CRIADA' as foreign_key,
    'RLS HABILITADO' as security,
    'TRIGGER ATIVO' as auto_profile,
    COUNT(*) as total_profiles
FROM profiles;

-- STEP 13: TESTAR A CONSTRAINT
-- Esta query deve falhar se tentarmos inserir um profile com ID inexistente
-- SELECT 'TESTE CONSTRAINT' as teste;
-- INSERT INTO profiles (id, name, email, role) 
-- VALUES ('00000000-0000-0000-0000-000000000000', 'Teste', '<EMAIL>', 'member');
-- Se der erro, a constraint está funcionando!

-- =====================================================
-- RESULTADOS ESPERADOS:
-- =====================================================
-- ✅ Todos os usuários de auth.users têm profiles correspondentes
-- ✅ Foreign key constraint ativa (profiles.id → auth.users.id)
-- ✅ RLS habilitado com políticas seguras
-- ✅ Trigger criado para novos usuários
-- ✅ Integridade referencial garantida
-- =====================================================

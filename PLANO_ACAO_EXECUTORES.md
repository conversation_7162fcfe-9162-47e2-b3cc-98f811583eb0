# PLANO DE AÇÃO URGENTE - Executor não vê conteúdo da tarefa

## 🔍 PROBLEMA CONFIRMADO
- **Evidência dos logs**: `[taskService.getFullById] contentBlocks: []`
- **Usuário**: `4b09be1f-5187-44c0-9b53-87b7c57e45b4` (role: member)
- **Tarefa**: `7c606667-9391-4660-933d-90d6bd276e88`
- **Comportamento**: contentBlocks retorna array vazio devido a RLS

## 🚨 AÇÕES IMEDIATAS

### 1. APLICAR CORREÇÃO RLS (URGENTE)
Execute no SQL Editor do Supabase:
```sql
-- Arquivo: APLICAR_CORRECAO_URGENTE_RLS.sql
-- Corrige políticas RLS para incluir executores
```

### 2. VERIFICAR DADOS
Execute no SQL Editor:
```sql
-- Arquivo: VERIFICAR_ESTADO_ATUAL.sql
-- Verifica se usuário é executor e se há blocos
```

### 3. TESTAR CORREÇÃO
1. Aplicar correção RLS
2. Recarregar página no navegador
3. Verificar logs: deve aparecer contentBlocks com dados
4. Confirmar que conteúdo aparece na aba "Executar"

## 📋 DIAGNÓSTICO ATUAL

### Logs Relevantes:
```
AuthProvider.tsx:94 Perfil encontrado: undefined
AuthProvider.tsx:99 Perfil não encontrado, criando perfil básico temporário...
TaskDetailsV2.tsx:121 profileRole: 'member'
taskService.ts:71 [taskService.getFullById] contentBlocks: []
TaskDetailsV2.tsx:224 [TaskDetailsV2] Blocos carregados do banco: []
```

### Status:
- ✅ Usuário autenticado
- ✅ Profile criado (role: member)
- ❌ contentBlocks vazio (RLS bloqueando)
- ❌ Nenhum conteúdo na aba "Executar"

## 🎯 SOLUÇÃO APLICADA

### Frontend:
- ✅ Logs de debug adicionados ao taskService
- ✅ Códigos de erro serão capturados

### Backend:
- ✅ Políticas RLS corrigidas para incluir executores
- ✅ Scripts de diagnóstico prontos

## 🔄 PRÓXIMOS PASSOS
1. **Aplicar `APLICAR_CORRECAO_URGENTE_RLS.sql`** ← CRÍTICO
2. **Executar `VERIFICAR_ESTADO_ATUAL.sql`** para validar
3. **Testar no navegador** - recarregar página
4. **Verificar logs** - contentBlocks deve ter dados
5. **Confirmar funcionalidade** - conteúdo visível na aba

## 📊 RESULTADO ESPERADO
```
[taskService.getFullById] contentBlocks result: {
  data: [{id: "...", type: "...", content: "..."}],
  error: null,
  count: 1
}
```

**Status**: ⚠️ AGUARDANDO APLICAÇÃO DA CORREÇÃO RLS
- **SELECT**: Inclui executores e aprovadores
- **INSERT**: Permite executores inserir blocos
- **UPDATE**: Permite executores atualizar blocos  
- **DELETE**: Permite executores deletar blocos

### 3. Arquivo de Diagnóstico Criado
`diagnostic-executor-access.sql` - Testes para validar correção

## Próximos Passos

### 1. Aplicar Correção no Supabase
```bash
# Executar no SQL Editor do Supabase
psql -f fix-task-content-blocks-permissions.sql
```

### 2. Validar Correção
```bash
# Executar testes de diagnóstico
psql -f diagnostic-executor-access.sql
```

### 3. Testar Aplicação
1. Admin: Criar tarefa com conteúdo
2. Admin: Atribuir member como executor
3. Member: Fazer login
4. Member: Acessar TaskDetailsV2
5. Member: Verificar se vê conteúdo na aba "Executar"

### 4. Verificar Logs
- Console do TaskDetailsV2: `[TaskDetailsV2] Blocos carregados do banco:`
- Console do EvidenceExecutionBlock: `✅ Evidências com aprovação carregadas:`
- Network tab: Verificar se queries do Supabase retornam dados

## Impacto Esperado

### ✅ ANTES da Correção
- Member executor: Não vê conteúdo da tarefa
- contentBlocks: Array vazio `[]`
- Aba "Executar": Sem blocos para executar

### ✅ DEPOIS da Correção  
- Member executor: Vê conteúdo completo da tarefa
- contentBlocks: Array com blocos `[{...}]`
- Aba "Executar": Blocos renderizados corretamente

## Arquivos Modificados
- ✅ `fix-task-content-blocks-permissions.sql` - Correção das políticas
- ✅ `diagnostic-executor-access.sql` - Testes de validação
- ✅ `PLANO_ACAO_EXECUTORES.md` - Documentação completa

## Validação Final
- [ ] Políticas RLS aplicadas no Supabase
- [ ] Testes de diagnóstico executados
- [ ] Member executor consegue ver conteúdo
- [ ] Upload de evidências funciona
- [ ] Logs mostram dados carregados corretamente

## Backup & Rollback
Se houver problemas, reverter com:
```sql
-- Restaurar política original
drop policy if exists "Project members can view task content blocks" on public.task_content_blocks;
create policy "Project members can view task content blocks"
  on public.task_content_blocks
  for select
  using (
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (select project_id from public.project_members where user_id = auth.uid())
    )
  );
```

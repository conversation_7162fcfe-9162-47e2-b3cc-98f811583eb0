import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TiptapTextEditor } from '../TiptapTextEditor';

// Mock do Tiptap
jest.mock('@tiptap/react', () => ({
  useEditor: jest.fn(() => ({
    getHTML: jest.fn(() => '<p>Test content</p>'),
    commands: {
      setContent: jest.fn(),
    },
    setEditable: jest.fn(),
    can: jest.fn(() => ({
      undo: jest.fn(() => true),
      redo: jest.fn(() => true),
    })),
    isActive: jest.fn(() => false),
    chain: jest.fn(() => ({
      focus: jest.fn(() => ({
        undo: jest.fn(() => ({ run: jest.fn() })),
        redo: jest.fn(() => ({ run: jest.fn() })),
        toggleBold: jest.fn(() => ({ run: jest.fn() })),
        toggleItalic: jest.fn(() => ({ run: jest.fn() })),
      })),
    })),
  })),
  EditorContent: ({ editor }: any) => <div data-testid="editor-content">Editor Content</div>,
}));

jest.mock('@tiptap/starter-kit', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

jest.mock('@tiptap/extension-link', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

jest.mock('@tiptap/extension-table', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

jest.mock('@tiptap/extension-table-row', () => ({
  __esModule: true,
  default: {},
}));

jest.mock('@tiptap/extension-table-cell', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

jest.mock('@tiptap/extension-table-header', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

jest.mock('@tiptap/extension-code-block', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

describe('TiptapTextEditor', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render editor content', () => {
    render(
      <TiptapTextEditor
        content="<p>Test content</p>"
        onChange={mockOnChange}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('should render toolbar by default', () => {
    render(
      <TiptapTextEditor
        content="<p>Test content</p>"
        onChange={mockOnChange}
      />
    );

    // Verificar se elementos do toolbar estão presentes
    expect(screen.getByTitle('Desfazer')).toBeInTheDocument();
    expect(screen.getByTitle('Refazer')).toBeInTheDocument();
    expect(screen.getByTitle('Negrito')).toBeInTheDocument();
  });

  it('should hide toolbar when showToolbar is false', () => {
    render(
      <TiptapTextEditor
        content="<p>Test content</p>"
        onChange={mockOnChange}
        showToolbar={false}
      />
    );

    // Verificar se elementos do toolbar não estão presentes
    expect(screen.queryByTitle('Desfazer')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Refazer')).not.toBeInTheDocument();
  });

  it('should handle Lexical content conversion', () => {
    const lexicalContent = JSON.stringify({
      root: {
        children: [
          {
            type: 'paragraph',
            children: [
              { type: 'text', text: 'Lexical content' }
            ]
          }
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    });

    render(
      <TiptapTextEditor
        content={lexicalContent}
        onChange={mockOnChange}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('should handle empty content', () => {
    render(
      <TiptapTextEditor
        content=""
        onChange={mockOnChange}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('should handle editable prop', () => {
    render(
      <TiptapTextEditor
        content="<p>Test content</p>"
        onChange={mockOnChange}
        editable={false}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    render(
      <TiptapTextEditor
        content="<p>Test content</p>"
        onChange={mockOnChange}
        className="custom-class"
      />
    );

    const editorContainer = screen.getByTestId('editor-content').closest('.tiptap-editor');
    expect(editorContainer).toBeInTheDocument();
  });

  it('should show loading state when editor is not ready', () => {
    const { useEditor } = require('@tiptap/react');
    useEditor.mockReturnValueOnce(null);

    render(
      <TiptapTextEditor
        content="<p>Test content</p>"
        onChange={mockOnChange}
      />
    );

    // Verificar se o estado de loading está sendo exibido
    expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
  });
});

import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from '@/hooks/ui/use-toast';

interface AuthContextType {
  user: any;
  profile: any;
  session: any;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  setProfile?: (profile: any) => void;
  checkAuthStatus: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Erro ao obter sessão:', error);
          setError(error.message);
          setSession(null);
          setUser(null);
        } else {
          setSession(data.session);
          setUser(data.session?.user ?? null);
        }
      } catch (err) {
        console.error('Erro inesperado ao obter sessão:', err);
        setError('Erro ao verificar autenticação');
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getSession();

    const { data: listener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);

      setSession(session);
      setUser(session?.user ?? null);

      // Limpar erro quando usuário faz login com sucesso
      if (session?.user) {
        setError(null);
      }

      // Se o usuário foi deslogado, limpar todos os dados
      if (event === 'SIGNED_OUT' || !session) {
        setProfile(null);
        setError(null);
      }
    });

    return () => {
      listener.subscription.unsubscribe();
    };
  }, []);

  // Buscar perfil do usuário sempre que user mudar
  useEffect(() => {
    const fetchProfile = async () => {
      if (user && user.id) {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

          if (error) {
            console.error('Erro ao buscar perfil:', error.message);

            // Se for erro 500, tentar fallback
            if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
              console.warn('Erro 500 no AuthProvider, usando fallback...');
              // Criar perfil básico temporário
              setProfile({
                id: user.id,
                name: user.email?.split('@')[0] || 'Usuário',
                email: user.email || '',
                role: 'member',
                position: null,
                phone: null,
                avatar_url: null,
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });
              return;
            }
          }

          setProfile(data || null);
        } catch (error) {
          console.error('Erro inesperado ao buscar perfil:', error);
          setProfile(null);
        }
      } else {
        setProfile(null);
      }
    };
    fetchProfile();
  }, [user]);

  // Bloquear acesso se o perfil estiver inativo
  useEffect(() => {
    if (profile && profile.is_active === false) {
      logout();
      toast({
        title: 'Acesso bloqueado',
        description: 'Seu acesso foi bloqueado. Entre em contato com o administrador.',
        variant: 'destructive',
      });
    }
  }, [profile]);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) setError(error.message);
    setLoading(false);
  };

  const register = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signUp({ email, password });
    if (error) setError(error.message);
    setLoading(false);
  };

  const logout = async () => {
    setLoading(true);
    setError(null);
    const { error } = await supabase.auth.signOut();
    setUser(null);
    setSession(null);
    setProfile(null);
    if (error) setError(error.message);
    setLoading(false);
  };

  // Verificar se o usuário está realmente autenticado
  const checkAuthStatus = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Erro ao verificar autenticação:', error);
        return false;
      }
      return !!session?.user;
    } catch (error) {
      console.error('Erro ao verificar status de autenticação:', error);
      return false;
    }
  };

  return (
    <AuthContext.Provider value={{ user, profile, session, loading, error, login, register, logout, setProfile, checkAuthStatus }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error("useAuthContext deve ser usado dentro de AuthProvider");
  return context;
};
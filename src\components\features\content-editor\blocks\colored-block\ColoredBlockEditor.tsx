import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ColoredBlockContent, BlockConfig, defaultBlockConfig } from '@/types';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { ICONS_BY_VARIANT, COLORS_BY_VARIANT, ColoredCardVariant, coloredBlockPresets } from '../shared/config-panel/constants/block-types';
import { coloredCardVariants } from '../shared/config-panel/constants/block-types/colored/presets';
import { presetToBlockConfig } from '../shared/config-panel/constants/migration';
import * as LucideIcons from 'lucide-react';
import { Plus } from 'lucide-react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { getPureContrastColor, darkenColor, lightenColor } from '../shared/utils/colorUtils';
import '../shared/responsive-button.css';

/**
 * Editor para blocos coloridos (info, warning, error, success).
 * @param editContent Conteúdo do bloco (tipado)
 * @param setEditContent Callback para atualizar o conteúdo
 * @param mode 'edit' para edição, 'preview' para visualização
 * @param config Optional block configuration
 * @param setConfig Callback to update the block configuration
 * @param truncateText Optional truncateText flag
 * @param onTypeChange Callback to notify parent when type changes
 */
export interface ColoredBlockEditorProps {
  editContent: ColoredBlockContent;
  setEditContent: (c: ColoredBlockContent) => void;
  mode: 'edit' | 'preview';
  config?: BlockConfig;
  setConfig?: (c: BlockConfig) => void;
  truncateText?: boolean;
  onTypeChange?: (type: ColoredCardVariant) => void;
}

const colorClasses = {
  info: 'bg-blue-50 border-blue-200 text-blue-900',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-900',
  error: 'bg-red-50 border-red-200 text-red-900',
  success: 'bg-green-50 border-green-200 text-green-900'
};

// Utilitário para obter o preset completo do colored-block para a variante
function getColoredBlockPresetByVariant(variant: ColoredCardVariant) {
  return coloredBlockPresets[variant];
}

export const ColoredBlockEditor: React.FC<ColoredBlockEditorProps> = ({ editContent, setEditContent, mode, config, setConfig, truncateText, onTypeChange }) => {
  const safeConfig = config || defaultBlockConfig;
  const [isHovering, setIsHovering] = useState(false);
  const [showTypeConfirm, setShowTypeConfirm] = useState(false);
  const [pendingType, setPendingType] = useState<ColoredCardVariant | null>(null);
  const [lastType, setLastType] = useState<ColoredCardVariant>(editContent.type as ColoredCardVariant || 'info');

  // Handlers para lista de itens
  const handleAddItem = () => {
    setEditContent({
      ...editContent,
      items: [...(editContent.items || []), { text: '', url: '' }],
    });
  };
  const handleRemoveItem = (idx: number) => {
    const newItems = (editContent.items || []).filter((_, i) => i !== idx);
    setEditContent({ ...editContent, items: newItems });
  };
  const handleItemTextChange = (idx: number, value: string) => {
    const newItems = (editContent.items || []).map((item, i) => {
      if (i !== idx) return item;
      if (typeof item === 'string') return value;
      return { ...item, text: value };
    });
    setEditContent({ ...editContent, items: newItems });
  };
  const handleItemClickableChange = (idx: number, checked: boolean) => {
    const newItems = (editContent.items || []).map((item, i) => {
      if (i !== idx) return item;
      if (checked) {
        // Se era string, vira objeto clicável
        if (typeof item === 'string') return { text: item, url: '' };
        return { ...item, url: item.url || '' };
      } else {
        // Se desmarcar, volta para string (apenas texto)
        if (typeof item === 'object') return item.text || '';
        return item;
      }
    });
    setEditContent({ ...editContent, items: newItems });
  };
  const handleItemUrlChange = (idx: number, url: string) => {
    const newItems = (editContent.items || []).map((item, i) => {
      if (i !== idx) return item;
      if (typeof item === 'string') return { text: item, url };
      return { ...item, url };
    });
    setEditContent({ ...editContent, items: newItems });
  };

  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newType = e.target.value as ColoredCardVariant;
    if (newType !== (editContent.type as ColoredCardVariant || 'info')) {
      setPendingType(newType);
      setShowTypeConfirm(true);
    }
  };

  const confirmTypeChange = () => {
    if (pendingType) {
      setEditContent({ ...editContent, type: pendingType });
      setLastType(pendingType);
      if (setConfig) {
        // Resetar completamente para o preset do novo tipo
        const preset = coloredBlockPresets[pendingType];
        setConfig(presetToBlockConfig(preset));
      }
      if (typeof onTypeChange === 'function') {
        onTypeChange(pendingType);
      }
    }
    setShowTypeConfirm(false);
    setPendingType(null);
  };

  const cancelTypeChange = () => {
    setShowTypeConfirm(false);
    setPendingType(null);
    // Forçar o select a voltar para o tipo anterior
    setEditContent({ ...editContent, type: lastType });
  };

  if (mode === 'edit') {
    const isImageValid = editContent.imageUrl && /^https?:\/\//.test(editContent.imageUrl);
    // Ícones permitidos para a variante selecionada
    const allowedIcons = ICONS_BY_VARIANT[editContent.type || 'info'];
    const iconColor = coloredCardVariants[editContent.type || 'info'].iconColor;
    // Opções visuais por tipo
    const borderColors = {
      info: ['#2563eb', '#1e40af'],
      warning: ['#f59e42', '#b45309'],
      error: ['#ef4444', '#991b1b'],
      success: ['#22c55e', '#166534'],
    };
    const bgVariants = {
      info: [coloredCardVariants.info.backgroundColor, '#1e40af'],
      warning: [coloredCardVariants.warning.backgroundColor, '#fbbf24'],
      error: [coloredCardVariants.error.backgroundColor, '#fee2e2'],
      success: [coloredCardVariants.success.backgroundColor, '#bbf7d0'],
    };
    const borderWidths = [1, 2, 3, 4];
    const shadowOptions = [
      { label: 'Nenhuma', value: 'none' },
      { label: 'Leve', value: 1 },
      { label: 'Média', value: 2 },
      { label: 'Forte', value: 4 },
    ];
    const formatOptions = [
      { label: 'Arredondado', value: 'rounded' },
      { label: 'Quadrado', value: 'square' },
      { label: 'Pílula', value: 'pill' },
    ];
    const type = editContent.type || 'info';
    const cardConfig = config?.card || {};
    return (
      <div className="flex flex-col gap-6 w-full">
        {/* Grupo 1: Tipo e Identidade */}
        <div className="flex flex-col gap-2 rounded-lg p-4 border border-gray-100">
          <div className="flex flex-col md:flex-row gap-2 items-end">
            <div className="flex-1">
              <label className="text-xs font-semibold text-gray-700 mb-1">Tipo do bloco</label>
              <select
                value={editContent.type as ColoredCardVariant || 'info'}
                onChange={handleTypeChange}
                className="border border-gray-300 rounded px-3 py-2 h-10 w-full focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="info">Informação</option>
                <option value="warning">Aviso</option>
                <option value="error">Erro</option>
                <option value="success">Sucesso</option>
              </select>
            </div>
            <div className="flex-1 mt-2 md:mt-0">
              <label className="text-xs font-semibold text-gray-700 mb-1">Badge/Tag</label>
              <Input
                value={editContent.badge || ''}
                onChange={e => setEditContent({ ...editContent, badge: e.target.value })}
                placeholder="Ex: Importante, Novo"
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>
          </div>
          {/* Seleção de ícone */}
          <div className="mt-2">
            <label className="text-xs font-semibold text-gray-700 mb-1">Ícone do bloco</label>
            <div className="flex gap-2 flex-wrap mt-1">
              {allowedIcons.map((icon, idx) => {
                const IconComp = LucideIcons[icon];
                if (!IconComp) return null;
                const selected = (editContent as any).iconName === icon;
                return (
                  <button
                    key={icon}
                    type="button"
                    className={`w-12 h-12 flex flex-col items-center justify-center border rounded transition-all ${selected ? 'border-blue-600 bg-blue-50' : 'border-gray-200 hover:border-blue-400'} focus:outline-none`}
                    onClick={() => setEditContent({ ...editContent, iconName: icon })}
                  >
                    <IconComp className="w-6 h-6" style={{ color: iconColor }} />
                    <span className="text-[10px] mt-1">{icon}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
        <div className="border-b border-gray-200" />
        {/* Grupo 2: Conteúdo principal */}
        <div className="flex flex-col gap-2 rounded-lg p-4 border border-gray-100">
          <label className="text-xs font-semibold text-gray-700">Título</label>
          <Input
            value={editContent.title}
            onChange={e => setEditContent({ ...editContent, title: e.target.value })}
            placeholder="Título do bloco"
            className="mb-1"
          />
          <label className="text-xs font-semibold text-gray-700">Subtítulo</label>
          <Input
            value={editContent.subtitle || ''}
            onChange={e => setEditContent({ ...editContent, subtitle: e.target.value })}
            placeholder="Subtítulo (opcional)"
            className="mb-1"
          />
          <label className="text-xs font-semibold text-gray-700">Texto principal</label>
          <Textarea
            value={editContent.text}
            onChange={e => setEditContent({ ...editContent, text: e.target.value })}
            placeholder="Texto do bloco"
            className="mb-1"
            rows={3}
          />
          {/* Lista de itens */}
          <div className="flex flex-col gap-1 mt-2">
            <label className="text-xs text-gray-600">Lista de itens <span className="text-gray-400">(opcional)</span></label>
            {(editContent.items || []).map((item, idx) => {
              const isObj = typeof item === 'object' && item !== null;
              const text = isObj ? item.text : item || '';
              const url = isObj ? item.url : '';
              const clickable = !!url;
              return (
                <div key={idx} className="flex flex-col gap-1 border border-gray-100 rounded p-2 mb-1 bg-white">
                  <div className="flex gap-1 items-center">
                    <Input
                      value={typeof item === 'object' ? item.text : item || ''}
                      onChange={e => handleItemTextChange(idx, e.target.value)}
                      placeholder={`Item ${idx + 1}`}
                      className="flex-1"
                    />
                    <button type="button" className="text-red-500 px-2" onClick={() => handleRemoveItem(idx)} title="Remover item">×</button>
                  </div>
                  <label className="flex items-center gap-2 text-xs mt-1">
                    <input
                      type="checkbox"
                      checked={typeof item === 'object' && 'url' in item}
                      onChange={e => handleItemClickableChange(idx, e.target.checked)}
                    />
                    Clicável
                  </label>
                  {typeof item === 'object' && 'url' in item && (
                    <Input
                      value={url}
                      onChange={e => handleItemUrlChange(idx, e.target.value)}
                      placeholder="URL do item (https://...)"
                      className="mt-1"
                    />
                  )}
                </div>
              );
            })}
            <button
              type="button"
              className="flex items-center gap-1 text-sm font-medium text-blue-500 w-fit mt-1 px-2 py-1 rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-200 hover:text-blue-700 hover:bg-blue-50"
              onClick={handleAddItem}
              aria-label="Adicionar item à lista"
            >
              <Plus className="w-4 h-4" strokeWidth={2} color="currentColor" /> Adicionar item
            </button>
          </div>
        </div>
        <div className="border-b border-gray-200" />
        {/* Grupo 4: Ação */}
        <div className="flex flex-col gap-2 rounded-lg p-4 border border-gray-100">
          <label className="text-xs font-semibold text-gray-700">Botão de ação (opcional)</label>
          <div className="flex gap-2 items-end">
            <Input
              value={editContent.actionButton?.text || ''}
              onChange={e => setEditContent({
                ...editContent,
                actionButton: {
                  ...editContent.actionButton,
                  text: e.target.value,
                  url: editContent.actionButton?.url || '',
                  newTab: editContent.actionButton?.newTab || false,
                },
              })}
              placeholder="Texto do botão (ex: Saiba mais)"
              className="flex-1"
            />
            <Input
              value={editContent.actionButton?.url || ''}
              onChange={e => setEditContent({
                ...editContent,
                actionButton: {
                  ...editContent.actionButton,
                  url: e.target.value,
                  text: editContent.actionButton?.text || '',
                  newTab: editContent.actionButton?.newTab || false,
                },
              })}
              placeholder="URL do botão (https://...)"
              className="flex-1"
            />
            <label className="flex items-center gap-1 text-xs">
              <input
                type="checkbox"
                checked={!!editContent.actionButton?.newTab}
                onChange={e => setEditContent({
                  ...editContent,
                  actionButton: {
                    ...editContent.actionButton,
                    newTab: e.target.checked,
                    text: editContent.actionButton?.text || '',
                    url: editContent.actionButton?.url || '',
                  },
                })}
              />
              Nova aba
            </label>
          </div>
        </div>
        {/* Modal de confirmação */}
        <Dialog open={showTypeConfirm} onOpenChange={setShowTypeConfirm}>
          <DialogContent style={{ maxWidth: 340, padding: 16 }}>
            <DialogTitle>Alterar tipo do bloco?</DialogTitle>
            <div className="mb-4">Tem certeza que deseja alterar o tipo do bloco? Todas as configurações visuais personalizadas serão resetadas para o padrão do novo tipo.</div>
            <div className="flex gap-2 justify-end">
              <button className="px-4 py-2 rounded bg-gray-200" onClick={cancelTypeChange}>Cancelar</button>
              <button className="px-4 py-2 rounded bg-indigo-600 text-white" onClick={confirmTypeChange}>Alterar tipo</button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
  // Preview
  if (mode === 'preview') {
    const card = config?.card || defaultBlockConfig.card;
    const hover = (card.hover || {}) as any;
    const hoverEnabled = !!hover.enabled;
    const isActiveHover = hoverEnabled && isHovering;
    const background = isActiveHover && hover.backgroundColor ? hover.backgroundColor : card.backgroundColor || '#fff';
    const borderColor = isActiveHover && hover.borderColor ? hover.borderColor : card.border?.color || '#e5e5e5';
    const border = card.border?.enabled
      ? `${card.border.width || 1}px solid ${borderColor}`
      : 'none';
    const boxShadow = isActiveHover && hover.shadow
      ? hover.shadow
      : card.shadow?.enabled
        ? `0 2px ${2 * (card.shadow.depth || 1)}px #0002`
        : 'none';
    const fontSize = card.font?.size || 16;
    // Contraste dinâmico para título/conteúdo
    const fontColor = getPureContrastColor(background);
    const fontStyle = card.font?.style || 'normal';
    const borderRadius = card.format === 'pill' ? 9999 : card.format === 'square' ? 0 : 12;
    const isPill = card.format === 'pill';
    // Diferenciação visual para botão e ícone
    const cardBg = background;
    const buttonTextColor = getPureContrastColor(cardBg);
    const buttonBg = buttonTextColor === '#fff'
      ? darkenColor(cardBg, 0.18)
      : lightenColor(cardBg, 0.18);
    const buttonColor = getPureContrastColor(buttonBg);
    // Hover do botão: variação ainda mais forte
    const buttonHoverBg = buttonTextColor === '#fff'
      ? darkenColor(cardBg, 0.32)
      : lightenColor(cardBg, 0.32);
    const buttonHoverColor = getPureContrastColor(buttonHoverBg);
    // Ícone segue a mesma lógica de diferenciação
    const iconBg = buttonTextColor === '#fff'
      ? darkenColor(cardBg, 0.12)
      : lightenColor(cardBg, 0.12);
    const iconColor = getPureContrastColor(iconBg);

    // Função para calcular padding adicional baseado na posição do botão
    const getButtonPadding = (position: string, hasButton: boolean) => {
      if (!hasButton) return {};

      const buttonHeight = 64; // Padding maior para evitar sobreposição
      switch (position) {
        case 'top-left':
        case 'top-center':
        case 'top-right':
          return { paddingTop: buttonHeight };
        case 'bottom-left':
        case 'bottom-center':
        case 'bottom-right':
          return { paddingBottom: buttonHeight };
        default:
          return { paddingBottom: buttonHeight };
      }
    };

    const buttonPosition = safeConfig.button?.position || 'bottom-center';
    const buttonPadding = getButtonPadding(buttonPosition, !!editContent.actionButton?.text);

    return (
      <div
        className={`gap-2${isPill ? ' flex items-center justify-center' : ' flex flex-col'}${editContent.actionButton?.text ? ' card-with-button' : ''}`}
        style={{
          background,
          color: fontColor,
          borderRadius,
          padding: isPill ? '24px 32px' : 12, // Padding menor para mobile
          minHeight: isPill ? 120 : undefined,
          border,
          boxShadow,
          fontSize,
          fontStyle,
          transition: 'all 0.2s',
          display: isPill ? 'flex' : 'flex',
          flexDirection: isPill ? 'column' : 'column',
          alignItems: isPill ? 'center' : undefined,
          justifyContent: isPill ? 'center' : undefined,
          ...buttonPadding,
        }}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {/* Badge/Tag */}
        {editContent.badge && (
          <span className="inline-block bg-white/80 text-xs font-semibold text-gray-700 px-2 py-0.5 rounded-full w-fit mb-1 border border-gray-200">{editContent.badge}</span>
        )}
        {/* Imagem ilustrativa */}
        {editContent.imageUrl && (
          <img src={editContent.imageUrl} alt="Imagem ilustrativa" className="w-16 h-16 object-contain rounded mb-2" />
        )}
        {/* Ícone, título, subtítulo */}
        <BlockCardIcon
          config={{ ...config?.icon, appearance: { ...config?.icon?.appearance, background: iconBg, color: iconColor } }}
          title={<span style={{ color: fontColor }}>{editContent.title}</span>}
        />
        {/* Subtítulo */}
        {editContent.subtitle && (
          <div className="text-sm opacity-80 mb-1" style={{ color: fontColor }}>{editContent.subtitle}</div>
        )}
        {/* Conteúdo principal */}
        {editContent.text && (
          <div className="text-base mb-2" style={{ color: fontColor }}>{editContent.text}</div>
        )}
        {/* Botão de ação */}
        {editContent.actionButton?.text && (() => {
          // Configurações de tamanho do botão (apenas largura, altura fixa)
          const buttonSize = safeConfig.button?.size || 'medium';
          const buttonPosition = safeConfig.button?.position || 'bottom-center';

          const getWidthStyles = (size: string) => {
            switch (size) {
              case 'small':
                return {
                  width: 'auto',
                  maxWidth: '100%', // Não pode exceder o container
                };
              case 'large':
                return {
                  width: '100%',
                  maxWidth: '100%', // Largura total respeitando limites
                };
              default: // medium
                return {
                  width: '50%',
                  minWidth: '120px', // Largura mínima para legibilidade
                  maxWidth: '100%', // Não pode exceder o container
                };
            }
          };

          // Configurações de posição do botão (posicionamento absoluto em relação ao card)
          const getPositionStyles = (position: string) => {
            const baseStyles = {
              position: 'absolute' as const,
              zIndex: 10,
              maxWidth: 'calc(100% - 24px)', // Garante que não saia do card (12px de cada lado)
            };

            switch (position) {
              case 'top-left':
                return { ...baseStyles, top: '12px', left: '12px' };
              case 'top-center':
                return {
                  ...baseStyles,
                  top: '12px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  maxWidth: 'calc(100% - 24px)', // Para posições centralizadas
                };
              case 'top-right':
                return { ...baseStyles, top: '12px', right: '12px' };
              case 'bottom-left':
                return { ...baseStyles, bottom: '12px', left: '12px' };
              case 'bottom-center':
                return {
                  ...baseStyles,
                  bottom: '12px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  maxWidth: 'calc(100% - 24px)', // Para posições centralizadas
                };
              case 'bottom-right':
                return { ...baseStyles, bottom: '12px', right: '12px' };
              default:
                return {
                  ...baseStyles,
                  bottom: '12px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  maxWidth: 'calc(100% - 24px)', // default: bottom-center
                };
            }
          };

          const widthStyles = getWidthStyles(buttonSize);
          const positionStyles = getPositionStyles(buttonPosition);

          // Estilos do container (posição + largura responsiva)
          const containerStyles = {
            ...positionStyles,
            ...widthStyles,
            maxWidth: 'calc(100% - 24px)', // Sempre respeitar limites do card
          };

          // Estilos do botão (configurações do usuário)
          const buttonStyles = {
            background: safeConfig.button?.backgroundColor || buttonBg,
            color: safeConfig.button?.color || buttonColor,
            borderRadius: safeConfig.button?.style === 'pill' ? 9999 : safeConfig.button?.style === 'square' ? 0 : 8,
            border: safeConfig.button?.border?.enabled ? `${safeConfig.button?.border?.width || 1}px solid ${safeConfig.button?.border?.color || buttonColor}` : 'none',
            boxShadow: safeConfig.button?.shadow?.enabled ? `0 ${(safeConfig.button?.shadow?.depth || 2) * 2}px ${(safeConfig.button?.shadow?.depth || 2) * 4}px rgba(0,0,0,${(safeConfig.button?.shadow?.depth || 2) * 0.1})` : 'none',
            transition: safeConfig.button?.hover?.enabled ? 'all 0.2s' : 'none',
            fontWeight: 600,
            cursor: 'pointer',
            width: '100%', // Ocupa 100% do container
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          };

          return (
            <div style={containerStyles}>
              <button
                style={buttonStyles}
                className="responsive-button"
              onMouseEnter={e => {
                if (safeConfig.button?.hover?.enabled) {
                  const hoverDepth = safeConfig.button?.hover?.shadowDepth || 3;
                  (e.currentTarget as HTMLButtonElement).style.transform = 'scale(1.05)';
                  (e.currentTarget as HTMLButtonElement).style.boxShadow = `0 ${hoverDepth * 2}px ${hoverDepth * 4}px rgba(0,0,0,${hoverDepth * 0.15})`;
                  (e.currentTarget as HTMLButtonElement).style.filter = 'brightness(1.05)';
                }
              }}
              onMouseLeave={e => {
                if (safeConfig.button?.hover?.enabled) {
                  const shadowDepth = safeConfig.button?.shadow?.depth || 2;
                  (e.currentTarget as HTMLButtonElement).style.transform = 'scale(1)';
                  (e.currentTarget as HTMLButtonElement).style.boxShadow = safeConfig.button?.shadow?.enabled ? `0 ${shadowDepth * 2}px ${shadowDepth * 4}px rgba(0,0,0,${shadowDepth * 0.1})` : 'none';
                  (e.currentTarget as HTMLButtonElement).style.filter = 'brightness(1)';
                }
              }}
              >
                {editContent.actionButton.text}
              </button>
            </div>
          );
        })()}
      </div>
    );
  }
  return (
    <div className="flex flex-col gap-1 w-full">
      <BlockCardIcon
        config={safeConfig.icon}
        title={editContent.title}
        content={<p className="text-sm">{editContent.text}</p>}
        truncateText={truncateText}
      />
    </div>
  );
};
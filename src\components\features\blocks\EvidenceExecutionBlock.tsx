import { useState } from 'react';
import { toast } from '../ui/toast';
import { Button } from '../ui/button';
import { evidenceService } from '../../services/evidenceService';
import { useAuth } from '../../hooks/useAuth';
import { Spinner } from '../ui/spinner';

interface EvidenceExecutionBlockProps {
    taskId: string;
    onUploadComplete?: () => void;
}

export const EvidenceExecutionBlock = ({ taskId, onUploadComplete }: EvidenceExecutionBlockProps) => {
    const [isUploading, setIsUploading] = useState(false);
    const { user } = useAuth();

    const handleFileUpload = async (file: File) => {
        if (!user) {
            toast.error('Faça login para enviar evidências');
            return;
        }

        setIsUploading(true);
        try {
            // Verificar tamanho do arquivo
            const MAX_SIZE = 5 * 1024 * 1024; // 5MB
            if (file.size > MAX_SIZE) {
                throw new Error('Arquivo muito grande. Máximo 5MB.');
            }

            await evidenceService.uploadEvidence(taskId, file);
            toast.success('Evidência enviada com sucesso!');
            onUploadComplete?.();
        } catch (error) {
            console.error('Erro no upload:', error);
            toast.error(
                error.message.includes('não autenticado') 
                    ? 'Faça login novamente para enviar evidências'
                    : `Erro ao enviar evidência: ${error.message}`
            );
        } finally {
            setIsUploading(false);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDrop = async (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        
        const file = e.dataTransfer.files[0];
        if (file) {
            await handleFileUpload(file);
        }
    };

    return (
        <div 
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
        >
            <input
                type="file"
                id="file-upload"
                className="hidden"
                onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
            />
            <label htmlFor="file-upload">
                <Button 
                    disabled={isUploading}
                    className="w-full"
                >
                    {isUploading ? (
                        <>
                            <Spinner className="mr-2" />
                            Enviando...
                        </>
                    ) : (
                        'Clique ou arraste arquivos aqui'
                    )}
                </Button>
            </label>
        </div>
    );
};

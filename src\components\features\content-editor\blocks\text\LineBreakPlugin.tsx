import { useEffect } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $getSelection,
  $isRangeSelection,
  KEY_ENTER_COMMAND,
  COMMAND_PRIORITY_LOW,
  $createLineBreakNode,
  $createParagraphNode,
} from 'lexical';
import { $isListItemNode, $isListNode } from '@lexical/list';

const LineBreakPlugin: React.FC = () => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const removeListener = editor.registerCommand(
      KEY_ENTER_COMMAND,
      (event) => {
        const selection = $getSelection();
        
        if (!$isRangeSelection(selection)) {
          return false;
        }

        // Check if Shift+Enter is pressed (for line break)
        if (event && event.shiftKey) {
          event.preventDefault();
          
          // Insert a line break node
          const lineBreak = $createLineBreakNode();
          selection.insertNodes([lineBreak]);
          
          return true; // Prevent default behavior
        }

        // For regular Enter, allow default behavior
        // The ListPlugin will handle list-specific logic
        return false;
      },
      COMMAND_PRIORITY_LOW
    );

    return removeListener;
  }, [editor]);

  return null;
};

export default LineBreakPlugin;
-- =====================================================
-- CORREÇÃO SIMPLES: RLS PROFILES PARA AUTOCOMPLETE
-- =====================================================
-- Versão mais permissiva para resolver problema de autocomplete

-- REMOVER POLÍTICA ATUAL
DROP POLICY IF EXISTS "profiles_collaboration" ON public.profiles;
DROP POLICY IF EXISTS "profiles_autocomplete_friendly" ON public.profiles;

-- POLÍTICA SIMPLES E FUNCIONAL
-- Permite ver todos os usuários ativos para facilitar colaboração
CREATE POLICY "profiles_simple_autocomplete" ON public.profiles FOR SELECT USING (
  id = auth.uid() OR    -- Próprio perfil sempre
  is_active = true      -- Todos os usuários ativos
);

-- LOGS
DO $$
BEGIN
  RAISE NOTICE '✅ POLÍTICA SIMPLES APLICADA: Todos os usuários ativos visíveis';
  RAISE NOTICE '🎯 RESULTADO: Autocomplete deve mostrar todos os membros ativos';
END $$;

-- =====================================================
-- DEBUG: VERIFICAR CARREGAMENTO DE PROJECT MEMBERS
-- =====================================================
-- Este script vai ajudar a identificar por que o autocomplete não mostra todos os membros

-- =====================================================
-- 1. VERIFICAR SE AINDA HÁ POLÍTICAS RLS BLOQUEANDO
-- =====================================================

-- Ver políticas que ainda existem
SELECT 
    'POLÍTICAS AINDA ATIVAS' as tipo,
    tablename as tabela,
    policyname as politica,
    cmd as operacao
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'project_members', 'projects')
ORDER BY tablename;

-- =====================================================
-- 2. TESTAR ACESSO DIRETO ÀS TABELAS
-- =====================================================

-- Testar profiles (base do autocomplete)
SELECT 
    'PROFILES ACESSÍVEIS' as teste,
    COUNT(*) as total,
    COUNT(CASE WHEN is_active = true THEN 1 END) as ativos
FROM profiles;

-- Amostra de profiles
SELECT 
    id, 
    name, 
    email, 
    is_active,
    role
FROM profiles 
WHERE is_active = true
LIMIT 5;

-- =====================================================
-- 3. TESTAR PROJECT_MEMBERS
-- =====================================================

-- Verificar se project_members é acessível
SELECT 
    'PROJECT_MEMBERS ACESSÍVEIS' as teste,
    COUNT(*) as total
FROM project_members;

-- Ver estrutura de project_members
SELECT 
    pm.id,
    pm.project_id,
    pm.user_id,
    pm.role as member_role,
    'ESTRUTURA PROJECT_MEMBERS' as tipo
FROM project_members pm
LIMIT 3;

-- =====================================================
-- 4. TESTAR JOIN PROFILES + PROJECT_MEMBERS
-- =====================================================

-- Simular a query que o serviço faz
SELECT 
    pm.id,
    pm.role,
    p.id as profile_id,
    p.name,
    p.email,
    p.avatar_url,
    'JOIN PROFILES + PROJECT_MEMBERS' as teste
FROM project_members pm
JOIN profiles p ON p.id = pm.user_id
WHERE pm.project_id = (SELECT id FROM projects LIMIT 1)
LIMIT 5;

-- =====================================================
-- 5. VERIFICAR SE SUPABASE SELECT COM JOIN FUNCIONA
-- =====================================================

-- Testar a query exata do serviço
SELECT 
    pm.id,
    pm.role,
    json_build_object(
        'id', p.id,
        'name', p.name,
        'email', p.email,
        'avatar_url', p.avatar_url
    ) as profile
FROM project_members pm
JOIN profiles p ON p.id = pm.user_id
WHERE pm.project_id = (SELECT id FROM projects LIMIT 1);

-- =====================================================
-- 6. DIAGNÓSTICO FINAL
-- =====================================================

-- Resumo para debug
SELECT 
    'DIAGNÓSTICO FINAL' as categoria,
    'Total profiles' as item,
    COUNT(*)::TEXT as valor
FROM profiles

UNION ALL

SELECT 
    '',
    'Profiles ativos',
    COUNT(*)::TEXT
FROM profiles 
WHERE is_active = true

UNION ALL

SELECT 
    '',
    'Total project_members',
    COUNT(*)::TEXT
FROM project_members

UNION ALL

SELECT 
    '',
    'Total projects',
    COUNT(*)::TEXT
FROM projects

UNION ALL

SELECT 
    '',
    'Policies RLS restantes',
    COUNT(*)::TEXT
FROM pg_policies 
WHERE schemaname = 'public';

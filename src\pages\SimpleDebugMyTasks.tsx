import React, { useState, useEffect } from 'react';
import { useAuth } from '@/auth/useAuth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabaseClient';

export const SimpleDebugMyTasks = () => {
  const { user } = useAuth();
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const runSimpleDiagnostic = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    const testResults = [];

    try {
      // Teste 1: Informações do usuário
      testResults.push({
        test: '👤 Usuário Atual',
        result: `ID: ${user.id}`,
        status: 'info',
        data: { 
          id: user.id, 
          email: user.email 
        }
      });

      // Teste 2: Verificar tabela task_executors
      console.log('🔍 Verificando tabela task_executors...');
      const { data: allExecutors, error: allExecError } = await supabase
        .from('task_executors')
        .select('*');

      testResults.push({
        test: '📊 Tabela task_executors',
        result: `Total: ${allExecutors?.length || 0} registros`,
        status: allExecutors?.length ? 'success' : 'warning',
        data: allExecutors?.slice(0, 5) // Apenas os primeiros 5
      });

      // Teste 3: Verificar para o usuário específico
      console.log('🔍 Verificando executores para o usuário atual...');
      const { data: userExecutors, error: userExecError } = await supabase
        .from('task_executors')
        .select('*')
        .eq('user_id', user.id);

      testResults.push({
        test: '🎯 Executores do Usuário Atual',
        result: `Encontrados: ${userExecutors?.length || 0} registros`,
        status: userExecutors?.length ? 'success' : 'warning',
        data: userExecutors
      });

      // Teste 4: Verificar tarefas no sistema
      console.log('🔍 Verificando tarefas no sistema...');
      const { data: allTasks, error: allTasksError } = await supabase
        .from('tasks')
        .select('id, title, status, created_at')
        .limit(5);

      testResults.push({
        test: '📋 Tarefas no Sistema',
        result: `Total verificado: ${allTasks?.length || 0} tarefas`,
        status: allTasks?.length ? 'success' : 'error',
        data: allTasks
      });

      // Teste 5: Verificar estágios
      console.log('🔍 Verificando estágios...');
      const { data: allStages, error: allStagesError } = await supabase
        .from('stages')
        .select('id, name, project_id')
        .limit(5);

      testResults.push({
        test: '🏗️ Estágios no Sistema',
        result: `Total verificado: ${allStages?.length || 0} estágios`,
        status: allStages?.length ? 'success' : 'error',
        data: allStages
      });

      // Teste 6: Verificar projetos
      console.log('🔍 Verificando projetos...');
      const { data: allProjects, error: allProjectsError } = await supabase
        .from('projects')
        .select('id, name, created_at')
        .limit(5);

      testResults.push({
        test: '📁 Projetos no Sistema',
        result: `Total verificado: ${allProjects?.length || 0} projetos`,
        status: allProjects?.length ? 'success' : 'error',
        data: allProjects
      });

      // Teste 7: Simular query da aplicação
      console.log('🔍 Simulando query da aplicação...');
      try {
        const { data: executorTasks, error: executorError } = await supabase
          .from('task_executors')
          .select('task_id, created_at')
          .eq('user_id', user.id);

        if (executorError) {
          testResults.push({
            test: '⚠️ Erro na Query Principal',
            result: `Erro: ${executorError.message}`,
            status: 'error',
            data: executorError
          });
        } else {
          testResults.push({
            test: '🎯 Query Principal - task_executors',
            result: `Resultado: ${executorTasks?.length || 0} tarefas como executor`,
            status: executorTasks?.length ? 'success' : 'warning',
            data: executorTasks
          });

          // Se encontrou tarefas, buscar detalhes
          if (executorTasks && executorTasks.length > 0) {
            const taskIds = executorTasks.map(et => et.task_id);
            const { data: taskDetails, error: taskError } = await supabase
              .from('tasks')
              .select('id, title, status, stage_id')
              .in('id', taskIds);

            testResults.push({
              test: '📝 Detalhes das Tarefas',
              result: `Detalhes: ${taskDetails?.length || 0} tarefas encontradas`,
              status: taskDetails?.length ? 'success' : 'warning',
              data: taskDetails
            });
          }
        }
      } catch (simError: any) {
        testResults.push({
          test: '❌ Erro na Simulação',
          result: `Erro: ${simError.message}`,
          status: 'error',
          data: simError
        });
      }

      // Teste 8: Verificar se há algum executor no sistema
      console.log('🔍 Verificando todos os executores...');
      const { data: distinctExecutors, error: distinctError } = await supabase
        .from('task_executors')
        .select('user_id');

      const uniqueExecutors = distinctExecutors ? [...new Set(distinctExecutors.map(e => e.user_id))] : [];

      testResults.push({
        test: '👥 Executores Únicos no Sistema',
        result: `Total: ${uniqueExecutors.length} usuários diferentes como executores`,
        status: uniqueExecutors.length ? 'success' : 'warning',
        data: uniqueExecutors.slice(0, 5)
      });

    } catch (error: any) {
      testResults.push({
        test: '❌ Erro Geral',
        result: error.message,
        status: 'error',
        data: error
      });
    } finally {
      setResults(testResults);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      runSimpleDiagnostic();
    }
  }, [user?.id]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'border-green-500 bg-green-50';
      case 'warning': return 'border-yellow-500 bg-yellow-50';
      case 'error': return 'border-red-500 bg-red-50';
      default: return 'border-blue-500 bg-blue-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return 'ℹ️';
    }
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>🔍 Diagnóstico Simples - Minhas Tarefas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded border-l-4 border-blue-500">
              <h3 className="font-semibold">📋 Consulta Atual:</h3>
              <code className="text-sm bg-gray-100 p-2 rounded block mt-2">
                SELECT task_id, created_at FROM task_executors WHERE user_id = '{user?.id}'
              </code>
            </div>

            <Button 
              onClick={runSimpleDiagnostic} 
              disabled={loading}
              className="mb-4"
            >
              {loading ? 'Executando Diagnóstico...' : 'Executar Diagnóstico'}
            </Button>

            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded border-l-4 ${getStatusColor(result.status)}`}
                >
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    {result.test}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1 font-medium">{result.result}</p>
                  {result.data && (
                    <details className="mt-3">
                      <summary className="cursor-pointer text-sm text-blue-600 hover:text-blue-800">
                        👀 Ver dados ({Array.isArray(result.data) ? result.data.length : 1} registros)
                      </summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-60 border">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>

            {results.length > 0 && (
              <div className="mt-8 p-4 bg-gray-50 rounded border">
                <h3 className="font-semibold mb-2">📊 Resumo do Diagnóstico:</h3>
                <ul className="space-y-1 text-sm">
                  <li>✅ Sucessos: {results.filter(r => r.status === 'success').length}</li>
                  <li>⚠️ Avisos: {results.filter(r => r.status === 'warning').length}</li>
                  <li>❌ Erros: {results.filter(r => r.status === 'error').length}</li>
                  <li>ℹ️ Informações: {results.filter(r => r.status === 'info').length}</li>
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

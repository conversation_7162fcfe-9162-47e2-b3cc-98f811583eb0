import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';

// Padrões de syntax highlighting para diferentes linguagens
const SYNTAX_PATTERNS = {
  sql: [
    { regex: /\b(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|TABLE|INDEX|DATABASE|JOIN|INNER|LEFT|RIGHT|OUTER|ON|GROUP|ORDER|BY|HAVING|LIMIT|OFFSET|UNION|ALL|DISTINCT|AS|AND|OR|NOT|IN|EXISTS|BETWEEN|LIKE|IS|NULL|PRIMARY|KEY|FOREIGN|REFERENCES|CONSTRAINT|DEFAULT|AUTO_INCREMENT|UNIQUE|CHECK)\b/gi, class: 'sql-keyword' },
    { regex: /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'sql-string' },
    { regex: /--.*$/gm, class: 'sql-comment' },
    { regex: /\/\*[\s\S]*?\*\//g, class: 'sql-comment' },
    { regex: /\b\d+(\.\d+)?\b/g, class: 'sql-number' },
  ],
  javascript: [
    { regex: /\b(function|const|let|var|if|else|for|while|return|class|extends|import|export|from|default|async|await|try|catch|finally|throw|new|this|super|static|public|private|protected)\b/g, class: 'js-keyword' },
    { regex: /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'js-string' },
    { regex: /\/\/.*$/gm, class: 'js-comment' },
    { regex: /\/\*[\s\S]*?\*\//g, class: 'js-comment' },
    { regex: /\b\d+(\.\d+)?\b/g, class: 'js-number' },
    { regex: /\b(true|false|null|undefined)\b/g, class: 'js-boolean' },
  ],
  python: [
    { regex: /\b(def|class|if|elif|else|for|while|return|import|from|as|try|except|finally|raise|with|lambda|yield|global|nonlocal|pass|break|continue|and|or|not|in|is)\b/g, class: 'py-keyword' },
    { regex: /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'py-string' },
    { regex: /#.*$/gm, class: 'py-comment' },
    { regex: /\b\d+(\.\d+)?\b/g, class: 'py-number' },
    { regex: /\b(True|False|None)\b/g, class: 'py-boolean' },
  ],
  typescript: [
    { regex: /\b(function|const|let|var|if|else|for|while|return|class|extends|import|export|from|default|async|await|try|catch|finally|throw|new|this|super|static|public|private|protected|interface|type|enum|namespace|declare|readonly)\b/g, class: 'ts-keyword' },
    { regex: /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'ts-string' },
    { regex: /\/\/.*$/gm, class: 'ts-comment' },
    { regex: /\/\*[\s\S]*?\*\//g, class: 'ts-comment' },
    { regex: /\b\d+(\.\d+)?\b/g, class: 'ts-number' },
    { regex: /\b(true|false|null|undefined)\b/g, class: 'ts-boolean' },
    { regex: /:\s*([A-Z][a-zA-Z0-9]*)/g, class: 'ts-type' },
  ],
};

/**
 * Extensão para syntax highlighting em code blocks
 */
export const CodeBlockHighlightExtension = Extension.create({
  name: 'codeBlockHighlight',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('codeBlockHighlight'),
        state: {
          init() {
            return DecorationSet.empty;
          },
          apply(tr, decorationSet) {
            // Mapear decorações existentes
            decorationSet = decorationSet.map(tr.mapping, tr.doc);

            // Encontrar code blocks e aplicar highlighting
            const decorations: Decoration[] = [];

            tr.doc.descendants((node, pos) => {
              if (node.type.name === 'codeBlock') {
                const language = node.attrs.language || 'text';
                const patterns = SYNTAX_PATTERNS[language as keyof typeof SYNTAX_PATTERNS];

                if (patterns && node.textContent) {
                  const text = node.textContent;
                  
                  patterns.forEach(({ regex, class: className }) => {
                    let match;
                    const globalRegex = new RegExp(regex.source, regex.flags);
                    
                    while ((match = globalRegex.exec(text)) !== null) {
                      const from = pos + 1 + match.index;
                      const to = from + match[0].length;
                      
                      decorations.push(
                        Decoration.inline(from, to, {
                          class: `syntax-highlight ${className}`,
                        })
                      );
                    }
                  });
                }
              }
            });

            return decorationSet.add(tr.doc, decorations);
          },
        },
        props: {
          decorations(state) {
            return this.getState(state);
          },
        },
      }),
    ];
  },
});

export default CodeBlockHighlightExtension;

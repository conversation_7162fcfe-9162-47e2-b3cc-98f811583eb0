import { supabase } from '@/lib/supabaseClient';
import { requireAuth } from '@/lib/authUtils';

export const taskService = {
  async list(stageId: string) {
    await requireAuth(); // Verificar autenticação
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        description,
        estimated_hours,
        actual_hours,
        due_date,
        status,
        priority,
        assigned_to,
        created_by,
        completed_at,
        created_at,
        updated_at,
        stage_id,
        responsible:profiles!tasks_assigned_to_fkey(id, name, email, avatar_url, role, is_active)
      `)
      .eq('stage_id', stageId);
    if (error) throw error;
    return data;
  },

  async getById(id: string) {
    await requireAuth(); // Verificar autenticação
    const { data, error } = await supabase
      .from('tasks')
      .select('id,title,description,status,priority,assigned_to,created_by,estimated_hours,actual_hours,due_date,completed_at,created_at,updated_at,stage_id')
      .eq('id', id)
      .single();
    if (error) throw error;
    return data;
  },

  async getFullById(id: string) {
    // Busca a tarefa principal
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select('id,title,description,status,priority,assigned_to,created_by,estimated_hours,actual_hours,due_date,completed_at,created_at,updated_at,stage_id')
      .eq('id', id)
      .single();
    if (taskError) throw taskError;

    // Busca executores
    const { data: executors, error: execError } = await supabase.from('task_executors').select('user_id').eq('task_id', id);
    console.log('[getFullById] executors:', executors, execError);
    let executorsFull = [];
    if (executors && executors.length > 0) {
      executorsFull = (
        await Promise.all(
          executors.map(async (e: any) => {
            const user = await import('@/services/userService').then(m => m.userService.getById(e.user_id));
            console.log('[getFullById] executor user:', user);
            return user;
          })
        )
      ).filter(Boolean);
    }

    // Busca aprovadores
    const { data: approvers } = await supabase.from('task_approvers').select('user_id').eq('task_id', id);
    
    // Busca blocos de conteúdo com logs detalhados
    console.log('[taskService.getFullById] Buscando contentBlocks para task:', id);
    const { data: contentBlocks, error: contentBlocksError } = await supabase
      .from('task_content_blocks')
      .select('*')
      .eq('task_id', id)
      .order('order', { ascending: true });
    
    console.log('[taskService.getFullById] contentBlocks result:', {
      data: contentBlocks,
      error: contentBlocksError,
      count: contentBlocks?.length || 0
    });
    
    if (contentBlocksError) {
      console.error('[taskService.getFullById] Erro ao buscar contentBlocks:', contentBlocksError);
    }
    
    // Busca arquivos/evidências
    const { data: attachments } = await supabase.from('evidence').select('*').eq('task_id', id);
    // Busca comentários (e replies)
    const { data: comments } = await supabase.from('task_comments').select('*').eq('task_id', id);

    return {
      ...task,
      executors: executorsFull,
      approvers,
      contentBlocks,
      attachments,
      comments,
    };
  },

  async create(task: any) {
    const { data, error } = await supabase.from('tasks').insert([task]).select().single();
    if (error) {
      console.error('[taskService.create] Erro ao inserir tarefa:', error, 'Payload:', task);
      throw error;
    }
    if (!data) {
      console.error('[taskService.create] Insert de tarefa não retornou data! Payload:', task);
      throw new Error('Falha ao criar tarefa: insert não retornou dados.');
    }
    return data;
  },

  async update(id: string, updates: any) {
    const { data, error } = await supabase.from('tasks').update(updates).eq('id', id).single();
    if (error) throw error;
    return data;
  },

  async remove(id: string) {
    const { error } = await supabase.from('tasks').delete().eq('id', id);
    if (error) throw error;
  },

  // Blocos de conteúdo: para blocos do tipo 'text', o campo 'text' deve ser JSON Lexical (stringificada)
  async addContentBlock(taskId: string, block: any) {
    const { data, error } = await supabase.from('task_content_blocks').insert([{ ...block, task_id: taskId }]).select().single();
    if (error) throw error;
    return data;
  },

  async updateContentBlock(blockId: string, updates: any) {
    const { data, error } = await supabase.from('task_content_blocks').update(updates).eq('id', blockId).select().single();
    if (error) throw error;
    return data;
  },

  async deleteContentBlock(blockId: string) {
    const { error } = await supabase.from('task_content_blocks').delete().eq('id', blockId);
    if (error) throw error;
  },

  async saveContentBlocks(taskId: string, blocks: any[]) {
    // Estratégia simples: remove todos e insere todos novamente (pode ser otimizado depois)
    const { error: delError } = await supabase.from('task_content_blocks').delete().eq('task_id', taskId);
    if (delError) throw delError;
    if (blocks.length === 0) return [];
    const blocksToInsert = blocks.map((b, idx) => ({ ...b, task_id: taskId, order: idx }));
    const { data, error } = await supabase.from('task_content_blocks').insert(blocksToInsert).select();
    if (error) throw error;
    return data;
  },

  // Funções para gerenciar aprovadores
  async addApprovers(taskId: string, userIds: string[]) {
    await requireAuth();
    const approversToInsert = userIds.map(userId => ({
      task_id: taskId,
      user_id: userId
    }));

    const { data, error } = await supabase
      .from('task_approvers')
      .insert(approversToInsert)
      .select();

    if (error) throw error;
    return data;
  },

  async removeApprover(taskId: string, userId: string) {
    await requireAuth();
    const { error } = await supabase
      .from('task_approvers')
      .delete()
      .eq('task_id', taskId)
      .eq('user_id', userId);

    if (error) throw error;
  },

  async getApprovers(taskId: string) {
    await requireAuth();
    const { data, error } = await supabase
      .from('task_approvers')
      .select(`
        user_id,
        profiles!task_approvers_user_id_fkey(id, name, email, avatar_url)
      `)
      .eq('task_id', taskId);

    if (error) throw error;
    return data?.map(item => item.profiles) || [];
  },

  // Funções para gerenciar executores
  async addExecutors(taskId: string, userIds: string[]) {
    await requireAuth();
    const executorsToInsert = userIds.map(userId => ({
      task_id: taskId,
      user_id: userId,
      created_at: new Date().toISOString()
    }));

    const { data, error } = await supabase
      .from('task_executors')
      .insert(executorsToInsert)
      .select();

    if (error) throw error;
    return data;
  },

  async removeExecutor(taskId: string, userId: string) {
    await requireAuth();
    const { error } = await supabase
      .from('task_executors')
      .delete()
      .eq('task_id', taskId)
      .eq('user_id', userId);

    if (error) throw error;
  },

  async getExecutors(taskId: string) {
    await requireAuth();
    const { data, error } = await supabase
      .from('task_executors')
      .select(`
        user_id,
        profiles!task_executors_user_id_fkey(id, name, email, avatar_url)
      `)
      .eq('task_id', taskId);

    if (error) throw error;
    return data?.map(item => item.profiles) || [];
  },

  // Funções para atualizar status e progresso
  async updateStatus(taskId: string, status: string) {
    await requireAuth();
    const { data, error } = await supabase
      .from('tasks')
      .update({ status })
      .eq('id', taskId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async updateProgress(taskId: string, progress: number) {
    await requireAuth();
    const { data, error } = await supabase
      .from('tasks')
      .update({ progress })
      .eq('id', taskId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Função para adicionar comentários
  async addComment(taskId: string, content: string) {
    await requireAuth();
    const user = await requireAuth();

    const { data, error } = await supabase
      .from('task_comments')
      .insert({
        task_id: taskId,
        author_id: user.id,
        content,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { stageService } from './stageService';
import { supabaseMock, mockSingleGetById, mockSingleCreate, mockSingleUpdate, mockEqList } from '../test-utils/supabaseMock';

vi.mock('@/lib/supabaseClient', () => ({ supabase: supabaseMock }));

describe('stageService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('deve retornar lista de etapas', async () => {
    mockEqList.mockReturnValueOnce({ data: [{ id: '1', name: 'Etapa Teste' }], error: null });
    const etapas = await stageService.list('project-1');
    expect(etapas).toEqual([{ id: '1', name: 'Etapa Teste' }]);
  });

  it('deve retornar etapa por id', async () => {
    mockSingleGetById.mockReturnValueOnce({ data: { id: '1', name: 'Etapa Teste' }, error: null });
    const etapa = await stageService.getById('1');
    expect(etapa).toEqual({ id: '1', name: 'Etapa Teste' });
  });

  it('deve criar uma nova etapa', async () => {
    mockSingleCreate.mockReturnValueOnce({ data: { id: '2', name: 'Nova Etapa' }, error: null });
    const etapa = await stageService.create({ name: 'Nova Etapa' });
    expect(etapa).toEqual({ id: '2', name: 'Nova Etapa' });
  });

  it('deve atualizar uma etapa', async () => {
    mockSingleUpdate.mockReturnValueOnce({ data: { id: '1', name: 'Etapa Atualizada' }, error: null });
    const etapa = await stageService.update('1', { name: 'Etapa Atualizada' });
    expect(etapa).toEqual({ id: '1', name: 'Etapa Atualizada' });
  });

  it('deve remover uma etapa', async () => {
    await expect(stageService.remove('1')).resolves.toBeUndefined();
  });

  it('deve lançar erro se houver erro no supabase', async () => {
    mockEqList.mockReturnValueOnce({ data: null, error: new Error('Erro Supabase') });
    await expect(stageService.list('project-1')).rejects.toThrow('Erro Supabase');
  });
}); 
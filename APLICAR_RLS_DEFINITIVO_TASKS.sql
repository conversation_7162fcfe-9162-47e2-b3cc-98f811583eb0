-- =====================================================
-- APLICAR POLÍTICAS RLS DEFINITIVAS PARA TASKS - BASEADO NO SCHEMA
-- =====================================================
-- Baseado em: supabase_schema.sql (políticas comentadas)
-- Problema: Políticas estão comentadas no schema, causando erro 406

-- <PERSON><PERSON>, garantir que RLS está habilitado
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Remover todas as políticas existentes para tasks
DROP POLICY IF EXISTS "Project members can view tasks" ON public.tasks;
DROP POLICY IF EXISTS "Project members can insert tasks" ON public.tasks;
DROP POLICY IF EXISTS "Project members can update tasks" ON public.tasks;
DROP POLICY IF EXISTS "Project members can delete tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can view accessible tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can create tasks in owned projects" ON public.tasks;
DROP POLICY IF EXISTS "Users can update accessible tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can delete tasks in owned projects" ON public.tasks;
DROP POLICY IF EXISTS "Users can create tasks in projects" ON public.tasks;

-- =====================================================
-- APLICAR POLÍTICAS BASEADAS NO SUPABASE_SCHEMA.SQL
-- =====================================================

-- Política para visualizar tasks: membros do projeto, executores e aprovadores
CREATE POLICY "Project members can view tasks"
  ON public.tasks
  FOR SELECT
  USING (
    -- Admin pode ver todas as tasks
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    ) OR
    -- Owner do projeto pode ver todas as tasks
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
    ) OR
    -- Membros do projeto podem ver tasks
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      JOIN public.project_members pm ON pm.project_id = p.id
      WHERE pm.user_id = auth.uid()
    ) OR
    -- Executores podem ver suas tasks
    id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    ) OR
    -- Aprovadores podem ver tasks que precisam aprovar
    id IN (
      SELECT ta.task_id FROM public.task_approvers ta
      WHERE ta.user_id = auth.uid()
    )
  );

-- Política para inserir tasks: owners e membros com permissão de editor/manager
CREATE POLICY "Project members can insert tasks"
  ON public.tasks
  FOR INSERT
  WITH CHECK (
    -- Admin pode inserir em qualquer lugar
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    ) OR
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'editor', 'admin')
         )
    )
  );

-- Política para atualizar tasks: owners, managers, editors e executores das suas próprias tasks
CREATE POLICY "Project members can update tasks"
  ON public.tasks
  FOR UPDATE
  USING (
    -- Admin pode atualizar qualquer task
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    ) OR
    -- Owner do projeto pode atualizar
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
    ) OR
    -- Membros com papel adequado podem atualizar
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      JOIN public.project_members pm ON pm.project_id = p.id
      WHERE pm.user_id = auth.uid() 
      AND pm.role IN ('manager', 'editor', 'admin')
    ) OR
    -- Executores podem atualizar suas próprias tasks
    id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

-- Política para deletar tasks: apenas owners e managers
CREATE POLICY "Project members can delete tasks"
  ON public.tasks
  FOR DELETE
  USING (
    -- Admin pode deletar qualquer task
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    ) OR
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'admin')
         )
    )
  );

-- =====================================================
-- VALIDAÇÃO E DIAGNÓSTICO
-- =====================================================

-- Verificar se as políticas foram aplicadas
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'tasks';
    
    IF policy_count >= 4 THEN
        RAISE NOTICE '✅ Políticas RLS para tasks aplicadas com sucesso! (%)', policy_count;
    ELSE
        RAISE EXCEPTION '❌ Falha ao aplicar políticas RLS. Apenas % políticas encontradas', policy_count;
    END IF;
END $$;

-- Listar todas as políticas ativas para tasks
SELECT 
    'Política ativa: ' || policyname as status,
    cmd as definicao
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'tasks'
ORDER BY policyname;

-- Teste de acesso básico
SELECT 
    'Teste de acesso concluído!' as resultado,
    COUNT(*) as tasks_acessiveis
FROM public.tasks
LIMIT 1;

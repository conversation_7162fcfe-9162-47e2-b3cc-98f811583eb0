-- =====================================================
-- DIAGNOSTICAR PROBLEMA: TELA EXECUÇÃO PARA MEMBROS
-- =====================================================

-- STEP 1: VERIFICAR USUÁRIO ATUAL COMO EXECUTOR
SELECT
    '🔍 VERIFICANDO USUÁRIO COMO EXECUTOR' as diagnostico,
    u.id as user_id,
    u.email as user_email,
    p.name as profile_name,
    p.role as profile_role,
    COUNT(te.task_id) as total_tasks_executor
FROM auth.users u
JOIN profiles p ON u.id = p.id
LEFT JOIN task_executors te ON u.id = te.user_id
GROUP BY u.id, u.email, p.name, p.role
ORDER BY u.email;

-- STEP 2: <PERSON><PERSON><PERSON>R TODAS AS TAREFAS QUE USUÁRIOS SÃO EXECUTORES
SELECT
    '📋 TAREFAS COMO EXECUTOR' as categoria,
    u.email as user_email,
    t.id as task_id,
    t.title as task_title,
    t.status as task_status,
    s.name as stage_name,
    proj.name as project_name,
    te.created_at as added_as_executor_at
FROM task_executors te
JOIN auth.users u ON te.user_id = u.id
JOIN tasks t ON te.task_id = t.id
JOIN stages s ON t.stage_id = s.id
JOIN projects proj ON s.project_id = proj.id
ORDER BY te.created_at DESC
LIMIT 10;

-- STEP 3: VERIFICAR SE HÁ TAREFAS NO SISTEMA
SELECT
    '📊 RESUMO GERAL TAREFAS' as categoria,
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as tasks_pending,
    COUNT(CASE WHEN t.status = 'in-progress' THEN 1 END) as tasks_in_progress,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as tasks_completed
FROM tasks t;

-- STEP 4: VERIFICAR RELAÇÃO TASK_EXECUTORS
SELECT
    '👥 EXECUTORES POR TAREFA' as categoria,
    COUNT(DISTINCT te.task_id) as tasks_with_executors,
    COUNT(DISTINCT te.user_id) as unique_executors,
    COUNT(*) as total_executor_assignments
FROM task_executors te;

-- STEP 5: VERIFICAR PROFILES E USUÁRIOS
SELECT
    '👤 USUÁRIOS E PROFILES' as categoria,
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT p.id) as total_profiles,
    COUNT(CASE WHEN p.role = 'member' THEN 1 END) as members,
    COUNT(CASE WHEN p.role = 'admin' THEN 1 END) as admins
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id;

-- CORREÇÃO: Configurar RLS para tabela task_executors

-- 1. Desabilitar RLS temporariamente para testar
ALTER TABLE task_executors DISABLE ROW LEVEL SECURITY;

-- 2. Ou criar uma política RLS adequada (escolha uma opção)
-- Opção A: Permitir que usuários vejam apenas seus próprios registros como executor
CREATE POLICY "Users can view their own executor assignments" ON task_executors
    FOR SELECT USING (auth.uid() = user_id);

-- Opção B: Permitir que usuários vejam todas as atribuições (mais permissivo)
CREATE POLICY "Users can view all executor assignments" ON task_executors
    FOR SELECT USING (true);

-- 3. Habilitar RLS novamente (se usar as políticas acima)
-- ALTER TABLE task_executors ENABLE ROW LEVEL SECURITY;

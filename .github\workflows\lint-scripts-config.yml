name: <PERSON><PERSON> and Configs

on:
  push:
    paths:
      - 'scripts/**/*.js'
      - 'scripts/**/*.ts'
  pull_request:
    paths:
      - 'scripts/**/*.js'
      - 'scripts/**/*.ts'

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run ESLint
        run: npx eslint scripts/**/*.js scripts/**/*.ts 
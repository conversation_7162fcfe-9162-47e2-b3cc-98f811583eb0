-- =====================================================
-- SCRIPT EMERGENCIAL ANTI-RECURSÃO TOTAL
-- =====================================================
-- ESTE SCRIPT RESOLVE O ERRO 42P17 IMEDIATAMENTE
-- Remove TODAS as políticas e recria apenas as essenciais

-- =====================================================
-- LIMPEZA TOTAL IMEDIATA
-- =====================================================

-- Desabilitar RLS
ALTER TABLE IF EXISTS public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.stages DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.project_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_executors DISABLE ROW LEVEL SECURITY;

-- Dropar TODAS as políticas (sem exceção)
DO $$
DECLARE
    policy_record RECORD;
    table_name TEXT;
BEGIN
    FOR table_name IN 
        SELECT t.tablename 
        FROM pg_tables t 
        WHERE t.schemaname = 'public'
    LOOP
        FOR policy_record IN
            SELECT policyname
            FROM pg_policies 
            WHERE tablename = table_name AND schemaname = 'public'
        LOOP
            BEGIN
                EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I CASCADE', 
                              policy_record.policyname, table_name);
            EXCEPTION 
                WHEN OTHERS THEN
                    NULL; -- Ignorar erros
            END;
        END LOOP;
    END LOOP;
    RAISE NOTICE '🧹 TODAS as políticas removidas';
END $$;

-- =====================================================
-- POLÍTICAS ULTRA-SIMPLES (SEM RECURSÃO)
-- =====================================================

-- Reabilitar RLS apenas nas tabelas essenciais
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- PROFILES - Apenas o básico
CREATE POLICY "profiles_select" ON public.profiles FOR SELECT USING (true);
CREATE POLICY "profiles_update" ON public.profiles FOR UPDATE USING (id = auth.uid());
CREATE POLICY "profiles_insert" ON public.profiles FOR INSERT WITH CHECK (id = auth.uid());

-- PROJECTS - Apenas owner, SEM referência a project_members
CREATE POLICY "projects_select" ON public.projects FOR SELECT USING (owner_id = auth.uid());
CREATE POLICY "projects_insert" ON public.projects FOR INSERT WITH CHECK (owner_id = auth.uid());
CREATE POLICY "projects_update" ON public.projects FOR UPDATE USING (owner_id = auth.uid());
CREATE POLICY "projects_delete" ON public.projects FOR DELETE USING (owner_id = auth.uid());

-- STAGES - Apenas owner direto
CREATE POLICY "stages_select" ON public.stages FOR SELECT USING (
  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
);
CREATE POLICY "stages_insert" ON public.stages FOR INSERT WITH CHECK (
  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
);
CREATE POLICY "stages_update" ON public.stages FOR UPDATE USING (
  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
);
CREATE POLICY "stages_delete" ON public.stages FOR DELETE USING (
  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
);

-- TASKS - Apenas owner e assigned
CREATE POLICY "tasks_select" ON public.tasks FOR SELECT USING (
  assigned_to = auth.uid() OR 
  created_by = auth.uid() OR
  stage_id IN (
    SELECT s.id FROM public.stages s 
    WHERE s.project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
  )
);
CREATE POLICY "tasks_insert" ON public.tasks FOR INSERT WITH CHECK (
  created_by = auth.uid() AND
  stage_id IN (
    SELECT s.id FROM public.stages s 
    WHERE s.project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
  )
);
CREATE POLICY "tasks_update" ON public.tasks FOR UPDATE USING (
  assigned_to = auth.uid() OR 
  created_by = auth.uid() OR
  stage_id IN (
    SELECT s.id FROM public.stages s 
    WHERE s.project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
  )
);
CREATE POLICY "tasks_delete" ON public.tasks FOR DELETE USING (
  stage_id IN (
    SELECT s.id FROM public.stages s 
    WHERE s.project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
  )
);

-- =====================================================
-- VALIDAÇÃO FINAL
-- =====================================================

DO $$
DECLARE
  policy_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO policy_count FROM pg_policies WHERE schemaname = 'public';
  RAISE NOTICE '🎯 EMERGÊNCIA RESOLVIDA: % políticas ativas (sem recursão)', policy_count;
  RAISE NOTICE '✅ Erro 42P17 deve estar ELIMINADO';
  RAISE NOTICE '🚀 Sistema básico funcionando';
END $$;

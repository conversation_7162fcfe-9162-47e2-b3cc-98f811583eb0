-- =====================================================
-- CORREÇÃO URGENTE - RECURSÃO INFINITA RLS
-- =====================================================
-- Problema: Erro 42P17 - infinite recursion detected in policy for relation "projects"
-- Solução: Políticas separadas e simplificadas para evitar referências circulares

-- =====================================================
-- PASSO 1: REMOVER POLÍTICAS PROBLEMÁTICAS
-- =====================================================

-- Remover todas as políticas de projects que podem causar recursão
DROP POLICY IF EXISTS "Users can view accessible projects" ON public.projects;
DROP POLICY IF EXISTS "Users can update accessible projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view owned projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view member projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view accessible stages" ON public.stages;
DROP POLICY IF EXISTS "Users can create stages in accessible projects" ON public.stages;
DROP POLICY IF EXISTS "Users can update accessible stages" ON public.stages;
DROP POLICY IF EXISTS "Users can delete accessible stages" ON public.stages;
DROP POLICY IF EXISTS "Users can view stages of owned projects" ON public.stages;
DROP POLICY IF EXISTS "Users can view stages of member projects" ON public.stages;
DROP POLICY IF EXISTS "Users can view accessible tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can update accessible tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can view owned tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can view tasks of owned projects" ON public.tasks;
DROP POLICY IF EXISTS "Users can view tasks as executor" ON public.tasks;
DROP POLICY IF EXISTS "Users can view tasks as approver" ON public.tasks;

-- =====================================================
-- PASSO 2: APLICAR POLÍTICAS SIMPLES (SEM RECURSÃO)
-- =====================================================

-- PROJECTS: Políticas separadas por tipo de acesso
CREATE POLICY "Users can view owned projects"
  ON public.projects
  FOR SELECT
  USING (owner_id = auth.uid());

CREATE POLICY "Users can view member projects"
  ON public.projects
  FOR SELECT
  USING (
    id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid()
    )
  );

-- STAGES: Políticas separadas por tipo de acesso
CREATE POLICY "Users can view stages of owned projects"
  ON public.stages
  FOR SELECT
  USING (
    project_id IN (
      SELECT p.id FROM public.projects p
      WHERE p.owner_id = auth.uid()
    )
  );

CREATE POLICY "Users can view stages of member projects"
  ON public.stages
  FOR SELECT
  USING (
    project_id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid()
    )
  );

-- TASKS: Políticas múltiplas específicas
CREATE POLICY "Users can view owned tasks"
  ON public.tasks
  FOR SELECT
  USING (
    assigned_to = auth.uid() OR
    created_by = auth.uid()
  );

CREATE POLICY "Users can view tasks of owned projects"
  ON public.tasks
  FOR SELECT
  USING (
    stage_id IN (
      SELECT s.id FROM public.stages s
      WHERE s.project_id IN (
        SELECT p.id FROM public.projects p
        WHERE p.owner_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can view tasks as executor"
  ON public.tasks
  FOR SELECT
  USING (
    id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view tasks as approver"
  ON public.tasks
  FOR SELECT
  USING (
    id IN (
      SELECT ta.task_id FROM public.task_approvers ta
      WHERE ta.user_id = auth.uid()
    )
  );

-- =====================================================
-- PASSO 3: VALIDAÇÃO
-- =====================================================

DO $$
BEGIN
  -- Verificar se não há políticas com nomes conflitantes
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'projects'
    AND policyname LIKE '%accessible%'
  ) THEN
    RAISE EXCEPTION 'Ainda existem políticas problemáticas! Execute novamente.';
  END IF;
  
  RAISE NOTICE '✅ RECURSÃO CORRIGIDA!';
  RAISE NOTICE '🔄 Políticas separadas aplicadas';
  RAISE NOTICE '🚫 Políticas recursivas removidas';
  RAISE NOTICE '⚡ Erro 42P17 deve estar resolvido';
END $$;

-- Teste rápido
SELECT 'Teste de acesso projects' as status, COUNT(*) as total_projects_acessiveis
FROM public.projects
LIMIT 1;

-- =====================
-- CORREÇÃO URGENTE: Políticas RLS para task_content_blocks
-- PROBLEMA: Executores não conseguem ver blocos de conteúdo
-- EVIDÊNCIA: taskService.getFullById retorna contentBlocks: []
-- =====================

-- Aplicar correção das políticas RLS
-- Política corrigida para visualizar blocos de conteúdo
DROP POLICY IF EXISTS "Project members can view task content blocks" ON public.task_content_blocks;
CREATE POLICY "Project members can view task content blocks"
  ON public.task_content_blocks
  FOR select
  USING (
    -- Membros do projeto podem ver
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
    )
    OR
    -- CORREÇÃO: Executores da tarefa podem ver
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
    OR
    -- CORREÇÃO: Aprovadores da tarefa podem ver
    task_id IN (
      SELECT ta.task_id FROM public.task_approvers ta
      WHERE ta.user_id = auth.uid()
    )
  );

-- Política corrigida para inserir blocos de conteúdo
DROP POLICY IF EXISTS "Project members can insert task content blocks" ON public.task_content_blocks;
CREATE POLICY "Project members can insert task content blocks"
  ON public.task_content_blocks
  FOR insert
  WITH CHECK (
    -- Membros do projeto com permissão podem inserir
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'editor', 'admin')
         )
    )
    OR
    -- CORREÇÃO: Executores podem inserir blocos
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

-- Política corrigida para atualizar blocos de conteúdo
DROP POLICY IF EXISTS "Project members can update task content blocks" ON public.task_content_blocks;
CREATE POLICY "Project members can update task content blocks"
  ON public.task_content_blocks
  FOR update
  USING (
    -- Membros do projeto com permissão podem atualizar
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'editor', 'admin')
         )
    )
    OR
    -- CORREÇÃO: Executores podem atualizar blocos
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

-- Política corrigida para deletar blocos de conteúdo
DROP POLICY IF EXISTS "Project members can delete task content blocks" ON public.task_content_blocks;
CREATE POLICY "Project members can delete task content blocks"
  ON public.task_content_blocks
  FOR delete
  USING (
    -- Membros do projeto com permissão podem deletar
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'editor', 'admin')
         )
    )
    OR
    -- CORREÇÃO: Executores podem deletar blocos (limitado)
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

-- =====================
-- TESTE IMEDIATO: Verificar se executores podem ver blocos
-- =====================
-- Execute este comando no console do navegador após aplicar as políticas:
-- 1. Recarregue a página
-- 2. Verifique se os logs mostram contentBlocks com dados
-- 3. Deveria aparecer: [taskService.getFullById] contentBlocks: [{...}]

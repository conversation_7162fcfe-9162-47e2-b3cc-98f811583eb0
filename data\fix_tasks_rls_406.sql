-- =====================================================
-- CORREÇÃO RLS PARA TAREFAS - ERRO 406
-- =====================================================
-- Problema: Políticas RLS muito restritivas impedindo acesso às tarefas
-- Erro: 406 Not Acceptable + "JSON object requested, multiple (or no) rows returned"

-- Remover políticas antigas
DROP POLICY IF EXISTS "Users can view accessible tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can create tasks in owned projects" ON public.tasks;
DROP POLICY IF EXISTS "Users can update accessible tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can delete tasks in owned projects" ON public.tasks;

-- =====================================================
-- POLÍTICAS RLS MAIS PERMISSIVAS PARA TASKS
-- =====================================================

-- VISUALIZAR: Usuários podem ver tarefas se:
-- 1. São proprietários do projeto
-- 2. São membros do projeto
-- 3. Estão atribuídos à tarefa
-- 4. Criaram a tarefa
-- 5. São executores da tarefa
-- 6. São aprovadores da tarefa
CREATE POLICY "Users can view accessible tasks"
  ON public.tasks
  FOR SELECT
  USING (
    -- Proprietário do projeto
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    ) OR
    -- Membro do projeto
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.project_members pm ON s.project_id = pm.project_id
      WHERE s.id = stage_id AND pm.user_id = auth.uid()
    ) OR
    -- Tarefa atribuída ao usuário
    assigned_to = auth.uid() OR
    -- Criador da tarefa
    created_by = auth.uid() OR
    -- Executor da tarefa
    EXISTS (
      SELECT 1 FROM public.task_executors te
      WHERE te.task_id = id AND te.user_id = auth.uid()
    ) OR
    -- Aprovador da tarefa
    EXISTS (
      SELECT 1 FROM public.task_approvers ta
      WHERE ta.task_id = id AND ta.user_id = auth.uid()
    )
  );

-- CRIAR: Usuários podem criar tarefas se são proprietários ou membros do projeto
CREATE POLICY "Users can create tasks in projects"
  ON public.tasks
  FOR INSERT
  WITH CHECK (
    created_by = auth.uid() AND
    (
      -- Proprietário do projeto
      EXISTS (
        SELECT 1 FROM public.stages s
        JOIN public.projects p ON s.project_id = p.id
        WHERE s.id = stage_id AND p.owner_id = auth.uid()
      ) OR
      -- Membro do projeto
      EXISTS (
        SELECT 1 FROM public.stages s
        JOIN public.project_members pm ON s.project_id = pm.project_id
        WHERE s.id = stage_id AND pm.user_id = auth.uid()
      )
    )
  );

-- ATUALIZAR: Usuários podem atualizar tarefas se têm acesso a elas
CREATE POLICY "Users can update accessible tasks"
  ON public.tasks
  FOR UPDATE
  USING (
    -- Proprietário do projeto
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    ) OR
    -- Membro do projeto
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.project_members pm ON s.project_id = pm.project_id
      WHERE s.id = stage_id AND pm.user_id = auth.uid()
    ) OR
    -- Tarefa atribuída ao usuário
    assigned_to = auth.uid() OR
    -- Criador da tarefa
    created_by = auth.uid() OR
    -- Executor da tarefa
    EXISTS (
      SELECT 1 FROM public.task_executors te
      WHERE te.task_id = id AND te.user_id = auth.uid()
    ) OR
    -- Aprovador da tarefa
    EXISTS (
      SELECT 1 FROM public.task_approvers ta
      WHERE ta.task_id = id AND ta.user_id = auth.uid()
    )
  );

-- DELETAR: Apenas proprietários do projeto podem deletar tarefas
CREATE POLICY "Users can delete tasks in owned projects"
  ON public.tasks
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    )
  );

-- =====================================================
-- VALIDAÇÃO
-- =====================================================

-- Verificar se as políticas foram aplicadas
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'tasks' 
    AND policyname = 'Users can view accessible tasks'
  ) THEN
    RAISE NOTICE '✅ Políticas RLS para tasks atualizadas com sucesso!';
  ELSE
    RAISE EXCEPTION '❌ Falha ao aplicar políticas RLS para tasks';
  END IF;
END $$;

-- Teste simples para verificar acesso
SELECT 'Políticas RLS atualizadas!' as status, 
       COUNT(*) as total_tasks_acessiveis
FROM public.tasks
LIMIT 1;

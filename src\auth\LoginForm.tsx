import React, { useState, useEffect } from "react";
import { useAuth } from "./useAuth";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { supabase } from "@/lib/supabaseClient";

const LoginForm: React.FC = () => {
  const { login, loading, error, user } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [success, setSuccess] = useState(false);
  const [oauthLoading, setOauthLoading] = useState(false);
  const [oauthError, setOauthError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (user) {
      // Verificar se há URL salva para redirecionamento
      const redirectUrl = localStorage.getItem('redirectAfterLogin');
      if (redirectUrl) {
        localStorage.removeItem('redirectAfterLogin');
        navigate(redirectUrl, { replace: true });
        return;
      }

      // Usar redirecionamento padrão
      const from = (location.state as any)?.from?.pathname || "/";
      navigate(from, { replace: true });
    }
  }, [user, navigate, location.state]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await login(email, password);
    if (!error) setSuccess(true);
  };

  const handleGoogleLogin = async () => {
    setOauthLoading(true);
    setOauthError(null);
    const { error } = await supabase.auth.signInWithOAuth({ provider: "google" });
    if (error) setOauthError(error.message);
    setOauthLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-sm mx-auto">
      <h2 className="text-xl font-bold">Entrar</h2>
      <div>
        <label className="block mb-1">Email</label>
        <input
          type="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="w-full border rounded px-3 py-2"
          required
        />
      </div>
      <div>
        <label className="block mb-1">Senha</label>
        <input
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          className="w-full border rounded px-3 py-2"
          required
        />
      </div>
      {error && <div className="text-red-600 text-sm">{error}</div>}
      {success && <div className="text-green-600 text-sm">Login realizado com sucesso!</div>}
      <button
        type="submit"
        className="w-full bg-blue-600 text-white py-2 rounded disabled:opacity-50"
        disabled={loading}
      >
        {loading ? "Entrando..." : "Entrar"}
      </button>
      <button
        type="button"
        onClick={handleGoogleLogin}
        className="w-full bg-gray-100 text-gray-800 py-2 rounded border mt-2 disabled:opacity-50"
        disabled={oauthLoading}
      >
        {oauthLoading ? "Redirecionando..." : "Entrar com Google"}
      </button>
      {oauthError && <div className="text-red-600 text-sm mt-1">{oauthError}</div>}
      <div className="flex justify-between text-sm mt-2">
        <Link to="/register" className="text-blue-600 hover:underline">Criar conta</Link>
        <Link to="/reset-password" className="text-blue-600 hover:underline">Esqueci a senha</Link>
      </div>
    </form>
  );
};

export default LoginForm; 
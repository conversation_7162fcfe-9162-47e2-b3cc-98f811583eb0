import React, { useState, ForwardRefExoticComponent } from 'react';
import { ALERT_PRESETS, AlertPreset, AlertType } from '../shared/config-panel/constants/block-types';
import { AlertBlock } from './AlertBlock';
import * as LucideIcons from 'lucide-react';
import { isValidElementType } from 'react-is';

interface AlertBlockGalleryProps {
  value: {
    presetId: string;
    title: string;
    message: string;
    icon: string;
    actionLabel?: string;
    actionUrl?: string;
  };
  onChange: (value: {
    presetId: string;
    title: string;
    message: string;
    icon: string;
    actionLabel?: string;
    actionUrl?: string;
  }) => void;
}

function isLucideIconComponent(comp: unknown): comp is ForwardRefExoticComponent<any> {
  return (
    typeof comp === 'function' &&
    Object.prototype.hasOwnProperty.call(comp, 'displayName') &&
    Object.prototype.hasOwnProperty.call(comp, 'render')
  );
}

export const AlertBlockGallery: React.FC<AlertBlockGalleryProps> = ({ value, onChange }) => {
  const [selectedType, setSelectedType] = useState<AlertType>(value.presetId ? (ALERT_PRESETS.find(p => p.id === value.presetId)?.type || 'success') : 'success');

  const filteredPresets = ALERT_PRESETS.filter(p => p.type === selectedType);
  const selectedPreset = ALERT_PRESETS.find(p => p.id === value.presetId) || filteredPresets[0];

  const handleTypeChange = (type: AlertType) => {
    setSelectedType(type);
    const firstPreset = ALERT_PRESETS.find(p => p.type === type) || ALERT_PRESETS[0];
    onChange({
      presetId: firstPreset.id,
      title: firstPreset.defaultTitle,
      message: firstPreset.defaultMessage,
      icon: firstPreset.defaultIcon,
      actionLabel: '',
      actionUrl: '',
    });
  };

  const handlePresetChange = (preset: AlertPreset) => {
    onChange({
      presetId: preset.id,
      title: preset.defaultTitle,
      message: preset.defaultMessage,
      icon: preset.defaultIcon,
      actionLabel: '',
      actionUrl: '',
    });
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-4">
      <h2 className="font-bold text-lg mb-4">Escolha o tipo de alerta</h2>
      <div className="flex gap-2 mb-4">
        {(['success', 'info', 'warning', 'error'] as AlertType[]).map(type => (
          <button
            key={type}
            type="button"
            className={`px-3 py-1 rounded-full font-semibold border transition-all text-sm ${selectedType === type ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-blue-700 border-blue-300 hover:bg-blue-50'}`}
            onClick={() => handleTypeChange(type)}
          >
            {type === 'success' && 'Sucesso'}
            {type === 'info' && 'Informação'}
            {type === 'warning' && 'Aviso'}
            {type === 'error' && 'Erro'}
          </button>
        ))}
      </div>
      <h3 className="font-semibold text-indigo-800 mb-2">Escolha um modelo visual</h3>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-4">
        {filteredPresets.map(preset => (
          <button
            key={preset.id}
            type="button"
            className={`border rounded-md p-1 transition-all text-left ${value.presetId === preset.id ? 'border-blue-600 ring-2 ring-blue-200' : 'border-gray-200 hover:border-blue-400'}`}
            onClick={() => handlePresetChange(preset)}
            aria-label={`Selecionar modelo ${preset.defaultTitle}`}
            style={{ minWidth: 0 }}
          >
            <AlertBlock
              presetId={preset.id}
              title={preset.defaultTitle}
              message={preset.defaultMessage}
              icon={preset.defaultIcon}
              layout="compact"
            />
          </button>
        ))}
      </div>
      <h3 className="font-semibold text-indigo-800 mb-2 mt-2">Escolha um ícone</h3>
      <div className="grid grid-cols-6 md:grid-cols-8 gap-1 mb-4">
        {selectedPreset.iconOptions.map(opt => {
          const IconComp = LucideIcons[opt as keyof typeof LucideIcons];
          const isLucideIcon = isValidElementType(IconComp);
          return (
            <button
              key={opt}
              type="button"
              className={`flex flex-col items-center justify-center gap-0.5 p-1 rounded border transition-all focus:outline-none ${value.icon === opt ? 'border-blue-600 ring-2 ring-blue-200 bg-blue-50' : 'border-gray-200 hover:border-blue-400 bg-white'}`}
              onClick={() => onChange({ ...value, icon: opt })}
              aria-label={`Selecionar ícone ${opt}`}
              style={{ minWidth: 0 }}
            >
              {isLucideIcon
                ? React.createElement(IconComp as React.ForwardRefExoticComponent<any>, { size: 20, style: { marginBottom: 2 } })
                : React.createElement(LucideIcons['Info'] as React.ForwardRefExoticComponent<any>, { size: 20, style: { marginBottom: 2 } })}
            </button>
          );
        })}
      </div>
      <div className="bg-white border rounded-lg p-4 mb-4">
        <h3 className="font-semibold text-indigo-800 mb-2">Editar alerta selecionado</h3>
        <div className="flex flex-col gap-3">
          <label className="flex flex-col gap-1">
            <span className="text-xs font-semibold">Título</span>
            <input
              type="text"
              value={value.title}
              onChange={e => onChange({ ...value, title: e.target.value })}
              className="border border-gray-300 rounded px-2 py-1"
              maxLength={60}
            />
          </label>
          <label className="flex flex-col gap-1">
            <span className="text-xs font-semibold">Mensagem</span>
            <input
              type="text"
              value={value.message}
              onChange={e => onChange({ ...value, message: e.target.value })}
              className="border border-gray-300 rounded px-2 py-1"
              maxLength={120}
            />
          </label>
          <label className="flex flex-col gap-1">
            <span className="text-xs font-semibold">Botão de ação (opcional)</span>
            <input
              type="text"
              value={value.actionLabel || ''}
              onChange={e => onChange({ ...value, actionLabel: e.target.value })}
              className="border border-gray-300 rounded px-2 py-1"
              maxLength={40}
              placeholder="Ex: Saiba mais, Tentar novamente..."
            />
          </label>
          <label className="flex flex-col gap-1">
            <span className="text-xs font-semibold">URL do botão (opcional)</span>
            <input
              type="url"
              value={value.actionUrl || ''}
              onChange={e => onChange({ ...value, actionUrl: e.target.value })}
              className="border border-gray-300 rounded px-2 py-1"
              maxLength={300}
              placeholder="https://exemplo.com"
            />
          </label>
        </div>
      </div>
      <div className="mb-4">
        <span className="font-semibold text-gray-700 block mb-2">Preview final:</span>
        <AlertBlock
          presetId={value.presetId}
          title={value.title}
          message={value.message}
          icon={value.icon}
          actionLabel={value.actionLabel || undefined}
          actionUrl={value.actionUrl || undefined}
          onAction={value.actionLabel ? () => alert('Ação!') : undefined}
          layout={selectedPreset.layout}
        />
        <div style={{ marginTop: 8, fontSize: 12, color: '#6b7280', fontStyle: 'italic' }}>
          Modelo selecionado: <b>{value.presetId}</b>
        </div>
      </div>
    </div>
  );
};
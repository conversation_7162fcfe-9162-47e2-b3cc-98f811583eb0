import { supabase } from '@/lib/supabaseClient';

/**
 * Verifica se o usuário está autenticado
 * @returns Promise<User> - Retorna o usuário autenticado ou lança erro
 */
export const requireAuth = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Erro ao verificar autenticação:', error);
      throw new Error('Erro ao verificar autenticação. Tente fazer login novamente.');
    }
    
    if (!session?.user) {
      throw new Error('Usuário não autenticado. Faça login para continuar.');
    }
    
    return session.user;
  } catch (error) {
    console.error('Erro inesperado na verificação de autenticação:', error);
    throw new Error('Erro ao verificar autenticação. Tente fazer login novamente.');
  }
};

/**
 * Verifica se o usuário está autenticado sem lançar erro
 * @returns Promise<boolean> - true se autenticado, false caso contrário
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    return !error && !!session?.user;
  } catch (error) {
    console.error('Erro ao verificar status de autenticação:', error);
    return false;
  }
};

/**
 * Obtém o usuário atual se autenticado
 * @returns Promise<User | null> - Usuário autenticado ou null
 */
export const getCurrentUser = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error || !session?.user) {
      return null;
    }
    
    return session.user;
  } catch (error) {
    console.error('Erro ao obter usuário atual:', error);
    return null;
  }
};

/**
 * Redireciona para login se não autenticado
 */
export const redirectToLoginIfNotAuth = async () => {
  const authenticated = await isAuthenticated();
  
  if (!authenticated) {
    // Salvar URL atual para redirecionamento após login
    if (typeof window !== 'undefined') {
      localStorage.setItem('redirectAfterLogin', window.location.pathname);
      window.location.href = '/login';
    }
    return false;
  }
  
  return true;
};

/**
 * Hook para verificar autenticação em componentes
 */
export const useAuthGuard = () => {
  return {
    requireAuth,
    isAuthenticated,
    getCurrentUser,
    redirectToLoginIfNotAuth
  };
};

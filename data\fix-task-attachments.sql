-- Criar estrutura de anexos
CREATE TABLE IF NOT EXISTS public.task_attachments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Habilitar RLS
ALTER TABLE public.task_attachments ENABLE ROW LEVEL SECURITY;

-- Remover políticas antigas se existirem
DROP POLICY IF EXISTS "Usuários podem gerenciar seus próprios anexos" ON public.task_attachments;
DROP POLICY IF EXISTS "Membros do projeto podem ver anexos" ON public.task_attachments;

-- Criar novas políticas
CREATE POLICY "Usuários podem gerenciar seus próprios anexos" 
ON public.task_attachments
FOR ALL USING (
    auth.uid() = user_id
);

CREATE POLICY "Membros do projeto podem ver anexos" 
ON public.task_attachments
FOR SELECT USING (
    EXISTS (
        SELECT 1 
        FROM public.tasks t
        JOIN public.project_members pm ON t.project_id = pm.project_id
        WHERE t.id = task_attachments.task_id 
        AND pm.user_id = auth.uid()
    )
);

-- Criar bucket para armazenamento se não existir
INSERT INTO storage.buckets (id, name, public)
VALUES ('task-attachments', 'task-attachments', false)
ON CONFLICT (id) DO NOTHING;

-- Criar política para storage
CREATE POLICY "Usuários autenticados podem gerenciar seus anexos"
ON storage.objects FOR ALL USING (
    bucket_id = 'task-attachments' AND 
    auth.role() = 'authenticated'
);

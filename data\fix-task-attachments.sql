-- =====================================================
-- CORREÇÃO: TASK ATTACHMENTS
-- =====================================================
-- Correção específica para tabela task_attachments
-- Versão: 2.0 - Julho 2025

-- Verificar se a tabela já existe
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'task_attachments' AND table_schema = 'public') THEN
    RAISE NOTICE '✅ Tabela task_attachments já existe';
  ELSE
    RAISE NOTICE '⚠️ Tabela task_attachments não existe, criando...';
    
    -- Criar estrutura de anexos
    CREATE TABLE public.task_attachments (
      id uuid DEFAULT gen_random_uuid() NOT NULL,
      task_id uuid NOT NULL,
      uploaded_by uuid NOT NULL,
      file_url text NOT NULL,
      file_name text,
      file_size bigint,
      mime_type text,
      description text,
      status varchar(20) DEFAULT 'pending',
      approved_by uuid,
      approved_at timestamp with time zone,
      rejection_reason text,
      block_id varchar(255),
      created_at timestamp with time zone DEFAULT now(),
      updated_at timestamp with time zone DEFAULT now(),
      
      CONSTRAINT task_attachments_pkey PRIMARY KEY (id),
      CONSTRAINT task_attachments_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks (id) ON DELETE CASCADE,
      CONSTRAINT task_attachments_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.profiles (id) ON DELETE CASCADE,
      CONSTRAINT task_attachments_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.profiles (id) ON DELETE SET NULL,
      CONSTRAINT task_attachments_status_check CHECK (status IN ('pending', 'approved', 'rejected')),
      CONSTRAINT task_attachments_file_size_check CHECK (file_size >= 0)
    );
    
    -- Criar índices
    CREATE INDEX idx_task_attachments_task ON public.task_attachments(task_id);
    CREATE INDEX idx_task_attachments_status ON public.task_attachments(status);
    CREATE INDEX idx_task_attachments_uploaded_by ON public.task_attachments(uploaded_by);
    CREATE INDEX idx_task_attachments_approved_by ON public.task_attachments(approved_by);
    CREATE INDEX idx_task_attachments_block_id ON public.task_attachments(block_id);
    CREATE INDEX idx_task_attachments_task_block ON public.task_attachments(task_id, block_id);
    
    RAISE NOTICE '✅ Tabela task_attachments criada';
  END IF;
END $$;

-- Habilitar RLS
ALTER TABLE public.task_attachments ENABLE ROW LEVEL SECURITY;

-- Remover políticas antigas se existirem
DROP POLICY IF EXISTS "Usuários podem gerenciar seus próprios anexos" ON public.task_attachments;
DROP POLICY IF EXISTS "Membros do projeto podem ver anexos" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_select" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_insert" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_update" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_delete" ON public.task_attachments;

-- Criar políticas RLS corrigidas
CREATE POLICY "task_attachments_select"
  ON public.task_attachments
  FOR SELECT
  USING (
    uploaded_by = auth.uid() OR
    approved_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

CREATE POLICY "task_attachments_insert"
  ON public.task_attachments
  FOR INSERT
  WITH CHECK (
    uploaded_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.tasks t
      WHERE t.id = task_id AND
      (
        t.assigned_to = auth.uid() OR
        t.id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid())
      )
    )
  );

CREATE POLICY "task_attachments_update"
  ON public.task_attachments
  FOR UPDATE
  USING (
    uploaded_by = auth.uid() OR
    approved_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

CREATE POLICY "task_attachments_delete"
  ON public.task_attachments
  FOR DELETE
  USING (
    uploaded_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Criar bucket para armazenamento se não existir
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'task-attachments',
  'task-attachments',
  false,
  52428800, -- 50MB
  ARRAY[
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'video/mp4',
    'audio/mpeg'
  ]
)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Remover políticas de storage antigas
DROP POLICY IF EXISTS "Usuários autenticados podem gerenciar seus anexos" ON storage.objects;
DROP POLICY IF EXISTS "task_attachments_storage_select" ON storage.objects;
DROP POLICY IF EXISTS "task_attachments_storage_insert" ON storage.objects;
DROP POLICY IF EXISTS "task_attachments_storage_update" ON storage.objects;
DROP POLICY IF EXISTS "task_attachments_storage_delete" ON storage.objects;

-- Criar políticas de storage corrigidas
CREATE POLICY "task_attachments_storage_select"
  ON storage.objects
  FOR SELECT
  USING (
    bucket_id = 'task-attachments' AND
    owner = auth.uid()
  );

CREATE POLICY "task_attachments_storage_insert"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'task-attachments' AND
    owner = auth.uid()
  );

CREATE POLICY "task_attachments_storage_update"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'task-attachments' AND
    owner = auth.uid()
  );

CREATE POLICY "task_attachments_storage_delete"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'task-attachments' AND
    owner = auth.uid()
  );

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
DECLARE
  table_exists BOOLEAN;
  policy_count INTEGER;
  bucket_exists BOOLEAN;
BEGIN
  -- Verificar se a tabela existe
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'task_attachments' AND table_schema = 'public'
  ) INTO table_exists;
  
  -- Contar políticas
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE tablename = 'task_attachments' AND schemaname = 'public';
  
  -- Verificar se o bucket existe
  SELECT EXISTS (
    SELECT 1 FROM storage.buckets WHERE id = 'task-attachments'
  ) INTO bucket_exists;
  
  RAISE NOTICE '✅ Correção de task_attachments concluída!';
  RAISE NOTICE '📋 Tabela existe: %', table_exists;
  RAISE NOTICE '🔒 Políticas RLS: %', policy_count;
  RAISE NOTICE '🗂️ Bucket existe: %', bucket_exists;
END $$;

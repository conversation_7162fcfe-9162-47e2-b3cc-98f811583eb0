import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ProjectStatus } from '@/types';
import { Search, Filter, X, Calendar, Info } from 'lucide-react';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ProjectFiltersProps {
  searchTerm: string;
  statusFilters: ProjectStatus[];
  onSearchChange: (value: string) => void;
  onStatusFilterChange: (values: ProjectStatus[]) => void;
  responsible?: any;
  onResponsibleChange?: (user: any) => void;
  startDate?: string;
  endDate?: string;
  onStartDateChange?: (date: string) => void;
  onEndDateChange?: (date: string) => void;
}

export const ProjectFilters: React.FC<ProjectFiltersProps> = ({
  searchTerm,
  statusFilters = [],
  onSearchChange,
  onStatusFilterChange,
  responsible,
  onResponsibleChange,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange
}) => {
  const statusOptions = [
    { value: 'planning', label: 'Planejamento' },
    { value: 'active', label: 'Ativo' },
    { value: 'on-hold', label: 'Pausado' },
    { value: 'completed', label: 'Concluído' },
    { value: 'cancelled', label: 'Cancelado' }
  ];

  const [statusOpen, setStatusOpen] = useState(false);
  const statusRef = useRef<HTMLDivElement>(null);

  // Fecha o dropdown ao clicar fora
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (statusRef.current && !statusRef.current.contains(event.target as Node)) {
        setStatusOpen(false);
      }
    }
    if (statusOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [statusOpen]);

  const activeFiltersCount = [
    searchTerm.length > 0,
    (statusFilters || []).length > 0,
    !!responsible,
    !!startDate,
    !!endDate
  ].filter(Boolean).length;

  const clearAllFilters = () => {
    onSearchChange('');
    onStatusFilterChange([]);
    onResponsibleChange?.(null);
    onStartDateChange?.('');
    onEndDateChange?.('');
  };

  const handleStatusChange = (status: ProjectStatus) => {
    if ((statusFilters || []).includes(status)) {
      onStatusFilterChange((statusFilters || []).filter(s => s !== status));
    } else {
      onStatusFilterChange([...(statusFilters || []), status]);
    }
  };

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full items-end">
        {/* Busca */}
        <div className="col-span-1">
          <div className="relative w-full h-10">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Buscar projetos..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 pr-4 w-full h-10"
            />
          </div>
        </div>
        {/* Filtro de Status (MultiSelect) */}
        <div className="col-span-1">
          <div className="flex flex-col gap-1">
            <span className="text-sm font-medium flex items-center gap-2"><Filter className="w-4 h-4" /> Status</span>
            <div ref={statusRef} className="relative">
              <button
                type="button"
                className="w-full h-10 border rounded px-3 flex items-center justify-between bg-white hover:border-project focus:outline-none"
                onClick={() => setStatusOpen((open) => !open)}
              >
                <div className="flex flex-wrap gap-1 items-center">
                  {statusFilters.length === 0 ? (
                    <span className="text-gray-400">Selecione status...</span>
                  ) : (
                    statusFilters.map(s => (
                      <Badge key={s} variant="secondary" className="bg-project-bg text-project">{statusOptions.find(o => o.value === s)?.label || s}</Badge>
                    ))
                  )}
                </div>
                <svg className={`w-4 h-4 ml-2 transition-transform ${statusOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.585l3.71-3.355a.75.75 0 111.02 1.1l-4.25 3.85a.75.75 0 01-1.02 0l-4.25-3.85a.75.75 0 01.02-1.06z" clipRule="evenodd" /></svg>
              </button>
              {statusOpen && (
                <div className="absolute z-20 mt-1 w-full bg-white border rounded shadow-lg p-2 flex flex-col gap-1">
                  {statusOptions.map((option) => (
                    <label key={option.value} className="flex items-center gap-2 cursor-pointer text-sm px-2 py-1 rounded hover:bg-gray-50">
                      <input
                        type="checkbox"
                        checked={(statusFilters || []).includes(option.value as ProjectStatus)}
                        onChange={() => handleStatusChange(option.value as ProjectStatus)}
                        className="accent-project"
                      />
                      {option.label}
                    </label>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Filtro de Responsável */}
        <div className="col-span-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {React.cloneElement(
                  <div className="w-full max-w-[420px] h-10 flex items-center">
                    <UserAutocomplete
                      onSelect={user => onResponsibleChange?.(user)}
                    />
                  </div>,
                  { ref: React.useRef() }
                )}
              </TooltipTrigger>
              <TooltipContent>Filtrar projetos pelo responsável principal</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        {/* Filtro de Datas */}
        <div className="col-span-1 flex gap-2 items-center w-full">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {React.cloneElement(
                  <div className="relative flex-1 min-w-0 h-10">
                    <Calendar className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input type="date" value={startDate || ''} onChange={e => onStartDateChange?.(e.target.value)} className="pl-8 h-10 w-full max-w-[140px]" placeholder="Início de" />
                  </div>,
                  { ref: React.useRef() }
                )}
              </TooltipTrigger>
              <TooltipContent>Filtrar projetos a partir desta data de início</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {React.cloneElement(
                  <div className="relative flex-1 min-w-0 h-10">
                    <Calendar className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input type="date" value={endDate || ''} onChange={e => onEndDateChange?.(e.target.value)} className="pl-8 h-10 w-full max-w-[140px]" placeholder="até" />
                  </div>,
                  { ref: React.useRef() }
                )}
                </TooltipTrigger>
              <TooltipContent>Filtrar projetos até esta data de término</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Filtros Ativos e Limpar */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-project-bg text-project">
            {activeFiltersCount} filtro{activeFiltersCount > 1 ? 's' : ''} ativo{activeFiltersCount > 1 ? 's' : ''}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4 mr-1" />
            Limpar
          </Button>
        </div>
      )}
    </div>
  );
};
import React, { useState } from "react";
import { useAuth } from "./useAuth";
import { Link } from "react-router-dom";

const RegisterForm: React.FC = () => {
  const { register, loading, error } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await register(email, password);
    if (!error) setSuccess(true);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-sm mx-auto">
      <h2 className="text-xl font-bold">Criar <PERSON>ta</h2>
      <div>
        <label className="block mb-1">Email</label>
        <input
          type="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="w-full border rounded px-3 py-2"
          required
        />
      </div>
      <div>
        <label className="block mb-1">Senha</label>
        <input
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          className="w-full border rounded px-3 py-2"
          required
        />
      </div>
      {error && <div className="text-red-600 text-sm">{error}</div>}
      {success && <div className="text-green-600 text-sm">Conta criada! Verifique seu email."</div>}
      <button
        type="submit"
        className="w-full bg-green-600 text-white py-2 rounded disabled:opacity-50"
        disabled={loading}
      >
        {loading ? "Criando..." : "Criar Conta"}
      </button>
      <div className="flex justify-end text-sm mt-2">
        <Link to="/login" className="text-blue-600 hover:underline">Já tenho conta</Link>
      </div>
    </form>
  );
};

export default RegisterForm; 
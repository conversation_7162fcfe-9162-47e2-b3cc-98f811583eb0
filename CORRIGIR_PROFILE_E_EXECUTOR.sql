-- =====================================================
-- SOLUÇÃO: CRIAR PROFILE PARA USUÁRIO EXISTENTE
-- Execute APENAS se TESTE 1 = ❌ e TESTE 3 = ✅
-- =====================================================

-- OPÇÃO 1: INSERIR PROFILE BASEADO EM AUTH.USERS
INSERT INTO profiles (id, name, email, role, created_at, updated_at)
SELECT 
    u.id,
    COALESCE(u.raw_user_meta_data->>'name', u.email) as name,
    u.email,
    'member' as role,  -- Role padrão
    u.created_at,
    NOW()
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
    AND NOT EXISTS (
        SELECT 1 FROM profiles p WHERE p.id = u.id
    );

-- VERIFICAR SE FOI INSERIDO
SELECT 
    'VERIFICAÇÃO: PROFILE CRIADO' as resultado,
    p.id,
    p.name,
    p.email,
    p.role
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- =====================================================
-- AGORA PODEMOS ADICIONAR COMO EXECUTOR
-- Execute APÓS criar o profile
-- =====================================================

-- INSERIR COMO EXECUTOR DA TAREFA
INSERT INTO task_executors (task_id, user_id, created_at)
VALUES (
    '7c606667-9391-4660-933d-90d6bd276e88',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    NOW()
)
ON CONFLICT (task_id, user_id) DO NOTHING;

-- VERIFICAR SE FOI ADICIONADO COMO EXECUTOR
SELECT 
    'VERIFICAÇÃO: EXECUTOR ADICIONADO' as resultado,
    te.task_id,
    te.user_id,
    p.name,
    p.email
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- =====================================================
-- TESTE FINAL: VERIFICAR ACESSO AOS CONTENT BLOCKS
-- =====================================================

SELECT 
    'TESTE FINAL: ACESSO AOS BLOCOS' as teste,
    tcb.id,
    tcb.type,
    LEFT(tcb.content::text, 50) as content_preview
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND tcb.task_id IN (
        SELECT te.task_id FROM task_executors te
        WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
    );

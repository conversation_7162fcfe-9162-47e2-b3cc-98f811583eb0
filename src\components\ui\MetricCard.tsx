
import React from 'react';
import { Card, CardContent } from './card';
import { cn } from '@/lib/utils';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    trend: 'up' | 'down' | 'neutral';
  };
  icon?: React.ReactNode;
  color?: 'project' | 'stage' | 'task' | 'default';
  className?: string;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  color = 'default',
  className
}) => {
  const colorClasses = {
    project: 'border-project/20 bg-project-bg',
    stage: 'border-stage/20 bg-stage-bg',
    task: 'border-task/20 bg-task-bg',
    default: 'border-gray-200 bg-white'
  };

  const iconColorClasses = {
    project: 'text-project',
    stage: 'text-stage',
    task: 'text-task',
    default: 'text-gray-600'
  };

  return (
    <Card 
      className={cn(
        'transition-all duration-200 hover:shadow-md',
        colorClasses[color],
        className
      )}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {change && (
              <div className="flex items-center gap-1">
                <span 
                  className={cn(
                    'text-xs font-medium',
                    change.trend === 'up' && 'text-status-success',
                    change.trend === 'down' && 'text-status-error',
                    change.trend === 'neutral' && 'text-gray-500'
                  )}
                >
                  {change.value}
                </span>
              </div>
            )}
          </div>
          {icon && (
            <div className={cn('p-3 rounded-lg bg-white/50', iconColorClasses[color])}>
              {icon}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

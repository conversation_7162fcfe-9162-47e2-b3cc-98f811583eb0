# 🏗️ FASE 1: FUNDAÇÃO - PLANO DETALHADO DE EXECUÇÃO
**Sistema de Gerenciamento de Projetos SaaS - Semanas 1-4**

**Versão:** 1.0  
**Duração:** 4 semanas  
**Status:** Pronto para Execução

---

## 📋 VISÃO GERAL DA FASE 1

### Objetivo Principal
Estabelecer a **fundação técnica sólida** do sistema, implementando a arquitetura base, infraestrutura, autenticação e modelos de dados fundamentais seguindo rigorosamente os princípios de **SRP, DRY, SSOT, KISS e YAGNI**.

### Critério de Sucesso
✅ **API de autenticação funcional (Supabase Auth) e ambiente de desenvolvimento rodando**
- Projeto Supabase configurado e rodando
- Frontend React com Vite configurado
- Sistema de autenticação Supabase implementado
- Tabelas de dados básicas criadas no Supabase
- Infraestrutura cloud Supabase funcional

---

## 🎯 ATIVIDADES DETALHADAS POR SEMANA

### **SEMANA 1: Setup Inicial e Infraestrutura**

#### **Dia 1-2: Configuração do Supabase**

**🔧 Atividade 1.1: Criação do Projeto Supabase**
1. Acesse o [painel do Supabase](https://app.supabase.com/)
2. Clique em "New Project" e siga as instruções
3. Copie a **Project URL** e a **Anon Public Key** (Project Settings > API)

**🔧 Atividade 1.2: Criação das Tabelas no Supabase**
- Crie as tabelas `projects`, `stages`, `tasks` e configure as relações e permissões (RLS) pelo painel do Supabase.
- Utilize campos JSONB para conteúdo dinâmico, conforme necessário.
- Configure políticas de Row Level Security (RLS) para garantir segurança multi-tenant.

**🔧 Atividade 1.3: Configuração do Auth no Supabase**
- Ative o Supabase Auth (email/senha, OAuth, etc.)
- Configure provedores de autenticação conforme necessidade do projeto.

#### **Dia 3-4: Setup Frontend React**

**🔧 Atividade 1.4: Criação do Projeto React**
```bash
# Estrutura frontend a ser criada
frontend/
├── package.json
├── vite.config.ts
├── tsconfig.json
├── index.html
├── src/
│   ├── main.tsx
│   ├── App.tsx
│   ├── components/
│   │   ├── ui/
│   │   └── layout/
│   ├── features/
│   ├── services/
│   ├── store/
│   ├── hooks/
│   ├── utils/
│   └── types/
└── public/
```

**Dependências Frontend (package.json):**
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@supabase/supabase-js": "^2.0.0",
    "zustand": "^4.4.6",
    "@tanstack/react-query": "^5.8.4",
    "react-router-dom": "^6.18.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.37",
    "@types/react-dom": "^18.2.15",
    "typescript": "^5.2.2",
    "vite": "^4.5.0"
  }
}
```

**🔧 Atividade 1.5: Configuração do Supabase Client no Frontend**
Crie o arquivo `src/lib/supabaseClient.ts`:
```ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

**🔧 Atividade 1.6: Configuração das Variáveis de Ambiente**
Crie um arquivo `.env` na raiz do projeto:
```
VITE_SUPABASE_URL=<SUA_SUPABASE_URL>
VITE_SUPABASE_ANON_KEY=<SUA_SUPABASE_ANON_KEY>
```
Adicione `.env` ao `.gitignore`.

#### **Dia 5: Configuração CI/CD Básico (Frontend)**

**🔧 Atividade 1.7: GitHub Actions para Frontend**
- Configure pipeline de build e deploy apenas para o frontend.
- O backend é gerenciado pelo Supabase Cloud.

---

### **SEMANA 2: Modelos de Dados e Autenticação**

#### **Dia 6-7: Modelagem no Supabase**
- Ajuste e normalize as tabelas `projects`, `stages`, `tasks` conforme a hierarquia do sistema.
- Adicione campos para controle de usuário, permissões e conteúdo dinâmico.

#### **Dia 8-9: Políticas de Segurança (RLS)**
- Configure políticas de Row Level Security para garantir que cada usuário só acesse seus próprios dados.
- Teste as permissões pelo painel do Supabase.

#### **Dia 10: Integração do Auth no Frontend**
- Implemente login, registro e gerenciamento de sessão usando o Supabase Auth no React.
- Utilize o client do Supabase para autenticação e controle de sessão.

---

### **SEMANA 3: CRUD e Integração**

#### **Dia 11-14: CRUD de Projetos, Etapas e Tarefas**
- Implemente operações CRUD no frontend usando o Supabase Client.
- Utilize React Query para cache e gerenciamento de dados.
- Adapte stores/hooks para consumir dados do Supabase.

---

### **SEMANA 4: UI Base e Testes**

#### **Dia 15-20: Componentes Base e Testes**
- Implemente componentes de UI conforme o design system.
- Implemente testes unitários e de integração para os fluxos principais.

---

## 🎯 CHECKLIST DE ENTREGA DA FASE 1

### ✅ Supabase
- [ ] Projeto Supabase criado e configurado
- [ ] Tabelas `projects`, `stages`, `tasks` criadas
- [ ] Políticas de RLS configuradas
- [ ] Supabase Auth ativado e testado

### ✅ Frontend
- [ ] Projeto React com Vite configurado
- [ ] Supabase Client integrado
- [ ] Zustand configurado para estado global
- [ ] React Query configurado para cache de API
- [ ] CRUD de projetos, etapas e tarefas funcionando via Supabase
- [ ] Componentes base (Button, Layout) criados
- [ ] Roteamento básico configurado

### ✅ Infraestrutura
- [ ] CI/CD básico para frontend funcionando
- [ ] Variáveis de ambiente configuradas
- [ ] Documentação README atualizada
- [ ] Estrutura de pastas seguindo princípios SRP

### ✅ Qualidade
- [ ] Código seguindo princípios DRY, SSOT, KISS, YAGNI
- [ ] Separação clara entre UI e lógica de negócio
- [ ] TypeScript configurado sem erros
- [ ] ESLint e Prettier configurados
- [ ] Cobertura de testes > 70%

---

## 🚀 PRÓXIMOS PASSOS (FASE 2)

Após a conclusão da Fase 1, a **Fase 2 (MVP Core)** focará em:

1. **CRUD completo** para Projetos, Etapas e Tarefas
2. **Dashboard Principal** com cards de projeto
3. **Navegação hierárquica** Projeto > Etapa > Tarefa
4. **Tela de Detalhes da Tarefa** com editor básico
5. **Sistema de permissões (RLS) refinado**
6. **Interface responsiva** mobile-first

---

**📝 Observações Importantes:**
- Seguir rigorosamente os princípios de arquitetura definidos
- Manter separação clara entre UI e lógica de negócio
- Implementar apenas o necessário (YAGNI)
- Documentar decisões técnicas importantes
- Realizar code review de todo código gerado por IA

**🎯 Meta da Fase 1:** Base sólida e funcional para desenvolvimento das próximas fases, com foco na qualidade e escalabilidade do código.
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { projectService } from './projectService';
import { supabaseMock, mockSingle, mockEqList, mockEqGetById, mockEqUpdate, mockEqCreate, mockInsert, mockUpdate, mockDelete } from '../test-utils/supabaseMock';

vi.mock('@/lib/supabaseClient', () => ({ supabase: supabaseMock }));

describe('projectService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('deve retornar lista de projetos', async () => {
    mockEqList.mockReturnValueOnce({ data: [{ id: '1', name: 'Projeto Teste' }], error: null });
    const projetos = await projectService.list();
    expect(projetos).toEqual([{ id: '1', name: 'Projeto Teste' }]);
  });

  it('deve retornar projeto por id', async () => {
    mockSingle.mockReturnValueOnce({ data: { id: '1', name: 'Projeto Teste' }, error: null });
    const projeto = await projectService.getById('1');
    expect(projeto).toEqual({ id: '1', name: 'Projeto Teste' });
  });

  it('deve criar um novo projeto', async () => {
    mockSingle.mockReturnValueOnce({ data: { id: '2', name: 'Novo Projeto' }, error: null });
    const projeto = await projectService.create({ name: 'Novo Projeto' });
    expect(projeto).toEqual({ id: '2', name: 'Novo Projeto' });
  });

  it('deve atualizar um projeto', async () => {
    mockSingle.mockReturnValueOnce({ data: { id: '1', name: 'Projeto Atualizado' }, error: null });
    const projeto = await projectService.update('1', { name: 'Projeto Atualizado' });
    expect(projeto).toEqual({ id: '1', name: 'Projeto Atualizado' });
  });

  it('deve remover um projeto', async () => {
    mockDelete.mockReturnValueOnce({ eq: vi.fn(() => ({ error: null })) });
    await expect(projectService.remove('1')).resolves.toBeUndefined();
  });

  it('deve lançar erro se houver erro no supabase', async () => {
    mockEqList.mockReturnValueOnce({ data: null, error: new Error('Erro Supabase') });
    await expect(projectService.list()).rejects.toThrow('Erro Supabase');
  });
}); 
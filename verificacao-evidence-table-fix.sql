-- Verificação da Correção da Tabela Evidence
-- Execute este script para verificar se a correção está funcionando
-- Data: 2025-01-17

-- 1. Verificar se a tabela evidence existe
SELECT 'Verificando tabela evidence...' as status;
SELECT 
    table_name,
    table_schema
FROM information_schema.tables 
WHERE table_name = 'evidence' AND table_schema = 'public';

-- 2. Verificar estrutura da tabela evidence
SELECT 'Verificando estrutura da tabela evidence...' as status;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'evidence' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Verificar dados existentes na tabela evidence
SELECT 'Verificando dados na tabela evidence...' as status;
SELECT 
    COUNT(*) as total_evidences,
    COUNT(DISTINCT task_id) as distinct_tasks,
    COUNT(DISTINCT block_id) as distinct_blocks
FROM evidence;

-- 4. Verificar últimas evidências adicionadas
SELECT 'Verificando últimas evidências adicionadas...' as status;
SELECT 
    e.id,
    e.task_id,
    e.block_id,
    e.type,
    e.file_name,
    e.file_size,
    e.status,
    e.uploaded_by,
    p.name as uploaded_by_name,
    e.created_at
FROM evidence e
LEFT JOIN profiles p ON e.uploaded_by = p.id
ORDER BY e.created_at DESC
LIMIT 10;

-- 5. Verificar se há dados na tabela task_attachments (tabela anterior)
SELECT 'Verificando dados na tabela task_attachments (tabela anterior)...' as status;
SELECT 
    COUNT(*) as total_attachments,
    COUNT(DISTINCT task_id) as distinct_tasks,
    COUNT(DISTINCT block_id) as distinct_blocks
FROM task_attachments;

-- 6. Verificar se há duplicatas entre as tabelas
SELECT 'Verificando duplicatas entre evidence e task_attachments...' as status;
SELECT 
    e.file_name as evidence_file,
    ta.description as attachment_description,
    e.task_id,
    e.created_at as evidence_created,
    ta.created_at as attachment_created
FROM evidence e
INNER JOIN task_attachments ta ON e.task_id = ta.task_id
WHERE e.file_name = ta.description OR e.created_at = ta.created_at;

-- 7. Teste de inserção na tabela evidence
SELECT 'Testando inserção na tabela evidence...' as status;
-- Não executar INSERT aqui para evitar dados de teste

-- 8. Verificar permissões RLS
SELECT 'Verificando Row Level Security...' as status;
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename IN ('evidence', 'task_attachments');

-- 9. Verificar políticas RLS
SELECT 'Verificando políticas RLS...' as status;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename IN ('evidence', 'task_attachments')
ORDER BY tablename, policyname;

-- 10. Resumo final
SELECT 'RESUMO FINAL:' as status;
SELECT 
    'evidence' as tabela,
    COUNT(*) as total_registros,
    MIN(created_at) as primeiro_registro,
    MAX(created_at) as ultimo_registro
FROM evidence
UNION ALL
SELECT 
    'task_attachments' as tabela,
    COUNT(*) as total_registros,
    MIN(created_at) as primeiro_registro,
    MAX(created_at) as ultimo_registro
FROM task_attachments;

-- Instruções para teste:
-- 1. Execute este script no SQL Editor do Supabase
-- 2. Faça upload de um arquivo através da interface
-- 3. Execute este script novamente para verificar se os dados apareceram na tabela evidence
-- 4. Verifique se o arquivo é exibido na interface após o upload

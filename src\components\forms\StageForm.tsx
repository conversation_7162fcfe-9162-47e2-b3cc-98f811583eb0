import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/ui/use-toast';
import { stageService } from '@/services/stageService';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { supabase } from '@/lib/supabaseClient';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card';
import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from '@/components/ui/avatar';
import { Users } from 'lucide-react';

// Defina o tipo Responsible fora do componente para uso global
export type Responsible = { id: string; name: string; email: string; avatar_url?: string };

interface StageFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stage?: any;
  mode: 'create' | 'edit';
  projectId: string;
  onCreated?: () => void;
  onUpdated?: () => void;
  projectStartDate?: string;
  projectEndDate?: string;
  members: Responsible[];
  projectMembers: Responsible[];
}

interface StageFormFields {
  name: string;
  description: string;
  start_date: string;
  end_date: string;
}

export const StageForm: React.FC<StageFormProps> = ({
  open,
  onOpenChange,
  stage,
  mode,
  projectId,
  onCreated,
  onUpdated,
  projectStartDate,
  projectEndDate,
  members,
  projectMembers
}) => {
  const { toast } = useToast();
  const [responsibles, setResponsibles] = React.useState<Responsible[]>(
    Array.isArray(stage?.responsibles)
      ? stage.responsibles.filter((r): r is Responsible =>
          r && typeof r === 'object' && 'id' in r && 'name' in r && 'email' in r
        )
      : []
  );

  const stageSchema = React.useMemo(() =>
    z.object({
      name: z.string().min(1, 'Nome é obrigatório'),
      description: z.string().min(1, 'Descrição é obrigatória'),
      start_date: z.string().min(1, 'Data de início é obrigatória'),
      end_date: z.string().min(1, 'Data de fim é obrigatória'),
    }).superRefine((data, ctx) => {
      if (projectStartDate && data.start_date < projectStartDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['start_date'],
          message: `Data de início não pode ser anterior ao início do projeto (${projectStartDate.split('-').reverse().join('/')})`
        });
      }
      if (projectEndDate && data.end_date > projectEndDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['end_date'],
          message: `Data de fim não pode ser posterior ao fim do projeto (${projectEndDate.split('-').reverse().join('/')})`
        });
      }
      if (data.end_date < data.start_date) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['end_date'],
          message: 'Data de fim não pode ser anterior à data de início da etapa'
        });
      }
    }), [projectStartDate, projectEndDate]);

  const form = useForm<StageFormFields>({
    resolver: zodResolver(stageSchema),
    defaultValues: {
      name: stage?.name || '',
      description: stage?.description || '',
      start_date: stage?.start_date || '',
      end_date: stage?.end_date || '',
    },
  });

  // Função utilitária para validar responsáveis
  async function validarResponsaveisExistem(responsibles: Responsible[]): Promise<Responsible[] | null> {
    const idsParaAdicionar = responsibles.map(r => r.id);
    const { data: perfisValidos } = await supabase
      .from('profiles')
      .select('id')
      .in('id', idsParaAdicionar);
    const idsValidos = (perfisValidos || []).map(p => p.id);
    const responsaveisValidos = responsibles.filter(r => idsValidos.includes(r.id));
    if (responsaveisValidos.length !== responsibles.length) {
      toast({
        title: 'Erro',
        description: 'Um ou mais responsáveis não existem mais no sistema.',
        variant: 'destructive',
      });
      return null;
    }
    return responsaveisValidos;
  }

  const onSubmit = async (data: StageFormFields) => {
    try {
      let stageId = stage?.id;
      if (mode === 'create') {
        const newStage = await stageService.create({ ...data, project_id: projectId });
        console.log('Retorno do insert da etapa:', newStage);
        stageId = newStage.id;
        const responsaveisValidos = await validarResponsaveisExistem(responsibles);
        if (!responsaveisValidos) return;
        if (responsaveisValidos.length > 0) {
          try {
            await Promise.all(responsaveisValidos.map(async r => {
              const { data: insertData, error: insertError } = await supabase.from('stage_responsibles').insert({ stage_id: stageId, user_id: r.id });
              console.log('Tentando inserir responsável:', { stage_id: stageId, user_id: r.id }, 'Retorno:', insertData, insertError);
              if (insertError) {
                toast({
                  title: 'Erro ao adicionar responsável',
                  description: insertError.message,
                  variant: 'destructive',
                });
                throw insertError;
              }
            }));
          } catch (err) {
            toast({
              title: 'Erro ao adicionar responsáveis',
              description: err?.message || 'Erro desconhecido ao adicionar responsáveis.',
              variant: 'destructive',
            });
            console.error('Erro ao inserir responsáveis:', err);
            return;
          }
        }
        onCreated?.();
        toast({
          title: 'Etapa criada',
          description: 'A etapa foi criada com sucesso.',
        });
      } else {
        const updateResult = await stageService.update(stage.id, data);
        console.log('Retorno do update da etapa:', updateResult);
        // Atualizar responsáveis (remover todos e inserir os atuais)
        await supabase.from('stage_responsibles').delete().eq('stage_id', stage.id);
        const responsaveisValidos = await validarResponsaveisExistem(responsibles);
        if (!responsaveisValidos) return;
        if (responsaveisValidos.length > 0) {
          try {
            await Promise.all(responsaveisValidos.map(async r => {
              const { data: insertData, error: insertError } = await supabase.from('stage_responsibles').insert({ stage_id: stage.id, user_id: r.id });
              console.log('Tentando inserir responsável (update):', { stage_id: stage.id, user_id: r.id }, 'Retorno:', insertData, insertError);
              if (insertError) {
                toast({
                  title: 'Erro ao adicionar responsável',
                  description: insertError.message,
                  variant: 'destructive',
                });
                throw insertError;
              }
            }));
          } catch (err) {
            toast({
              title: 'Erro ao adicionar responsáveis',
              description: err?.message || 'Erro desconhecido ao adicionar responsáveis.',
              variant: 'destructive',
            });
            console.error('Erro ao inserir responsáveis (update):', err);
            return;
          }
        }
        toast({
          title: 'Etapa atualizada',
          description: 'A etapa foi atualizada com sucesso.',
        });
        onUpdated?.();
      }
      onOpenChange(false);
      form.reset();
      setResponsibles([]);
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao salvar a etapa.',
        variant: 'destructive',
      });
      console.error('Erro geral ao salvar etapa:', error);
    }
  };

  React.useEffect(() => {
    async function fetchResponsibles() {
      if (open && mode === 'edit' && stage?.id) {
        try {
          const responsiblesFromDb = await stageService.getResponsiblesByStageId(stage.id);
          setResponsibles(responsiblesFromDb);
        } catch (err) {
          toast({
            title: 'Erro ao carregar responsáveis',
            description: err?.message || 'Não foi possível carregar os responsáveis da etapa.',
            variant: 'destructive',
          });
          setResponsibles([]);
        }
      }
      if (!open) {
        setResponsibles([]);
      }
    }
    fetchResponsibles();
  }, [open, mode, stage]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md" aria-describedby="stage-form-desc">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Nova Etapa' : 'Editar Etapa'}
          </DialogTitle>
          <DialogDescription id="stage-form-desc">
            Preencha os campos obrigatórios para {mode === 'create' ? 'criar' : 'editar'} uma etapa.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome da Etapa</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o nome da etapa" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Descreva a etapa" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Início</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Fim</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Card className="mb-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-project" />
                  Responsáveis da Etapa
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 mb-2 min-h-[40px]">
                  {responsibles.length === 0 ? (
                    <span className="text-gray-400 text-sm">Nenhum responsável adicionado</span>
                  ) : (
                    responsibles.map(responsible => (
                      <span
                        key={responsible.id}
                        className="inline-flex items-center gap-2 bg-gray-100 text-gray-800 px-3 py-1 rounded-full shadow-sm border border-gray-200 min-w-0 max-w-full"
                        style={{ maxWidth: '100%' }}
                      >
                        <Avatar className="w-7 h-7">
                          <AvatarImage src={responsible.avatar_url || '/placeholder.svg'} alt={responsible.name || ''} />
                          <AvatarFallback className="text-xs">{(responsible.name || '?').charAt(0).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col min-w-0 max-w-[120px]">
                          <span className="truncate font-medium text-sm">{responsible.name}</span>
                          <span className="truncate text-xs text-gray-500">{responsible.email}</span>
                        </div>
                        <button
                          type="button"
                          className="ml-1 text-gray-400 hover:text-red-500 transition"
                          onClick={() => setResponsibles(prev => prev.filter(r => r.id !== responsible.id))}
                          aria-label="Remover responsável"
                        >
                          ×
                        </button>
                      </span>
                    ))
                  )}
                </div>
                <UserAutocomplete
                  onSelect={user => {
                    setResponsibles(prev => {
                      if (prev.some(r => r.id === user.id)) return prev;
                      return [
                        ...prev,
                        {
                          ...user,
                          role: 'responsible',
                        },
                      ];
                    });
                  }}
                  excludeIds={responsibles.map(r => r.id)}
                  users={projectMembers}
                />
              </CardContent>
            </Card>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" className="bg-stage hover:bg-stage-dark">
                {mode === 'create' ? 'Criar Etapa' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

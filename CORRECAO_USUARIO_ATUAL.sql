-- =====================================================
-- SOLUÇÃO ALTERNATIVA: CRIAR PROFILE E ADICIONAR COMO EXECUTOR
-- Vamos resolver o problema atual sem precisar fazer logout
-- =====================================================

-- STEP 1: CRIAR PROFILE PARA O USUÁRIO ATUAL
-- Primeiro, vamos buscar dados do auth.users para criar o profile

SELECT 
    'DADOS DO USUÁRIO EM AUTH.USERS' as info,
    u.id,
    u.email,
    u.created_at,
    u.raw_user_meta_data
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 2: CRIAR PROFILE BASEADO NOS DADOS DO AUTH.USERS
INSERT INTO profiles (id, name, email, role, created_at, updated_at)
SELECT 
    u.id,
    COALESCE(
        u.raw_user_meta_data->>'name', 
        u.raw_user_meta_data->>'full_name',
        SPLIT_PART(u.email, '@', 1)
    ) as name,
    u.email,
    'member' as role,
    u.created_at,
    NOW()
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
    AND NOT EXISTS (
        SELECT 1 FROM profiles p WHERE p.id = u.id
    );

-- STEP 3: VERIFICAR SE O PROFILE FOI CRIADO
SELECT 
    'PROFILE CRIADO' as resultado,
    p.id,
    p.name,
    p.email,
    p.role
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 4: ADICIONAR COMO EXECUTOR DA TAREFA
INSERT INTO task_executors (task_id, user_id, created_at)
SELECT 
    '7c606667-9391-4660-933d-90d6bd276e88',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM task_executors 
    WHERE task_id = '7c606667-9391-4660-933d-90d6bd276e88' 
      AND user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
);

-- STEP 5: CONFIRMAR QUE FOI ADICIONADO COMO EXECUTOR
SELECT 
    'CONFIRMAÇÃO: USUÁRIO ATUAL É EXECUTOR' as resultado,
    te.user_id,
    p.name,
    p.email,
    p.role
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 6: LISTAR TODOS OS EXECUTORES DA TAREFA
SELECT 
    'TODOS OS EXECUTORES AGORA' as info,
    te.user_id,
    p.name,
    p.email,
    p.role
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- =====================================================
-- APÓS EXECUTAR ESTE SCRIPT:
-- =====================================================
-- 1. Recarregue a página da aplicação (F5)
-- 2. Navegue para a tarefa
-- 3. Clique na aba "Executar"
-- 4. Agora deve aparecer o conteúdo!
-- =====================================================

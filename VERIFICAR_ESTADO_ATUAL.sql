-- =====================
-- VERIFICAÇÃO IMEDIATA: Estado atual do sistema
-- =====================

-- 1. Verificar se o usuário logado é executor da tarefa
SELECT 
    te.task_id,
    te.user_id,
    p.name as executor_name,
    p.email as executor_email,
    p.role as executor_role,
    t.title as task_title,
    'USER IS EXECUTOR' as status
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
JOIN tasks t ON te.task_id = t.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'  -- Task ID dos logs
ORDER BY te.created_at DESC;

-- 2. Verificar se há blocos de conteúdo para esta tarefa
SELECT 
    tcb.id as block_id,
    tcb.task_id,
    tcb.type,
    tcb.content,
    tcb.created_at,
    'CONTENT BLOCK EXISTS' as status
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
ORDER BY tcb.order, tcb.created_at;

-- 3. Verificar detalhes da tarefa
SELECT 
    t.id,
    t.title,
    t.description,
    t.status,
    t.assigned_to,
    s.name as stage_name,
    p.name as project_name,
    p.owner_id as project_owner
FROM tasks t
JOIN stages s ON t.stage_id = s.id
JOIN projects p ON s.project_id = p.id
WHERE t.id = '7c606667-9391-4660-933d-90d6bd276e88';

-- 4. Verificar se o usuário atual é membro do projeto
SELECT 
    pm.project_id,
    pm.user_id,
    pm.role as project_role,
    pr.name as user_name,
    pr.email as user_email,
    p.name as project_name,
    'PROJECT MEMBER' as status
FROM project_members pm
JOIN profiles pr ON pm.user_id = pr.id
JOIN projects p ON pm.project_id = p.id
WHERE pm.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'  -- User ID dos logs
ORDER BY pm.created_at DESC;

-- 5. Verificar status RLS da tabela task_content_blocks
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    hasrls as has_rls
FROM pg_tables pt
JOIN pg_class pc ON pt.tablename = pc.relname
WHERE schemaname = 'public' 
    AND tablename = 'task_content_blocks';

-- 6. DIAGNÓSTICO: Simular acesso como usuário específico
-- (Descomente para executar como usuário específico)
/*
-- Simular auth.uid() = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
SELECT 
    tcb.*,
    'ACCESSIBLE_TO_USER' as access_test
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND (
        -- Teste: usuário é executor
        tcb.task_id IN (
            SELECT te.task_id FROM task_executors te
            WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
        )
        OR
        -- Teste: usuário é membro do projeto
        tcb.task_id IN (
            SELECT t.id FROM tasks t
            JOIN stages s ON t.stage_id = s.id
            JOIN projects p ON s.project_id = p.id
            WHERE p.id IN (
                SELECT pm.project_id FROM project_members pm
                WHERE pm.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
            )
        )
    );
*/

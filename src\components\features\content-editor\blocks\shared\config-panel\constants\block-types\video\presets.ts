/**
 * Presets específicos para blocos de vídeo
 */

// Definição local do tipo para evitar dependência circular
interface BlockTypePreset {
  icon: {
    name: string;
    position: string;
    type: string;
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  card: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  button?: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
}

export const videoBlockPresets: Record<string, BlockTypePreset> = {
  default: {
    icon: {
      name: 'Video',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#a78bfa', // purple-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#a78bfa', width: 1 },
      shadow: '0 2px 8px #a78bfa40',
      hover: {
        backgroundColor: '#c4b5fd', // purple-300
        color: '#fff',
        shadow: '0 4px 16px #a78bfa40',
        borderColor: '#a78bfa',
      },
    },
    button: {
      backgroundColor: '#a78bfa', // purple-500
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#a78bfa', width: 1 },
      shadow: '0 2px 8px #a78bfa40',
      hover: {
        backgroundColor: '#c4b5fd', // purple-300
        color: '#fff',
        shadow: '0 4px 16px #a78bfa40',
        borderColor: '#a78bfa',
      },
    },
    card: {
      backgroundColor: '#f3f0ff',
      color: '#a78bfa',
      format: 'rounded',
      border: { enabled: true, color: '#a78bfa', width: 1 },
      shadow: '0 2px 8px #a78bfa40',
      hover: {
        backgroundColor: '#c4b5fd',
        color: '#fff',
        shadow: '0 4px 16px #a78bfa40',
        borderColor: '#a78bfa',
      },
    },
  },
  youtube: {
    icon: {
      name: 'Youtube',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#ef4444', // red-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#ef4444', width: 1 },
      shadow: '0 2px 8px #ef444440',
      hover: {
        backgroundColor: '#f87171', // red-400
        color: '#fff',
        shadow: '0 4px 16px #ef444440',
        borderColor: '#ef4444',
      },
    },
    button: {
      backgroundColor: '#ef4444',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#ef4444', width: 1 },
      shadow: '0 2px 8px #ef444440',
      hover: {
        backgroundColor: '#f87171',
        color: '#fff',
        shadow: '0 4px 16px #ef444440',
        borderColor: '#ef4444',
      },
    },
    card: {
      backgroundColor: '#fef2f2',
      color: '#ef4444',
      format: 'rounded',
      border: { enabled: true, color: '#ef4444', width: 1 },
      shadow: '0 2px 8px #ef444440',
      hover: {
        backgroundColor: '#f87171',
        color: '#fff',
        shadow: '0 4px 16px #ef444440',
        borderColor: '#ef4444',
      },
    },
  },
  vimeo: {
    icon: {
      name: 'Play',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#06b6d4', // cyan-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#06b6d4', width: 1 },
      shadow: '0 2px 8px #06b6d440',
      hover: {
        backgroundColor: '#22d3ee', // cyan-400
        color: '#fff',
        shadow: '0 4px 16px #06b6d440',
        borderColor: '#06b6d4',
      },
    },
    button: {
      backgroundColor: '#06b6d4',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#06b6d4', width: 1 },
      shadow: '0 2px 8px #06b6d440',
      hover: {
        backgroundColor: '#22d3ee',
        color: '#fff',
        shadow: '0 4px 16px #06b6d440',
        borderColor: '#06b6d4',
      },
    },
    card: {
      backgroundColor: '#ecfeff',
      color: '#06b6d4',
      format: 'rounded',
      border: { enabled: true, color: '#06b6d4', width: 1 },
      shadow: '0 2px 8px #06b6d440',
      hover: {
        backgroundColor: '#22d3ee',
        color: '#fff',
        shadow: '0 4px 16px #06b6d440',
        borderColor: '#06b6d4',
      },
    },
  },
};

export type VideoBlockVariant = 'youtube' | 'vimeo' | 'upload' | 'embed';

export const videoBlockVariants = {
  youtube: 'YouTube',
  vimeo: 'Vimeo',
  upload: 'Upload',
  embed: 'Embed',
};

export const videoBlockIcons = {
  youtube: 'Youtube',
  vimeo: 'Play',
  upload: 'Upload',
  embed: 'Video',
};
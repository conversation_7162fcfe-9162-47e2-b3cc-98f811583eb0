import React, { useState, useRef } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Header } from '@/components/layout/Header';
import { TaskForm } from '@/components/forms/TaskForm';
import { RichContentEditor } from '@/components/features/content-editor';
import { TextEditorWrapper } from '@/components/features/content-editor/blocks/text';
import { isLexicalJson, convertTextToLexicalJson, EMPTY_LEXICAL_STATE } from '@/lib/utils';
import { useToast } from '@/hooks/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ContentBlock } from '@/types';
import * as LucideIcons from 'lucide-react';
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Play,
  Pause,
  Upload,
  MessageCircle,
  FileText,
  Video,
  Image as ImageIcon,
  Paperclip,
  Send,
  Eye,
  Edit,
  Trash2,
  Plus,
  Settings,
  PlayCircle,
  FolderOpen,
  Target,
  Loader2
} from 'lucide-react';
import { taskService } from '@/services/taskService';
import { userService } from '@/services/userService';
import { stageService } from '@/services/stageService';
import { projectService } from '@/services/projectService';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { useAuth } from '@/auth/useAuth';
import { supabase } from '@/lib/supabaseClient';
import { SidebarProvider, Sidebar, SidebarTrigger } from '@/components/ui/sidebar';
import { useSidebarMenu } from '@/components/ui/sidebar';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { TaskHeader } from './TaskDetails/TaskHeader';
import { TaskContentTabs } from './TaskDetails/TaskContentTabs';
import { TaskComments } from './TaskDetails/TaskComments';
import { TaskControlPanel } from './TaskDetails/TaskControlPanel';
import { TaskTeamPanel } from './TaskDetails/TaskTeamPanel';
import { TaskQuickActions } from './TaskDetails/TaskQuickActions';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { ContentSection } from '@/components/features/content-editor/blocks/shared/ContentSection';
import { VideoBlockEditor } from '@/components/features/content-editor/blocks/video/VideoBlockEditor';
import { ImageBlockEditor } from '@/components/features/content-editor/blocks/image/ImageBlockEditor';
import { QuizBlockEditor } from '@/components/features/content-editor/blocks/quiz/QuizBlockEditor';
import { QuizExecutionBlock } from '@/components/features/content-editor/blocks/quiz/QuizExecutionBlock';
import { ColoredBlockEditor } from '@/components/features/content-editor/blocks/colored-block/ColoredBlockEditor';
import { EvidenceExecutionBlock } from '@/components/features/content-editor/blocks/evidence/EvidenceExecutionBlock';
import { FileBlockEditor } from '@/components/features/content-editor/blocks/file/FileBlockEditor';
import { VideoBlockCard } from '@/components/features/content-editor/blocks/video/VideoBlockCard';
import { BlockCard } from '@/components/ui/BlockCard';
import { AlertBlock } from '@/components/features/content-editor/blocks/alert/AlertBlock';

function isCanvaEmbedUrl(url) {
  return /canva\.com\/design\/.+\/(watch|view)\?embed/.test(url || '');
}

function isCanvaUrl(url) {
  return /canva\.com\/design\/.+\/(watch|view)/.test(url || '');
}

// Função utilitária para ajustar link do Canva
function autoEmbedCanvaUrl(url) {
  if (/canva\.com\/design\/.+\/(watch|view)/.test(url) && !/[?&]embed($|&)/.test(url)) {
    return url + (url.includes('?') ? '&embed' : '?embed');
  }
  return url;
}

// Função utilitária para gerar hash simples do conteúdo dos blocos
function hashBlocks(blocks) {
  try {
    return btoa(unescape(encodeURIComponent(JSON.stringify(blocks)))).slice(0, 12);
  } catch {
    return String(Date.now());
  }
}

// Função utilitária para ordenar blocos
function sortBlocks(blocks) {
  return [...(blocks || [])].sort((a, b) => a.order - b.order);
}

export const TaskDetails = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const [newComment, setNewComment] = useState('');
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [taskData, setTaskData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('content');
  const [taskStatus, setTaskStatus] = useState('in-progress');
  const { toast } = useToast();
  const [contentBlocks, setContentBlocks] = useState<ContentBlock[] | null>(null);
  const { user } = useAuth();
  const [executors, setExecutors] = useState<any[]>([]);
  const [executorsLoading, setExecutorsLoading] = useState(false);
  const [approvers, setApprovers] = useState<any[]>([]);
  const [approversLoading, setApproversLoading] = useState(false);
  const menuItems = useSidebarMenu();
  const [projectMembers, setProjectMembers] = useState<any[]>([]);
  const [savingBlocks, setSavingBlocks] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const saveTimeout = useRef<NodeJS.Timeout | null>(null);
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);
  const [inlineVideoBlockId, setInlineVideoBlockId] = useState<string | null>(null);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  // Mover fetchTask para fora do useEffect para poder reutilizar
  const fetchTask = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await taskService.getFullById(taskId!);
      let responsible = null;
      if (data.assigned_to) responsible = await userService.getById(data.assigned_to);
      const approvers = data.approvers?.length
        ? await Promise.all(data.approvers.map((a: any) => userService.getById(a.user_id)))
        : [];
      const comments = data.comments?.length
        ? await Promise.all(data.comments.map(async (c: any) => ({
            ...c,
            author: await userService.getById(c.author),
            replies: data.comments.filter((r: any) => r.parent_id === c.id)
          })))
        : [];

      // Buscar nome da etapa e do projeto
      let stageName = '';
      let projectId = '';
      let projectName = '';
      if (data.stage_id) {
        const stage = await stageService.getById(data.stage_id);
        stageName = stage?.name || '';
        projectId = stage?.project_id || '';
        if (projectId) {
          const project = await projectService.getById(projectId);
          projectName = project?.name || '';
          // Buscar membros do projeto
          const membersData = await projectService.getProjectMembers(projectId);
          const mappedMembers = (membersData || []).map((m: any) => {
            const profile = m.profile || m.profiles?.[0] || m;
            return profile && profile.id ? {
              id: profile.id,
              name: profile.name,
              email: profile.email,
              avatar_url: profile.avatar_url
            } : null;
          }).filter(Boolean);
          setProjectMembers(mappedMembers);
        }
      }

      setTaskData({ ...data, responsible, approvers, comments, stageName, projectId, projectName });
      // Processar contentBlocks para garantir estrutura correta
      const processedContentBlocks = (data.contentBlocks || []).map((block: any) => {
        let content = block.content;
        // Se vier como string, faz o parse
        if (typeof content === 'string') {
          try {
            content = JSON.parse(content);
          } catch {
            // fallback: mantém como string se não for JSON válido
          }
        }
        // Para blocos de texto, garantir campo text
        if (block.type === 'text') {
          if (!content.text) {
            content.text = '';
          }
        }
        return { ...block, content };
      });
      console.log('[TaskDetails] Blocos carregados do banco:', JSON.stringify(processedContentBlocks, null, 2));
      setContentBlocks(processedContentBlocks);
    } catch (err: any) {
      setError('Erro ao carregar tarefa.');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (taskId) fetchTask();
  }, [taskId]);

  // Sincroniza o status da tarefa carregada com o estado local
  React.useEffect(() => {
    if (taskData?.status) {
      setTaskStatus(taskData.status);
    }
  }, [taskData?.status]);

  // Atualiza executores locais quando taskData mudar
  React.useEffect(() => {
    if (taskData?.executors) setExecutors(taskData.executors);
  }, [taskData?.executors]);

  // Atualiza aprovadores locais quando taskData mudar
  React.useEffect(() => {
    if (taskData?.approvers) setApprovers(taskData.approvers);
  }, [taskData?.approvers]);

  // Função para salvar blocos no banco
  const persistContentBlocks = async (blocks: ContentBlock[]) => {
    if (!taskId) return;
    console.log('[TaskDetails] Salvando contentBlocks:', JSON.stringify(blocks, null, 2));
    setSavingBlocks(true);
    setSaveStatus('saving');
    try {
      await taskService.saveContentBlocks(taskId, blocks);
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 1500);
      // Removido fetchTask() para evitar atualização desnecessária da tela inteira
      // Os blocos já estão atualizados no estado local através de setContentBlocks
    } catch (err) {
      setSaveStatus('error');
      toast({ title: 'Erro ao salvar conteúdo', description: 'Não foi possível salvar os blocos.', variant: 'destructive' });
    } finally {
      setSavingBlocks(false);
    }
  };

  // Handler para alterações nos blocos de conteúdo
  const handleBlockSave = (updatedBlocks: ContentBlock[]) => {
    setContentBlocks(updatedBlocks);
    setUnsavedChanges(true);
  };

  // Handler para salvar conteúdo manualmente
  const handleManualSave = async (blocksArg?: ContentBlock[]) => {
    const blocksToSave = blocksArg || contentBlocks;
    if (!blocksToSave) return;
    setSaveStatus('saving');
    try {
      await persistContentBlocks(blocksToSave);
      setSaveStatus('saved');
      setUnsavedChanges(false);
      toast({ title: 'Conteúdo salvo', description: 'O conteúdo da tarefa foi salvo com sucesso.', variant: 'success' });
    } catch (err) {
      setSaveStatus('error');
      toast({ title: 'Erro ao salvar conteúdo', description: err?.message || 'Erro desconhecido', variant: 'destructive' });
    } finally {
      setTimeout(() => setSaveStatus('idle'), 1500);
    }
  };

  // Handler para salvar a tarefa (campos principais)
  const handleSaveTask = async () => {
    if (!taskData) return;
    try {
      await taskService.update(taskData.id, {
        title: taskData.name || taskData.title,
        description: taskData.description,
        status: taskData.status,
        estimated_hours: taskData.estimated_hours,
        actual_hours: taskData.actual_hours,
        due_date: taskData.due_date,
        priority: taskData.priority,
        assigned_to: taskData.responsible?.id,
        progress: taskData.progress,
        // Adicione outros campos se necessário
      });
      toast({ title: 'Tarefa salva', description: 'As alterações da tarefa foram salvas com sucesso.' });
      fetchTask();
    } catch (err) {
      toast({ title: 'Erro ao salvar tarefa', description: err?.message || 'Erro desconhecido', variant: 'destructive' });
    }
  };

  if (loading) return <div className="p-8 text-center">Carregando...</div>;
  if (error) return <div className="p-8 text-center text-red-600">{error}</div>;

  const task = {
    id: taskData.id,
    name: taskData.title,
    description: taskData.description,
    status: taskData.status,
    progress: taskData.progress ?? 0,
    estimatedHours: taskData.estimated_hours,
    actualHours: taskData.actual_hours,
    responsible: taskData.responsible,
    executors: Array.isArray(taskData.executors) ? taskData.executors.filter(Boolean) : [],
    approvers: taskData.approvers,
    dueDate: taskData.due_date,
    evidence: taskData.attachments?.map((a: any) => ({
      id: a.id,
      type: 'file',
      name: a.description || a.file_url,
      url: a.file_url,
      uploadedBy: a.uploaded_by,
      uploadedAt: a.uploaded_at
    })) || [],
    comments: taskData.comments || [],
    stage: { id: taskData.stage_id, name: taskData.stageName },
    project: { id: taskData.projectId, name: taskData.projectName }
  };

  // Permissão: admin, manager, responsável da tarefa
  const canEditExecutors = user?.role === 'admin' || user?.role === 'manager' || user?.id === taskData?.responsible?.id;

  // Remover executor
  const handleRemoveExecutor = async (executorId: string) => {
    setExecutorsLoading(true);
    const { error } = await supabase.from('task_executors').delete().eq('task_id', task.id).eq('user_id', executorId);
    if (!error) {
      setExecutors((prev) => prev.filter((e) => e.id !== executorId));
      toast({ title: 'Executor removido', description: 'Executor removido com sucesso.' });
    } else {
      toast({ title: 'Erro', description: 'Não foi possível remover o executor.', variant: 'destructive' });
    }
    setExecutorsLoading(false);
  };

  // Adicionar executor
  const handleAddExecutors = async (user: any) => {
    setExecutorsLoading(true);
    if (!executors.some((e) => e.id === user.id)) {
      await supabase.from('task_executors').insert({ task_id: task.id, user_id: user.id });
      setExecutors([...executors, user]);
      toast({ title: 'Executor adicionado', description: 'Executor adicionado com sucesso.' });
    }
    setExecutorsLoading(false);
  };

  // Remover aprovador
  const handleRemoveApprover = async (approverId: string) => {
    setApproversLoading(true);
    const { error } = await supabase.from('task_approvers').delete().eq('task_id', task.id).eq('user_id', approverId);
    if (!error) {
      setApprovers((prev) => prev.filter((a) => a.id !== approverId));
      toast({ title: 'Aprovador removido', description: 'Aprovador removido com sucesso.' });
    } else {
      toast({ title: 'Erro', description: 'Não foi possível remover o aprovador.', variant: 'destructive' });
    }
    setApproversLoading(false);
  };

  // Adicionar aprovador
  const handleAddApprovers = async (user: any) => {
    setApproversLoading(true);
    if (!approvers.some((a) => a.id === user.id)) {
      await supabase.from('task_approvers').insert({ task_id: task.id, user_id: user.id });
      setApprovers([...approvers, user]);
      toast({ title: 'Aprovador adicionado', description: 'Aprovador adicionado com sucesso.' });
    }
    setApproversLoading(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-status-success';
      case 'in-progress': return 'bg-task';
      case 'todo': return 'bg-gray-400';
      case 'review': return 'bg-blue-500';
      default: return 'bg-gray-400';
    }
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      toast({
        title: 'Comentário adicionado',
        description: 'Seu comentário foi adicionado com sucesso.',
      });
      setNewComment('');
    }
  };

  const handleEditTask = () => {
    if (task) {
      setShowTaskForm(true);
    }
  };

  const handleCompleteTask = () => {
    setTaskStatus('completed');
    toast({
      title: 'Tarefa concluída',
      description: 'A tarefa foi marcada como concluída.',
    });
  };

  const handleStatusChange = (newStatus: string) => {
    setTaskStatus(newStatus);
    setTaskData((prev: any) => ({
      ...prev,
      status: newStatus
    }));
    toast({
      title: 'Status atualizado',
      description: `Status da tarefa alterado para: ${newStatus}`,
    });
  };



  const handleWatchVideo = (url: string) => {
    setCurrentVideoUrl(url);
    setVideoModalOpen(true);
  };

  // Funções utilitárias para YouTube
  function isYoutubeUrl(url: string) {
    return /youtube\.com|youtu\.be/.test(url);
  }
  function getYoutubeEmbedUrl(url: string) {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/);
    return match ? `https://www.youtube.com/embed/${match[1]}` : url;
  }

  function getContrastColor(bg) {
    if (!bg || typeof bg !== 'string' || !bg.startsWith('#') || bg.length < 7) return '#312e81';
    const r = parseInt(bg.substr(1,2),16);
    const g = parseInt(bg.substr(3,2),16);
    const b = parseInt(bg.substr(5,2),16);
    const luminance = (0.299*r + 0.587*g + 0.114*b)/255;
    return luminance > 0.5 ? '#312e81' : '#fff';
  }

  const renderContentForExecution = (block: ContentBlock) => {
    switch (block.type) {
      case 'text': {
        const editContent = block.content;
        return <TextEditorWrapper editContent={editContent} setEditContent={() => {}} mode="preview" editorKey={block.id + '-execution'} config={block.config} />;
      }
      case 'video':
        return <VideoBlockCard content={block.content} config={block.config} onWatch={handleWatchVideo} />;
      case 'image':
        return <ImageBlockEditor editContent={block.content} setEditContent={() => {}} mode="preview" config={block.config} />;
      case 'quiz':
        return (
          <QuizExecutionBlock
            content={block.content}
            config={block.config}
            taskId={task?.id || ''}
            blockId={block.id}
            onComplete={(passed, score) => {
              // Callback quando quiz é completado
              console.log('Quiz completado:', { passed, score });
              // Aqui você pode atualizar o progresso da tarefa se necessário
            }}
          />
        );
      case 'evidence':
        return (
          <EvidenceExecutionBlock
            content={block.content}
            config={block.config}
            taskId={task?.id || ''}
            blockId={block.id}
            onEvidenceChange={(evidences) => {
              // Callback quando evidências são alteradas
              console.log('Evidências alteradas:', evidences);
              // Aqui você pode salvar as evidências no banco de dados
            }}
          />
        );
      case 'colored-block': {
        const config = block.config || {};
        return (
          <ColoredBlockEditor
            editContent={block.content}
            setEditContent={() => {}}
            mode="preview"
            config={config}
          />
        );
      }
      case 'file':
        return <FileBlockEditor editContent={block.content} setEditContent={() => {}} mode="preview" config={block.config} />;
      case 'alert': {
        const alertContent = block.content as {
          presetId: string;
          title: string;
          message: string;
          icon: string;
          actionLabel?: string;
          actionUrl?: string;
        };
        return (
          <AlertBlock
            presetId={alertContent.presetId}
            title={alertContent.title}
            message={alertContent.message}
            icon={alertContent.icon}
            actionLabel={alertContent.actionLabel}
            actionUrl={alertContent.actionUrl}
            layout={undefined}
          />
        );
      }
      default:
        return <div className="p-4 bg-gray-100 rounded-lg">Tipo de bloco não suportado</div>;
    }
  };

  const formatDateBR = (dateString?: string) => {
    if (!dateString) return '';
    const d = new Date(dateString);
    if (isNaN(d.getTime())) return dateString;
    return d.toLocaleDateString('pt-BR');
  };

  const canEditContent = canEditExecutors;

  function handleSaveContentBlocks(blocks: ContentBlock[]) {
    setContentBlocks(blocks);
    persistContentBlocks(blocks);
    setUnsavedChanges(false);
  }

  function handleCancelEditContent() {
    fetchTask();
    setUnsavedChanges(false);
  }

  // Permissão de edição: admin, manager ou responsável pela tarefa
  const canEditTaskContent = user?.role === 'admin' || user?.role === 'manager' || user?.id === taskData?.responsible?.id;

  return (
    <>
      <Header showSidebarButton={true} />
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`w-full min-w-0 max-w-full bg-gray-50 transition-all duration-300 ${sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'} md:pl-0 px-4 sm:px-8 py-4 space-y-6 overflow-x-hidden lg:ml-6`}>
        {/* Conteúdo principal da tarefa */}
        <div className="flex flex-col min-h-screen gap-6 min-w-0">
          <main className="flex-1 min-w-0">
            {/* Organograma visual/hierárquico */}
            <div className="w-full min-w-0 overflow-x-auto mb-2">
              <div className="flex flex-col text-sm mt-3 ml-2 mb-4 gap-0.5">
                <button
                  className="flex items-center font-normal text-project hover:bg-blue-50 rounded px-2 py-1 transition"
                  onClick={() => navigate(`/project/${taskData?.projectId}`)}
                  title={taskData?.projectName}
                  type="button"
                >
                  <FolderOpen className="w-4 h-4 mr-2 text-project" />
                  <span className="truncate">{taskData?.projectName}</span>
                </button>
                <div className="flex items-center font-normal ml-6 border-l-2 border-gray-200 pl-3 relative text-stage hover:bg-green-50 rounded px-2 py-1 transition">
                  <span className="absolute -left-6 top-1.5 w-4 h-4 flex items-center justify-center">
                    <span className="block w-0.5 h-6 bg-gray-200"></span>
                  </span>
                  <button
                    className="flex items-center text-stage font-normal"
                    onClick={() => navigate(`/stage/${taskData?.stage_id}`)}
                    title={taskData?.stageName}
                    type="button"
                  >
                    <Target className="w-4 h-4 mr-2 text-stage" />
                    <span className="truncate">{taskData?.stageName}</span>
                  </button>
                </div>
                <div className="flex items-center font-bold ml-12 border-l-2 border-gray-200 pl-3 relative text-task rounded px-2 py-1 shadow-sm">
                  <span className="absolute -left-6 top-1.5 w-4 h-4 flex items-center justify-center">
                    <span className="block w-0.5 h-6 bg-gray-200"></span>
                  </span>
                  <FileText className="w-4 h-4 mr-2 text-task" />
                  <span className="truncate">{taskData?.title || taskData?.name || taskData?.taskName || 'Tarefa'}</span>
                </div>
              </div>
            </div>

            {/* Header da Tarefa */}
            <div className="mt-6 min-w-0 mb-4">
              <TaskHeader
                name={task.name}
                description={task.description}
                status={task.status}
                onEdit={handleEditTask}
                onComplete={handleCompleteTask}
              />
            </div>

            {/* Tabs de Conteúdo/Edição */}
            <div className="min-w-0">
              <TaskContentTabs
                key={hashBlocks(contentBlocks)}
                activeTab={activeTab}
                onTabChange={setActiveTab}
                contentBlocks={contentBlocks}
                onBlocksChange={handleBlockSave}
                renderContentForExecution={renderContentForExecution}
                saveStatus={saveStatus}
                onManualSave={handleManualSave}
                onSaveTask={handleSaveTask}
                unsavedChanges={unsavedChanges}
                onCancel={handleCancelEditContent}
              />
            </div>

            {/* Renderizar apenas se NÃO estiver na aba de edição */}
            {activeTab === 'content' && (
              <>
                {/* MOBILE: ordem linear perfeita */}
                <div className="block lg:hidden space-y-4 mt-6 min-w-0">
                  {/* Ordem: Ações rápidas, Controle, Botão, Conteúdo, Evidências, Equipe, Comentários */}
                  <TaskQuickActions />
                  <TaskControlPanel
                    status={taskStatus}
                    onStatusChange={handleStatusChange}
                    progress={task.progress}
                    responsible={task.responsible}
                    estimatedHours={task.estimatedHours}
                    actualHours={task.actualHours}
                    dueDate={task.dueDate}
                    formatDate={formatDateBR}
                  />
                  <div className="">
                    <Button size="lg" className="w-full font-bold bg-blue-600 hover:bg-blue-700 text-white shadow min-w-0" onClick={handleSaveTask}>
                      <PlayCircle className="w-5 h-5 mr-2" /> Salvar Tarefa
                    </Button>
                  </div>
                  {/* Conteúdo da tarefa */}
                  <Card className="min-w-0">
                    <CardHeader className="min-w-0">
                      <CardTitle className="text-lg font-bold flex items-center gap-2 min-w-0">
                        <PlayCircle className="w-5 h-5 text-blue-600" /> Conteúdo da Tarefa
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 min-w-0">
                      {contentBlocks && contentBlocks.length > 0 ? (
                        sortBlocks(contentBlocks).map((block) => (
                          <BlockCard key={block.id} className="space-y-4 min-w-0">
                            {renderContentForExecution(block)}
                          </BlockCard>
                        ))
                      ) : (
                        <div className="text-center py-12 text-gray-500 min-w-0">
                          <FileText className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                          <h3 className="font-medium text-lg mb-2">Nenhum conteúdo disponível</h3>
                          <p className="text-sm">Adicione conteúdo na aba \"Editar Conteúdo\" para começar.</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Equipe */}
                  <TaskTeamPanel
                    executors={executors}
                    approvers={approvers}
                    canEditExecutors={canEditExecutors}
                    executorsLoading={executorsLoading}
                    approversLoading={approversLoading}
                    onRemoveExecutor={handleRemoveExecutor}
                    onAddExecutor={handleAddExecutors}
                    onRemoveApprover={handleRemoveApprover}
                    onAddApprover={handleAddApprovers}
                  />
                  {/* Comentários */}
                  <Card className="min-w-0">
                    <CardHeader className="min-w-0">
                      <CardTitle className="text-lg font-bold flex items-center gap-2 min-w-0">
                        <MessageCircle className="w-5 h-5 text-blue-600" /> Comentários
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="min-w-0">
                      <TaskComments
                        comments={task.comments}
                        newComment={newComment}
                        onNewCommentChange={setNewComment}
                        onAddComment={handleAddComment}
                      />
                    </CardContent>
                  </Card>
                </div>
                {/* DESKTOP: grid 12 colunas, conteúdo principal destacado */}
                <div className="hidden lg:grid grid-cols-12 gap-8 mt-6 min-w-0">
                  {/* Coluna esquerda: conteúdo ocupa 8/12 colunas (2/3 da tela) */}
                  <div className="col-span-8 space-y-6 min-w-0">
                    <Card className="min-w-0 shadow-lg border-2 border-blue-100">
                      <CardHeader className="min-w-0 pb-2">
                        <CardTitle className="text-2xl font-bold flex items-center gap-2 min-w-0">
                          <PlayCircle className="w-6 h-6 text-blue-600" /> Conteúdo da Tarefa
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6 min-w-0 text-lg">
                        {contentBlocks && contentBlocks.length > 0 ? (
                          sortBlocks(contentBlocks).map((block) => (
                            <div key={block.id} className="space-y-4 min-w-0">
                              {renderContentForExecution(block)}
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-12 text-gray-500 min-w-0">
                            <FileText className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                            <h3 className="font-medium text-lg mb-2">Nenhum conteúdo disponível</h3>
                            <p className="text-sm">Adicione conteúdo na aba \"Editar Conteúdo\" para começar.</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    <Card className="min-w-0">
                      <CardHeader className="min-w-0 pb-2">
                        <CardTitle className="text-lg font-bold flex items-center gap-2 min-w-0">
                          <MessageCircle className="w-5 h-5 text-blue-600" /> Comentários
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="min-w-0">
                        <TaskComments
                          comments={task.comments}
                          newComment={newComment}
                          onNewCommentChange={setNewComment}
                          onAddComment={handleAddComment}
                        />
                      </CardContent>
                    </Card>
                  </div>
                  {/* Coluna direita: cards compactos, ocupa 4/12 colunas (1/3 da tela) */}
                  <div className="col-span-4 space-y-4 min-w-0">
                    {/* Botão Salvar fixo no topo da coluna lateral */}
                    <div className="sticky top-6 z-20 bg-white/80 backdrop-blur-md rounded-lg shadow p-4 border border-blue-100 flex flex-col gap-2">
                      <Button size="lg" className="w-full font-bold bg-blue-600 hover:bg-blue-700 text-white shadow min-w-0" onClick={handleSaveTask}>
                        <PlayCircle className="w-5 h-5 mr-2" /> Salvar Tarefa
                      </Button>
                    </div>
                    <div className="rounded-lg shadow border border-blue-100 bg-white p-4">
                      <TaskControlPanel
                        status={taskStatus}
                        onStatusChange={handleStatusChange}
                        progress={task.progress}
                        responsible={task.responsible}
                        estimatedHours={task.estimatedHours}
                        actualHours={task.actualHours}
                        dueDate={task.dueDate}
                        formatDate={formatDateBR}
                      />
                    </div>
                    <div className="rounded-lg shadow border border-blue-100 bg-white p-4">
                      <TaskQuickActions
                        onSendToReview={() => {}}
                        onRequestApproval={() => {}}
                        onReportProblem={() => {}}
                      />
                    </div>
                    <div className="rounded-lg shadow border border-blue-100 bg-white p-4">
                      <TaskTeamPanel
                        executors={executors}
                        approvers={approvers}
                        canEditExecutors={canEditExecutors}
                        executorsLoading={executorsLoading}
                        approversLoading={approversLoading}
                        onRemoveExecutor={handleRemoveExecutor}
                        onAddExecutor={handleAddExecutors}
                        onRemoveApprover={handleRemoveApprover}
                        onAddApprover={handleAddApprovers}
                      />
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Renderizar editor de conteúdo reutilizável na aba de edição */}
            {activeTab === 'edit' && contentBlocks && (
              <div className="w-full max-w-3xl mx-auto p-4">
                {/* Removido bloco de botões duplicado abaixo do editor */}
              </div>
            )}
          </main>

          <TaskForm
            open={showTaskForm}
            onOpenChange={setShowTaskForm}
            task={{
              ...task,
              title: task.name,
              executors: Array.isArray(task.executors) ? task.executors.filter(Boolean) : []
            }}
            mode="edit"
            stageId={task.stage.id}
            onCreated={fetchTask}
            projectMembers={projectMembers}
          />


        </div>

        {/* Modal de vídeo */}
        <Dialog open={videoModalOpen} onOpenChange={setVideoModalOpen}>
          <DialogContent className="max-w-2xl" aria-describedby="video-modal-desc">
            <DialogTitle>Visualizar vídeo</DialogTitle>
            <div id="video-modal-desc" className="sr-only">Modal para visualização de vídeo da tarefa. Use Tab para navegar e Esc para fechar.</div>
            {currentVideoUrl ? (
              isYoutubeUrl(currentVideoUrl) ? (
                <iframe
                  width="100%"
                  height="400"
                  src={getYoutubeEmbedUrl(currentVideoUrl)}
                  frameBorder="0"
                  allow="autoplay; encrypted-media"
                  allowFullScreen
                  title="Vídeo"
                />
              ) : isCanvaEmbedUrl(currentVideoUrl) ? (
                <div style={{
                  position: 'relative',
                  width: '100%',
                  height: 0,
                  paddingTop: '56.25%',
                  boxShadow: '0 2px 8px 0 rgba(63,69,81,0.16)',
                  marginTop: '1.6em',
                  marginBottom: '0.9em',
                  overflow: 'hidden',
                  borderRadius: '8px',
                  willChange: 'transform'
                }}>
                  <iframe
                    loading="lazy"
                    style={{
                      position: 'absolute',
                      width: '100%',
                      height: '100%',
                      top: 0,
                      left: 0,
                      border: 'none',
                      padding: 0,
                      margin: 0
                    }}
                    src={autoEmbedCanvaUrl(currentVideoUrl)}
                    allowFullScreen
                    allow="fullscreen"
                    title="Vídeo Canva"
                  />
                </div>
              ) : (
                <video src={currentVideoUrl} controls style={{ width: '100%' }} />
              )
            ) : null}
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

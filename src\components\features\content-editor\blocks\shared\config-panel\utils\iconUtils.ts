/**
 * Utilitários para manipulação de ícones
 */
import * as LucideIcons from 'lucide-react';
import { FileText } from 'lucide-react';

export function getLucideComponent(iconName: string | undefined) {
  const icon = iconName && LucideIcons[iconName as keyof typeof LucideIcons];
  if (
    typeof icon === 'function' &&
    '$$typeof' in icon &&
    /^[A-Z]/.test(iconName || '') &&
    !['createLucideIcon', 'default'].includes(iconName || '')
  ) {
    return icon as React.FC;
  }
  return LucideIcons.FileText as React.FC;
}
/**
 * Presets específicos para blocos de texto
 */

// Definição local do tipo para evitar dependência circular
interface BlockTypePreset {
  icon: {
    name: string;
    position: string;
    type: string;
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  card: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  button?: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
}

export const textBlockPresets: Record<string, BlockTypePreset> = {
  default: {
    icon: {
      name: 'AlignLeft',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#3b82f6', // blue-500
      color: '#fff',
      format: 'square',
      border: { enabled: false, color: '#3b82f6', width: 1 },
      shadow: '0 2px 8px #3b82f640',
      hover: {
        backgroundColor: '#60a5fa', // blue-400
        color: '#fff',
        shadow: '0 4px 16px #3b82f640',
        borderColor: '#3b82f6',
      },
    },
    card: {
      backgroundColor: '#f8fafc',
      color: '#3b82f6',
      format: 'square',
      border: { enabled: true, color: '#3b82f6', width: 1 },
      shadow: '0 2px 8px #3b82f640',
      hover: {
        backgroundColor: '#60a5fa',
        color: '#fff',
        shadow: '0 4px 16px #3b82f640',
        borderColor: '#3b82f6',
      },
    },
  },
  minimal: {
    icon: {
      name: 'Type',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#6b7280',
      color: '#fff',
      format: 'circle',
      border: { enabled: false, color: '#6b7280', width: 1 },
      shadow: '0 1px 4px #6b728040',
      hover: {
        backgroundColor: '#9ca3af',
        color: '#fff',
        shadow: '0 2px 8px #6b728040',
        borderColor: '#6b7280',
      },
    },
    card: {
      backgroundColor: '#ffffff',
      color: '#374151',
      format: 'rounded',
      border: { enabled: true, color: '#e5e7eb', width: 1 },
      shadow: '0 1px 3px #0000001a',
      hover: {
        backgroundColor: '#f9fafb',
        color: '#374151',
        shadow: '0 2px 6px #0000001a',
        borderColor: '#d1d5db',
      },
    },
  },
};

export type TextBlockVariant = 'paragraph' | 'heading' | 'quote' | 'code';

export const textBlockVariants = {
  paragraph: 'Parágrafo',
  heading: 'Título',
  quote: 'Citação',
  code: 'Código',
};

export const textBlockIcons = {
  paragraph: 'AlignLeft',
  heading: 'Heading',
  quote: 'Quote',
  code: 'Code',
};
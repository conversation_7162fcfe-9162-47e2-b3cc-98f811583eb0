import React, { useEffect, useState } from 'react';
import { Undo2, Redo2, Bold, Italic, Underline, Strikethrough, Code, Link, List, ListOrdered, AlignLeft, AlignCenter, AlignRight, AlignJustify, Type, Smile, ChevronDown, Plus, Asterisk, Indent, Outdent, CornerDownLeft, Split, Palette, <PERSON>lighter, Settings } from 'lucide-react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  FORMAT_TEXT_COMMAND,
  FORMAT_ELEMENT_COMMAND,
  UNDO_COMMAND,
  REDO_COMMAND,
  $getSelection,
  $isRangeSelection,
  $getNodeByKey,
  $isRootOrShadowRoot,
  SELECTION_CHANGE_COMMAND,
  INDENT_CONTENT_COMMAND,
  OUTDENT_CONTENT_COMMAND,
  KEY_ENTER_COMMAND,
  $createLineBreakNode,
  $createTextNode,
} from 'lexical';
import { INSERT_UNORDERED_LIST_COMMAND, INSERT_ORDERED_LIST_COMMAND, REMOVE_LIST_COMMAND } from '@lexical/list';
import { $isLinkNode, TOGGLE_LINK_COMMAND } from '@lexical/link';
import { INSERT_CHECK_LIST_COMMAND } from '@lexical/list';
import { $createParagraphNode } from 'lexical';
import { $createHeadingNode, $createQuoteNode } from '@lexical/rich-text';
import { $createCodeNode, $isCodeNode, getDefaultCodeLanguage } from '@lexical/code';
import { SUPPORTED_LANGUAGES } from './CodeHighlightPlugin';

const ToolbarButton = ({ children, active, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement> & { active?: boolean }) => (
  <button
    type="button"
    className={`p-2 rounded transition flex items-center justify-center ${active ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-200 focus:bg-gray-300'}`}
    {...props}
  >
    {children}
  </button>
);

const ToolbarSeparator = () => <div className="w-px h-6 bg-gray-200 mx-2" />;

// Componente de seleção de cores
const ColorPicker = ({ currentColor, onColorChange, onClose }: { currentColor: string; onColorChange: (color: string) => void; onClose: () => void }) => {
  const [hexValue, setHexValue] = useState(currentColor);
  
  const predefinedColors = [
    '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#efefef', '#f3f3f3', '#ffffff',
    '#980000', '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#4a86e8', '#0000ff', '#9900ff', '#ff00ff',
    '#e6b8af', '#f4cccc', '#fce5cd', '#fff2cc', '#d9ead3', '#d0e0e3', '#c9daf8', '#cfe2f3', '#d9d2e9', '#ead1dc',
    '#dd7e6b', '#ea9999', '#f9cb9c', '#ffe599', '#b6d7a8', '#a2c4c9', '#a4c2f4', '#9fc5e8', '#b4a7d6', '#d5a6bd',
    '#cc4125', '#e06666', '#f6b26b', '#ffd966', '#93c47d', '#76a5af', '#6d9eeb', '#6fa8dc', '#8e7cc3', '#c27ba0',
    '#a61c00', '#cc0000', '#e69138', '#f1c232', '#6aa84f', '#45818e', '#3c78d8', '#3d85c6', '#674ea7', '#a64d79',
    '#85200c', '#990000', '#b45f06', '#bf9000', '#38761d', '#134f5c', '#1155cc', '#0b5394', '#351c75', '#741b47',
    '#5b0f00', '#660000', '#783f04', '#7f6000', '#274e13', '#0c343d', '#1c4587', '#073763', '#20124d', '#4c1130'
  ];

  const handleHexChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setHexValue(value);
    if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
      onColorChange(value);
    }
  };

  return (
    <div className="absolute top-full left-0 mt-1 bg-white border rounded-lg shadow-lg z-50 p-4 w-64">
      <div className="mb-3">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-sm font-medium">Hex</span>
          <input
            type="text"
            value={hexValue}
            onChange={handleHexChange}
            className="flex-1 px-2 py-1 border rounded text-sm"
            placeholder="#000000"
          />
        </div>
        <div className="w-full h-8 rounded border" style={{ backgroundColor: currentColor }}></div>
      </div>
      <div className="grid grid-cols-10 gap-1 mb-3">
        {predefinedColors.map((color, index) => (
          <button
            key={index}
            className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
            style={{ backgroundColor: color }}
            onClick={() => onColorChange(color)}
            title={color}
          />
        ))}
      </div>
      <div className="flex justify-end">
        <button
          onClick={onClose}
          className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
        >
          Fechar
        </button>
      </div>
    </div>
  );
};

// Componente EmojiPicker
  const EmojiPicker = ({ onEmojiSelect }: { onEmojiSelect: (emoji: string) => void }) => {
    const emojis = [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
      '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
      '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
      '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
      '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
      '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
      '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
      '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
      '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
      '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
      '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏',
      '🙌', '🤲', '🤝', '🙏', '✍️', '💪', '🦾', '🦿', '🦵', '🦶',
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
      '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
      '⭐', '🌟', '✨', '⚡', '☄️', '💥', '🔥', '🌈', '☀️', '🌤️'
    ];

    return (
      <div className="absolute top-full left-0 mt-1 p-3 bg-white border border-gray-300 rounded-lg shadow-lg z-50 w-80">
        <div className="grid grid-cols-10 gap-1 max-h-48 overflow-y-auto">
          {emojis.map((emoji, index) => (
            <button
              key={index}
              className="w-8 h-8 text-lg hover:bg-gray-100 rounded transition-colors flex items-center justify-center"
              onClick={() => onEmojiSelect(emoji)}
              title={emoji}
            >
              {emoji}
            </button>
          ))}
        </div>
      </div>
    );
  };

  // Componente CodeLanguageSelector
  const CodeLanguageSelector = ({ onLanguageSelect, currentLanguage }: { onLanguageSelect: (language: string) => void; currentLanguage: string }) => {
    return (
      <div className="absolute top-full left-0 mt-1 p-2 bg-white border border-gray-300 rounded-lg shadow-lg z-50 w-48">
        <div className="text-xs font-medium text-gray-700 mb-2 px-2">Linguagem do Código</div>
        <div className="max-h-48 overflow-y-auto">
          {SUPPORTED_LANGUAGES.map((lang) => (
            <button
              key={lang.value}
              className={`block w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors ${
                currentLanguage === lang.value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
              }`}
              onClick={() => onLanguageSelect(lang.value)}
            >
              {lang.label}
            </button>
          ))}
        </div>
      </div>
    );
  };

const ToolbarDropdown = ({ label, children }: { label: React.ReactNode; children: React.ReactNode }) => {
  const [open, setOpen] = useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Fecha o dropdown ao clicar fora
  useEffect(() => {
    if (!open) return;
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);

  // Função para fechar ao selecionar uma opção
  const handleOptionClick = (fn: () => void) => () => {
    fn();
    setOpen(false);
  };

  return (
    <div className="relative group" ref={dropdownRef}>
      <button type="button" className="flex items-center gap-1 p-2 rounded hover:bg-gray-200" onClick={() => setOpen((v) => !v)}>
        {label}
        <ChevronDown className="w-4 h-4" />
      </button>
      {open && (
        <div className="absolute left-0 top-full mt-1 bg-white border rounded shadow-lg z-10 min-w-[180px]">
          {/* Clona os filhos e injeta o fechamento ao clicar apenas em botões */}
          {React.Children.map(children, child => {
            if (
              React.isValidElement(child) &&
              child.type === 'button' &&
              typeof child.props.onClick === 'function'
            ) {
              return React.cloneElement(child as React.ReactElement, {
                onClick: handleOptionClick(child.props.onClick)
              });
            }
            return child;
          })}
        </div>
      )}
    </div>
  );
};

function getSelectedNode(selection: any) {
  const anchor = selection.anchor;
  const focus = selection.focus;
  const anchorNode = $getNodeByKey(anchor.key);
  const focusNode = $getNodeByKey(focus.key);
  if (anchorNode === focusNode) {
    return anchorNode;
  }
  const isBackward = selection.isBackward();
  return isBackward ? focusNode : anchorNode;
}

// Função utilitária para transformar o node selecionado
function setBlockTypeInEditor(editor: any, type: string) {
  editor.update(() => {
    console.log('[Lexical] setBlockTypeInEditor chamada para tipo:', type);
    const selection = $getSelection();
    if (!$isRangeSelection(selection)) {
      console.log('[Lexical] Seleção não é RangeSelection. Abortando.');
      return;
    }
    const nodes = selection.getNodes();
    const transformed = new Set();
    nodes.forEach(node => {
      // Busca o node pai de bloco
      let blockNode = node;
      while (blockNode && typeof blockNode.getType === 'function' && !['paragraph', 'heading', 'quote', 'code', 'listitem'].includes(blockNode.getType())) {
        blockNode = blockNode.getParent();
      }
      if (!blockNode || typeof blockNode.getType !== 'function') {
        console.log('[Lexical] Nenhum node de bloco encontrado para:', node);
        return;
      }
      const blockKey = blockNode.getKey();
      if (transformed.has(blockKey)) return; // Evita transformar o mesmo bloco mais de uma vez
      transformed.add(blockKey);
      let nodeType = blockNode.getType();
      console.log('[Lexical] Node de bloco alvo:', blockNode, 'Tipo:', nodeType, 'Texto:', blockNode.getTextContent && blockNode.getTextContent());
      let newNode = null;
      if (type === 'h1' || type === 'h2' || type === 'h3') {
        if (nodeType === 'listitem') {
          // Transforma em parágrafo primeiro
          const paragraph = $createParagraphNode();
          while (blockNode.getFirstChild()) {
            paragraph.append(blockNode.getFirstChild());
          }
          blockNode.replace(paragraph);
          blockNode = paragraph;
          nodeType = blockNode.getType();
          console.log('[Lexical] Transformado listitem em parágrafo:', blockNode, 'Texto:', blockNode.getTextContent && blockNode.getTextContent());
        }
        if (nodeType === 'paragraph' || nodeType === 'heading') {
          newNode = $createHeadingNode(type);
        }
      } else if (type === 'paragraph') {
        if (nodeType === 'listitem' || nodeType === 'heading' || nodeType === 'quote' || nodeType === 'code') {
          newNode = $createParagraphNode();
        }
      } else if (type === 'quote') {
        if (nodeType === 'paragraph' || nodeType === 'heading') {
          newNode = $createQuoteNode();
        }
      } else if (type === 'code') {
        if (nodeType === 'paragraph' || nodeType === 'heading') {
          newNode = $createCodeNode();
        }
      }
      if (newNode) {
        let hasChildren = false;
        while (blockNode.getFirstChild()) {
          newNode.append(blockNode.getFirstChild());
          hasChildren = true;
        }
        if (!hasChildren && blockNode.getTextContent && blockNode.getTextContent()) {
          const { $createTextNode } = require('lexical');
          newNode.append($createTextNode(blockNode.getTextContent()));
        }
        console.log('[Lexical] Novo node criado:', newNode, 'Texto:', newNode.getTextContent && newNode.getTextContent());
        blockNode.replace(newNode);
        console.log('[Lexical] Node após replace:', newNode, 'Tipo:', newNode.getType(), 'Texto:', newNode.getTextContent && newNode.getTextContent());
      } else {
        console.log('[Lexical] Nenhuma transformação aplicada para node:', blockNode, 'Tipo:', nodeType);
      }
    });
  });
}

const TextToolbar = () => {
  const [editor] = useLexicalComposerContext();
  const [formats, setFormats] = useState({
    bold: false,
    italic: false,
    underline: false,
    strikethrough: false,
    code: false,
    link: false,
  });
  const [blockType, setBlockType] = useState('paragraph');
  const [alignment, setAlignment] = useState('left');
  const [listType, setListType] = useState<'none' | 'ul' | 'ol'>('none');
  const [fontColor, setFontColor] = useState('#000000');
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [showFontColorPicker, setShowFontColorPicker] = useState(false);
  const [showBackgroundColorPicker, setShowBackgroundColorPicker] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showCodeLanguageSelector, setShowCodeLanguageSelector] = useState(false);
  const [currentCodeLanguage, setCurrentCodeLanguage] = useState(getDefaultCodeLanguage());
  const fontColorRef = React.useRef<HTMLDivElement>(null);
  const backgroundColorRef = React.useRef<HTMLDivElement>(null);
  const emojiPickerRef = React.useRef<HTMLDivElement>(null);
  const codeLanguageRef = React.useRef<HTMLDivElement>(null);

  // Fechar seletores ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fontColorRef.current && !fontColorRef.current.contains(event.target as Node)) {
        setShowFontColorPicker(false);
      }
      if (backgroundColorRef.current && !backgroundColorRef.current.contains(event.target as Node)) {
        setShowBackgroundColorPicker(false);
      }
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target as Node)) {
        setShowEmojiPicker(false);
      }
      if (codeLanguageRef.current && !codeLanguageRef.current.contains(event.target as Node)) {
        setShowCodeLanguageSelector(false);
      }
    };

    if (showFontColorPicker || showBackgroundColorPicker || showEmojiPicker || showCodeLanguageSelector) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showFontColorPicker, showBackgroundColorPicker, showEmojiPicker, showCodeLanguageSelector]);

  // Atualiza estado de formatação e tipo de bloco
  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          setFormats({
            bold: selection.hasFormat('bold'),
            italic: selection.hasFormat('italic'),
            underline: selection.hasFormat('underline'),
            strikethrough: selection.hasFormat('strikethrough'),
            code: selection.hasFormat('code'),
            link: (() => {
              const node = getSelectedNode(selection);
              const parent = node.getParent();
              return $isLinkNode(parent) || $isLinkNode(node);
            })(),
          });
          // Tipo de bloco
          const anchorNode = selection.anchor.getNode();
          let element = anchorNode.getKey ? anchorNode : anchorNode.getParent();
          while (element && !element.getType) {
            element = element.getParent();
          }
          const type = element && element.getType ? element.getType() : 'paragraph';
          setBlockType(type);
          // Alinhamento
          setAlignment(element && element.getFormat ? element.getFormat() : 'left');
          // Lista
          if (type === 'ul') setListType('ul');
          else if (type === 'ol') setListType('ol');
          else setListType('none');
        }
      });
    });
  }, [editor]);

  // Comandos de bloco avançados
  const handleBlockType = (type: string) => {
    if (['paragraph', 'h1', 'h2', 'h3', 'quote', 'code'].includes(type)) {
      setBlockTypeInEditor(editor, type);
    } else if (type === 'numbered-list') {
      // Verificamos se já estamos em uma lista numerada
      if (listType === 'ol') {
        editor.update(() => {
          editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
          setListType('none');
        });
      } else {
        // Primeiro removemos qualquer lista existente
        if (listType !== 'none') {
          editor.update(() => {
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
          });
        }
        // Depois aplicamos a nova lista
        editor.update(() => {
          editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
          setListType('ol');
        });
      }
    } else if (type === 'bullet-list') {
      // Verificamos se já estamos em uma lista com marcadores
      if (listType === 'ul') {
        editor.update(() => {
          editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
          setListType('none');
        });
      } else {
        // Primeiro removemos qualquer lista existente
        if (listType !== 'none') {
          editor.update(() => {
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
          });
        }
        // Depois aplicamos a nova lista
        editor.update(() => {
          editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
          setListType('ul');
        });
      }
    } else if (type === 'check-list') {
      // Primeiro removemos qualquer lista existente
      if (listType !== 'none') {
        editor.update(() => {
          editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
        });
      }
      // Depois aplicamos a nova lista
      editor.update(() => {
        editor.dispatchCommand(INSERT_CHECK_LIST_COMMAND, undefined);
        setListType('check');
      });
    }
  };

  // Comandos de alinhamento
  const handleAlignment = (align: 'left' | 'center' | 'right' | 'justify') => {
    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, align);
  };

  // Comandos de lista
  const handleList = (type: 'ul' | 'ol') => {
    editor.update(() => {
      // Primeiro verificamos se já estamos em uma lista do mesmo tipo
      if (listType === type) {
        // Se estamos, removemos a lista
        editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
        setListType('none');
        return;
      }

      const selection = $getSelection();
      if (!$isRangeSelection(selection)) {
        console.log('[Lexical] Seleção não é RangeSelection ao aplicar lista.');
        return;
      }

      // Verificamos se a seleção está vazia (cursor apenas)
      if (selection.isCollapsed()) {
        console.log('[Lexical] Seleção está colapsada, aplicando apenas ao parágrafo atual');
        // Aplicamos apenas ao parágrafo atual
      } else {
        console.log('[Lexical] Seleção não está colapsada, aplicando aos nós selecionados');
        // Aplicamos aos nós selecionados
      }

      // Primeiro removemos qualquer lista existente
      if (listType !== 'none') {
        editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
      }

      // Aplicamos o comando apropriado
      if (type === 'ul') {
        editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
        setListType('ul');
      } else if (type === 'ol') {
        editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
        setListType('ol');
      }
    });
  };

  // Comandos de indentação
  const handleIndent = () => {
    editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined);
  };

  const handleOutdent = () => {
    editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined);
  };

  // Comando de link
  const handleLink = () => {
    if (formats.link) {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
    } else {
      const url = window.prompt('Digite a URL do link:');
      if (url) {
        editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);
      }
    }
  };

  // Comandos de quebra de linha
  const handleLineBreak = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const lineBreak = $createLineBreakNode();
        selection.insertNodes([lineBreak]);
      }
    });
  };

  const handleParagraphBreak = () => {
    // Simula Shift+Enter para quebra de linha
    const event = new KeyboardEvent('keydown', {
      key: 'Enter',
      shiftKey: true,
      bubbles: true,
      cancelable: true
    });
    editor.dispatchCommand(KEY_ENTER_COMMAND, event);
  };

  // Funções para aplicar cores
  const handleFontColor = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const nodes = selection.getNodes();
        nodes.forEach(node => {
          const parent = node.getParent();
          if (parent && parent.setStyle) {
            const currentStyle = parent.getStyle() || '';
            const newStyle = currentStyle.replace(/color:[^;]*;?/g, '') + `color: ${color};`;
            parent.setStyle(newStyle.replace(/;+/g, ';').replace(/^;|;$/g, ''));
          } else if (node.setStyle) {
            const currentStyle = node.getStyle() || '';
            const newStyle = currentStyle.replace(/color:[^;]*;?/g, '') + `color: ${color};`;
            node.setStyle(newStyle.replace(/;+/g, ';').replace(/^;|;$/g, ''));
          }
        });
      }
    });
    setFontColor(color);
    setShowFontColorPicker(false);
  };

  const handleBackgroundColor = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const nodes = selection.getNodes();
        nodes.forEach(node => {
          const parent = node.getParent();
          if (parent && parent.setStyle) {
            const currentStyle = parent.getStyle() || '';
            const newStyle = currentStyle.replace(/background-color:[^;]*;?/g, '') + `background-color: ${color};`;
            parent.setStyle(newStyle.replace(/;+/g, ';').replace(/^;|;$/g, ''));
          } else if (node.setStyle) {
            const currentStyle = node.getStyle() || '';
            const newStyle = currentStyle.replace(/background-color:[^;]*;?/g, '') + `background-color: ${color};`;
            node.setStyle(newStyle.replace(/;+/g, ';').replace(/^;|;$/g, ''));
          }
        });
      }
    });
    setBackgroundColor(color);
    setShowBackgroundColorPicker(false);
  };

  const handleEmojiSelect = (emoji: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        selection.insertText(emoji);
      }
    });
    setShowEmojiPicker(false);
  };

  const handleCodeLanguageChange = (language: string) => {
    console.log('Formatting text as code with language:', language);
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // Pega o texto selecionado
        const selectedText = selection.getTextContent();
        console.log('Selected text:', selectedText);
        
        // Cria um novo nó de código com a linguagem especificada
        const codeNode = $createCodeNode(language);
        
        // Se há texto selecionado, adiciona ao nó de código
        if (selectedText) {
          codeNode.append($createTextNode(selectedText));
        } else {
          // Se não há texto selecionado, adiciona um texto placeholder
          codeNode.append($createTextNode('// Digite seu código aqui'));
        }
        
        // Remove o conteúdo selecionado e insere o nó de código
        selection.removeText();
        selection.insertNodes([codeNode]);
        
        console.log('Code block created with language:', language);
      }
    });
    setCurrentCodeLanguage(language);
    setShowCodeLanguageSelector(false);
  };

  // Determinar label do tipo de bloco atual (em português)
  const blockTypeLabel =
    blockType === 'paragraph' ? 'Normal' :
    blockType === 'h1' ? 'Título 1' :
    blockType === 'h2' ? 'Título 2' :
    blockType === 'h3' ? 'Título 3' :
    blockType === 'ul' ? 'Lista com Marcadores' :
    blockType === 'ol' ? 'Lista Numerada' :
    blockType === 'check' ? 'Lista de Tarefas' :
    blockType === 'quote' ? 'Citação' :
    blockType === 'code' ? 'Bloco de Código' :
    'Normal';

  return (
    <div className="flex flex-wrap items-center gap-1 p-2 border-b bg-gray-50 rounded-t-lg">
      {/* Desfazer/Refazer */}
      <ToolbarButton onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}><Undo2 className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}><Redo2 className="w-4 h-4" /></ToolbarButton>
      <ToolbarSeparator />

      {/* Tipo de bloco */}
      <ToolbarDropdown label={<span className="font-medium">{blockTypeLabel}</span>}>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('paragraph')}>Normal</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('h1')}>Título 1</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('h2')}>Título 2</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('h3')}>Título 3</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('numbered-list')}>Lista Numerada</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('bullet-list')}>Lista com Marcadores</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('check-list')}>Lista de Tarefas</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('quote')}>Citação</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100" onClick={() => handleBlockType('code')}>Bloco de Código</button>
      </ToolbarDropdown>
      <ToolbarSeparator />

      {/* Formatação básica */}
      <ToolbarButton active={formats.bold} onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold')}><Bold className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton active={formats.italic} onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic')}><Italic className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton active={formats.underline} onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline')}><Underline className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton active={formats.strikethrough} onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'strikethrough')}><Strikethrough className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton active={formats.code} onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'code')}><Code className="w-4 h-4" /></ToolbarButton>
      
      {/* Seletor de linguagem de código */}
      <div className="relative" ref={codeLanguageRef}>
        <ToolbarButton 
          onClick={() => {
            setShowCodeLanguageSelector(!showCodeLanguageSelector);
            setShowFontColorPicker(false);
            setShowBackgroundColorPicker(false);
            setShowEmojiPicker(false);
          }}
          title="Selecionar linguagem do código"
        >
          <Settings className="w-4 h-4" />
        </ToolbarButton>
        {showCodeLanguageSelector && (
          <CodeLanguageSelector 
            currentLanguage={currentCodeLanguage}
            onLanguageSelect={handleCodeLanguageChange}
          />
        )}
      </div>
      
      <ToolbarButton active={formats.link} onClick={handleLink}><Link className="w-4 h-4" /></ToolbarButton>
      <ToolbarSeparator />

      {/* Quebras de linha */}
      <ToolbarButton onClick={handleLineBreak} title="Quebra de linha (Shift+Enter)"><CornerDownLeft className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton onClick={handleParagraphBreak} title="Quebra de linha suave"><Split className="w-4 h-4" /></ToolbarButton>
      <ToolbarSeparator />

      {/* Listas */}
      <ToolbarButton active={listType === 'ul'} onClick={() => handleList('ul')}><List className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton active={listType === 'ol'} onClick={() => handleList('ol')}><ListOrdered className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton onClick={handleIndent}><Indent className="w-4 h-4" /></ToolbarButton>
      <ToolbarButton onClick={handleOutdent}><Outdent className="w-4 h-4" /></ToolbarButton>
      <ToolbarSeparator />

      {/* Alinhamento */}
      <ToolbarDropdown label={
        alignment === 'center' ? <AlignCenter className="w-4 h-4" /> :
        alignment === 'right' ? <AlignRight className="w-4 h-4" /> :
        alignment === 'justify' ? <AlignJustify className="w-4 h-4" /> :
        <AlignLeft className="w-4 h-4" />
      }>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2" onClick={() => handleAlignment('left')}><AlignLeft className="w-4 h-4" />Alinhar à Esquerda</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2" onClick={() => handleAlignment('center')}><AlignCenter className="w-4 h-4" />Centralizar</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2" onClick={() => handleAlignment('right')}><AlignRight className="w-4 h-4" />Alinhar à Direita</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2" onClick={() => handleAlignment('justify')}><AlignJustify className="w-4 h-4" />Justificar</button>
      </ToolbarDropdown>
      <ToolbarSeparator />

      {/* Cor da fonte e cor do fundo */}
      <div className="relative" ref={fontColorRef}>
        <ToolbarButton 
          onClick={() => {
            setShowFontColorPicker(!showFontColorPicker);
            setShowBackgroundColorPicker(false);
          }}
          title="Cor da fonte"
        >
          <div className="relative">
            <Palette className="w-4 h-4" />
            <div 
              className="absolute -bottom-1 left-0 w-4 h-1 rounded"
              style={{ backgroundColor: fontColor }}
            ></div>
          </div>
        </ToolbarButton>
        {showFontColorPicker && (
          <ColorPicker
            currentColor={fontColor}
            onColorChange={handleFontColor}
            onClose={() => setShowFontColorPicker(false)}
          />
        )}
      </div>
      <div className="relative" ref={backgroundColorRef}>
        <ToolbarButton 
          onClick={() => {
            setShowBackgroundColorPicker(!showBackgroundColorPicker);
            setShowFontColorPicker(false);
          }}
          title="Cor do fundo"
        >
          <div className="relative">
            <Highlighter className="w-4 h-4" />
            <div 
              className="absolute -bottom-1 left-0 w-4 h-1 rounded"
              style={{ backgroundColor: backgroundColor }}
            ></div>
          </div>
        </ToolbarButton>
        {showBackgroundColorPicker && (
          <ColorPicker
            currentColor={backgroundColor}
            onColorChange={handleBackgroundColor}
            onClose={() => setShowBackgroundColorPicker(false)}
          />
        )}
      </div>
      <ToolbarSeparator />

      {/* Emoji */}
      <div className="relative" ref={emojiPickerRef}>
        <ToolbarButton 
          onClick={() => {
            setShowEmojiPicker(!showEmojiPicker);
            setShowFontColorPicker(false);
            setShowBackgroundColorPicker(false);
          }}
          title="Inserir emoji"
        >
          <Smile className="w-4 h-4" />
        </ToolbarButton>
        {showEmojiPicker && (
          <EmojiPicker onEmojiSelect={handleEmojiSelect} />
        )}
      </div>
      <ToolbarSeparator />

      {/* + Inserir (placeholder) */}
      <ToolbarDropdown label={<><Plus className="w-4 h-4" /> Inserir</>}>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2"><Type className="w-4 h-4" />Texto</button>
        <button className="block w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2"><Asterisk className="w-4 h-4" />Divisor</button>
        {/* Adicione mais opções conforme necessário */}
      </ToolbarDropdown>
    </div>
  );
};

export default TextToolbar;
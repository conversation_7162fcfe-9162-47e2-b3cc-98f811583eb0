# 🔍 Diagnóstico: Autocomplete mostra apenas Administradores

## 🚨 **Problema Identificado**

O autocomplete de usuários nos campos de **Executores** e **Aprovadores** está limitado e mostra apenas usuários com role `admin`, excluindo `member` e outros roles.

## 🎯 **Causa Raiz: Política RLS Restritiva**

A política RLS atual na tabela `profiles` está muito restritiva:

```sql
-- POLÍTICA ATUAL (RESTRITIVA)
CREATE POLICY "profiles_collaboration" ON public.profiles FOR SELECT USING (
  id = auth.uid() OR -- Próprio perfil
  id IN ( -- APENAS membros de projetos comuns
    SELECT DISTINCT pm1.user_id 
    FROM project_members pm1 
    JOIN project_members pm2 ON pm1.project_id = pm2.project_id 
    WHERE pm2.user_id = auth.uid()
  )
);
```

### ⚠️ **Por que isso limita aos administradores:**

1. **Administradores** geralmente estão em **mais projetos**
2. **Membros comuns** podem não ter projetos em comum
3. **Novos usuários** ficam invisíveis até serem adicionados a projetos
4. **Colaboração limitada** - não permite formar novas equipes

## 🛠️ **Soluções Disponíveis**

### **Solução 1: Política Mais Permissiva (Recomendada)**
```sql
-- Execute: CORRECAO_RLS_AUTOCOMPLETE.sql
CREATE POLICY "profiles_autocomplete_friendly" ON public.profiles FOR SELECT USING (
  id = auth.uid() OR 
  (
    is_active = true AND 
    (
      -- Membros de projetos comuns
      id IN (SELECT DISTINCT pm1.user_id FROM project_members pm1 
             JOIN project_members pm2 ON pm1.project_id = pm2.project_id 
             WHERE pm2.user_id = auth.uid())
      OR
      -- OU usuários que podem ser adicionados (gerentes/admins)
      EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_id = auth.uid()
              AND EXISTS (SELECT 1 FROM projects p WHERE p.id = pm.project_id 
                         AND (p.owner_id = auth.uid() OR pm.role IN ('admin', 'manager'))))
    )
  )
);
```

### **Solução 2: Política Simples (Para teste rápido)**
```sql
-- Execute: CORRECAO_RLS_SIMPLES.sql
CREATE POLICY "profiles_simple_autocomplete" ON public.profiles FOR SELECT USING (
  id = auth.uid() OR    -- Próprio perfil
  is_active = true      -- TODOS os usuários ativos
);
```

## 🧪 **Como Testar**

### **1. Executar Script SQL**
```sql
-- No Supabase SQL Editor, execute:
\i CORRECAO_RLS_SIMPLES.sql
```

### **2. Verificar no Console do Navegador**
```javascript
// Cole no console da página da tarefa:
fetch('/debug-user-autocomplete.js').then(r=>r.text()).then(eval);
```

### **3. Testar Autocomplete**
1. Acesse uma tarefa
2. Vá na aba "Visão Geral" → Card "Equipe da Tarefa"
3. Clique no campo "Buscar e adicionar membros..."
4. **Resultado esperado**: Lista todos os usuários ativos

## 📊 **Diagnóstico Detalhado**

### **Scripts Disponíveis:**

1. **`diagnostico_autocomplete_admin.sql`** - Diagnóstico completo via SQL
2. **`debug-user-autocomplete.js`** - Debug via console do navegador
3. **`CORRECAO_RLS_AUTOCOMPLETE.sql`** - Correção com política balanceada
4. **`CORRECAO_RLS_SIMPLES.sql`** - Correção simples para teste

### **Logs para Monitorar:**

No console do navegador, verifique:
```
[TaskDetailsV2] Membros do projeto mapeados: Array(X)
[TaskTeamPanel] Membros do projeto recebidos: Array(X)
[UserAutocomplete] Renderizando com: {usersLength: X}
[UserAutocomplete] Mostrando todos os membros: Array(X)
```

## 🔐 **Considerações de Segurança**

### **Política Recomendada (Balanceada):**
- ✅ Mostra usuários de projetos comuns
- ✅ Permite gerentes/admins ver mais usuários
- ✅ Mantém privacidade entre usuários sem conexão
- ✅ Facilita formação de equipes

### **Política Simples (Para teste):**
- ✅ Mostra todos os usuários ativos
- ⚠️ Menos privacidade (todos veem todos)
- ✅ Facilita colaboração máxima
- ✅ Adequada para empresas pequenas

## 🎯 **Próximos Passos**

1. **Execute a correção SQL** (SIMPLES para teste rápido)
2. **Teste o autocomplete** na interface
3. **Monitore os logs** no console
4. **Se funcionar**, considere a política balanceada para produção
5. **Documente** a mudança para a equipe

## ⚡ **Ação Imediata**

**Para resolver agora mesmo:**
```sql
-- Execute no Supabase SQL Editor:
DROP POLICY IF EXISTS "profiles_collaboration" ON public.profiles;
CREATE POLICY "profiles_simple_autocomplete" ON public.profiles FOR SELECT USING (
  id = auth.uid() OR is_active = true
);
```

**Resultado:** Autocomplete deve mostrar todos os usuários ativos imediatamente! 🎉

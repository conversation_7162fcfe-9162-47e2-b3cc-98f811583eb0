# Content Editor Blocks

## Visão Geral

Esta pasta contém todos os blocos modulares do editor de conteúdo, organizados por tipo (alert, colored-block, file, image, quiz, text, video) e uma pasta `shared/` para componentes, utilitários e tipos compartilhados.

## Estrutura

```
blocks/
  alert/
  colored-block/
  file/
  image/
  quiz/
  text/
  video/
  shared/
    ColorPicker.tsx
    IconPicker.tsx
    config-panel/
      components/
      constants/
      types/
      utils/
    utils/
    hooks/
    __tests__/
  index.ts
```

- **Cada tipo de bloco** possui sua própria pasta e um `index.ts` para exportação.
- **Tudo que é compartilhado** (componentes, utilitários, presets, tipos) deve estar em `shared/`.
- **Testes de utilitários** ficam em `shared/__tests__/`.

## Padrões e Boas Práticas

- **Centralização:**
  - Use sempre os utilitários e componentes centralizados de cor, ícone, presets e tipos.
  - Não crie ColorPicker, IconPicker, colorUtils ou iconUtils fora de `shared/`.
- **Modularização:**
  - Se um arquivo de bloco crescer demais (>300 linhas), divida em subcomponentes auxiliares.
  - Exporte componentes compartilhados de forma nomeada (evite `export default`).
- **Documentação:**
  - Mantenha este README atualizado ao adicionar novos blocos ou utilitários.
- **Testes:**
  - Adicione testes para utilitários e componentes críticos.
- **Limpeza:**
  - Remova arquivos vazios, duplicados ou obsoletos periodicamente.

## Convenções de Importação

- Para utilitários compartilhados:
  ```ts
  import { ColorPicker } from '../shared/ColorPicker';
  import { getPureContrastColor } from '../shared/utils/colorUtils';
  ```
- Para presets e tipos:
  ```ts
  import { ALERT_PRESETS } from '../shared/config-panel/constants/block-types/alert/presets';
  import { BlockConfig } from '@/types';
  ```

## Manutenção

- Antes de criar um novo utilitário ou componente, verifique se já existe algo semelhante em `shared/`.
- Siga sempre as regras de modularização e centralização para garantir escalabilidade e fácil manutenção. 
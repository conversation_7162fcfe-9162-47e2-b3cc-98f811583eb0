-- =====================
-- VERIFICAR STATUS ATUAL DO BANCO
-- Execute este script para diagnosticar problemas
-- =====================

-- VERIFICAR RLS STATUS
SELECT 'STATUS RLS ATUAL:' as check_type;
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    case 
        when rowsecurity then '❌ RLS ATIVO (pode causar problemas)'
        else '✅ RLS DESABILITADO'
    end as status
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN ('profiles', 'projects', 'stages', 'tasks')
ORDER BY tablename;

-- VERIFICAR POLÍTICAS EXISTENTES
SELECT 'POLÍTICAS EXISTENTES:' as check_type;
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd as command_type,
    case 
        when cmd = 'SELECT' then '⚠️ POLÍTICA DE SELECT (pode causar recursão)'
        else '📝 ' || cmd
    end as policy_info
FROM pg_policies 
WHERE schemaname = 'public' 
    AND tablename IN ('profiles', 'projects', 'stages', 'tasks')
ORDER BY tablename, policyname;

-- VERIFICAR PERFIS DUPLICADOS
SELECT 'PERFIS DUPLICADOS:' as check_type;
SELECT 
    id,
    email,
    name,
    count(*) as duplicate_count,
    case 
        when count(*) > 1 then '❌ DUPLICADO'
        else '✅ ÚNICO'
    end as status
FROM public.profiles 
GROUP BY id, email, name
HAVING count(*) > 1
ORDER BY duplicate_count DESC;

-- VERIFICAR PERFIL ESPECÍFICO PROBLEMÁTICO
SELECT 'PERFIL ESPECÍFICO (4b09be1f-5187-44c0-9b53-87b7c57e45b4):' as check_type;
SELECT 
    id,
    email,
    name,
    created_at,
    updated_at
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
ORDER BY created_at;

-- VERIFICAR PROJETO ESPECÍFICO PROBLEMÁTICO
SELECT 'PROJETO ESPECÍFICO (17b4065f-7179-4c95-868d-4fa168c368f9):' as check_type;
SELECT 
    id,
    name,
    status,
    owner_id,
    created_at
FROM public.projects 
WHERE id = '17b4065f-7179-4c95-868d-4fa168c368f9';

-- CONTAR REGISTROS TOTAIS
SELECT 'CONTAGEM DE REGISTROS:' as check_type;
SELECT 
    'profiles' as table_name,
    count(*) as total_records
FROM public.profiles
UNION ALL
SELECT 
    'projects' as table_name,
    count(*) as total_records
FROM public.projects
UNION ALL
SELECT 
    'stages' as table_name,
    count(*) as total_records
FROM public.stages
UNION ALL
SELECT 
    'tasks' as table_name,
    count(*) as total_records
FROM public.tasks
ORDER BY table_name;

-- RESUMO FINAL
SELECT 'RESUMO DO DIAGNÓSTICO:' as summary;
SELECT 
    case 
        when (SELECT count(*) FROM pg_tables WHERE rowsecurity = true AND tablename IN ('profiles', 'projects', 'stages', 'tasks')) > 0 
        then '❌ RLS ATIVO - Execute emergency_fix.sql'
        else '✅ RLS DESABILITADO - Problema pode ser no código'
    end as rls_status,
    case 
        when (SELECT count(*) FROM pg_policies WHERE tablename IN ('profiles', 'projects', 'stages', 'tasks')) > 0 
        then '❌ POLÍTICAS EXISTEM - Execute emergency_fix.sql'
        else '✅ NENHUMA POLÍTICA'
    end as policies_status,
    case 
        when (SELECT count(*) FROM (SELECT id FROM public.profiles GROUP BY id HAVING count(*) > 1) as duplicates) > 0 
        then '❌ PERFIS DUPLICADOS - Execute emergency_fix.sql'
        else '✅ NENHUM DUPLICADO'
    end as duplicates_status;

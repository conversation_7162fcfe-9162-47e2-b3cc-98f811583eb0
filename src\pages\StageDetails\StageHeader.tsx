import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { User, Calendar, Clock } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { RequirePermission } from '@/components/auth/PermissionWrappers';

interface StageHeaderProps {
  status?: string;
  name?: string;
  description?: string;
  responsible?: { name?: string; avatar_url?: string };
  startDate?: string;
  endDate?: string;
  progress?: number;
  onEdit?: () => void;
  projectId?: string;
}

const formatDateBR = (dateString?: string) => {
  if (!dateString) return '';
  const [year, month, day] = dateString.split('-');
  if (!year || !month || !day) return dateString;
  return `${day}/${month}/${year}`;
};

export const StageHeader: React.FC<StageHeaderProps> = ({
  status,
  name,
  description,
  responsible,
  startDate,
  endDate,
  progress,
  onEdit,
  projectId,
}) => {
  return (
    <Card className="border-stage/20 bg-stage-bg">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge 
                variant="secondary" 
                className="bg-stage text-white hover:text-stage hover:bg-white border-stage transition-colors"
              >
                Etapa
              </Badge>
              <Badge variant="outline" className="border-stage text-stage">
                {status === 'in-progress' ? 'Em Andamento' : status === 'completed' ? 'Concluída' : 'Pendente'}
              </Badge>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">{name || 'Etapa'}</h1>
            <p className="text-gray-600 max-w-2xl">{description || ''}</p>
          </div>
          <RequirePermission permissions="edit_stage" projectId={projectId}>
            <Button 
              size="sm" 
              className="bg-stage hover:bg-stage-dark"
              onClick={onEdit}
            >
              Editar Etapa
            </Button>
          </RequirePermission>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="flex items-center gap-3">
            <User className="w-5 h-5 text-stage" />
            <div>
              <p className="text-sm font-medium">Responsável</p>
              <div className="flex items-center gap-2">
                <Avatar className="w-6 h-6">
                  <AvatarImage src={responsible?.avatar_url || '/placeholder.svg'} />
                  <AvatarFallback>{responsible?.name?.[0] || '?'}</AvatarFallback>
                </Avatar>
                <span className="text-sm text-gray-600">{responsible?.name || 'Responsável'}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Calendar className="w-5 h-5 text-stage" />
            <div>
              <p className="text-sm font-medium">Prazo</p>
              <p className="text-sm text-gray-600">{formatDateBR(startDate) || ''} - {formatDateBR(endDate) || ''}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Clock className="w-5 h-5 text-stage" />
            <div>
              <p className="text-sm font-medium">Progresso</p>
              <div className="flex items-center gap-2">
                <Progress value={progress ?? 0} className="w-20" />
                <span className="text-sm font-medium">{progress ?? 0}%</span>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}; 
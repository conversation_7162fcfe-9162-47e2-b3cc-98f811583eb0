-- CRIAR DADOS DE TESTE PARA TASK_EXECUTORS
-- Este script vai atribuir o usuário wgvasque como executor das tarefas existentes

-- Inserir dados de teste na tabela task_executors
-- Vamos atribuir o usuário wgvasque como executor das tarefas existentes
INSERT INTO task_executors (task_id, user_id, created_at, updated_at)
SELECT 
    t.id as task_id,
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4' as user_id,
    NOW() as created_at,
    NOW() as updated_at
FROM tasks t
WHERE t.id IN (
    '7c606667-9391-4660-933d-90d6bd276e88',
    '408f3082-3cc2-4107-b163-9f86fc7d6e3c',
    'd23bd1ab-1176-43c0-9b58-620a4500b7a5'
)
ON CONFLICT (task_id, user_id) DO NOTHING;

-- Verificar se os dados foram inseridos
SELECT 
    te.task_id,
    te.user_id,
    t.title,
    te.created_at
FROM task_executors te
JOIN tasks t ON te.task_id = t.id
WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
ORDER BY te.created_at DESC;

-- Verificar o total de executores no sistema
SELECT COUNT(*) as total_executors FROM task_executors;

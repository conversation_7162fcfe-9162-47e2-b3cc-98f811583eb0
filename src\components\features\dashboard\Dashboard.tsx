import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useProjectStore } from '@/store/useProjectStore';
import { MetricCard } from '@/components/ui/MetricCard';
import { ProjectCard } from '@/components/ui/ProjectCard';
import { ProjectFilters } from './ProjectFilters';
import { ProjectForm } from '@/components/forms/ProjectForm';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ProjectStatus } from '@/types';
import { 
  FolderOpen, 
  CheckCircle, 
  Clock, 
  Users,
  TrendingUp,
  Activity,
  Plus
} from 'lucide-react';

export const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const {
    projects,
    dashboardMetrics,
    isLoading,
    error,
    loadProjects,
    loadDashboardMetrics,
    setSelectedProject
  } = useProjectStore();

  const [searchTerm, setSearchTerm] = React.useState('');
  const [statusFilters, setStatusFilters] = React.useState<ProjectStatus[]>([]);
  const [showProjectForm, setShowProjectForm] = React.useState(false);

  // Carregar dados iniciais
  React.useEffect(() => {
    console.log('Dashboard: Carregando dados iniciais');
    loadProjects();
    loadDashboardMetrics();
  }, [loadProjects, loadDashboardMetrics]);

  // Aplicar filtros
  React.useEffect(() => {
    console.log('Dashboard: Aplicando filtros', { searchTerm, statusFilters });
    const filters = {
      search: searchTerm || undefined,
      status: statusFilters.length > 0 ? statusFilters : undefined
    };
    loadProjects(filters);
  }, [searchTerm, statusFilters, loadProjects]);

  const handleProjectClick = (project: any) => {
    console.log('Dashboard: Projeto selecionado', project.name);
    setSelectedProject(project);
    navigate(`/project/${project.id}`);
  };

  const handleViewDetails = (project: any) => {
    console.log('Dashboard: Visualizar detalhes originais', project.name);
    setSelectedProject(project);
    navigate(`/project/${project.id}`);
  };

  const handleViewDetails2 = (project: any) => {
    console.log('Dashboard: Visualizar detalhes layout 2', project.name);
    setSelectedProject(project);
    navigate(`/project2/${project.id}`);
  };

  const handleNewProject = () => {
    setShowProjectForm(true);
  };

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => loadProjects()}
            className="text-project hover:text-project-dark underline"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8 animate-fade-in">
      {/* Cabeçalho */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">
          Visão geral dos seus projetos e atividades
        </p>
      </div>

      {/* Métricas */}
      {dashboardMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total de Projetos"
            value={dashboardMetrics.totalProjects}
            icon={<FolderOpen className="w-6 h-6" />}
            color="project"
            change={{ value: "+2 este mês", trend: "up" }}
          />
          <MetricCard
            title="Projetos Ativos"
            value={dashboardMetrics.activeProjects}
            icon={<Activity className="w-6 h-6" />}
            color="stage"
            change={{ value: `${dashboardMetrics.avgProgress}% progresso médio`, trend: "up" }}
          />
          <MetricCard
            title="Tarefas Concluídas"
            value={dashboardMetrics.completedTasks}
            icon={<CheckCircle className="w-6 h-6" />}
            color="task"
            change={{ value: "+8 esta semana", trend: "up" }}
          />
          <MetricCard
            title="Membros da Equipe"
            value={dashboardMetrics.teamMembers}
            icon={<Users className="w-6 h-6" />}
            change={{ value: "3 novos membros", trend: "up" }}
          />
        </div>
      )}

      {/* Filtros de Projetos */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FolderOpen className="w-5 h-5 text-project" />
              Meus Projetos
            </CardTitle>
            <Button onClick={handleNewProject} className="bg-project hover:bg-project-dark">
              <Plus className="w-4 h-4 mr-2" />
              Novo Projeto
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <ProjectFilters
            searchTerm={searchTerm}
            statusFilters={statusFilters}
            onSearchChange={setSearchTerm}
            onStatusFilterChange={setStatusFilters}
          />

          {/* Lista de Projetos */}
          {isLoading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : projects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.filter(Boolean).map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onClick={handleProjectClick}
                  onViewDetails={handleViewDetails}
                  onViewDetails2={handleViewDetails2}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FolderOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nenhum projeto encontrado
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilters.length > 0 
                  ? 'Tente ajustar os filtros ou criar um novo projeto.' 
                  : 'Comece criando seu primeiro projeto.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Atividades Recentes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-stage" />
            Atividades Recentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Mock de atividades recentes */}
            <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
              <div className="w-2 h-2 bg-project rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Ana Silva atualizou o projeto "Redesign do Site"</p>
                <p className="text-xs text-gray-600">há 2 horas</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
              <div className="w-2 h-2 bg-stage rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Nova tarefa adicionada à etapa "Desenvolvimento"</p>
                <p className="text-xs text-gray-600">há 4 horas</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
              <div className="w-2 h-2 bg-task rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">João Santos concluiu a tarefa "Wireframes da homepage"</p>
                <p className="text-xs text-gray-600">ontem</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <ProjectForm
        open={showProjectForm}
        onOpenChange={setShowProjectForm}
        mode="create"
      />
    </div>
  );
};

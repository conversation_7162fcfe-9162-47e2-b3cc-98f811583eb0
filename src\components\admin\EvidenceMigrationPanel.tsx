import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/ui/use-toast';
import { 
  Database, 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Info,
  RefreshCw,
  FileText
} from 'lucide-react';

import { EvidenceMigrationService, MigrationResult } from '@/services/evidenceMigrationService';

interface MigrationStats {
  totalTasksWithAttachments: number;
  tasksWithEvidenceBlocks: number;
  tasksNeedingMigration: number;
}

export const EvidenceMigrationPanel: React.FC = () => {
  const { toast } = useToast();
  const [stats, setStats] = useState<MigrationStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationResult, setMigrationResult] = useState<MigrationResult | null>(null);

  const loadStats = async () => {
    setIsLoading(true);
    try {
      const migrationStats = await EvidenceMigrationService.getMigrationStats();
      setStats(migrationStats);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar as estatísticas de migração.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runMigration = async () => {
    setIsMigrating(true);
    setMigrationResult(null);
    
    try {
      const result = await EvidenceMigrationService.migrateExistingEvidences();
      setMigrationResult(result);
      
      if (result.success) {
        toast({
          title: 'Migração concluída',
          description: `${result.blocksCreated} blocos de evidências criados com sucesso.`,
        });
      } else {
        toast({
          title: 'Migração concluída com erros',
          description: `${result.blocksCreated} blocos criados, mas houve ${result.errors.length} erro(s).`,
          variant: 'destructive'
        });
      }
      
      // Recarregar estatísticas
      await loadStats();
      
    } catch (error) {
      console.error('Erro na migração:', error);
      toast({
        title: 'Erro na migração',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    } finally {
      setIsMigrating(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  const migrationProgress = stats 
    ? ((stats.tasksWithEvidenceBlocks / Math.max(stats.totalTasksWithAttachments, 1)) * 100)
    : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Database className="w-6 h-6" />
        <h2 className="text-2xl font-bold">Migração de Evidências</h2>
      </div>

      {/* Estatísticas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-5 h-5" />
            Estatísticas de Migração
            <Button
              size="sm"
              variant="outline"
              onClick={loadStats}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {stats ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {stats.totalTasksWithAttachments}
                  </div>
                  <div className="text-sm text-gray-600">
                    Tarefas com anexos
                  </div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {stats.tasksWithEvidenceBlocks}
                  </div>
                  <div className="text-sm text-gray-600">
                    Com bloco de evidências
                  </div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {stats.tasksNeedingMigration}
                  </div>
                  <div className="text-sm text-gray-600">
                    Precisam de migração
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progresso da migração</span>
                  <span>{migrationProgress.toFixed(1)}%</span>
                </div>
                <Progress value={migrationProgress} className="w-full" />
              </div>

              {stats.tasksNeedingMigration === 0 && (
                <div className="flex items-center gap-2 text-green-600 bg-green-50 p-3 rounded-lg">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">
                    Todas as tarefas já foram migradas!
                  </span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
              <p>Carregando estatísticas...</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Ação de migração */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-5 h-5" />
            Executar Migração
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">O que esta migração faz:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Identifica tarefas que possuem anexos mas não possuem bloco de evidências</li>
              <li>• Cria automaticamente um bloco de evidências para essas tarefas</li>
              <li>• Converte os anexos existentes para o novo formato de evidências</li>
              <li>• Mantém todas as informações originais (nome, data, usuário)</li>
              <li>• Não remove nem modifica os anexos originais</li>
            </ul>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={runMigration}
              disabled={isMigrating || (stats?.tasksNeedingMigration === 0)}
              className="flex items-center gap-2"
            >
              {isMigrating ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              {isMigrating ? 'Migrando...' : 'Executar Migração'}
            </Button>

            {stats?.tasksNeedingMigration === 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Migração completa
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Resultado da migração */}
      {migrationResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {migrationResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-600" />
              )}
              Resultado da Migração
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Tarefas processadas</div>
                  <div className="text-lg font-semibold">{migrationResult.tasksProcessed}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Blocos criados</div>
                  <div className="text-lg font-semibold text-green-600">{migrationResult.blocksCreated}</div>
                </div>
              </div>

              {migrationResult.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-900 mb-2">
                    Erros encontrados ({migrationResult.errors.length}):
                  </h4>
                  <ul className="text-sm text-red-800 space-y-1 max-h-32 overflow-y-auto">
                    {migrationResult.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {migrationResult.success && migrationResult.errors.length === 0 && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-green-800">
                    <CheckCircle className="w-5 h-5" />
                    <span className="font-medium">
                      Migração concluída com sucesso!
                    </span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Informações adicionais */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="pt-4">
          <div className="text-sm text-yellow-800">
            <p className="font-medium mb-2">⚠️ Importante</p>
            <ul className="space-y-1 text-xs">
              <li>• Esta migração é segura e não remove dados existentes</li>
              <li>• Os anexos originais continuarão funcionando normalmente</li>
              <li>• A migração pode ser executada múltiplas vezes sem problemas</li>
              <li>• Recomenda-se fazer backup antes de executar em produção</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

-- =====================================================
-- VIEWS E FUNCTIONS
-- =====================================================
-- Contém: Views úteis, functions de negócio, triggers
-- Dependências: Todas as tabelas criadas
-- Versão: 2.0 - Julho 2025

-- Verificar dependências
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks') THEN
    RAISE EXCEPTION 'Tabelas não encontradas. Execute os scripts anteriores primeiro.';
  END IF;
  RAISE NOTICE '✅ Dependências verificadas';
END $$;

-- =====================================================
-- VIEWS ÚTEIS
-- =====================================================

-- VIEW: Projetos com estatísticas
CREATE OR REPLACE VIEW public.project_stats AS
SELECT 
  p.*,
  COUNT(DISTINCT s.id) as total_stages,
  COUNT(DISTINCT t.id) as total_tasks,
  COUNT(DISTINCT CASE WHEN t.status = 'completed' THEN t.id END) as completed_tasks,
  COUNT(DISTINCT pm.user_id) as total_members,
  ROUND(
    CASE 
      WHEN COUNT(DISTINCT t.id) > 0 
      THEN (COUNT(DISTINCT CASE WHEN t.status = 'completed' THEN t.id END) * 100.0 / COUNT(DISTINCT t.id))
      ELSE 0 
    END, 2
  ) as calculated_progress
FROM public.projects p
LEFT JOIN public.stages s ON s.project_id = p.id
LEFT JOIN public.tasks t ON t.stage_id = s.id
LEFT JOIN public.project_members pm ON pm.project_id = p.id
GROUP BY p.id;

-- VIEW: Tarefas com informações completas
CREATE OR REPLACE VIEW public.task_details AS
SELECT 
  t.*,
  s.name as stage_name,
  p.name as project_name,
  p.id as project_id,
  assigned_profile.name as assigned_to_name,
  assigned_profile.email as assigned_to_email,
  created_profile.name as created_by_name,
  created_profile.email as created_by_email,
  COUNT(DISTINCT te.user_id) as executor_count,
  COUNT(DISTINCT ta.user_id) as approver_count,
  COUNT(DISTINCT tc.id) as comment_count,
  COUNT(DISTINCT att.id) as attachment_count
FROM public.tasks t
JOIN public.stages s ON t.stage_id = s.id
JOIN public.projects p ON s.project_id = p.id
LEFT JOIN public.profiles assigned_profile ON t.assigned_to = assigned_profile.id
LEFT JOIN public.profiles created_profile ON t.created_by = created_profile.id
LEFT JOIN public.task_executors te ON t.id = te.task_id
LEFT JOIN public.task_approvers ta ON t.id = ta.task_id
LEFT JOIN public.task_comments tc ON t.id = tc.task_id
LEFT JOIN public.task_attachments att ON t.id = att.task_id
GROUP BY t.id, s.name, p.name, p.id, assigned_profile.name, assigned_profile.email, created_profile.name, created_profile.email;

-- VIEW: Usuários com estatísticas
CREATE OR REPLACE VIEW public.user_stats AS
SELECT 
  u.*,
  COUNT(DISTINCT p.id) as projects_owned,
  COUNT(DISTINCT pm.project_id) as projects_member,
  COUNT(DISTINCT t.id) as tasks_assigned,
  COUNT(DISTINCT te.task_id) as tasks_executing,
  COUNT(DISTINCT ta.task_id) as tasks_approving,
  COUNT(DISTINCT CASE WHEN t.status = 'completed' THEN t.id END) as tasks_completed
FROM public.profiles u
LEFT JOIN public.projects p ON u.id = p.owner_id
LEFT JOIN public.project_members pm ON u.id = pm.user_id
LEFT JOIN public.tasks t ON u.id = t.assigned_to
LEFT JOIN public.task_executors te ON u.id = te.user_id
LEFT JOIN public.task_approvers ta ON u.id = ta.user_id
GROUP BY u.id;

-- VIEW: Dashboard de quizzes
CREATE OR REPLACE VIEW public.quiz_dashboard AS
SELECT 
  q.*,
  t.title as task_title,
  s.name as stage_name,
  p.name as project_name,
  qs.total_attempts,
  qs.unique_users,
  qs.average_score,
  qs.pass_rate
FROM public.quizzes q
JOIN public.tasks t ON q.task_id = t.id
JOIN public.stages s ON t.stage_id = s.id
JOIN public.projects p ON s.project_id = p.id
LEFT JOIN public.quiz_statistics qs ON q.id = qs.quiz_id;

-- VIEW: Responsáveis de estágios com perfil
CREATE OR REPLACE VIEW public.stage_responsibles_with_profile AS
SELECT
  sr.id,
  sr.stage_id,
  sr.user_id,
  sr.assigned_at,
  p.name as profile_name,
  p.email as profile_email,
  p.avatar_url as profile_avatar_url
FROM public.stage_responsibles sr
JOIN public.profiles p ON sr.user_id = p.id;

-- =====================================================
-- FUNCTIONS DE NEGÓCIO
-- =====================================================

-- Function: Calcular progresso do projeto
DROP FUNCTION IF EXISTS public.calculate_project_progress(uuid);
CREATE OR REPLACE FUNCTION public.calculate_project_progress(project_id_param uuid)
RETURNS integer AS $$
DECLARE
  total_tasks integer;
  completed_tasks integer;
  progress_value integer;
BEGIN
  SELECT 
    COUNT(*),
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END)
  INTO total_tasks, completed_tasks
  FROM public.tasks t
  JOIN public.stages s ON t.stage_id = s.id
  WHERE s.project_id = project_id_param;
  
  IF total_tasks = 0 THEN
    RETURN 0;
  END IF;
  
  progress_value := ROUND((completed_tasks * 100.0) / total_tasks);
  
  -- Atualizar o progresso na tabela
  UPDATE public.projects 
  SET progress = progress_value, updated_at = NOW()
  WHERE id = project_id_param;
  
  RETURN progress_value;
END;
$$ LANGUAGE plpgsql;

-- Function: Calcular progresso do estágio
DROP FUNCTION IF EXISTS public.calculate_stage_progress(uuid);
CREATE OR REPLACE FUNCTION public.calculate_stage_progress(stage_id_param uuid)
RETURNS integer AS $$
DECLARE
  total_tasks integer;
  completed_tasks integer;
  progress_value integer;
BEGIN
  SELECT 
    COUNT(*),
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END)
  INTO total_tasks, completed_tasks
  FROM public.tasks t
  WHERE t.stage_id = stage_id_param;
  
  IF total_tasks = 0 THEN
    RETURN 0;
  END IF;
  
  progress_value := ROUND((completed_tasks * 100.0) / total_tasks);
  
  -- Atualizar o progresso na tabela
  UPDATE public.stages 
  SET progress = progress_value, updated_at = NOW()
  WHERE id = stage_id_param;
  
  RETURN progress_value;
END;
$$ LANGUAGE plpgsql;

-- Function: Criar notificação
CREATE OR REPLACE FUNCTION public.create_notification(
  user_id uuid,
  title text,
  message text,
  type text DEFAULT 'info',
  link text DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  notification_id uuid;
BEGIN
  INSERT INTO public.user_notifications (user_id, title, message, type, link)
  VALUES (user_id, title, message, type, link)
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function: Atualizar estatísticas do quiz
CREATE OR REPLACE FUNCTION public.update_quiz_statistics(quiz_uuid uuid)
RETURNS void AS $$
DECLARE
  stats_record RECORD;
BEGIN
  -- Calcular estatísticas agregadas
  SELECT
    COUNT(*) as total_attempts,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(score) as average_score,
    (COUNT(*) FILTER (WHERE passed = true)::decimal / COUNT(*) * 100) as pass_rate,
    AVG(time_spent) as average_time_spent
  INTO stats_record
  FROM public.quiz_attempts
  WHERE quiz_id = quiz_uuid AND status = 'graded';

  -- Inserir ou atualizar estatísticas
  INSERT INTO public.quiz_statistics (
    quiz_id,
    total_attempts,
    unique_users,
    average_score,
    pass_rate,
    average_time_spent,
    last_calculated_at
  ) VALUES (
    quiz_uuid,
    COALESCE(stats_record.total_attempts, 0),
    COALESCE(stats_record.unique_users, 0),
    COALESCE(stats_record.average_score, 0),
    COALESCE(stats_record.pass_rate, 0),
    COALESCE(stats_record.average_time_spent, 0),
    NOW()
  )
  ON CONFLICT (quiz_id)
  DO UPDATE SET
    total_attempts = EXCLUDED.total_attempts,
    unique_users = EXCLUDED.unique_users,
    average_score = EXCLUDED.average_score,
    pass_rate = EXCLUDED.pass_rate,
    average_time_spent = EXCLUDED.average_time_spent,
    last_calculated_at = EXCLUDED.last_calculated_at;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger: Atualizar progresso do estágio quando tarefa muda
CREATE OR REPLACE FUNCTION public.trigger_update_stage_progress()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM public.calculate_stage_progress(NEW.stage_id);
  
  -- Se mudou de estágio, atualizar ambos
  IF TG_OP = 'UPDATE' AND OLD.stage_id != NEW.stage_id THEN
    PERFORM public.calculate_stage_progress(OLD.stage_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_task_update_stage_progress ON public.tasks;
CREATE TRIGGER trigger_task_update_stage_progress
  AFTER INSERT OR UPDATE OR DELETE ON public.tasks
  FOR EACH ROW EXECUTE FUNCTION public.trigger_update_stage_progress();

-- Trigger: Atualizar progresso do projeto quando estágio muda
CREATE OR REPLACE FUNCTION public.trigger_update_project_progress()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM public.calculate_project_progress(NEW.project_id);
  
  -- Se mudou de projeto, atualizar ambos
  IF TG_OP = 'UPDATE' AND OLD.project_id != NEW.project_id THEN
    PERFORM public.calculate_project_progress(OLD.project_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_stage_update_project_progress ON public.stages;
CREATE TRIGGER trigger_stage_update_project_progress
  AFTER INSERT OR UPDATE OR DELETE ON public.stages
  FOR EACH ROW EXECUTE FUNCTION public.trigger_update_project_progress();

-- Trigger: Atualizar timestamps
CREATE OR REPLACE FUNCTION public.trigger_update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger de timestamp nas tabelas principais
DROP TRIGGER IF EXISTS trigger_profiles_update_timestamp ON public.profiles;
CREATE TRIGGER trigger_profiles_update_timestamp
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.trigger_update_timestamp();

DROP TRIGGER IF EXISTS trigger_projects_update_timestamp ON public.projects;
CREATE TRIGGER trigger_projects_update_timestamp
  BEFORE UPDATE ON public.projects
  FOR EACH ROW EXECUTE FUNCTION public.trigger_update_timestamp();

DROP TRIGGER IF EXISTS trigger_stages_update_timestamp ON public.stages;
CREATE TRIGGER trigger_stages_update_timestamp
  BEFORE UPDATE ON public.stages
  FOR EACH ROW EXECUTE FUNCTION public.trigger_update_timestamp();

DROP TRIGGER IF EXISTS trigger_tasks_update_timestamp ON public.tasks;
CREATE TRIGGER trigger_tasks_update_timestamp
  BEFORE UPDATE ON public.tasks
  FOR EACH ROW EXECUTE FUNCTION public.trigger_update_timestamp();

-- Trigger: Atualizar estatísticas do quiz
CREATE OR REPLACE FUNCTION public.trigger_quiz_stats_update()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM public.update_quiz_statistics(NEW.quiz_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_quiz_attempt_stats ON public.quiz_attempts;
CREATE TRIGGER trigger_quiz_attempt_stats
  AFTER INSERT OR UPDATE ON public.quiz_attempts
  FOR EACH ROW EXECUTE FUNCTION public.trigger_quiz_stats_update();

-- =====================================================
-- INDEXES ADICIONAIS PARA PERFORMANCE
-- =====================================================

-- Índices compostos para queries frequentes
CREATE INDEX IF NOT EXISTS idx_tasks_stage_status ON public.tasks(stage_id, status);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_status ON public.tasks(assigned_to, status);
CREATE INDEX IF NOT EXISTS idx_project_members_project_role ON public.project_members(project_id, role);
CREATE INDEX IF NOT EXISTS idx_task_executors_user_task ON public.task_executors(user_id, task_id);

-- Índices para buscas de texto
CREATE INDEX IF NOT EXISTS idx_projects_name_trgm ON public.projects USING gin(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_tasks_title_trgm ON public.tasks USING gin(title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_profiles_name_trgm ON public.profiles USING gin(name gin_trgm_ops);

-- Habilitar extensão para busca de texto (se não existir)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE '✅ Views e functions criadas com sucesso!';
  RAISE NOTICE '📊 Views: project_stats, task_details, user_stats, quiz_dashboard, stage_responsibles_with_profile';
  RAISE NOTICE '⚙️ Functions: calculate_project_progress, calculate_stage_progress, create_notification, update_quiz_statistics';
  RAISE NOTICE '🔄 Triggers: Atualização automática de progresso e timestamps';
  RAISE NOTICE '📈 Índices adicionais criados para performance';
END $$;

import React from 'react';

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  label?: string;
  disabled?: boolean;
}

// TODO: Se o componente crescer, dividir em subcomponentes auxiliares
export const ColorPicker: React.FC<ColorPickerProps> = ({ value, onChange, label, disabled }) => {
  return (
    <div className="flex flex-col gap-2">
      {label && <label className="text-sm font-medium mb-1">{label}</label>}
      <input
        type="color"
        value={value}
        onChange={e => onChange(e.target.value)}
        disabled={disabled}
        className="w-10 h-10 rounded border border-gray-200 cursor-pointer"
        aria-label={label || 'Selecionar cor'}
      />
    </div>
  );
}; 
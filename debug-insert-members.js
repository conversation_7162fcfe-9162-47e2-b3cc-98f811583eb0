// =====================================================
// SCRIPT PARA INSERIR MEMBROS MANUALMENTE VIA FRONTEND
// Execute no console do navegador quando estiver logado
// =====================================================

async function debugInsertMembers() {
    console.log('🔧 INSERINDO MEMBROS NO PROJETO...');
    
    const PROJECT_ID = '5a7676a2-d69e-4410-86ce-ec43872c70d2';
    
    try {
        // Verificar se supabase está disponível
        let supabaseClient;
        
        if (typeof window !== 'undefined' && window.supabase) {
            supabaseClient = window.supabase;
            console.log('✅ Usando window.supabase');
        } else {
            console.log('⚠️  window.supabase não disponível, tentando importar...');
            
            // Tentar importar dinamicamente
            try {
                const module = await import('/src/lib/supabaseClient.ts');
                supabaseClient = module.supabase;
                console.log('✅ Supabase importado dinamicamente');
            } catch (importError) {
                console.error('❌ Erro ao importar supabase:', importError);
                console.log('💡 Recarregue a página e tente novamente');
                return;
            }
        }
        
        if (!supabaseClient) {
            console.error('❌ Supabase client não disponível');
            return;
        }
        
        // 1. Buscar profiles disponíveis
        console.log('1. Buscando profiles disponíveis...');
        const { data: profiles, error: profilesError } = await supabaseClient
            .from('profiles')
            .select('id, name, email, is_active')
            .eq('is_active', true)
            .order('name');
            
        if (profilesError) {
            console.error('❌ Erro ao buscar profiles:', profilesError);
            return;
        }
        
        console.log('✅ Profiles encontrados:', profiles.length);
        profiles.forEach(p => console.log(`  - ${p.name} (${p.email})`));
        
        // 2. Verificar membros atuais
        console.log('\n2. Verificando membros atuais...');
        const { data: currentMembers, error: membersError } = await supabaseClient
            .from('project_members')
            .select('*')
            .eq('project_id', PROJECT_ID);
            
        if (membersError) {
            console.error('❌ Erro ao buscar membros atuais:', membersError);
            return;
        }
        
        console.log('✅ Membros atuais:', currentMembers.length);
        
        // 3. Inserir profiles como membros
        console.log('\n3. Inserindo membros...');
        let insertCount = 0;
        let errorCount = 0;
        
        for (const profile of profiles) {
            // Verificar se já é membro
            const isAlreadyMember = currentMembers.some(m => m.user_id === profile.id);
            
            if (isAlreadyMember) {
                console.log(`⚠️  ${profile.name} já é membro`);
                continue;
            }
            
            try {
                const { error: insertError } = await supabaseClient
                    .from('project_members')
                    .insert({
                        project_id: PROJECT_ID,
                        user_id: profile.id,
                        role: 'member'
                    });
                    
                if (insertError) {
                    console.error(`❌ Erro ao inserir ${profile.name}:`, insertError);
                    errorCount++;
                } else {
                    console.log(`✅ ${profile.name} inserido como membro`);
                    insertCount++;
                }
            } catch (err) {
                console.error(`❌ Erro inesperado ao inserir ${profile.name}:`, err);
                errorCount++;
            }
        }
        
        console.log(`\n📊 RESUMO:`);
        console.log(`✅ Inserções bem-sucedidas: ${insertCount}`);
        console.log(`❌ Erros: ${errorCount}`);
        
        // 4. Verificar resultado final
        console.log('\n4. Verificando resultado final...');
        const { data: finalMembers, error: finalError } = await supabaseClient
            .from('project_members')
            .select(`
                id,
                role,
                profile:profiles!project_members_user_id_fkey (
                    id,
                    name,
                    email,
                    avatar_url
                )
            `)
            .eq('project_id', PROJECT_ID);
            
        if (finalError) {
            console.error('❌ Erro ao verificar resultado:', finalError);
            return;
        }
        
        console.log('🎉 MEMBROS FINAIS:');
        console.log(`Total: ${finalMembers.length}`);
        finalMembers.forEach(m => {
            const profile = m.profile;
            console.log(`  - ${profile.name} (${profile.email}) - ${m.role}`);
        });
        
        console.log('\n🔄 Agora recarregue a página para ver os membros no autocomplete!');
        
    } catch (error) {
        console.error('❌ Erro geral:', error);
    }
}

// Disponibilizar globalmente
if (typeof window !== 'undefined') {
    window.debugInsertMembers = debugInsertMembers;
}

console.log('🛠️  Script carregado! Execute: debugInsertMembers()');

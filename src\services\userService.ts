import { supabase } from '@/lib/supabaseClient';
import { User } from '@/types';

export const userService = {
  async getById(id: string): Promise<User | null> {
    if (!id) return null;
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single();
    if (error) {
      console.error('Erro ao buscar usuário por id:', error.message);
      return null;
    }
    return data as User;
  },

  async list({ search }: { search?: string } = {}): Promise<User[]> {
    try {
      let query = supabase.from('profiles').select('*');
      if (search) {
        query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
      }
      const { data, error } = await query;
      if (error) {
        console.error('Erro ao listar usuários:', error.message);

        // Se for erro 403 (Forbidden), tentar buscar apenas usuários relacionados
        if (error.message.includes('403') || error.message.includes('Forbidden')) {
          console.warn('Erro 403 detectado, tentando buscar usuários relacionados...');
          return await this.listRelatedUsers(search);
        }

        return [];
      }
      console.log('DEBUG DATA SUPABASE:', data);
      return data as User[];
    } catch (error) {
      console.error('Erro inesperado ao listar usuários:', error);
      return [];
    }
  },

  // Método alternativo para buscar usuários relacionados quando há erro 403
  async listRelatedUsers(search?: string): Promise<User[]> {
    try {
      // Buscar usuários através de project_members (usuários do mesmo projeto)
      let query = supabase
        .from('project_members')
        .select(`
          profiles!project_members_user_id_fkey(
            id, name, email, role, position, phone, avatar_url, is_active, created_at, updated_at
          )
        `);

      const { data, error } = await query;
      if (error) {
        console.error('Erro ao buscar usuários relacionados:', error.message);
        return [];
      }

      // Extrair perfis únicos
      const profiles = data
        ?.map(item => item.profiles)
        .filter(profile => profile !== null)
        .reduce((unique, profile) => {
          if (!unique.find(p => p.id === profile.id)) {
            unique.push(profile);
          }
          return unique;
        }, [] as User[]) || [];

      // Aplicar filtro de busca se fornecido
      if (search) {
        const searchLower = search.toLowerCase();
        return profiles.filter(profile =>
          profile.name?.toLowerCase().includes(searchLower) ||
          profile.email?.toLowerCase().includes(searchLower)
        );
      }

      return profiles;
    } catch (error) {
      console.error('Erro ao buscar usuários relacionados:', error);
      return [];
    }
  },

  // Métodos utilitários futuros podem ser adicionados aqui
};

export async function createProfileFromAuthUser(authUser: any, extraData: Partial<any> = {}) {
  if (!authUser || !authUser.id) throw new Error('Usuário inválido');
  const { error } = await supabase.from('profiles').insert([{
    id: authUser.id,
    email: authUser.email,
    name: extraData.name || authUser.user_metadata?.name || 'Novo Usuário',
    role: extraData.role || authUser.user_metadata?.role || 'member',
    position: extraData.position || authUser.user_metadata?.position || null,
    phone: extraData.phone || authUser.user_metadata?.phone || null,
    avatar_url: extraData.avatar_url || authUser.user_metadata?.avatar_url || null,
    is_active: extraData.is_active ?? true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }]);
  if (error) throw new Error('Erro ao criar perfil: ' + error.message);
}

export async function updateProfile(id: string, data: Partial<any>) {
  const { data: updated, error } = await supabase.from('profiles').update({
    ...data,
    updated_at: new Date().toISOString(),
  }).eq('id', id).select();
  if (error) throw new Error('Erro ao atualizar perfil: ' + error.message);
  return updated;
}

export async function deleteProfile(id: string) {
  const { error } = await supabase.from('profiles').delete().eq('id', id);
  if (error) throw new Error('Erro ao excluir perfil: ' + error.message);
}

export async function deleteAuthUser(id: string) {
  // Só é possível via Service Role Key (backend). No front, não é permitido.
  // Aqui, apenas retorna erro para lembrar da limitação.
  throw new Error('Exclusão de usuário do Auth só é permitida via backend com Service Role Key.');
}

export async function setUserActiveStatus(id: string, isActive: boolean) {
  const { error } = await supabase.from('profiles').update({
    is_active: isActive,
    updated_at: new Date().toISOString(),
  }).eq('id', id);
  if (error) throw new Error('Erro ao atualizar status: ' + error.message);
} 
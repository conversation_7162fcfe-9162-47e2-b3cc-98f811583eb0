import { supabase } from '@/lib/supabaseClient';
import { User } from '@/types';

export const userService = {
  async getById(id: string): Promise<User | null> {
    if (!id) return null;

    try {
      // FORÇAR busca sem .single() para evitar erro 406
      let { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', id)
        .limit(1);

      if (error) {
        console.error('Erro ao buscar usuário por id:', error.message);
        return await this.getByIdFallback(id);
      }

      // Pegar o primeiro registro se for array
      const userData = Array.isArray(data) ? data[0] : data;
      return userData as User;
    } catch (error) {
      console.error('Erro inesperado ao buscar usuário por id:', error);
      return await this.getByIdFallback(id);
    }
  },

  // Método alternativo para buscar usuário por ID quando há erro 500
  async getByIdFallback(id: string): Promise<User | null> {
    if (!id) return null;

    try {
      // Tentar buscar através de project_members
      const { data, error } = await supabase
        .from('project_members')
        .select(`
          profiles!project_members_user_id_fkey(
            id, name, email, role, position, phone, avatar_url, is_active, created_at, updated_at
          )
        `)
        .eq('user_id', id)
        .limit(1)
        .single();

      if (error || !data?.profiles) {
        // Se não encontrar em project_members, tentar em task_executors
        const { data: executorData, error: executorError } = await supabase
          .from('task_executors')
          .select(`
            profiles!task_executors_user_id_fkey(
              id, name, email, role, position, phone, avatar_url, is_active, created_at, updated_at
            )
          `)
          .eq('user_id', id)
          .limit(1)
          .single();

        if (executorError || !executorData?.profiles) {
          console.warn(`Usuário ${id} não encontrado em fallback`);
          return null;
        }

        return executorData.profiles as User;
      }

      return data.profiles as User;
    } catch (error) {
      console.error('Erro no fallback getById:', error);
      return null;
    }
  },

  async list({ search }: { search?: string } = {}): Promise<User[]> {
    try {
      let query = supabase.from('profiles').select('*');
      if (search) {
        query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
      }
      const { data, error } = await query;
      if (error) {
        console.error('Erro ao listar usuários:', error.message);

        // Se for erro 403 (Forbidden), tentar buscar apenas usuários relacionados
        if (error.message.includes('403') || error.message.includes('Forbidden')) {
          console.warn('Erro 403 detectado, tentando buscar usuários relacionados...');
          return await this.listRelatedUsers(search);
        }

        // Se for erro 409 (Conflict), pode ser problema de política RLS
        if (error.message.includes('409') || error.message.includes('Conflict')) {
          console.warn('Erro 409 detectado, tentando buscar usuários relacionados...');
          return await this.listRelatedUsers(search);
        }

        return [];
      }
      console.log('DEBUG DATA SUPABASE:', data);
      return data as User[];
    } catch (error) {
      console.error('Erro inesperado ao listar usuários:', error);
      return [];
    }
  },

  // Método alternativo para buscar usuários relacionados quando há erro 403
  async listRelatedUsers(search?: string): Promise<User[]> {
    try {
      // Buscar usuários através de project_members (usuários do mesmo projeto)
      let query = supabase
        .from('project_members')
        .select(`
          profiles!project_members_user_id_fkey(
            id, name, email, role, position, phone, avatar_url, is_active, created_at, updated_at
          )
        `);

      const { data, error } = await query;
      if (error) {
        console.error('Erro ao buscar usuários relacionados:', error.message);
        return [];
      }

      // Extrair perfis únicos
      const profiles = data
        ?.map(item => item.profiles)
        .filter(profile => profile !== null)
        .reduce((unique, profile) => {
          if (!unique.find(p => p.id === profile.id)) {
            unique.push(profile);
          }
          return unique;
        }, [] as User[]) || [];

      // Aplicar filtro de busca se fornecido
      if (search) {
        const searchLower = search.toLowerCase();
        return profiles.filter(profile =>
          profile.name?.toLowerCase().includes(searchLower) ||
          profile.email?.toLowerCase().includes(searchLower)
        );
      }

      return profiles;
    } catch (error) {
      console.error('Erro ao buscar usuários relacionados:', error);
      return [];
    }
  },

  // Métodos utilitários futuros podem ser adicionados aqui
};

export async function createProfileFromAuthUser(authUser: any, extraData: Partial<any> = {}) {
  if (!authUser || !authUser.id) {
    throw new Error('Usuário inválido: dados de autenticação não fornecidos');
  }

  console.log('Criando perfil para usuário Auth:', authUser.id, authUser.email);

  // Verificar se o usuário existe no auth.users antes de criar perfil
  try {
    const { data: authCheck, error: authCheckError } = await supabase.auth.getUser();
    console.log('Verificação de usuário Auth:', authCheck, authCheckError);
  } catch (error) {
    console.warn('Não foi possível verificar usuário Auth:', error);
  }

  try {
    // Usar UPSERT para evitar problemas de foreign key e duplicatas
    const profileData = {
      id: authUser.id,
      email: authUser.email,
      name: extraData.name || authUser.user_metadata?.name || 'Novo Usuário',
      role: extraData.role || authUser.user_metadata?.role || 'member',
      position: extraData.position || authUser.user_metadata?.position || null,
      phone: extraData.phone || authUser.user_metadata?.phone || null,
      avatar_url: extraData.avatar_url || authUser.user_metadata?.avatar_url || null,
      is_active: extraData.is_active ?? true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    console.log('Tentando UPSERT do perfil:', profileData);

    const { error } = await supabase
      .from('profiles')
      .upsert([profileData], {
        onConflict: 'id',
        ignoreDuplicates: false
      });

    if (error) {
      console.error('Erro ao inserir perfil:', error);

      // Se for erro de foreign key constraint (usuário não existe no auth.users)
      if (error.message.includes('profiles_id_fkey') || error.message.includes('foreign key constraint')) {
        console.warn('Foreign key constraint violada, tentando criar perfil sem constraint...');

        // Tentar criar perfil removendo temporariamente a constraint
        try {
          const { error: fallbackError } = await supabase.rpc('create_profile_without_fk', {
            profile_id: authUser.id,
            profile_email: authUser.email,
            profile_name: extraData.name || authUser.user_metadata?.name || 'Novo Usuário',
            profile_role: extraData.role || authUser.user_metadata?.role || 'member',
            profile_position: extraData.position || authUser.user_metadata?.position || null,
            profile_phone: extraData.phone || authUser.user_metadata?.phone || null,
            profile_avatar_url: extraData.avatar_url || authUser.user_metadata?.avatar_url || null,
            profile_is_active: extraData.is_active ?? true
          });

          if (fallbackError) {
            console.error('Erro no fallback:', fallbackError);
            throw new Error('Erro: O usuário não foi criado corretamente no sistema de autenticação. Tente novamente.');
          }

          console.log('Perfil criado com fallback (sem FK):', authUser.id);
          return;
        } catch (fallbackError) {
          console.error('Fallback também falhou:', fallbackError);
          throw new Error('Erro: O usuário não foi criado corretamente no sistema de autenticação. Tente novamente.');
        }
      }

      // Se for erro 409 (Conflict), pode ser que o perfil já existe
      if (error.message.includes('409') || error.message.includes('Conflict') ||
          error.message.includes('duplicate') || error.message.includes('already exists')) {
        console.warn('Perfil já existe, tentando atualizar:', authUser.id);

        // Tentar atualizar ao invés de inserir
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            name: extraData.name || authUser.user_metadata?.name || 'Novo Usuário',
            role: extraData.role || authUser.user_metadata?.role || 'member',
            position: extraData.position || authUser.user_metadata?.position || null,
            phone: extraData.phone || authUser.user_metadata?.phone || null,
            avatar_url: extraData.avatar_url || authUser.user_metadata?.avatar_url || null,
            is_active: extraData.is_active ?? true,
            updated_at: new Date().toISOString(),
          })
          .eq('id', authUser.id);

        if (updateError) {
          console.error('Erro ao atualizar perfil existente:', updateError);
          throw new Error('Erro ao atualizar perfil existente: ' + updateError.message);
        }

        console.log('Perfil atualizado com sucesso:', authUser.id);
        return;
      }

      throw new Error('Erro ao criar perfil: ' + error.message);
    }

    console.log('Perfil criado com sucesso:', authUser.id);
  } catch (error: any) {
    console.error('Erro inesperado ao criar/atualizar perfil:', error);
    throw new Error('Erro ao processar perfil: ' + error.message);
  }
}

export async function updateProfile(id: string, data: Partial<any>) {
  const { data: updated, error } = await supabase.from('profiles').update({
    ...data,
    updated_at: new Date().toISOString(),
  }).eq('id', id).select();
  if (error) throw new Error('Erro ao atualizar perfil: ' + error.message);
  return updated;
}

export async function deleteProfile(id: string) {
  const { error } = await supabase.from('profiles').delete().eq('id', id);
  if (error) throw new Error('Erro ao excluir perfil: ' + error.message);
}

export async function deleteAuthUser(id: string) {
  // Só é possível via Service Role Key (backend). No front, não é permitido.
  // Aqui, apenas retorna erro para lembrar da limitação.
  throw new Error('Exclusão de usuário do Auth só é permitida via backend com Service Role Key.');
}

export async function setUserActiveStatus(id: string, isActive: boolean) {
  const { error } = await supabase.from('profiles').update({
    is_active: isActive,
    updated_at: new Date().toISOString(),
  }).eq('id', id);
  if (error) throw new Error('Erro ao atualizar status: ' + error.message);
} 
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { PROJECT_ROLES, getRoleInfo } from '@/utils/roleUtils';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useProjectStore } from '@/store/useProjectStore';
import { useToast } from '@/hooks/ui/use-toast';
import { useAuth } from '@/auth/useAuth';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { supabase } from '@/lib/supabaseClient';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { X, Users, ChevronDown } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { verificarAntesDeInserirMembro, projectService } from '@/services/projectService';

// Tipos e configurações de roles (movido para utils/roleUtils.ts)

const projectSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  description: z.string().min(1, 'Descrição é obrigatória'),
  start_date: z.string().min(1, 'Data de início é obrigatória'),
  end_date: z.string().min(1, 'Data de fim é obrigatória'),
});

type ProjectFormData = z.infer<typeof projectSchema>;

interface ProjectFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project?: any;
  mode: 'create' | 'edit';
  onUpdated?: () => void;
}

export const ProjectForm: React.FC<ProjectFormProps> = ({
  open,
  onOpenChange,
  project,
  mode,
  onUpdated
}) => {
  const { createProject, updateProject, loadProjects } = useProjectStore();
  const { toast } = useToast();
  const { user } = useAuth();
  const [members, setMembers] = React.useState<any[]>(project?.members || []);
  const [projectMembers, setProjectMembers] = React.useState<any[]>([]);

  // Carregar membros do projeto para autocomplete E para o estado local
  React.useEffect(() => {
    const loadProjectMembers = async () => {
      if (project?.id) {
        try {
          console.log('[ProjectForm] Carregando membros do projeto:', project.id);
          const projectMembersData = await projectService.getProjectMembers(project.id);
          console.log('[ProjectForm] Membros carregados:', projectMembersData);
          
          // Mapear membros do projeto para o formato esperado (autocomplete)
          const mappedMembersForAutocomplete = (projectMembersData || []).map((m: any) => {
            const profile = m.profile || m.profiles?.[0] || m;
            return profile && profile.id ? {
              id: profile.id,
              name: profile.name,
              email: profile.email,
              avatar_url: profile.avatar_url
            } : null;
          }).filter(Boolean);
          
          // Mapear membros do projeto para o estado local (formulário)
          const mappedMembersForForm = (projectMembersData || []).map((m: any) => {
            const profile = m.profile || m.profiles?.[0] || m;
            return profile && profile.id ? {
              id: profile.id,
              name: profile.name,
              email: profile.email,
              avatar_url: profile.avatar_url,
              role: m.role || 'member'
            } : null;
          }).filter(Boolean);
          
          console.log('[ProjectForm] Membros mapeados para autocomplete:', mappedMembersForAutocomplete);
          console.log('[ProjectForm] Membros mapeados para formulário:', mappedMembersForForm);
          
          setProjectMembers(mappedMembersForAutocomplete);
          setMembers(mappedMembersForForm); // ✅ AGORA USA A MESMA FONTE!
        } catch (error) {
          console.error('[ProjectForm] Erro ao carregar membros:', error);
        }
      } else {
        // Se não há projeto (criação), limpar os estados
        setProjectMembers([]);
        setMembers([]);
      }
    };

    if (open) {
      loadProjectMembers();
    }
  }, [project?.id, open]);

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: project?.name || '',
      description: project?.description || '',
      start_date: project?.start_date || '',
      end_date: project?.end_date || '',
    },
  });

  const onSubmit = async (data: ProjectFormData) => {
    try {
      let projectId = project?.id;
      if (mode === 'create') {
        if (!user?.id) {
          toast({
            title: 'Erro',
            description: 'Usuário não autenticado. Faça login novamente.',
            variant: 'destructive',
          });
          return;
        }
        let newProject;
        try {
          newProject = await createProject({ ...data, owner_id: user.id });
        } catch (err) {
          toast({
            title: 'Erro ao criar projeto',
            description: err?.message || 'Erro desconhecido ao criar projeto.',
            variant: 'destructive',
          });
          return;
        }
        projectId = newProject.id;
        // Salvar membros na tabela project_members
        const membrosParaAdicionar = [...members].filter(m => m.id);
        if (!membrosParaAdicionar.some(m => m.id === user.id)) {
          membrosParaAdicionar.push({ id: user.id, role: 'admin' });
        }
        // Validação: garantir que todos os membros existem em profiles
        const idsParaAdicionar = membrosParaAdicionar.map(m => m.id);
        const { data: perfisValidos } = await supabase
          .from('profiles')
          .select('id')
          .in('id', idsParaAdicionar);
        const idsValidos = (perfisValidos || []).map(p => p.id);
        const membrosValidos = membrosParaAdicionar.filter(m => idsValidos.includes(m.id));
        if (membrosValidos.length !== membrosParaAdicionar.length) {
          toast({
            title: 'Erro',
            description: 'Um ou mais membros não existem mais no sistema.',
            variant: 'destructive',
          });
          return;
        }
        if (membrosValidos.length > 0) {
          try {
            await Promise.all(membrosValidos.map(async m => {
              const role = m.role || (m.id === user.id ? 'admin' : 'member');
              const roleValido = role === 'owner' ? 'admin' : role;
              // Verificação automática antes do insert
              const resultado = await verificarAntesDeInserirMembro({ project_id: projectId, user_id: m.id, role: roleValido });
              if (!resultado.ok) {
                toast({
                  title: 'Erro ao adicionar membro',
                  description: resultado.motivo,
                  variant: 'destructive',
                });
                return;
              }
              const { data: membroData, error: membroError } = await supabase.from('project_members').insert({ project_id: projectId, user_id: m.id, role: roleValido }).select();
              if (membroError) {
                toast({
                  title: 'Erro ao adicionar membro',
                  description: membroError.message,
                  variant: 'destructive',
                });
                throw membroError;
              } else {
                toast({
                  title: 'Membro adicionado',
                  description: `Membro ${m.name || m.id} adicionado com sucesso!`,
                  variant: 'default',
                });
              }
            }));
          } catch (err) {
            toast({
              title: 'Erro ao adicionar membros',
              description: err?.message || 'Erro desconhecido ao adicionar membros.',
              variant: 'destructive',
            });
            return;
          }
        }
        await loadProjects();
        toast({
          title: 'Projeto criado',
          description: 'O projeto foi criado com sucesso.',
        });
      } else {
        await updateProject(project.id, data);
        
        console.log('Membros a adicionar (edição):', members);
        console.log('Iniciando atualização inteligente de membros...');
        
        try {
          // 1. Buscar membros atuais do projeto
          const { data: membrosAtuais, error: erroMembrosAtuais } = await supabase
            .from('project_members')
            .select('user_id, role')
            .eq('project_id', project.id);
            
          if (erroMembrosAtuais) {
            throw new Error(`Erro ao buscar membros atuais: ${erroMembrosAtuais.message}`);
          }
          
          console.log('Membros atuais no banco:', membrosAtuais);
          
          // 2. Preparar listas para comparação
          const membrosNovos = members.filter(m => m.id); // Apenas membros com ID válido
          const idsAtuais = new Set((membrosAtuais || []).map(m => m.user_id));
          const idsNovos = new Set(membrosNovos.map(m => m.id));
          
          console.log('DEBUG - Estado atual do componente:');
          console.log('- members (estado local):', members);
          console.log('- membrosNovos (filtrados):', membrosNovos);
          console.log('- idsAtuais (no banco):', Array.from(idsAtuais));
          console.log('- idsNovos (do formulário):', Array.from(idsNovos));
          
          // 3. Identificar membros para adicionar
          const paraAdicionar = membrosNovos.filter(m => !idsAtuais.has(m.id));
          
          // 4. Identificar membros para remover (que estão no banco mas não na lista nova)
          const paraRemover = (membrosAtuais || []).filter(m => !idsNovos.has(m.user_id));
          
          // 5. Identificar membros para atualizar role (que mudaram de papel)
          const paraAtualizar = membrosNovos.filter(m => {
            const membroAtual = (membrosAtuais || []).find(ma => ma.user_id === m.id);
            return membroAtual && membroAtual.role !== m.role;
          });
          
          console.log('Operações a realizar:');
          console.log('- Adicionar:', paraAdicionar.length, paraAdicionar);
          console.log('- Remover:', paraRemover.length, paraRemover);
          console.log('- Atualizar role:', paraAtualizar.length, paraAtualizar);
          
          // 6. Executar adições
          if (paraAdicionar.length > 0) {
            for (const membro of paraAdicionar) {
              const role = membro.role || 'member';
              const roleValido = role === 'owner' ? 'admin' : role;
              
              console.log(`Adicionando membro: ${membro.name} como ${roleValido}`);
              
              try {
                // Verificação antes do insert
                const resultado = await verificarAntesDeInserirMembro({ 
                  project_id: project.id, 
                  user_id: membro.id, 
                  role: roleValido 
                });
                
                if (!resultado.ok) {
                  console.warn(`Membro ${membro.name} não pôde ser adicionado: ${resultado.motivo}`);
                  continue;
                }
                
                const { error: erroInsert } = await supabase
                  .from('project_members')
                  .insert({ 
                    project_id: project.id, 
                    user_id: membro.id, 
                    role: roleValido 
                  });
                  
                if (erroInsert) {
                  console.error(`Erro ao adicionar ${membro.name}:`, erroInsert);
                } else {
                  console.log(`✅ ${membro.name} adicionado com sucesso`);
                }
              } catch (err) {
                console.error(`Erro inesperado ao adicionar ${membro.name}:`, err);
              }
            }
          }
          
          // 7. Executar remoções
          if (paraRemover.length > 0) {
            const idsParaRemover = paraRemover.map(m => m.user_id);
            console.log('Removendo membros:', idsParaRemover);
            
            const { error: erroRemove } = await supabase
              .from('project_members')
              .delete()
              .eq('project_id', project.id)
              .in('user_id', idsParaRemover);
              
            if (erroRemove) {
              console.error('Erro ao remover membros:', erroRemove);
            } else {
              console.log(`✅ ${paraRemover.length} membros removidos`);
            }
          }
          
          // 8. Executar atualizações de role
          if (paraAtualizar.length > 0) {
            for (const membro of paraAtualizar) {
              const role = membro.role || 'member';
              const roleValido = role === 'owner' ? 'admin' : role;
              
              console.log(`Atualizando role de ${membro.name} para ${roleValido}`);
              
              const { error: erroUpdate } = await supabase
                .from('project_members')
                .update({ role: roleValido })
                .eq('project_id', project.id)
                .eq('user_id', membro.id);
                
              if (erroUpdate) {
                console.error(`Erro ao atualizar role de ${membro.name}:`, erroUpdate);
              } else {
                console.log(`✅ Role de ${membro.name} atualizado`);
              }
            }
          }
          
          console.log('✅ Atualização de membros concluída com sucesso!');
          
        } catch (error) {
          console.error('❌ Erro na atualização inteligente de membros:', error);
          toast({
            title: 'Erro ao atualizar membros',
            description: 'Houve um problema ao atualizar os membros do projeto.',
            variant: 'destructive',
          });
        }
        
        onUpdated?.();
        toast({
          title: 'Projeto atualizado',
          description: 'O projeto foi atualizado com sucesso.',
        });
      }
      onOpenChange(false);
      form.reset();
      setMembers([]);
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error?.message || 'Ocorreu um erro ao salvar o projeto.',
        variant: 'destructive',
      });
      console.error('Erro ao salvar projeto:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="dialog-content w-full sm:max-w-md md:max-w-lg lg:max-w-2xl xl:max-w-3xl mx-auto p-2 sm:p-6 rounded-lg shadow-lg bg-white flex flex-col !top-0 !h-screen sm:!h-auto !max-h-screen overflow-y-auto
          sm:!top-1/2 sm:!left-1/2 sm:!-translate-x-1/2 sm:!-translate-y-1/2"
        aria-describedby="project-form-desc"
        style={{ minHeight: '100dvh', height: '100dvh', maxHeight: '100dvh', top: 0, transform: 'none' }}
      >
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Novo Projeto' : 'Editar Projeto'}
          </DialogTitle>
          <DialogDescription id="project-form-desc">
            Preencha os campos abaixo para {mode === 'create' ? 'criar um novo projeto.' : 'editar o projeto.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Projeto</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o nome do projeto" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Descreva o projeto" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Início</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Fim</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Card className="mb-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-project" />
                  Membros do Projeto
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Header da Grid */}
                {members.length > 0 && (
                  <div className="hidden sm:grid grid-cols-[40px_1fr_1fr_120px_40px] gap-3 py-2 px-1 border-b border-gray-100 text-xs font-medium text-gray-500 uppercase tracking-wide">
                    <div></div>
                    <div>Nome</div>
                    <div>Email</div>
                    <div>Função</div>
                    <div></div>
                  </div>
                )}
                
                {/* Lista de Membros em Grid */}
                <div className="space-y-2">
                  {members.length === 0 ? (
                    <div className="text-center py-8">
                      <Users className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                      <span className="text-gray-400 text-sm">Nenhum membro adicionado</span>
                      <p className="text-xs text-gray-300 mt-1">Use o campo abaixo para adicionar membros</p>
                    </div>
                  ) : (
                    members.map(member => {
                      const profile = member.profile || member;
                      const roleInfo = getRoleInfo(member.role || 'member');
                      return (
                        <div key={member.id}>
                          {/* Mobile Layout */}
                          <div className="flex sm:hidden items-start gap-3 py-3 px-2 border border-gray-100 rounded-lg hover:bg-gray-50/50 transition-colors group">
                            <Avatar className="w-10 h-10 border border-gray-200 flex-shrink-0">
                              <AvatarImage src={profile.avatar_url || '/placeholder.svg'} alt={profile.name || ''} />
                              <AvatarFallback className="text-xs bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-700 font-medium">
                                {(profile.name || profile.email || '?').charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="min-w-0 flex-1">
                                  <p className="text-sm font-medium text-gray-900 truncate">{profile.name || 'Nome não informado'}</p>
                                  <p className="text-xs text-gray-600 truncate">{profile.email || 'Email não informado'}</p>
                                </div>
                                <button
                                  type="button"
                                  onClick={() => setMembers(prev => prev.filter(m => m.id !== member.id))}
                                  className="ml-2 w-6 h-6 rounded-md text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors flex-shrink-0"
                                  title="Remover membro"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                              <div className="mt-2">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <button className="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors">
                                      <span className={`w-2 h-2 rounded-full ${roleInfo.color.split(' ')[0]}`}></span>
                                      <span>{roleInfo.label}</span>
                                      <ChevronDown className="w-3 h-3 text-gray-400" />
                                    </button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="start" className="min-w-[140px]">
                                    {PROJECT_ROLES.map(role => (
                                      <DropdownMenuItem
                                        key={role.value}
                                        onClick={() => setMembers(prev => 
                                          prev.map(m => 
                                            m.id === member.id 
                                              ? { ...m, role: role.value }
                                              : m
                                          )
                                        )}
                                        className={`${member.role === role.value ? 'bg-blue-50 text-blue-700' : ''} flex items-center gap-2`}
                                      >
                                        <span className={`w-2 h-2 rounded-full ${role.color.split(' ')[0]}`}></span>
                                        <span className="text-xs font-medium">{role.label}</span>
                                      </DropdownMenuItem>
                                    ))}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                          </div>
                          
                          {/* Desktop Grid Layout */}
                          <div className="hidden sm:grid grid-cols-[40px_1fr_1fr_120px_40px] gap-3 py-3 px-1 border-b border-gray-50 hover:bg-gray-50/50 transition-colors group">
                            {/* Avatar */}
                            <div className="flex items-center">
                              <Avatar className="w-8 h-8 border border-gray-200">
                                <AvatarImage src={profile.avatar_url || '/placeholder.svg'} alt={profile.name || ''} />
                                <AvatarFallback className="text-xs bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-700 font-medium">
                                  {(profile.name || profile.email || '?').charAt(0).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                            </div>

                            {/* Nome */}
                            <div className="flex items-center min-w-0">
                              <span className="text-sm font-medium text-gray-900 truncate" title={profile.name}>
                                {profile.name || 'Nome não informado'}
                              </span>
                            </div>

                            {/* Email */}
                            <div className="flex items-center min-w-0">
                              <span className="text-sm text-gray-600 truncate" title={profile.email}>
                                {profile.email || 'Email não informado'}
                              </span>
                            </div>

                            {/* Role Dropdown */}
                            <div className="flex items-center">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <button className="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1">
                                    <span className={`w-2 h-2 rounded-full ${roleInfo.color.split(' ')[0]}`}></span>
                                    <span className="truncate max-w-[70px]" title={roleInfo.label}>
                                      {roleInfo.label}
                                    </span>
                                    <ChevronDown className="w-3 h-3 text-gray-400" />
                                  </button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="min-w-[140px]">
                                  {PROJECT_ROLES.map(role => (
                                    <DropdownMenuItem
                                      key={role.value}
                                      onClick={() => setMembers(prev => 
                                        prev.map(m => 
                                          m.id === member.id 
                                            ? { ...m, role: role.value }
                                            : m
                                        )
                                      )}
                                      className={`${member.role === role.value ? 'bg-blue-50 text-blue-700' : ''} flex items-center gap-2`}
                                    >
                                      <span className={`w-2 h-2 rounded-full ${role.color.split(' ')[0]}`}></span>
                                      <span className="text-xs font-medium">{role.label}</span>
                                    </DropdownMenuItem>
                                  ))}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>

                            {/* Botão Remover */}
                            <div className="flex items-center">
                              <button
                                type="button"
                                onClick={() => setMembers(prev => prev.filter(m => m.id !== member.id))}
                                className="w-6 h-6 rounded-md text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors opacity-0 group-hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
                                title="Remover membro"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>

                {/* Adicionar Novo Membro */}
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Adicionar novo membro
                  </label>
                  <UserAutocomplete
                    onSelect={user => setMembers(prev => {
                      if (prev.some(m => m.id === user.id)) return prev;
                      return [
                        ...prev,
                        {
                          ...user,
                          name: user.name || '',
                          email: user.email || '',
                          avatar_url: user.avatar_url || '',
                          role: 'member',
                        },
                      ];
                    })}
                    excludeIds={members.map(m => m.id)}
                    users={projectMembers}
                  />
                </div>
              </CardContent>
            </Card>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" className="bg-project hover:bg-project-dark">
                {mode === 'create' ? 'Criar Projeto' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

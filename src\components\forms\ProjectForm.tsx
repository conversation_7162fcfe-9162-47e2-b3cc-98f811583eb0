import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useProjectStore } from '@/store/useProjectStore';
import { useToast } from '@/hooks/ui/use-toast';
import { useAuth } from '@/auth/useAuth';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { supabase } from '@/lib/supabaseClient';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { X, Users } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { verificarAntesDeInserirMembro } from '@/services/projectService';

const projectSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  description: z.string().min(1, 'Descrição é obrigatória'),
  start_date: z.string().min(1, 'Data de início é obrigatória'),
  end_date: z.string().min(1, 'Data de fim é obrigatória'),
});

type ProjectFormData = z.infer<typeof projectSchema>;

interface ProjectFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project?: any;
  mode: 'create' | 'edit';
  onUpdated?: () => void;
}

export const ProjectForm: React.FC<ProjectFormProps> = ({
  open,
  onOpenChange,
  project,
  mode,
  onUpdated
}) => {
  const { createProject, updateProject, loadProjects } = useProjectStore();
  const { toast } = useToast();
  const { user } = useAuth();
  const [members, setMembers] = React.useState<any[]>(project?.members || []);

  React.useEffect(() => {
    setMembers(
      (project?.members || []).map(m => {
        if (Array.isArray(m.profiles) && m.profiles.length > 0) {
          const p = m.profiles[0];
          return {
            ...m,
            name: p.name,
            email: p.email,
            avatar_url: p.avatar_url,
          };
        }
        return m;
      })
    );
  }, [project, open]);

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: project?.name || '',
      description: project?.description || '',
      start_date: project?.start_date || '',
      end_date: project?.end_date || '',
    },
  });

  const onSubmit = async (data: ProjectFormData) => {
    try {
      let projectId = project?.id;
      if (mode === 'create') {
        if (!user?.id) {
          toast({
            title: 'Erro',
            description: 'Usuário não autenticado. Faça login novamente.',
            variant: 'destructive',
          });
          return;
        }
        let newProject;
        try {
          newProject = await createProject({ ...data, owner_id: user.id });
        } catch (err) {
          toast({
            title: 'Erro ao criar projeto',
            description: err?.message || 'Erro desconhecido ao criar projeto.',
            variant: 'destructive',
          });
          return;
        }
        projectId = newProject.id;
        // Salvar membros na tabela project_members
        const membrosParaAdicionar = [...members].filter(m => m.id);
        if (!membrosParaAdicionar.some(m => m.id === user.id)) {
          membrosParaAdicionar.push({ id: user.id, role: 'admin' });
        }
        // Validação: garantir que todos os membros existem em profiles
        const idsParaAdicionar = membrosParaAdicionar.map(m => m.id);
        const { data: perfisValidos } = await supabase
          .from('profiles')
          .select('id')
          .in('id', idsParaAdicionar);
        const idsValidos = (perfisValidos || []).map(p => p.id);
        const membrosValidos = membrosParaAdicionar.filter(m => idsValidos.includes(m.id));
        if (membrosValidos.length !== membrosParaAdicionar.length) {
          toast({
            title: 'Erro',
            description: 'Um ou mais membros não existem mais no sistema.',
            variant: 'destructive',
          });
          return;
        }
        if (membrosValidos.length > 0) {
          try {
            await Promise.all(membrosValidos.map(async m => {
              const role = m.role || (m.id === user.id ? 'admin' : 'member');
              const roleValido = role === 'owner' ? 'admin' : role;
              // Verificação automática antes do insert
              const resultado = await verificarAntesDeInserirMembro({ project_id: projectId, user_id: m.id, role: roleValido });
              if (!resultado.ok) {
                toast({
                  title: 'Erro ao adicionar membro',
                  description: resultado.motivo,
                  variant: 'destructive',
                });
                return;
              }
              const { data: membroData, error: membroError } = await supabase.from('project_members').insert({ project_id: projectId, user_id: m.id, role: roleValido }).select();
              if (membroError) {
                toast({
                  title: 'Erro ao adicionar membro',
                  description: membroError.message,
                  variant: 'destructive',
                });
                throw membroError;
              } else {
                toast({
                  title: 'Membro adicionado',
                  description: `Membro ${m.name || m.id} adicionado com sucesso!`,
                  variant: 'default',
                });
              }
            }));
          } catch (err) {
            toast({
              title: 'Erro ao adicionar membros',
              description: err?.message || 'Erro desconhecido ao adicionar membros.',
              variant: 'destructive',
            });
            return;
          }
        }
        await loadProjects();
        toast({
          title: 'Projeto criado',
          description: 'O projeto foi criado com sucesso.',
        });
      } else {
        await updateProject(project.id, data);
        // Atualizar membros (exemplo simples: remover todos e inserir os atuais)
        await supabase.from('project_members').delete().eq('project_id', project.id);
        console.log('Membros a adicionar (edição):', members);
        if (members.length > 0) {
          const membrosValidos = members.filter(m => m.id);
          // Validação: garantir que todos os membros existem em profiles
          const idsParaAdicionar = membrosValidos.map(m => m.id);
          const { data: perfisValidos } = await supabase
            .from('profiles')
            .select('id')
            .in('id', idsParaAdicionar);
          const idsValidos = (perfisValidos || []).map(p => p.id);
          const membrosRealmenteValidos = membrosValidos.filter(m => idsValidos.includes(m.id));
          if (membrosRealmenteValidos.length !== membrosValidos.length) {
            toast({
              title: 'Erro',
              description: 'Um ou mais membros não existem mais no sistema.',
              variant: 'destructive',
            });
            return;
          }
          if (membrosRealmenteValidos.length > 0) {
            await Promise.all(membrosRealmenteValidos.map(async m => {
              const role = m.role || 'member';
              const roleValido = role === 'owner' ? 'admin' : role;
              // Verificação automática antes do insert
              const resultado = await verificarAntesDeInserirMembro({ project_id: project.id, user_id: m.id, role: roleValido });
              if (!resultado.ok) {
                toast({
                  title: 'Erro ao adicionar membro',
                  description: resultado.motivo,
                  variant: 'destructive',
                });
                return;
              }
              const { data: membroData, error: membroError } = await supabase.from('project_members').insert({ project_id: project.id, user_id: m.id, role: roleValido }).select();
              if (membroError) {
                toast({
                  title: 'Erro ao adicionar membro',
                  description: membroError.message,
                  variant: 'destructive',
                });
                throw membroError;
              } else {
                toast({
                  title: 'Membro adicionado',
                  description: `Membro ${m.name || m.id} adicionado com sucesso!`,
                  variant: 'default',
                });
              }
            }));
          }
        }
        onUpdated?.();
        toast({
          title: 'Projeto atualizado',
          description: 'O projeto foi atualizado com sucesso.',
        });
      }
      onOpenChange(false);
      form.reset();
      setMembers([]);
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error?.message || 'Ocorreu um erro ao salvar o projeto.',
        variant: 'destructive',
      });
      console.error('Erro ao salvar projeto:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="dialog-content w-full sm:max-w-md md:max-w-lg lg:max-w-2xl xl:max-w-3xl mx-auto p-2 sm:p-6 rounded-lg shadow-lg bg-white flex flex-col !top-0 !h-screen sm:!h-auto !max-h-screen overflow-y-auto
          sm:!top-1/2 sm:!left-1/2 sm:!-translate-x-1/2 sm:!-translate-y-1/2"
        aria-describedby="project-form-desc"
        style={{ minHeight: '100dvh', height: '100dvh', maxHeight: '100dvh', top: 0, transform: 'none' }}
      >
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Novo Projeto' : 'Editar Projeto'}
          </DialogTitle>
          <DialogDescription id="project-form-desc">
            Preencha os campos abaixo para {mode === 'create' ? 'criar um novo projeto.' : 'editar o projeto.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Projeto</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o nome do projeto" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Descreva o projeto" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Início</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Fim</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Card className="mb-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-project" />
                  Membros do Projeto
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 mb-2 min-h-[40px]">
                  {members.length === 0 ? (
                    <span className="text-gray-400 text-sm">Nenhum membro adicionado</span>
                  ) : (
                    members.map(member => {
                      const profile = member.profile || member;
                      return (
                        <span
                          key={member.id}
                          className="inline-flex items-center gap-2 bg-gray-100 text-gray-800 px-3 py-1 rounded-full shadow-sm border border-gray-200 min-w-0 max-w-full"
                          style={{ maxWidth: '100%' }}
                        >
                          <Avatar className="w-7 h-7">
                            <AvatarImage src={profile.avatar_url || '/placeholder.svg'} alt={profile.name || ''} />
                            <AvatarFallback className="text-xs">{(profile.name || '?').charAt(0).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col min-w-0 max-w-[120px]">
                            <span className="truncate font-medium text-sm">{profile.name}</span>
                            <span className="truncate text-xs text-gray-500">{profile.email}</span>
                          </div>
                          <button
                            type="button"
                            className="ml-1 text-gray-400 hover:text-red-500 transition"
                            onClick={() => setMembers(prev => prev.filter(m => m.id !== member.id))}
                            aria-label="Remover membro"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </span>
                      );
                    })
                  )}
                </div>
                <UserAutocomplete
                  onSelect={user => setMembers(prev => {
                    if (prev.some(m => m.id === user.id)) return prev;
                    return [
                      ...prev,
                      {
                        ...user,
                        name: user.name || '',
                        email: user.email || '',
                        avatar_url: user.avatar_url || '',
                        role: 'member',
                      },
                    ];
                  })}
                  excludeIds={members.map(m => m.id)}
                />
              </CardContent>
            </Card>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" className="bg-project hover:bg-project-dark">
                {mode === 'create' ? 'Criar Projeto' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

# PLANO DE IMPLEMENTAÇÃO FASEADA
**RLS sem Recursão - Sistema Haiku Project Flow**  
**Versão:** 2.0 - Julho 2025  
**Estratégia:** Implementação incremental e segura

---

## 🎯 OBJETIVOS E CRONOGRAMA

### **TIMELINE GERAL: 15-20 dias**
```
Fase 1: Preparação        → 2-3 dias
Fase 2: Base              → 4-5 dias
Fase 3: Intermediário     → 5-6 dias
Fase 4: Avançado          → 3-4 dias
Fase 5: Otimização        → 2-3 dias
```

### **CRITÉRIOS DE SUCESSO**
- ✅ Zero recursão infinita
- ✅ Performance < 100ms por query
- ✅ 100% funcionalidades preservadas
- ✅ Rollback disponível a qualquer momento

---

## 📋 FASE 1: PREPARAÇÃO (2-3 dias)

### **DIA 1: Limpeza e Análise**

#### **Manhã: Execução da Limpeza**
```bash
# 1. Executar script de limpeza
REMOVE_ALL_RLS.sql

# 2. Verificar limpeza completa
SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public';
-- Deve retornar 0

# 3. Testar funcionalidades básicas
```

#### **Tarde: Mapeamento de Queries**
```bash
# Identificar todas as queries críticas do sistema
grep -r "supabase.from" src/ > queries_map.txt
grep -r ".select(" src/ >> queries_map.txt
grep -r ".insert(" src/ >> queries_map.txt
grep -r ".update(" src/ >> queries_map.txt
```

**Entregáveis Dia 1:**
- [ ] Sistema funcionando sem RLS
- [ ] Mapa de queries críticas
- [ ] Baseline de performance

### **DIA 2: Criação de Estruturas de Apoio**

#### **Helper Functions**
```sql
-- Criar functions que evitam recursão
CREATE OR REPLACE FUNCTION auth_user_id() 
RETURNS UUID AS $$
  SELECT auth.uid()
$$ LANGUAGE sql STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_admin_user(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id AND role = 'admin'
  )
$$ LANGUAGE sql STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION user_project_ids(user_id UUID DEFAULT auth.uid())
RETURNS UUID[] AS $$
  SELECT ARRAY(
    SELECT DISTINCT project_id 
    FROM project_members 
    WHERE user_id = user_id
    UNION
    SELECT DISTINCT id 
    FROM projects 
    WHERE created_by = user_id
  )
$$ LANGUAGE sql STABLE SECURITY DEFINER;
```

#### **Índices para Performance**
```sql
-- Índices específicos para RLS
CREATE INDEX IF NOT EXISTS idx_profiles_role_active ON profiles(role) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_project_members_user_project ON project_members(user_id, project_id);
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON projects(created_by);
CREATE INDEX IF NOT EXISTS idx_tasks_stage_id ON tasks(stage_id);
CREATE INDEX IF NOT EXISTS idx_task_executors_user_task ON task_executors(user_id, task_id);
CREATE INDEX IF NOT EXISTS idx_task_approvers_user_task ON task_approvers(user_id, task_id);
```

**Entregáveis Dia 2:**
- [ ] Helper functions criadas
- [ ] Índices otimizados
- [ ] Views de apoio configuradas

### **DIA 3: Testes e Baseline**

#### **Scripts de Teste**
```sql
-- Criar scripts para validar cada funcionalidade
-- teste_crud_projects.sql
-- teste_crud_tasks.sql
-- teste_autocomplete.sql
-- teste_performance.sql
```

#### **Monitoramento**
```sql
-- Configurar logs de performance
ALTER SYSTEM SET log_min_duration_statement = 100;
SELECT pg_reload_conf();
```

**Entregáveis Dia 3:**
- [ ] Scripts de teste criados
- [ ] Baseline de performance documentado
- [ ] Monitoramento configurado

---

## 🏗️ FASE 2: IMPLEMENTAÇÃO BASE (4-5 dias)

### **DIA 4: Tabela profiles (Base do Sistema)**

#### **Políticas profiles**
```sql
-- 1. Próprio perfil sempre visível
CREATE POLICY "profiles_own_access" ON profiles FOR SELECT USING (
  id = auth.uid()
);

-- 2. Admins veem todos os perfis
CREATE POLICY "profiles_admin_full" ON profiles FOR ALL USING (
  is_admin_user()
);

-- 3. Usuários ativos visíveis para autocomplete (básico)
CREATE POLICY "profiles_active_basic" ON profiles FOR SELECT USING (
  is_active = true
);

-- 4. Inserção de novos perfis
CREATE POLICY "profiles_insert_own" ON profiles FOR INSERT WITH CHECK (
  id = auth.uid() OR is_admin_user()
);

-- 5. Atualização apenas próprio perfil (ou admin)
CREATE POLICY "profiles_update_own" ON profiles FOR UPDATE USING (
  id = auth.uid() OR is_admin_user()
);

-- Habilitar RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
```

#### **Testes profiles**
```sql
-- Testar login
-- Testar autocomplete
-- Testar edição de perfil
-- Verificar que não há recursão
```

**Checklist Dia 4:**
- [ ] Políticas profiles implementadas
- [ ] Login funcionando
- [ ] Autocomplete funcionando
- [ ] Edição de perfil funcionando
- [ ] Performance OK (< 50ms)

### **DIA 5: Tabela projects**

#### **Políticas projects**
```sql
-- 1. Admins veem todos os projetos
CREATE POLICY "projects_admin_all" ON projects FOR ALL USING (
  is_admin_user()
);

-- 2. Projetos onde usuário é membro
CREATE POLICY "projects_member_access" ON projects FOR SELECT USING (
  id = ANY(user_project_ids())
);

-- 3. Inserção de novos projetos
CREATE POLICY "projects_insert_any" ON projects FOR INSERT WITH CHECK (
  auth.uid() IS NOT NULL
);

-- 4. Atualização apenas projetos próprios
CREATE POLICY "projects_update_own" ON projects FOR UPDATE USING (
  created_by = auth.uid() OR is_admin_user()
);

-- 5. Exclusão apenas projetos próprios
CREATE POLICY "projects_delete_own" ON projects FOR DELETE USING (
  created_by = auth.uid() OR is_admin_user()
);

-- Habilitar RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
```

#### **Testes projects**
```sql
-- Testar criação de projeto
-- Testar listagem de projetos
-- Testar edição de projeto
-- Verificar que membros veem projetos corretos
```

**Checklist Dia 5:**
- [ ] Políticas projects implementadas
- [ ] CRUD de projetos funcionando
- [ ] Membros veem projetos corretos
- [ ] Performance OK

### **DIA 6: Tabela project_members**

#### **Políticas project_members**
```sql
-- 1. Admins veem todos
CREATE POLICY "project_members_admin_all" ON project_members FOR ALL USING (
  is_admin_user()
);

-- 2. Membros veem outros membros do mesmo projeto
CREATE POLICY "project_members_same_project" ON project_members FOR SELECT USING (
  project_id = ANY(user_project_ids())
);

-- 3. Inserção apenas em projetos onde é owner ou admin
CREATE POLICY "project_members_insert_owner" ON project_members FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM projects 
    WHERE id = project_id 
    AND (created_by = auth.uid() OR is_admin_user())
  )
);

-- 4. Atualização/exclusão apenas projetos próprios
CREATE POLICY "project_members_manage_own" ON project_members FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM projects 
    WHERE id = project_id 
    AND (created_by = auth.uid() OR is_admin_user())
  )
);

CREATE POLICY "project_members_delete_own" ON project_members FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM projects 
    WHERE id = project_id 
    AND (created_by = auth.uid() OR is_admin_user())
  )
);

-- Habilitar RLS
ALTER TABLE project_members ENABLE ROW LEVEL SECURITY;
```

**Checklist Dia 6:**
- [ ] Políticas project_members implementadas
- [ ] Gestão de membros funcionando
- [ ] Visualização de membros correta

### **DIA 7: Teste e Validação Base**

#### **Testes Integrados**
```sql
-- 1. Teste completo: criar projeto + adicionar membros
-- 2. Teste de permissões: verificar acessos corretos
-- 3. Teste de performance: medir tempos de resposta
-- 4. Teste de segurança: tentar acessar dados não autorizados
```

#### **Debugging**
```sql
-- Verificar queries lentas
SELECT query, mean_exec_time 
FROM pg_stat_statements 
WHERE query LIKE '%policies%'
ORDER BY mean_exec_time DESC;
```

**Checklist Dia 7:**
- [ ] Todos os CRUDs base funcionando
- [ ] Performance dentro do esperado
- [ ] Segurança validada
- [ ] Zero recursão detectada

---

## ⚡ FASE 3: INTERMEDIÁRIO (5-6 dias)

### **DIA 8-9: Tabelas stages + stage_responsibles**

#### **Estratégia**
- Stages herdam permissões do projeto pai
- Stage responsibles têm acesso adicional às suas etapas
- Evitar consultas recursivas usando user_project_ids()

#### **Implementação stages**
```sql
-- 1. Acesso por projeto
CREATE POLICY "stages_project_access" ON stages FOR SELECT USING (
  project_id = ANY(user_project_ids()) OR is_admin_user()
);

-- 2. Responsáveis veem suas etapas
CREATE POLICY "stages_responsible_access" ON stages FOR SELECT USING (
  id IN (
    SELECT stage_id FROM stage_responsibles 
    WHERE user_id = auth.uid()
  )
);

-- 3. CRUD para membros do projeto
CREATE POLICY "stages_project_crud" ON stages FOR ALL USING (
  project_id = ANY(user_project_ids()) OR is_admin_user()
);
```

**Checklist Dia 8-9:**
- [ ] Stages funcionando
- [ ] Stage responsibles funcionando
- [ ] Permissões corretas
- [ ] Performance OK

### **DIA 10-11: Tabela tasks (Core)**

#### **Estratégia Complexa**
- Tasks herdam permissões da stage (que herda do projeto)
- Executores/Aprovadores têm acesso adicional
- Múltiplos paths de acesso sem recursão

#### **Implementação tasks**
```sql
-- 1. Acesso via projeto (através de stage)
CREATE POLICY "tasks_project_access" ON tasks FOR SELECT USING (
  stage_id IN (
    SELECT s.id FROM stages s 
    WHERE s.project_id = ANY(user_project_ids())
  )
  OR is_admin_user()
);

-- 2. Executores veem suas tarefas
CREATE POLICY "tasks_executor_access" ON tasks FOR SELECT USING (
  id IN (
    SELECT task_id FROM task_executors 
    WHERE user_id = auth.uid()
  )
);

-- 3. Aprovadores veem tarefas para aprovação
CREATE POLICY "tasks_approver_access" ON tasks FOR SELECT USING (
  id IN (
    SELECT task_id FROM task_approvers 
    WHERE user_id = auth.uid()
  )
);

-- 4. CRUD baseado em projeto
CREATE POLICY "tasks_project_crud" ON tasks FOR ALL USING (
  stage_id IN (
    SELECT s.id FROM stages s 
    WHERE s.project_id = ANY(user_project_ids())
  )
  OR is_admin_user()
);
```

**Checklist Dia 10-11:**
- [ ] Tasks visíveis corretamente
- [ ] Executores veem suas tarefas
- [ ] Aprovadores veem tarefas para aprovação
- [ ] CRUD funcionando
- [ ] Performance aceitável

### **DIA 12: Tabelas Relacionais (task_executors, task_approvers)**

#### **Estratégia Simples**
- Herdam permissões das tasks
- Acesso direto para próprias atribuições

#### **Implementação**
```sql
-- task_executors
CREATE POLICY "task_executors_task_access" ON task_executors FOR SELECT USING (
  task_id IN (SELECT id FROM tasks)  -- RLS da tasks já filtra
  OR user_id = auth.uid()  -- Próprias atribuições
  OR is_admin_user()
);

-- task_approvers (similar)
```

**Checklist Dia 12:**
- [ ] Atribuições funcionando
- [ ] Próprias atribuições visíveis
- [ ] CRUD de atribuições OK

---

## 🚀 FASE 4: AVANÇADO (3-4 dias)

### **DIA 13: Conteúdo e Comentários**

#### **Tabelas:**
- task_content_blocks
- task_comments
- evidence

#### **Estratégia:**
- Herdam permissões da task pai
- Comentários: autor sempre pode ver/editar próprios
- Evidence: executores podem adicionar

### **DIA 14: Quiz e Avaliações (se aplicável)**

#### **Tabelas:**
- quizzes
- quiz_attempts
- quiz_answers
- user_quiz_progress

### **DIA 15: Notificações e Histórico**

#### **Tabelas:**
- user_notifications
- project_history
- audit_logs

**Checklist Dias 13-15:**
- [ ] Todas as funcionalidades principais
- [ ] Funcionalidades auxiliares
- [ ] Sistema completo funcionando

---

## 🔧 FASE 5: OTIMIZAÇÃO (2-3 dias)

### **DIA 16: Performance Tuning**

#### **Análise de Performance**
```sql
-- Identificar queries lentas
SELECT query, calls, mean_exec_time, total_exec_time
FROM pg_stat_statements 
WHERE mean_exec_time > 100
ORDER BY total_exec_time DESC;
```

#### **Otimizações**
- Ajustar índices específicos
- Otimizar políticas complexas
- Implementar cache quando necessário

### **DIA 17: Monitoramento e Alertas**

#### **Dashboards**
- Tempo de resposta por endpoint
- Uso de recursos por política
- Detecção de recursão

#### **Alertas**
- Query > 500ms
- Timeout de políticas
- Falhas de autenticação

### **DIA 18: Documentação e Rollback**

#### **Documentação Final**
- Políticas implementadas
- Performance benchmarks
- Troubleshooting guide
- Rollback procedures

---

## 🧪 TESTES E VALIDAÇÃO

### **Testes por Fase**

#### **Automatizados**
```javascript
// Jest tests para cada funcionalidade
describe('RLS Policies', () => {
  test('Admin can see all projects', async () => {
    // Test implementation
  });
  
  test('Member can only see assigned projects', async () => {
    // Test implementation
  });
});
```

#### **Manuais**
- [ ] Login como diferentes roles
- [ ] Tentar acessar dados não autorizados
- [ ] Verificar autocomplete
- [ ] Testar performance sob carga

### **Critérios de Aceitação**

#### **Funcionalidade**
- [ ] 100% das funcionalidades preservadas
- [ ] Autocomplete funcionando
- [ ] Todas as permissões corretas

#### **Performance**
- [ ] Queries RLS < 100ms (95th percentile)
- [ ] Dashboard < 2s para carregar
- [ ] Autocomplete < 500ms

#### **Segurança**
- [ ] Zero bypass de segurança detectado
- [ ] Logs de auditoria funcionando
- [ ] Dados isolados por contexto

#### **Manutenibilidade**
- [ ] Zero recursão infinita
- [ ] Código documentado
- [ ] Rollback testado

---

## 🚨 CONTINGÊNCIA E ROLLBACK

### **Plano de Rollback**

#### **Rollback Rápido (< 5 min)**
```sql
-- Desabilitar RLS em caso de emergência
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE projects DISABLE ROW LEVEL SECURITY;
-- ... outras tabelas
```

#### **Rollback Completo (< 30 min)**
```sql
-- Restaurar estado anterior completo
-- Script específico por fase implementada
```

### **Monitoramento Crítico**
```sql
-- Alertas automáticos
-- Query timeout > 30s
-- Error rate > 5%
-- Response time > 1s (95th percentile)
```

---

**✅ CHECKLIST FINAL**

- [ ] Todas as fases implementadas
- [ ] Testes passing
- [ ] Performance OK
- [ ] Segurança validada
- [ ] Documentação completa
- [ ] Rollback disponível
- [ ] Equipe treinada

**🎯 SUCESSO:** Sistema funcionando com RLS seguro, performático e sem recursão!

import React from 'react';
import { useProjectPermissions, useGlobalPermissions, type Permission, type ProjectRole } from '@/hooks/usePermissions';

interface RequireProjectRoleProps {
  roles: ProjectRole | ProjectRole[];
  projectId?: string;
  userId?: string;
  projectOwnerId?: string;
  taskResponsibleId?: string;
  taskExecutorIds?: string[];
  taskApproverIds?: string[];
  stageResponsibleIds?: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  hideIfNoAccess?: boolean;
}

/**
 * Componente que renderiza children apenas se o usuário tiver um dos roles especificados no projeto
 */
export const RequireProjectRole: React.FC<RequireProjectRoleProps> = ({
  roles,
  projectId,
  userId,
  projectOwnerId,
  taskResponsibleId,
  taskExecutorIds,
  taskApproverIds,
  stageResponsibleIds,
  children,
  fallback = null,
  hideIfNoAccess = true
}) => {
  const context = {
    userId,
    projectOwnerId,
    taskResponsibleId,
    taskExecutorIds,
    taskApproverIds,
    stageResponsibleIds
  };

  const { roles: userRoles } = useProjectPermissions(projectId, context);
  const rolesToCheck = Array.isArray(roles) ? roles : [roles];
  
  const hasRequiredRole = rolesToCheck.some(role => userRoles.includes(role));

  if (hasRequiredRole) {
    return <>{children}</>;
  }

  if (hideIfNoAccess) {
    return null;
  }

  return <>{fallback}</>;
};

interface RequirePermissionProps {
  permissions: Permission | Permission[];
  projectId?: string;
  userId?: string;
  projectOwnerId?: string;
  taskResponsibleId?: string;
  taskExecutorIds?: string[];
  taskApproverIds?: string[];
  stageResponsibleIds?: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  hideIfNoAccess?: boolean;
}

/**
 * Componente que renderiza children apenas se o usuário tiver as permissões especificadas
 */
export const RequirePermission: React.FC<RequirePermissionProps> = ({
  permissions,
  projectId,
  userId,
  projectOwnerId,
  taskResponsibleId,
  taskExecutorIds,
  taskApproverIds,
  stageResponsibleIds,
  children,
  fallback = null,
  hideIfNoAccess = true
}) => {
  const context = {
    userId,
    projectOwnerId,
    taskResponsibleId,
    taskExecutorIds,
    taskApproverIds,
    stageResponsibleIds
  };

  const { hasPermission, hasAnyPermission } = useProjectPermissions(projectId, context);
  
  const permissionsToCheck = Array.isArray(permissions) ? permissions : [permissions];
  const hasRequiredPermission = hasAnyPermission(permissionsToCheck);

  if (hasRequiredPermission) {
    return <>{children}</>;
  }

  if (hideIfNoAccess) {
    return null;
  }

  return <>{fallback}</>;
};

interface RequireGlobalRoleProps {
  roles: 'admin' | 'manager' | ('admin' | 'manager')[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  hideIfNoAccess?: boolean;
}

/**
 * Componente que renderiza children apenas se o usuário tiver um dos roles globais especificados
 */
export const RequireGlobalRole: React.FC<RequireGlobalRoleProps> = ({
  roles,
  children,
  fallback = null,
  hideIfNoAccess = true
}) => {
  const { isAdmin, isManager } = useGlobalPermissions();
  
  const rolesToCheck = Array.isArray(roles) ? roles : [roles];
  const hasRequiredRole = rolesToCheck.some(role => {
    if (role === 'admin') return isAdmin;
    if (role === 'manager') return isManager;
    return false;
  });

  if (hasRequiredRole) {
    return <>{children}</>;
  }

  if (hideIfNoAccess) {
    return null;
  }

  return <>{fallback}</>;
};

interface AccessDeniedCardProps {
  title?: string;
  description?: string;
  className?: string;
}

/**
 * Componente padrão para exibir quando o acesso é negado
 */
export const AccessDeniedCard: React.FC<AccessDeniedCardProps> = ({
  title = "Acesso Negado",
  description = "Você não tem permissão para acessar esta funcionalidade.",
  className = ""
}) => {
  return (
    <div className={`p-4 bg-yellow-50 border border-yellow-200 rounded-lg ${className}`}>
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-yellow-800">{title}</h3>
          <p className="mt-1 text-sm text-yellow-700">{description}</p>
        </div>
      </div>
    </div>
  );
};

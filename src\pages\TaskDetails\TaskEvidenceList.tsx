import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Paperclip, Upload, Eye, Image as ImageIcon } from 'lucide-react';

interface Evidence {
  id: string;
  name: string;
  url: string;
  uploadedBy: any;
  uploadedAt: string;
}

interface TaskEvidenceListProps {
  evidence: Evidence[];
  onUploadClick: () => void;
}

export const TaskEvidenceList: React.FC<TaskEvidenceListProps> = ({ evidence, onUploadClick }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between">
      <CardTitle className="flex items-center gap-2">
        <Paperclip className="w-5 h-5" />
        Evidências ({evidence.length})
      </CardTitle>
      <Button 
        size="sm" 
        variant="outline"
        onClick={onUploadClick}
      >
        <Upload className="w-4 h-4 mr-1" />
        Enviar Arquivo
      </Button>
    </CardH<PERSON>er>
    <CardContent>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {evidence.map((e) => (
          <div key={e.id} className="border rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                <ImageIcon className="w-5 h-5 text-gray-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-sm">{e.name}</p>
                <p className="text-xs text-gray-600">
                  Por {e.uploadedBy?.name || e.uploadedBy || 'Desconhecido'} • {e.uploadedAt}
                </p>
              </div>
              <Button size="sm" variant="ghost">
                <Eye className="w-4 h-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
); 
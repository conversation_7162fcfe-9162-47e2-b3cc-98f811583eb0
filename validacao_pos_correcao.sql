-- =====================================================
-- TESTE VALIDAÇÃO RLS - PÓS CORREÇÃO
-- =====================================================

-- 1. Verificar se políticas problemáticas foram removidas
SELECT 
  'POLÍTICAS PROBLEMÁTICAS' as check_tipo,
  COUNT(*) as encontradas,
  CASE 
    WHEN COUNT(*) = 0 THEN '✅ LIMPO'
    ELSE '❌ AINDA HÁ PROBLEMAS'
  END as status
FROM pg_policies 
WHERE schemaname = 'public' 
AND policyname LIKE '%accessible%';

-- 2. Teste básico de acesso a projects (deve funcionar)
SELECT 
  'TESTE ACESSO PROJECTS' as teste,
  COUNT(*) as projects_acessiveis,
  '✅ SEM ERRO 42P17' as status
FROM public.projects;

-- 3. Verificar políticas ativas para projects
SELECT 
  'POLÍTICAS PROJECTS' as secao,
  policyname
FROM pg_policies 
WHERE tablename = 'projects' 
AND schemaname = 'public'
ORDER BY policyname;

-- 4. Verificar políticas ativas para tasks  
SELECT 
  'POLÍTICAS TASKS' as secao,
  policyname
FROM pg_policies 
WHERE tablename = 'tasks' 
AND schemaname = 'public'
ORDER BY policyname;

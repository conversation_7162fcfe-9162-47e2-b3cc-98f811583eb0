-- TESTE DE PERMISSÕES: Sistema de Roles e Upload de Evidências
-- Execute este script para testar as permissões implementadas
-- Data: 2025-01-17

-- ========================================
-- PARTE 1: SETUP DE TESTE
-- ========================================

SELECT '=== CONFIGURAÇÃO DE TESTE ===' as secao;

-- Verificar usuários existentes com diferentes roles
SELECT 'Usuários por role:' as status;
SELECT 
    role,
    COUNT(*) as total_users,
    ARRAY_AGG(name) as users
FROM profiles
WHERE is_active = true
GROUP BY role
ORDER BY role;

-- Verificar se temos usuários com role 'member'
SELECT 'Verificando usuários member:' as status;
SELECT 
    id,
    name,
    email,
    role,
    is_active
FROM profiles
WHERE role = 'member' AND is_active = true
LIMIT 5;

-- ========================================
-- PARTE 2: TESTE DE ESTRUTURA DE PROJETOS
-- ========================================

SELECT '=== ESTRUTURA DE PROJETOS ===' as secao;

-- Verificar projetos e seus membros
SELECT 'Projetos com membros:' as status;
SELECT 
    p.id as project_id,
    p.name as project_name,
    COUNT(pm.user_id) as member_count,
    ARRAY_AGG(DISTINCT pm.role) as member_roles
FROM projects p
LEFT JOIN project_members pm ON p.id = pm.project_id
GROUP BY p.id, p.name
ORDER BY p.created_at DESC
LIMIT 5;

-- Verificar tasks e seus executores
SELECT 'Tasks com executores:' as status;
SELECT 
    t.id as task_id,
    t.title,
    t.project_id,
    COUNT(te.user_id) as executor_count,
    ARRAY_AGG(pr.name) as executor_names
FROM tasks t
LEFT JOIN task_executors te ON t.id = te.task_id
LEFT JOIN profiles pr ON te.user_id = pr.id
GROUP BY t.id, t.title, t.project_id
ORDER BY t.created_at DESC
LIMIT 5;

-- ========================================
-- PARTE 3: TESTE DE PERMISSÕES DE UPLOAD
-- ========================================

SELECT '=== TESTE DE PERMISSÕES DE UPLOAD ===' as secao;

-- Simular query do EvidenceApprovalService.canUploadEvidence
-- para verificar se membros têm permissão de upload
SELECT 'Teste de permissões de upload:' as status;
SELECT 
    t.id as task_id,
    t.title,
    t.project_id,
    -- Executores da tarefa
    COUNT(te.user_id) as executor_count,
    -- Membros do projeto
    COUNT(pm.user_id) as project_member_count,
    -- Verificar se task tem projeto válido
    CASE WHEN t.project_id IS NOT NULL THEN 'Sim' ELSE 'Não' END as has_project
FROM tasks t
LEFT JOIN task_executors te ON t.id = te.task_id
LEFT JOIN projects proj ON t.project_id = proj.id
LEFT JOIN project_members pm ON proj.id = pm.project_id
GROUP BY t.id, t.title, t.project_id
ORDER BY t.created_at DESC
LIMIT 3;

-- ========================================
-- PARTE 4: TESTE ESPECÍFICO PARA USUÁRIO MEMBER
-- ========================================

SELECT '=== TESTE PARA USUÁRIO MEMBER ===' as secao;

-- Pegar um usuário member para teste
WITH member_user AS (
    SELECT id, name, email, role
    FROM profiles
    WHERE role = 'member' AND is_active = true
    LIMIT 1
)
SELECT 'Usuário member selecionado para teste:' as status;
SELECT * FROM member_user;

-- Verificar quais projetos este usuário member pode acessar
WITH member_user AS (
    SELECT id, name, email, role
    FROM profiles
    WHERE role = 'member' AND is_active = true
    LIMIT 1
)
SELECT 'Projetos acessíveis ao usuário member:' as status;
SELECT 
    p.id as project_id,
    p.name as project_name,
    pm.role as member_role,
    mu.name as member_name
FROM member_user mu
JOIN project_members pm ON mu.id = pm.user_id
JOIN projects p ON pm.project_id = p.id
ORDER BY p.name;

-- Verificar tasks onde este usuário member é executor
WITH member_user AS (
    SELECT id, name, email, role
    FROM profiles
    WHERE role = 'member' AND is_active = true
    LIMIT 1
)
SELECT 'Tasks onde o usuário member é executor:' as status;
SELECT 
    t.id as task_id,
    t.title,
    t.project_id,
    mu.name as member_name,
    'executor' as relation_type
FROM member_user mu
JOIN task_executors te ON mu.id = te.user_id
JOIN tasks t ON te.task_id = t.id
ORDER BY t.created_at DESC;

-- ========================================
-- PARTE 5: VALIDAÇÃO DE EVIDÊNCIAS
-- ========================================

SELECT '=== VALIDAÇÃO DE EVIDÊNCIAS ===' as secao;

-- Verificar evidências existentes por usuário member
WITH member_user AS (
    SELECT id, name, email, role
    FROM profiles
    WHERE role = 'member' AND is_active = true
    LIMIT 1
)
SELECT 'Evidências enviadas por usuário member:' as status;
SELECT 
    e.id,
    e.task_id,
    e.block_id,
    e.file_name,
    e.type,
    e.status,
    e.created_at,
    mu.name as uploaded_by_name
FROM member_user mu
JOIN evidence e ON mu.id = e.uploaded_by
ORDER BY e.created_at DESC
LIMIT 5;

-- ========================================
-- INSTRUÇÕES PARA TESTE MANUAL
-- ========================================

SELECT '=== INSTRUÇÕES PARA TESTE MANUAL ===' as secao;

/*
TESTE MANUAL DAS PERMISSÕES:

1. TESTE DE TABS (TaskDetailsV2):
   - Faça login como usuário com role 'member'
   - Acesse uma tarefa em TaskDetailsV2
   - Verifique se apenas 2 tabs aparecem (Visão Geral e Executar Tarefa)
   - Verifique se a tab "Editar Conteúdo" NÃO aparece
   - Tente acessar manualmente a tab edit via URL (deve redirecionar)

2. TESTE DE UPLOAD DE EVIDÊNCIAS:
   - Na tab "Executar Tarefa", procure por blocos de evidência
   - Verifique se o botão "Adicionar" está disponível (se for membro do projeto)
   - Tente fazer upload de um arquivo
   - Verifique se o upload funciona normalmente

3. TESTE DE MENSAGENS CONTEXTUAIS:
   - Verifique se aparecem mensagens específicas para role 'member'
   - Mensagem: "Como membro, você pode fazer upload de evidências"
   - Tooltip no botão desabilitado quando não tem permissão

4. TESTE DE PERMISSÕES DIFERENTES:
   - Teste com usuário 'admin' (deve ver todas as tabs)
   - Teste com usuário 'manager' (deve ver todas as tabs)
   - Teste com usuário 'member' (deve ver apenas 2 tabs)

5. VERIFICAR LOGS:
   - Console do navegador deve mostrar logs de permissões
   - Procure por "🔐 Permissão para upload:"
   - Verifique se retorna true para membros do projeto

CASOS DE TESTE ESPERADOS:
- Member + Executor da tarefa = Pode fazer upload
- Member + Membro do projeto = Pode fazer upload
- Member + Não relacionado = Não pode fazer upload
- Admin/Manager = Sempre pode editar conteúdo
*/

-- ========================================
-- PARTE 6: CONSULTAS DE DEPURAÇÃO
-- ========================================

-- Query para depurar permissões de upload em tempo real
/*
-- Substitua {TASK_ID} e {USER_ID} pelos valores reais

SELECT 
    t.id as task_id,
    t.title,
    t.project_id,
    -- Verificar se é executor
    CASE WHEN EXISTS (
        SELECT 1 FROM task_executors te 
        WHERE te.task_id = t.id AND te.user_id = '{USER_ID}'
    ) THEN 'Sim' ELSE 'Não' END as is_executor,
    -- Verificar se é membro do projeto
    CASE WHEN EXISTS (
        SELECT 1 FROM project_members pm 
        WHERE pm.project_id = t.project_id AND pm.user_id = '{USER_ID}'
    ) THEN 'Sim' ELSE 'Não' END as is_project_member,
    -- Resultado final
    CASE WHEN EXISTS (
        SELECT 1 FROM task_executors te 
        WHERE te.task_id = t.id AND te.user_id = '{USER_ID}'
    ) OR EXISTS (
        SELECT 1 FROM project_members pm 
        WHERE pm.project_id = t.project_id AND pm.user_id = '{USER_ID}'
    ) THEN 'PODE_FAZER_UPLOAD' ELSE 'NÃO_PODE_FAZER_UPLOAD' END as permission_result
FROM tasks t
WHERE t.id = '{TASK_ID}';
*/

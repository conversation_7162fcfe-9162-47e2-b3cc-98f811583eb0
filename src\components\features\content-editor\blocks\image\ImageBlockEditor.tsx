import React from 'react';
import { Input } from '@/components/ui/input';
import { ImageBlockContent, BlockConfig, defaultBlockConfig } from '@/types';
import { BlockCardIcon } from '../shared/BlockCardIcon';

/**
 * Editor para blocos de imagem.
 * @param editContent Conteúdo do bloco (tipado)
 * @param setEditContent Callback para atualizar o conteúdo
 * @param mode 'edit' para edição, 'preview' para visualização
 * @param config Configurações do bloco
 */
export interface ImageBlockEditorProps {
  editContent: ImageBlockContent;
  setEditContent: (c: ImageBlockContent) => void;
  mode: 'edit' | 'preview';
  config?: BlockConfig;
}

export const ImageBlockEditor: React.FC<ImageBlockEditorProps> = ({ editContent, setEditContent, mode, config }) => {
  const safeConfig = config || defaultBlockConfig;
  if (mode === 'edit') {
    return (
      <div className="flex flex-col gap-1 w-full">
        <Input
          value={editContent.alt || ''}
          onChange={e => setEditContent({ ...editContent, alt: e.target.value })}
          placeholder="Título da imagem"
        />
        <Input
          value={editContent.url || ''}
          onChange={e => setEditContent({ ...editContent, url: e.target.value })}
          placeholder="URL da imagem"
        />
        <Input
          value={editContent.caption || ''}
          onChange={e => setEditContent({ ...editContent, caption: e.target.value })}
          placeholder="Legenda"
        />
      </div>
    );
  }
  // Preview
  return (
    <div
      style={{
        background: safeConfig.card?.backgroundColor || '#f8fafc',
        border: safeConfig.card?.border?.enabled
          ? `${safeConfig.card?.border?.width || 1}px solid ${safeConfig.card?.border?.color || '#e5e5e5'}`
          : 'none',
        borderRadius:
          safeConfig.card?.format === 'pill'
            ? 9999
            : safeConfig.card?.format === 'square'
            ? 0
            : 12,
        boxShadow: safeConfig.card?.shadow?.enabled
          ? `0 2px ${2 * (safeConfig.card?.shadow?.depth || 1)}px #0002`
          : 'none',
        padding: 16,
        minHeight: 80,
        width: '100%',
        transition: 'all 0.2s',
      }}
      className="flex flex-col gap-1 w-full"
    >
      <BlockCardIcon
        config={{
          ...safeConfig.icon,
          iconName: safeConfig.icon?.iconName || 'Image',
        }}
        title={editContent.alt}
        description={editContent.caption}
        content={
          <div className="w-16 h-12 bg-gray-300 rounded flex items-center justify-center">
            {editContent.url ? (
              <img src={editContent.url} alt={editContent.alt} className="w-full h-full object-contain rounded" />
            ) : null}
          </div>
        }
      />
    </div>
  );
};
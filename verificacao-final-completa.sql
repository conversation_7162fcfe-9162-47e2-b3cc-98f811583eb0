-- VERIFICAÇÃO FINAL: Upload de Arquivos em Blocos de Evidência
-- Execute este script para verificar se o sistema está funcionando corretamente
-- Data: 2025-01-17

-- ========================================
-- PARTE 1: DIAGNÓSTICO DA SITUAÇÃO ATUAL
-- ========================================

SELECT '=== DIAGNÓSTICO INICIAL ===' as secao;

-- Verificar tabelas existentes
SELECT 'Verificando tabelas de evidência...' as status;
SELECT 
    table_name,
    table_schema
FROM information_schema.tables 
WHERE table_name IN ('evidence', 'task_attachments') 
AND table_schema = 'public';

-- Verificar estrutura da tabela evidence
SELECT 'Estrutura da tabela evidence:' as status;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'evidence' AND table_schema = 'public'
ORDER BY ordinal_position;

-- ========================================
-- PARTE 2: VERIFICAÇÃO DE DADOS
-- ========================================

SELECT '=== VERIFICAÇÃO DE DADOS ===' as secao;

-- Dados existentes na tabela evidence
SELECT 'Dados na tabela evidence:' as status;
SELECT 
    COUNT(*) as total_evidences,
    COUNT(DISTINCT task_id) as distinct_tasks,
    COUNT(DISTINCT block_id) as distinct_blocks,
    MIN(created_at) as primeira_evidencia,
    MAX(created_at) as ultima_evidencia
FROM evidence;

-- Dados existentes na tabela task_attachments (tabela antiga)
SELECT 'Dados na tabela task_attachments (antiga):' as status;
SELECT 
    COUNT(*) as total_attachments,
    COUNT(DISTINCT task_id) as distinct_tasks,
    COUNT(DISTINCT block_id) as distinct_blocks,
    MIN(created_at) as primeiro_attachment,
    MAX(created_at) as ultimo_attachment
FROM task_attachments
WHERE task_attachments.task_id IS NOT NULL;

-- Últimas evidências na tabela evidence
SELECT 'Últimas 5 evidências na tabela evidence:' as status;
SELECT 
    e.id,
    e.task_id,
    e.block_id,
    e.type,
    e.file_name,
    e.file_size,
    e.status,
    p.name as uploaded_by_name,
    e.created_at
FROM evidence e
LEFT JOIN profiles p ON e.uploaded_by = p.id
ORDER BY e.created_at DESC
LIMIT 5;

-- ========================================
-- PARTE 3: VERIFICAÇÃO DE TASKS E USUÁRIOS
-- ========================================

SELECT '=== VERIFICAÇÃO DE TASKS E USUÁRIOS ===' as secao;

-- Verificar tasks existentes
SELECT 'Tasks disponíveis para teste:' as status;
SELECT 
    t.id,
    t.title,
    t.status,
    p.name as owner_name,
    t.created_at
FROM tasks t
LEFT JOIN profiles p ON t.owner_id = p.id
ORDER BY t.created_at DESC
LIMIT 5;

-- Verificar usuários/profiles
SELECT 'Usuários/Profiles disponíveis:' as status;
SELECT 
    id,
    name,
    email,
    role,
    is_active
FROM profiles
WHERE is_active = true
ORDER BY created_at DESC
LIMIT 5;

-- ========================================
-- PARTE 4: VERIFICAÇÃO DE PERMISSÕES
-- ========================================

SELECT '=== VERIFICAÇÃO DE PERMISSÕES ===' as secao;

-- Verificar RLS nas tabelas
SELECT 'Status de Row Level Security:' as status;
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename IN ('evidence', 'task_attachments', 'tasks', 'profiles');

-- Verificar políticas ativas
SELECT 'Políticas RLS ativas:' as status;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd
FROM pg_policies 
WHERE tablename IN ('evidence', 'task_attachments')
ORDER BY tablename, policyname;

-- ========================================
-- PARTE 5: TESTE DE CONSULTA SIMULADA
-- ========================================

SELECT '=== TESTE DE CONSULTA SIMULADA ===' as secao;

-- Simular consulta do EvidenceApprovalService (se houver dados)
SELECT 'Simulando consulta EvidenceApprovalService:' as status;
SELECT 
    e.*,
    uploaded_by_profile.name as uploaded_by_name,
    uploaded_by_profile.email as uploaded_by_email,
    approved_by_profile.name as approved_by_name
FROM evidence e
LEFT JOIN profiles uploaded_by_profile ON e.uploaded_by = uploaded_by_profile.id
LEFT JOIN profiles approved_by_profile ON e.approved_by = approved_by_profile.id
ORDER BY e.created_at DESC
LIMIT 3;

-- ========================================
-- INSTRUÇÕES PARA TESTE MANUAL
-- ========================================

SELECT '=== INSTRUÇÕES PARA TESTE MANUAL ===' as secao;

/*
PASSOS PARA TESTAR O UPLOAD DE EVIDÊNCIAS:

1. ANTES DO TESTE:
   - Execute este script para ver o estado atual
   - Anote quantas evidências existem na tabela evidence

2. TESTE NA INTERFACE:
   - Abra uma tarefa com bloco de evidência
   - Tente fazer upload de um arquivo (imagem ou PDF pequeno)
   - Observe o console do navegador para logs

3. APÓS O TESTE:
   - Execute este script novamente
   - Verifique se o número de evidências aumentou
   - Verifique se o arquivo aparece na interface

4. LOGS IMPORTANTES:
   - Console do navegador: procure por "🔍 Carregando evidências"
   - Console do navegador: procure por "✅ Evidências com aprovação carregadas"
   - Console do navegador: procure por "📊 Total de evidências encontradas"

5. SE NÃO FUNCIONAR:
   - Verifique se o bucket 'evidence' existe no Supabase Storage
   - Verifique se as permissões RLS estão corretas
   - Verifique se o usuário tem permissão na tarefa
*/

-- ========================================
-- PARTE 6: VERIFICAÇÃO PÓS-TESTE
-- ========================================

-- Execute após fazer upload para ver se funcionou
/*
SELECT 'VERIFICAÇÃO PÓS-UPLOAD:' as status;
SELECT 
    e.id,
    e.task_id,
    e.block_id,
    e.file_name,
    e.file_size,
    e.type,
    e.status,
    p.name as uploaded_by,
    e.created_at
FROM evidence e
LEFT JOIN profiles p ON e.uploaded_by = p.id
WHERE e.created_at > NOW() - INTERVAL '1 hour'
ORDER BY e.created_at DESC;
*/

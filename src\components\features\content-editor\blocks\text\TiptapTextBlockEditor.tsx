import React from 'react';
import { TiptapTextEditor } from './TiptapTextEditor';
import { TextBlockContent, BlockConfig, defaultBlockConfig } from '@/types';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { BlockCard } from '@/components/ui/BlockCard';
import { convertLexicalToTiptap, isLexicalFormat, convertTiptapToLexical } from './utils/lexical-tiptap-converter';

/**
 * Adaptador do TiptapTextEditor para manter compatibilidade com a interface do TextBlockEditor
 */
export interface TiptapTextBlockEditorProps {
  editContent: TextBlockContent;
  setEditContent: (c: TextBlockContent) => void;
  mode: 'edit' | 'preview';
  editorKey?: string;
  config?: BlockConfig;
  truncateText?: boolean;
}

export const TiptapTextBlockEditor: React.FC<TiptapTextBlockEditorProps> = (props) => {
  const { editContent = { value: '' }, setEditContent, mode = 'edit', editorKey, config, truncateText } = props;
  const preview = mode === 'preview';
  const safeConfig = config || defaultBlockConfig;

  // CORREÇÃO CRÍTICA: Estabilizar conversão de conteúdo
  const htmlContent = React.useMemo(() => {
    const value = editContent?.value || '';
    if (!value) return '';
    return isLexicalFormat(value) ? convertLexicalToTiptap(value) : value;
  }, [editContent?.value]);

  // CORREÇÃO CRÍTICA: Estabilizar callback
  const handleContentChange = React.useCallback((content: string) => {
    if (setEditContent && !preview) {
      setEditContent({ value: content });
    }
  }, [setEditContent, preview]);

  // CORREÇÃO CRÍTICA: Renderização única sem condicionais
  // Sempre renderizar o mesmo componente para evitar problemas de hooks
  const editorComponent = (
    <TiptapTextEditor
      content={htmlContent}
      onChange={handleContentChange}
      editable={!preview}
      showToolbar={!preview}
      className="min-h-[100px]"
      placeholder="Digite seu texto..."
    />
  );

  // Configurações de estilo do card (similar aos outros blocos)
  const cardBgColor = safeConfig.card?.backgroundColor || '#ffffff';
  const cardTextColor = safeConfig.card?.font?.color || '#1f2937';
  const cardFormat = safeConfig.card?.format || 'rounded';
  const cardBorder = safeConfig.card?.border?.enabled;
  const cardBorderColor = safeConfig.card?.border?.color || '#e5e7eb';
  const cardBorderWidth = safeConfig.card?.border?.width || 1;
  const cardShadow = safeConfig.card?.shadow?.enabled;
  const cardShadowDepth = safeConfig.card?.shadow?.depth || 1;
  const cardHover = safeConfig.card?.hover?.enabled;
  const cardHoverShadowDepth = safeConfig.card?.hover?.shadowDepth || 2;

  // Função para gerar box-shadow
  const getBoxShadow = (depth: number) => `0 ${depth * 2}px ${depth * 4}px rgba(0,0,0,${depth * 0.1})`;

  // Estilos do card
  const cardStyles: React.CSSProperties = {
    background: cardBgColor,
    color: cardTextColor,
    borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
    border: cardBorder ? `${cardBorderWidth}px solid ${cardBorderColor}` : 'none',
    boxShadow: cardShadow ? getBoxShadow(cardShadowDepth) : 'none',
    transition: cardHover ? 'box-shadow 0.3s ease, transform 0.2s ease' : 'none',
    cursor: cardHover ? 'pointer' : 'default'
  };

  // Handlers de hover
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = getBoxShadow(cardHoverShadowDepth);
      e.currentTarget.style.transform = 'translateY(-2px)';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = cardShadow ? getBoxShadow(cardShadowDepth) : 'none';
      e.currentTarget.style.transform = 'translateY(0)';
    }
  };

  // Renderização com BlockCard (consistente com outros blocos)
  return (
    <BlockCard
      className="flex flex-col gap-1 p-4"
      style={cardStyles}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <BlockCardIcon
        config={{
          ...safeConfig.icon,
          iconName: safeConfig.icon?.iconName || 'AlignLeft',
        }}
        title={undefined}
        description={undefined}
        content={editorComponent}
        textColor={cardTextColor}
        truncateText={truncateText}
      />
    </BlockCard>
  );
};

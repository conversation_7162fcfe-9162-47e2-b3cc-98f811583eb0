-- =====================================================
-- CORREÇÃO EMERGENCIAL - LIMPEZA TOTAL RLS
-- =====================================================
-- Remove TODAS as políticas e recria apenas as necessárias

-- =====================================================
-- PASSO 1: LIMPEZA TOTAL
-- =====================================================

-- Function para remover todas as políticas de uma tabela
CREATE OR REPLACE FUNCTION public.remove_all_policies_from_table(target_table text)
RETURNS void AS $$
DECLARE
  policy_record RECORD;
BEGIN
  FOR policy_record IN
    SELECT policyname
    FROM pg_policies p
    WHERE p.tablename = target_table AND p.schemaname = 'public'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I', policy_record.policyname, target_table);
    RAISE NOTICE 'Removida política: % de %', policy_record.policyname, target_table;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Remover TODAS as políticas das tabelas principais
SELECT public.remove_all_policies_from_table('projects');
SELECT public.remove_all_policies_from_table('stages');
SELECT public.remove_all_policies_from_table('tasks');

-- =====================================================
-- PASSO 2: RECRIAR POLÍTICAS ESSENCIAIS (SEM RECURSÃO)
-- =====================================================

-- PROJECTS: Políticas básicas
CREATE POLICY "view_owned_projects"
  ON public.projects
  FOR SELECT
  USING (owner_id = auth.uid());

CREATE POLICY "view_member_projects"
  ON public.projects
  FOR SELECT
  USING (
    id IN (
      SELECT project_id FROM public.project_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "create_projects"
  ON public.projects
  FOR INSERT
  WITH CHECK (owner_id = auth.uid());

CREATE POLICY "update_owned_projects"
  ON public.projects
  FOR UPDATE
  USING (owner_id = auth.uid());

CREATE POLICY "delete_owned_projects"
  ON public.projects
  FOR DELETE
  USING (owner_id = auth.uid());

-- STAGES: Políticas básicas
CREATE POLICY "view_stages_owned_projects"
  ON public.stages
  FOR SELECT
  USING (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    )
  );

CREATE POLICY "view_stages_member_projects"
  ON public.stages
  FOR SELECT
  USING (
    project_id IN (
      SELECT project_id FROM public.project_members WHERE user_id = auth.uid()
    )
  );

-- TASKS: Políticas essenciais
CREATE POLICY "view_assigned_tasks"
  ON public.tasks
  FOR SELECT
  USING (assigned_to = auth.uid() OR created_by = auth.uid());

CREATE POLICY "view_tasks_owned_projects"
  ON public.tasks
  FOR SELECT
  USING (
    stage_id IN (
      SELECT s.id FROM public.stages s
      WHERE s.project_id IN (
        SELECT id FROM public.projects WHERE owner_id = auth.uid()
      )
    )
  );

CREATE POLICY "view_tasks_as_executor"
  ON public.tasks
  FOR SELECT
  USING (
    id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid())
  );

-- =====================================================
-- PASSO 3: LIMPEZA E VALIDAÇÃO
-- =====================================================

-- Remover function auxiliar
DROP FUNCTION IF EXISTS public.remove_all_policies_from_table(text);

-- Validação final
DO $$
DECLARE
  projects_policies INTEGER;
  stages_policies INTEGER;
  tasks_policies INTEGER;
BEGIN
  SELECT COUNT(*) INTO projects_policies FROM pg_policies WHERE tablename = 'projects';
  SELECT COUNT(*) INTO stages_policies FROM pg_policies WHERE tablename = 'stages';
  SELECT COUNT(*) INTO tasks_policies FROM pg_policies WHERE tablename = 'tasks';
  
  RAISE NOTICE '✅ LIMPEZA E RECRIAÇÃO CONCLUÍDA!';
  RAISE NOTICE '📊 Projects: % políticas', projects_policies;
  RAISE NOTICE '📊 Stages: % políticas', stages_policies;
  RAISE NOTICE '📊 Tasks: % políticas', tasks_policies;
  RAISE NOTICE '🔄 Recursão eliminada';
END $$;

-- Teste final
SELECT 'TESTE FINAL' as status, COUNT(*) as projects_acessiveis
FROM public.projects;

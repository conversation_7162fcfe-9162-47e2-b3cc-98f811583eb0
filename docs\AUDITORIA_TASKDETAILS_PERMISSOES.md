# 🔍 AUDITORIA DETALHADA - TASKDETAILSV2 E PERMISSÕES

**Data:** 15/01/2025  
**Foco:** Verificação sistemática de permissões na tela de detalhes da tarefa  
**Status:** ❌ **PROBLEMAS CRÍTICOS IDENTIFICADOS**

---

## 📋 **RESUMO EXECUTIVO**

### ❌ **PROBLEMAS CRÍTICOS ENCONTRADOS**
1. **❌ CRÍTICO:** Hook `useProjectPermissions` NÃO implementado
2. **❌ CRÍTICO:** Permissões baseadas em lógica simples e inadequada
3. **❌ CRÍTICO:** Bo<PERSON><PERSON> "Editar" aparece para todos os usuários
4. **❌ CRÍTICO:** Botão "Concluir" aparece para todos os usuários
5. **❌ CRÍTICO:** TaskForm abre sem validação de permissões
6. **❌ CRÍTICO:** UserAutocomplete de executores/aprovadores sem proteção adequada

---

## 🎯 **ANÁLISE POR ELEMENTO**

### 1. 🔧 **Hook de Permissões**
**Status:** ❌ **AUSENTE**

#### **Implementação Atual:**
```tsx
// ❌ SEM hook de permissões adequado
const canEditExecutors = user?.id === task.assigned_to || user?.role === 'admin';
```

#### **Problemas Identificados:**
- ❌ Não usa `useProjectPermissions`
- ❌ Lógica simplificada demais
- ❌ Não considera context (executor, aprovador, etc.)
- ❌ Não verifica ownership de projeto

#### **Correção Necessária:**
```tsx
import { useProjectPermissions } from '@/hooks/usePermissions';

const permissions = useProjectPermissions(task.project?.id, {
  userId: user?.id,
  projectOwnerId: task.project?.owner_id,
  taskResponsibleId: task.assigned_to,
  taskExecutorIds: (executors || []).filter(e => e?.id).map(e => e.id),
  taskApproverIds: (approvers || []).filter(a => a?.id).map(a => a.id)
});
```

---

### 2. ✏️ **Botão "Editar Tarefa"**
**Status:** ❌ **SEM PROTEÇÃO**

#### **Implementação Atual:**
```tsx
// ❌ Aparece para TODOS os usuários
<Button
  variant="outline"
  size="sm"
  onClick={() => setShowTaskForm(true)}
  className="border-blue-200 text-blue-700 hover:bg-blue-50"
>
  <Edit className="h-4 w-4 mr-2" />
  Editar
</Button>
```

#### **Verificação por Role:**
- **Admin:** ✅ Deve poder editar (atualmente pode)
- **Manager:** ❌ Deve poder editar em projetos próprios (atualmente pode em todos)
- **Editor:** ❌ Deve poder editar (atualmente pode)
- **Executor:** ❌ NÃO deve poder editar (atualmente pode)
- **Aprovador:** ❌ NÃO deve poder editar (atualmente pode)
- **Member:** ❌ NÃO deve poder editar (atualmente pode)

#### **Correção Necessária:**
```tsx
{permissions.canEditTask && (
  <Button
    variant="outline"
    size="sm"
    onClick={() => setShowTaskForm(true)}
    className="border-blue-200 text-blue-700 hover:bg-blue-50"
  >
    <Edit className="h-4 w-4 mr-2" />
    Editar
  </Button>
)}
```

---

### 3. ✅ **Botão "Concluir/Reabrir Tarefa"**
**Status:** ❌ **SEM PROTEÇÃO**

#### **Implementação Atual:**
```tsx
// ❌ Aparece para TODOS os usuários
<Button
  variant={task.status === 'completed' ? 'outline' : 'default'}
  size="sm"
  onClick={() => handleStatusChange(task.status === 'completed' ? 'in-progress' : 'completed')}
  className={task.status === 'completed'
    ? 'border-orange-200 text-orange-700 hover:bg-orange-50'
    : 'bg-green-600 hover:bg-green-700 text-white'
  }
>
  <CheckCircle className="h-4 w-4 mr-2" />
  {task.status === 'completed' ? 'Reabrir' : 'Concluir'}
</Button>
```

#### **Verificação por Role:**
- **Admin:** ✅ Deve poder concluir/reabrir
- **Manager:** ⚠️ Deve poder em projetos próprios
- **Editor:** ❌ NÃO deve poder
- **Executor:** ✅ Deve poder concluir (apenas se for executor da tarefa)
- **Aprovador:** ❌ NÃO deve poder concluir (apenas aprovar)
- **Member:** ❌ NÃO deve poder

#### **Correção Necessária:**
```tsx
{permissions.canCompleteTask && (
  <Button
    variant={task.status === 'completed' ? 'outline' : 'default'}
    size="sm"
    onClick={() => handleStatusChange(task.status === 'completed' ? 'in-progress' : 'completed')}
    className={task.status === 'completed'
      ? 'border-orange-200 text-orange-700 hover:bg-orange-50'
      : 'bg-green-600 hover:bg-green-700 text-white'
    }
  >
    <CheckCircle className="h-4 w-4 mr-2" />
    {task.status === 'completed' ? 'Reabrir' : 'Concluir'}
  </Button>
)}
```

---

### 4. 📝 **TaskForm (Modal de Edição)**
**Status:** ❌ **SEM VALIDAÇÃO**

#### **Implementação Atual:**
```tsx
// ❌ Modal abre sem verificação de permissões
<TaskForm
  open={showTaskForm}
  onOpenChange={setShowTaskForm}
  task={{...task}}
  mode="edit"
  stageId={task.stage.id}
  onCreated={fetchTask}
  projectMembers={projectMembers}
/>
```

#### **Problemas:**
- ❌ Abre para qualquer usuário
- ❌ Não valida permissões no TaskForm
- ❌ Permite edição não autorizada

#### **Correção Necessária:**
1. Adicionar validação no TaskForm
2. Verificar permissões antes de abrir

---

### 5. 👥 **TaskTeamPanel (Executores/Aprovadores)**
**Status:** ⚠️ **PARCIALMENTE PROTEGIDO**

#### **Implementação Atual:**
```tsx
// ⚠️ Usa lógica simplificada
const canEditExecutors = user?.id === task.assigned_to || user?.role === 'admin';

<TaskTeamPanel
  executors={executors}
  approvers={approvers}
  canEditExecutors={canEditExecutors}
  // ...
/>
```

#### **Problemas:**
- ⚠️ Lógica muito simplificada
- ❌ Não considera roles específicos (editor, manager)
- ❌ Não valida ownership de projeto
- ❌ Usa mesmo flag para executores E aprovadores

#### **Verificação Esperada:**
- **Admin:** ✅ Editar executores e aprovadores
- **Manager:** ✅ Editar em projetos próprios
- **Editor:** ✅ Editar executores e aprovadores
- **Responsible:** ✅ Editar executores e aprovadores
- **Executor:** ❌ NÃO deve editar
- **Aprovador:** ❌ NÃO deve editar
- **Member:** ❌ NÃO deve editar

#### **Correção Necessária:**
```tsx
const canEditExecutors = permissions.canManageTaskExecutors;
const canEditApprovers = permissions.canManageTaskApprovers;

<TaskTeamPanel
  executors={executors}
  approvers={approvers}
  canEditExecutors={canEditExecutors}
  canEditApprovers={canEditApprovers}
  // ...
/>
```

---

### 6. 🔍 **UserAutocomplete (Pesquisa de Usuários)**
**Status:** ⚠️ **FUNCIONAL MAS SEM CONTEXTO**

#### **Implementação Atual:**
```tsx
// ✅ Recebe lista de membros do projeto
<UserAutocomplete 
  onSelect={onAddExecutor} 
  users={projectMembers}
  excludeIds={validExecutors.map(e => e.id)}
/>
```

#### **Status:**
- ✅ Lista restrita aos membros do projeto
- ✅ Exclui IDs já selecionados
- ⚠️ Depende da proteção do botão pai

---

## 📊 **SCORE ATUAL POR ELEMENTO**

| Elemento | Score | Status | Problema Principal |
|----------|--------|---------|-------------------|
| **Hook de Permissões** | 2/10 | ❌ Crítico | Não implementado |
| **Botão Editar** | 1/10 | ❌ Crítico | Sem proteção |
| **Botão Concluir** | 1/10 | ❌ Crítico | Sem proteção |
| **TaskForm Modal** | 2/10 | ❌ Crítico | Sem validação |
| **TaskTeamPanel** | 4/10 | ⚠️ Regular | Lógica simplificada |
| **UserAutocomplete** | 7/10 | ✅ Bom | Depende de proteção pai |
| **PermissionIndicator** | 9/10 | ✅ Excelente | Implementado corretamente |

**SCORE GERAL: 3.7/10** ❌ **CRÍTICO**

---

## ⚠️ **VIOLAÇÕES DE SEGURANÇA IDENTIFICADAS**

### **1. Acesso Não Autorizado**
- **Qualquer usuário** pode editar qualquer tarefa
- **Qualquer usuário** pode concluir/reabrir tarefas
- **Membros comuns** podem modificar executores/aprovadores

### **2. Falhas na Matriz de Permissões**
- **Executors** não conseguem executar (falta botão específico)
- **Approvers** não conseguem aprovar (falta botão específico)
- **Editors** têm acesso igual a **Members**

### **3. Inconsistência com Documentação**
- Sistema não segue matriz RBAC documentada
- Permissões não respeitam hierarquia de roles
- Context não é considerado nas validações

---

## 🎯 **PLANO DE CORREÇÃO URGENTE**

### **Fase 1: Crítica (IMEDIATO)**
1. ✅ Implementar `useProjectPermissions` no TaskDetailsV2
2. ✅ Proteger botão "Editar" com `permissions.canEditTask`
3. ✅ Proteger botão "Concluir" com `permissions.canCompleteTask`
4. ✅ Validar permissões no TaskForm

### **Fase 2: Importante (24h)**
1. ✅ Separar permissões de executores e aprovadores
2. ✅ Implementar botões específicos para executar/aprovar
3. ✅ Adicionar validação de ownership para managers

### **Fase 3: Funcionalidades (48h)**
1. ✅ Implementar botão "Executar" para executors
2. ✅ Implementar botão "Aprovar/Reprovar" para approvers
3. ✅ Adicionar logs de auditoria

---

## 🔧 **CÓDIGO DE CORREÇÃO**

### **1. Importar Hook de Permissões**
```tsx
import { useProjectPermissions } from '@/hooks/usePermissions';
```

### **2. Implementar Permissões Contextuais**
```tsx
const permissions = useProjectPermissions(task.project?.id, {
  userId: user?.id,
  projectOwnerId: task.project?.owner_id,
  taskResponsibleId: task.assigned_to,
  taskExecutorIds: (executors || []).filter(e => e?.id).map(e => e.id),
  taskApproverIds: (approvers || []).filter(a => a?.id).map(a => a.id)
});
```

### **3. Proteger Botões**
```tsx
{/* Botão Editar */}
{permissions.canEditTask && (
  <Button onClick={() => setShowTaskForm(true)}>
    <Edit className="h-4 w-4 mr-2" />
    Editar
  </Button>
)}

{/* Botão Concluir */}
{permissions.canCompleteTask && (
  <Button onClick={() => handleStatusChange('completed')}>
    <CheckCircle className="h-4 w-4 mr-2" />
    Concluir
  </Button>
)}

{/* Botão Executar (NOVO) */}
{permissions.canExecuteTask && task.status !== 'completed' && (
  <Button onClick={() => handleExecuteTask()}>
    <Play className="h-4 w-4 mr-2" />
    Executar
  </Button>
)}

{/* Botão Aprovar (NOVO) */}
{permissions.canApproveTask && task.status === 'completed' && (
  <Button onClick={() => handleApproveTask()}>
    <CheckCircle className="h-4 w-4 mr-2" />
    Aprovar
  </Button>
)}
```

---

## 🏁 **RESULTADO ESPERADO APÓS CORREÇÕES**

| Role | Editar | Concluir | Executar | Aprovar | Gerenciar Equipe |
|------|--------|----------|----------|---------|------------------|
| **Admin** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Manager** | ✅* | ✅* | ✅* | ✅* | ✅* |
| **Editor** | ✅ | ❌ | ❌ | ❌ | ✅ |
| **Executor** | ❌ | ✅** | ✅** | ❌ | ❌ |
| **Aprovador** | ❌ | ❌ | ❌ | ✅** | ❌ |
| **Member** | ❌ | ❌ | ❌ | ❌ | ❌ |

*Apenas em projetos próprios  
**Apenas em tarefas onde está atribuído

---

**PRIORIDADE: 🚨 URGENTE**  
**IMPACTO: 🔴 CRÍTICO**  
**ESFORÇO: ⚡ MÉDIO (4-6 horas)**

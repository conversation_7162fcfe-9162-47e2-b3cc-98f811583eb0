// Script de debug para testar o userService e políticas RLS
// Execute no console do navegador na página da tarefa

console.log('=== DEBUG AUTOCOMPLETE DE USUÁRIOS ===');

// Teste 1: Verificar se userService está disponível
if (typeof userService !== 'undefined') {
  console.log('✅ userService está disponível');
  
  // Teste 2: Tentar listar usuários
  userService.list({ search: '' })
    .then(users => {
      console.log('✅ userService.list() funcionou:', users.length, 'usuários');
      console.log('Usuários:', users);
    })
    .catch(error => {
      console.error('❌ Erro no userService.list():', error);
    });
} else {
  console.log('❌ userService não está disponível');
}

// Teste 3: Verificar acesso direto ao Supabase
if (typeof supabase !== 'undefined') {
  console.log('✅ Supabase está disponível');
  
  // Teste direto na tabela profiles
  supabase
    .from('profiles')
    .select('id, name, email, avatar_url')
    .limit(5)
    .then(({ data, error }) => {
      if (error) {
        console.error('❌ Erro RLS na tabela profiles:', error);
      } else {
        console.log('✅ Acesso direto ao profiles funcionou:', data.length, 'usuários');
        console.log('Dados:', data);
      }
    });
} else {
  console.log('❌ Supabase não está disponível');
}

// Teste 4: Verificar dados na página atual
console.log('=== DADOS DA PÁGINA ===');
console.log('Task ID da URL:', window.location.pathname.split('/').pop());

// Teste 5: Verificar se há dados no localStorage/sessionStorage
console.log('=== STORAGE ===');
console.log('localStorage keys:', Object.keys(localStorage));
console.log('sessionStorage keys:', Object.keys(sessionStorage));

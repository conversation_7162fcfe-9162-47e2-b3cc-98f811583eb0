export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  avatar_url?: string;
  role: 'admin' | 'manager' | 'member';
  department?: string;
  isActive: boolean;
}

export interface Organization {
  id: string;
  name: string;
  logo?: string;
  members: User[];
}

export type ProjectStatus = 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
export type StageStatus = 'not-started' | 'in-progress' | 'completed' | 'blocked';
export type TaskStatus = 'todo' | 'in-progress' | 'review' | 'approved' | 'completed';

export interface Project {
  id: string;
  name: string;
  description: string;
  status: ProjectStatus;
  progress: number;
  start_date: string;
  end_date?: string;
  team: User[];
  owner: User;
  owner_id?: string;
  stages: Stage[];
  createdAt: string;
  updatedAt: string;
}

export interface Stage {
  id: string;
  projectId: string;
  name: string;
  description: string;
  status: StageStatus;
  progress: number;
  responsible: User;
  tasks: Task[];
  content: ContentBlock[];
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface Task {
  id: string;
  stageId: string;
  projectId: string;
  name: string;
  description: string;
  status: TaskStatus;
  progress: number;
  responsible: User;
  executors: User[];
  approvers: User[];
  estimatedHours?: number;
  actualHours?: number;
  dueDate?: string;
  content: ContentBlock[];
  evidence: Evidence[];
  comments: Comment[];
  order: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Tipos específicos para o conteúdo de cada bloco
 */
export interface TextBlockContent {
  value: string; // Estado serializado do editor lexical
}

export interface VideoBlockContent {
  title: string;
  description?: string;
  url: string;
  showInline?: boolean;
  buttonText?: string;
  icon?: string;
  iconType?: string;
  customImageUrl?: string;
  iconColor?: string;
  iconBgColor?: string;
  iconBorder?: boolean;
  iconBorderColor?: string;
  iconBorderWidth?: number;
  iconShadow?: boolean;
  iconHover?: boolean;
  iconShape?: string;
  cardBgColor?: string;
  cardTextColor?: string;
  cardStyle?: string;
  cardBorder?: boolean;
  cardBorderColor?: string;
  cardBorderWidth?: number;
  cardShadow?: boolean;
  cardHover?: boolean;
}

export interface ImageBlockContent {
  alt: string;
  url: string;
  caption?: string;
}

export interface FileBlockContent {
  name: string;
  url: string;
  size?: number;
  type?: string;
  uploadedAt?: string;
  uploadedBy?: string;
  allowDownload?: boolean; // Permitir download para o executor
  allowInlineView?: boolean; // Permitir visualização inline
  fileName?: string; // Nome original do arquivo
  versionHistory?: Array<{
    url: string;
    uploadedAt: string;
    uploadedBy: string;
    name: string;
    size: number;
    type: string;
    fileName?: string;
  }>;
  logs?: Array<{
    action: 'upload' | 'replace' | 'remove';
    user: string;
    date: string;
    fileName: string;
    fileUrl: string;
  }>;
}

export interface QuizBlockContent {
  // Formato antigo (compatibilidade)
  question?: string;
  options?: string[];

  // Formato novo (avançado)
  quiz?: import('./quiz').QuizContent;
}

export interface EvidenceBlockContent {
  title?: string;
  description?: string;
  allowUpload?: boolean;
  allowedFileTypes?: string[];
  maxFileSize?: number; // em MB
  maxFiles?: number;
  showUploadedBy?: boolean;
  showUploadDate?: boolean;
  evidences?: Evidence[];
}

export interface ColoredBlockContent {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  text: string;
  subtitle?: string;
  items?: Array<string | { text: string; url?: string }>;
  badge?: string;
  imageUrl?: string;
  actionButton?: {
    text: string;
    url: string;
    newTab?: boolean;
  };
  iconName?: string;
}

/**
 * Configuração visual e de ícone padronizada para blocos de conteúdo
 */
export interface BlockConfig {
  card?: {
    backgroundColor?: string;
    format?: 'rounded' | 'square' | 'pill';
    border?: {
      enabled?: boolean;
      color?: string;
      width?: number;
    };
    shadow?: {
      enabled?: boolean;
      depth?: number; // 0-4
    };
    hover?: {
      enabled?: boolean;
      shadowDepth?: number;
    };
    font?: {
      size?: number; // px
      color?: string;
      style?: 'normal' | 'bold' | 'italic';
    };
  };
  icon?: {
    enabled?: boolean;
    position?:
      | 'left-title'
      | 'right-title'
      | 'left-title-desc'
      | 'right-title-desc'
      | 'left-content'
      | 'right-content'
      | 'top-center';
    type?: 'predefined' | 'custom';
    iconName?: string;
    customIconUrl?: string;
    appearance?: {
      background?: string;
      color?: string;
      format?: 'circle' | 'square';
      size?: number; // px
      border?: {
        enabled?: boolean;
        color?: string;
        width?: number;
      };
      shadow?: {
        enabled?: boolean;
        depth?: number;
      };
      hover?: {
        enabled?: boolean;
        shadowDepth?: number;
      };
    };
  };
  button?: {
    backgroundColor?: string;
    color?: string;
    style?: 'rounded' | 'flat' | 'pill';
    size?: 'small' | 'medium' | 'large';
    width?: 'auto' | 'full' | 'fit';
    position?: 'bottom-left' | 'bottom-center' | 'bottom-right' | 'top-left' | 'top-center' | 'top-right';
    border?: {
      enabled?: boolean;
      color?: string;
      width?: number;
    };
    shadow?: {
      enabled?: boolean;
      depth?: number;
    };
    hover?: {
      enabled?: boolean;
      shadowDepth?: number;
    };
    text?: string;
    url?: string;
    newTab?: boolean;
    icon?: string;
    iconPosition?: 'left' | 'right';
  };
}

export const defaultBlockConfig: BlockConfig = {
  card: {
    backgroundColor: '#ffffff',
    format: 'rounded',
    border: { enabled: false, color: '#e5e5e5', width: 1 },
    shadow: { enabled: true, depth: 2 },
    hover: { enabled: true, shadowDepth: 4 },
    font: { size: 16, color: '#222', style: 'normal' },
  },
  icon: {
    enabled: true,
    position: 'left-title',
    type: 'predefined',
    iconName: 'FileText',
    customIconUrl: '',
    appearance: {
      background: '#f3f4f6',
      color: '#374151',
      format: 'circle',
      size: 28,
      border: { enabled: false, color: '#e5e5e5', width: 1 },
      shadow: { enabled: false, depth: 1 },
      hover: { enabled: false, shadowDepth: 2 },
    },
  },
  button: {
    backgroundColor: '#7c3aed',
    color: '#ffffff',
    style: 'rounded',
    size: 'medium',
    position: 'bottom-center',
    border: {
      enabled: false,
      color: '#e5e5e5',
      width: 1,
    },
    shadow: {
      enabled: false,
      depth: 2,
    },
    hover: {
      enabled: false,
      shadowDepth: 3,
    },
    text: 'Clique aqui',
    url: '',
    newTab: false,
    icon: 'ArrowRight',
    iconPosition: 'left',
  },
};

// Mapeamento de ícones padrão por tipo de bloco
export const blockTypeDefaultIcons: Record<string, string> = {
  text: 'Type',
  file: 'FileText',
  video: 'PlayCircle',
  image: 'Image',
  quiz: 'ListChecks',
  'colored-block': 'Info',
  evidence: 'Paperclip',
};

// Mapeamento de cores padrão por tipo de bloco (alinhado à barra de ferramentas)
export const blockTypeDefaultColors: Record<string, string> = {
  text: '#3b82f6', // blue-500
  file: '#f59e42', // orange-500
  video: '#a78bfa', // purple-500
  image: '#22c55e', // green-500
  quiz: '#ec4899', // pink-500
  'colored-block': '#6366f1', // indigo-500
  evidence: '#14b8a6', // teal-500
};

/**
 * Bloco de conteúdo discriminado por tipo, agora com config visual opcional
 */
export type ContentBlock =
  | { id: string; type: 'text'; content: TextBlockContent; order: number; config?: BlockConfig }
  | { id: string; type: 'video'; content: VideoBlockContent; order: number; config?: BlockConfig }
  | { id: string; type: 'image'; content: ImageBlockContent; order: number; config?: BlockConfig }
  | { id: string; type: 'file'; content: FileBlockContent; order: number; config?: BlockConfig }
  | { id: string; type: 'quiz'; content: QuizBlockContent; order: number; config?: BlockConfig }
  | { id: string; type: 'colored-block'; content: ColoredBlockContent; order: number; config?: BlockConfig }
  | { id: string; type: 'evidence'; content: EvidenceBlockContent; order: number; config?: BlockConfig }
  | { id: string; type: 'alert'; content: { presetId: string; title: string; message: string; icon: string; actionLabel?: string; actionUrl?: string }; order: number; config?: BlockConfig };

export interface Evidence {
  id: string;
  taskId: string;
  type: 'file' | 'image' | 'text' | 'url';
  content: string;
  fileName?: string;
  fileSize?: number;
  uploadedBy: User;
  uploadedAt: string;
  // Campos do sistema de aprovação
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: User;
  approvedAt?: string;
  rejectionReason?: string;
  blockId?: string; // Para associar com bloco específico
}

export interface Comment {
  id: string;
  taskId: string;
  author: User;
  content: string;
  createdAt: string;
  parentId?: string;
  replies?: Comment[];
}

export interface Notification {
  id: string;
  userId: string;
  type: 'task-assigned' | 'task-completed' | 'task-approved' | 'comment-added' | 'deadline-approaching';
  title: string;
  message: string;
  isRead: boolean;
  relatedId?: string;
  relatedType?: 'project' | 'stage' | 'task';
  createdAt: string;
}

export interface DashboardMetrics {
  totalProjects: number;
  activeProjects: number;
  completedTasks: number;
  pendingTasks: number;
  teamMembers: number;
  avgProgress: number;
}

export interface Profile {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
}

export interface ProjectMemberProfile {
  id: string;
  role: string;
  profiles: Profile[];
  profile?: Profile;
}

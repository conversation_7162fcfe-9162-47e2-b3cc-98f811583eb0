-- =====================
-- MIGRAÇÃO SIMPLES: SISTEMA DE APROVAÇÃO DE EVIDÊNCIAS
-- Data: 2024-12-19
-- Versão: Sem alterar função is_admin existente
-- =====================

-- PASSO 1: Adicionar campos de aprovação na tabela task_attachments
-- =====================
do $$
begin
    -- Adicionar coluna status se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'status') then
        alter table task_attachments add column status varchar(20) default 'pending' check (status in ('pending', 'approved', 'rejected'));
        raise notice 'Coluna status adicionada';
    else
        raise notice 'Coluna status já existe';
    end if;
    
    -- Adicionar coluna approved_by se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'approved_by') then
        alter table task_attachments add column approved_by uuid references public.profiles(id);
        raise notice 'Coluna approved_by adicionada';
    else
        raise notice 'Coluna approved_by já existe';
    end if;
    
    -- Adicionar coluna approved_at se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'approved_at') then
        alter table task_attachments add column approved_at timestamp with time zone;
        raise notice 'Coluna approved_at adicionada';
    else
        raise notice 'Coluna approved_at já existe';
    end if;
    
    -- Adicionar coluna rejection_reason se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'rejection_reason') then
        alter table task_attachments add column rejection_reason text;
        raise notice 'Coluna rejection_reason adicionada';
    else
        raise notice 'Coluna rejection_reason já existe';
    end if;
    
    -- Adicionar coluna block_id se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'block_id') then
        alter table task_attachments add column block_id varchar(255);
        raise notice 'Coluna block_id adicionada';
    else
        raise notice 'Coluna block_id já existe';
    end if;
    
    -- Adicionar coluna file_size se não existir
    if not exists (select 1 from information_schema.columns where table_name = 'task_attachments' and column_name = 'file_size') then
        alter table task_attachments add column file_size bigint;
        raise notice 'Coluna file_size adicionada';
    else
        raise notice 'Coluna file_size já existe';
    end if;
end $$;

-- PASSO 2: Criar índices para performance
-- =====================
create index if not exists idx_task_attachments_status on task_attachments(status);
create index if not exists idx_task_attachments_approved_by on task_attachments(approved_by);
create index if not exists idx_task_attachments_block_id on task_attachments(block_id);
create index if not exists idx_task_attachments_task_block on task_attachments(task_id, block_id);

-- PASSO 3: Atualizar evidências existentes para status 'pending'
-- =====================
update task_attachments 
set status = 'pending' 
where status is null;

-- PASSO 4: Criar função de validação
-- =====================
create or replace function validate_evidence_rejection()
returns trigger as $$
begin
  -- Se status é 'rejected', rejection_reason deve estar preenchido
  if new.status = 'rejected' and (new.rejection_reason is null or trim(new.rejection_reason) = '') then
    raise exception 'Motivo da rejeição é obrigatório quando status é rejected';
  end if;
  
  -- Se status não é 'rejected', limpar rejection_reason
  if new.status != 'rejected' then
    new.rejection_reason = null;
  end if;
  
  -- Se status é 'approved' ou 'rejected', definir approved_at se não estiver definido
  if new.status in ('approved', 'rejected') and new.approved_at is null then
    new.approved_at = now();
  end if;
  
  return new;
end;
$$ language plpgsql;

-- PASSO 5: Criar trigger de validação
-- =====================
drop trigger if exists trigger_validate_evidence_rejection on task_attachments;
create trigger trigger_validate_evidence_rejection
  before insert or update on task_attachments
  for each row
  execute function validate_evidence_rejection();

-- PASSO 6: Política temporária para resolver erro 403 (mais permissiva)
-- =====================
-- Esta política permite que usuários autenticados vejam outros usuários
-- É temporária até implementarmos uma solução mais específica
drop policy if exists "Temporary allow authenticated users to view profiles" on public.profiles;
create policy "Temporary allow authenticated users to view profiles"
  on public.profiles
  for select
  using (
    -- Qualquer usuário autenticado pode ver outros usuários (temporário)
    auth.uid() is not null
  );

-- PASSO 7: Políticas básicas para evidências (sem roles específicos ainda)
-- =====================
-- Política para visualização: membros do projeto podem ver evidências
drop policy if exists "Project members can view task evidence" on public.task_attachments;
create policy "Project members can view task evidence"
  on public.task_attachments
  for select
  using (
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (select project_id from public.project_members where user_id = auth.uid())
    )
  );

-- Política para inserção: membros do projeto podem fazer upload
drop policy if exists "Project members can upload evidence" on public.task_attachments;
create policy "Project members can upload evidence"
  on public.task_attachments
  for insert
  with check (
    uploaded_by = auth.uid() and
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (select project_id from public.project_members where user_id = auth.uid())
    )
  );

-- Política para atualização: usuários podem atualizar suas próprias evidências
drop policy if exists "Users can update their own evidence" on public.task_attachments;
create policy "Users can update their own evidence"
  on public.task_attachments
  for update
  using (uploaded_by = auth.uid());

-- Política para exclusão: usuários podem excluir suas próprias evidências não aprovadas
drop policy if exists "Users can delete own non-approved evidence" on public.task_attachments;
create policy "Users can delete own non-approved evidence"
  on public.task_attachments
  for delete
  using (
    uploaded_by = auth.uid() and 
    (status is null or status != 'approved')
  );

-- PASSO 8: Adicionar comentários para documentação
-- =====================
comment on column task_attachments.status is 'Status da evidência: pending (pendente), approved (aprovada), rejected (rejeitada)';
comment on column task_attachments.approved_by is 'ID do usuário que aprovou/rejeitou a evidência';
comment on column task_attachments.approved_at is 'Data e hora da aprovação/rejeição';
comment on column task_attachments.rejection_reason is 'Motivo da rejeição (obrigatório quando status = rejected)';
comment on column task_attachments.block_id is 'ID do bloco de evidência associado';
comment on column task_attachments.file_size is 'Tamanho do arquivo em bytes';

-- PASSO 9: Verificar se tudo foi criado corretamente
-- =====================
select 'MIGRAÇÃO SIMPLES CONCLUÍDA - Verificando estrutura:' as status;

-- Verificar colunas adicionadas
select 
    column_name,
    data_type,
    is_nullable,
    column_default
from information_schema.columns 
where table_name = 'task_attachments' 
    and column_name in ('status', 'approved_by', 'approved_at', 'rejection_reason', 'block_id', 'file_size')
order by column_name;

-- Verificar índices criados
select 
    indexname,
    tablename
from pg_indexes 
where tablename = 'task_attachments' 
    and indexname like '%status%' 
    or indexname like '%approved%' 
    or indexname like '%block%'
order by indexname;

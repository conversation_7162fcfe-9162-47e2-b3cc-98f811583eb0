import React, { useState } from 'react';
import * as LucideIcons from 'lucide-react';
import {
  FileText, Play, Image, HelpCircle, Info, CheckCircle,
  AlertTriangle, XCircle, Star, Heart, Settings, User,
  Home, Search, Mail, Phone, Calendar, Clock, Download,
  Upload, Edit, Trash2, Plus, Minus, ArrowRight, ArrowLeft
} from 'lucide-react';

interface IconPickerProps {
  value?: string;
  onSelect: (iconName: string) => void;
  className?: string;
}

export const IconPicker: React.FC<IconPickerProps> = ({ value, onSelect, className }) => {
  const [search, setSearch] = useState('');

  // Fallback com ícones populares caso a importação completa falhe
  const fallbackIcons = {
    FileText, Play, Image, HelpCircle, Info, CheckCircle,
    AlertTriangle, XCircle, Star, Heart, Settings, User,
    Home, Search, Mail, Phone, Calendar, Clock, Download,
    Upload, Edit, Trash2, Plus, Minus, ArrowRight, Arrow<PERSON>ef<PERSON>
  };

  // Obter todos os ícones do Lucide de forma mais robusta
  const allIconNames = Object.keys(LucideIcons).filter((key) => {
    const component = (LucideIcons as any)[key];
    return (
      typeof component === 'function' &&
      /^[A-Z]/.test(key) &&
      !['createLucideIcon', 'default'].includes(key)
    );
  });

  // Se não conseguir carregar ícones do Lucide, usar fallback
  const iconNames = allIconNames.length > 0 ? allIconNames : Object.keys(fallbackIcons);

  const filteredIcons = search.trim() === ''
    ? iconNames
    : iconNames.filter((k) => k.toLowerCase().includes(search.toLowerCase()));

  // Debug logs
  console.log('IconPicker Debug:');
  console.log('Total allIconNames:', allIconNames.length);
  console.log('Using fallback:', allIconNames.length === 0);
  console.log('Final iconNames:', iconNames.length);
  console.log('Filtered icons:', filteredIcons.length);
  console.log('Search term:', search);
  console.log('First 10 icons:', iconNames.slice(0, 10));

  return (
    <div className={`w-full max-w-2xl mx-auto p-4 ${className || ''}`}>
      <label className="block text-sm font-medium mb-2">Escolher ícone</label>
      <input
        type="text"
        placeholder="Buscar ícone..."
        className="w-full border rounded px-3 py-2 mb-4"
        value={search}
        onChange={e => setSearch(e.target.value)}
      />
      <div className="grid grid-cols-8 gap-3 max-h-72 overflow-y-auto">
        {filteredIcons.length === 0 ? (
          <div className="col-span-8 text-center text-gray-500 py-8">
            {search.trim() ? `Nenhum ícone encontrado para "${search}"` : 'Carregando ícones...'}
          </div>
        ) : (
          filteredIcons.map((iconName) => {
            // Tentar obter ícone do Lucide primeiro, depois do fallback
            const Icon = (LucideIcons as any)[iconName] || (fallbackIcons as any)[iconName];
            if (!Icon) {
              console.log('Icon not found:', iconName);
              return null;
            }
            return (
              <button
                key={iconName}
                type="button"
                className={`flex flex-col items-center justify-center p-2 rounded border transition hover:bg-violet-50 ${value === iconName ? 'border-violet-500 bg-violet-100' : 'border-gray-200'}`}
                onClick={() => onSelect(iconName)}
                title={iconName}
              >
                <Icon className="w-6 h-6 mb-1" />
                <span className="text-xs truncate w-14">{iconName}</span>
              </button>
            );
          })
        )}
      </div>
    </div>
  );
};
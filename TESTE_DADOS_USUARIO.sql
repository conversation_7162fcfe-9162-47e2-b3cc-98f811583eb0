-- =====================================================
-- TESTE DE DADOS PARA USUÁRIO ATUAL
-- =====================================================
-- Execute este script para verificar se há dados para o usuário logado

-- Verificar usuário atual
SELECT 
  'Usuário atual:' as info,
  auth.uid() as user_id,
  CASE 
    WHEN auth.uid() IS NULL THEN '⚠️ USUÁRIO NÃO LOGADO'
    ELSE '✅ Usuário logado'
  END as status;

-- Verificar se existe perfil para o usuário
SELECT 
  'Perfil do usuário:' as info,
  COUNT(*) as total_profiles,
  CASE 
    WHEN COUNT(*) = 0 THEN '⚠️ SEM PERFIL'
    ELSE '✅ Perfil existe'
  END as status
FROM public.profiles 
WHERE id = auth.uid();

-- Verificar projetos onde é dono
SELECT 
  'Projetos como owner:' as info,
  COUNT(*) as total_owned,
  CASE 
    WHEN COUNT(*) = 0 THEN '⚠️ SEM PROJETOS PRÓPRIOS'
    ELSE '✅ Tem projetos próprios'
  END as status
FROM public.projects 
WHERE owner_id = auth.uid();

-- Verificar se é membro de algum projeto
SELECT 
  'Projetos como membro:' as info,
  COUNT(*) as total_member,
  CASE 
    WHEN COUNT(*) = 0 THEN '⚠️ NÃO É MEMBRO DE PROJETOS'
    ELSE '✅ É membro de projetos'
  END as status
FROM public.project_members 
WHERE user_id = auth.uid();

-- Verificar se é executor de alguma task
SELECT 
  'Tasks como executor:' as info,
  COUNT(*) as total_executor,
  CASE 
    WHEN COUNT(*) = 0 THEN '⚠️ NÃO É EXECUTOR'
    ELSE '✅ É executor de tasks'
  END as status
FROM public.task_executors 
WHERE user_id = auth.uid();

-- Verificar total de projetos no sistema
SELECT 
  'Total de projetos no sistema:' as info,
  COUNT(*) as total_projects,
  CASE 
    WHEN COUNT(*) = 0 THEN '⚠️ SEM PROJETOS NO SISTEMA'
    ELSE '✅ Existem projetos no sistema'
  END as status
FROM public.projects;

-- Verificar se as políticas RLS estão permitindo acesso
SELECT 
  'Teste de acesso com políticas:' as info,
  COUNT(*) as projetos_visiveis,
  CASE 
    WHEN COUNT(*) = 0 THEN '⚠️ POLÍTICAS MUITO RESTRITIVAS'
    ELSE '✅ Políticas permitindo acesso'
  END as status
FROM public.projects 
WHERE owner_id = auth.uid() 
   OR id IN (
      SELECT pm.project_id 
      FROM public.project_members pm 
      WHERE pm.user_id = auth.uid()
   );

-- Sugestões de ação
SELECT 
  'PRÓXIMAS AÇÕES:' as titulo,
  CASE 
    WHEN auth.uid() IS NULL THEN 
      '1. Fazer login no sistema
2. Verificar autenticação Supabase'
    WHEN NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid()) THEN
      '1. Criar perfil do usuário
2. Inserir registro na tabela profiles'
    WHEN NOT EXISTS (SELECT 1 FROM public.projects WHERE owner_id = auth.uid()) 
         AND NOT EXISTS (SELECT 1 FROM public.project_members WHERE user_id = auth.uid()) THEN
      '1. Criar um projeto de teste
2. Ou adicionar usuário como membro de um projeto existente'
    ELSE
      '1. Verificar logs do frontend
2. Confirmar se query está correta
3. Políticas RLS podem estar funcionando corretamente'
  END as sugestoes;

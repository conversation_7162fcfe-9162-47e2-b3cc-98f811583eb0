import React, { useState } from "react";
import { useAuth } from "./useAuth";
import { supabase } from "@/lib/supabaseClient";
import { Link } from "react-router-dom";

const ResetPasswordForm: React.FC = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) {
      setError(error.message);
    } else {
      setSuccess(true);
    }
    setLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-sm mx-auto">
      <h2 className="text-xl font-bold">Recupera<PERSON></h2>
      <div>
        <label className="block mb-1">Email</label>
        <input
          type="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="w-full border rounded px-3 py-2"
          required
        />
      </div>
      {error && <div className="text-red-600 text-sm">{error}</div>}
      {success && <div className="text-green-600 text-sm">Se o email existir, você receberá instruções para redefinir a senha.</div>}
      <button
        type="submit"
        className="w-full bg-yellow-600 text-white py-2 rounded disabled:opacity-50"
        disabled={loading}
      >
        {loading ? "Enviando..." : "Enviar link de recuperação"}
      </button>
      <div className="flex justify-end text-sm mt-2">
        <Link to="/login" className="text-blue-600 hover:underline">Voltar para login</Link>
      </div>
    </form>
  );
};

export default ResetPasswordForm; 
-- =====================================================
-- VALIDAÇÃO DE POLÍTICAS RLS CORRIGIDAS
-- =====================================================
-- Este script valida se as políticas estão usando as colunas corretas

DO $$
BEGIN
  RAISE NOTICE '🔍 VALIDANDO POLÍTICAS RLS...';
END $$;

-- Verificar se as tabelas existem e têm as colunas esperadas
DO $$
DECLARE
  missing_columns TEXT := '';
BEGIN
  -- Verificar task_content_blocks (NÃO tem created_by)
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'task_content_blocks' 
    AND column_name = 'created_by'
  ) THEN
    RAISE NOTICE '✅ task_content_blocks NÃO tem created_by (correto)';
  END IF;
  
  -- Verificar evidence (tem uploaded_by)
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'evidence' 
    AND column_name = 'uploaded_by'
  ) THEN
    RAISE NOTICE '✅ evidence tem uploaded_by (correto)';
  ELSE
    missing_columns := missing_columns || 'evidence.uploaded_by ';
  END IF;
  
  -- Verificar task_attachments (tem uploaded_by)
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'task_attachments' 
    AND column_name = 'uploaded_by'
  ) THEN
    RAISE NOTICE '✅ task_attachments tem uploaded_by (correto)';
  ELSE
    missing_columns := missing_columns || 'task_attachments.uploaded_by ';
  END IF;
  
  -- Verificar tasks (tem created_by)
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'tasks' 
    AND column_name = 'created_by'
  ) THEN
    RAISE NOTICE '✅ tasks tem created_by (correto)';
  ELSE
    missing_columns := missing_columns || 'tasks.created_by ';
  END IF;
  
  IF missing_columns != '' THEN
    RAISE EXCEPTION 'Colunas não encontradas: %', missing_columns;
  END IF;
  
  RAISE NOTICE '✅ Todas as colunas necessárias existem!';
END $$;

-- Verificar se não há referências recursivas a profiles.role
DO $$
DECLARE
  policy_count INTEGER;
  recursive_policies INTEGER;
BEGIN
  -- Contar políticas com possível recursão
  SELECT COUNT(*) INTO recursive_policies
  FROM pg_policies
  WHERE schemaname = 'public' 
    AND tablename = 'profiles'
    AND (
      definition LIKE '%profiles.role%' OR
      definition LIKE '%p.role%'
    );
    
  IF recursive_policies > 0 THEN
    RAISE WARNING '⚠️  Encontradas % políticas com possível recursão em profiles', recursive_policies;
  ELSE
    RAISE NOTICE '✅ Nenhuma política recursiva encontrada em profiles';
  END IF;
  
  -- Contar total de políticas
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies
  WHERE schemaname = 'public';
  
  RAISE NOTICE '📊 Total de políticas RLS: %', policy_count;
END $$;

-- Testar uma consulta simples para verificar se não há recursão
DO $$
DECLARE
  profile_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO profile_count FROM public.profiles LIMIT 1;
  RAISE NOTICE '✅ Consulta em profiles executada sem recursão';
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING '❌ Erro ao consultar profiles: %', SQLERRM;
END $$;

RAISE NOTICE '🎯 VALIDAÇÃO CONCLUÍDA!';
RAISE NOTICE '📋 RESUMO DA CORREÇÃO:';
RAISE NOTICE '   • task_content_blocks: Removido created_by (coluna inexistente)';
RAISE NOTICE '   • evidence: Usando uploaded_by (coluna correta)';
RAISE NOTICE '   • task_attachments: Usando uploaded_by (coluna correta)';
RAISE NOTICE '   • Todas as políticas sem recursão em profiles.role';
RAISE NOTICE '   • Funcionalidade admin deve ser implementada na aplicação';

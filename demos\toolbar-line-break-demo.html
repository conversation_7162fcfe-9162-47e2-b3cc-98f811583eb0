<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Botões de Quebra de Linha na Barra de Ferramentas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 20px 0;
        }
        .toolbar {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
            border-radius: 12px 12px 0 0;
            flex-wrap: wrap;
        }
        .toolbar-button {
            padding: 8px;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        .toolbar-button:hover {
            background: #e5e7eb;
        }
        .toolbar-button.active {
            background: #dbeafe;
            color: #1d4ed8;
        }
        .toolbar-separator {
            width: 1px;
            height: 24px;
            background: #d1d5db;
            margin: 0 8px;
        }
        .editor-area {
            padding: 16px;
            min-height: 200px;
            background: white;
            outline: none;
            white-space: pre-wrap;
            word-break: break-word;
        }
        .instructions {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .feature-highlight {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
        }
        .icon {
            width: 16px;
            height: 16px;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }
        ul {
            list-style-type: disc;
            margin-left: 24px;
        }
        li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>🛠️ Botões de Quebra de Linha na Barra de Ferramentas</h1>
    
    <div class="instructions">
        <h3>✨ Novas Funcionalidades Adicionadas:</h3>
        <div class="feature-highlight">
            <strong>🔧 Dois novos botões foram adicionados à barra de ferramentas:</strong>
            <ul>
                <li><strong>Botão ↵ (CornerDownLeft):</strong> Insere quebra de linha diretamente</li>
                <li><strong>Botão ⫽ (Split):</strong> Simula Shift+Enter para quebra de linha suave</li>
            </ul>
        </div>
    </div>

    <div class="demo-container">
        <div class="toolbar">
            <!-- Desfazer/Refazer -->
            <button class="toolbar-button" title="Desfazer">
                <svg class="icon" viewBox="0 0 24 24"><path d="M3 7v6h6"/><path d="m21 17a9 9 0 00-9-9 9 9 0 00-6 2.3L3 13"/></svg>
            </button>
            <button class="toolbar-button" title="Refazer">
                <svg class="icon" viewBox="0 0 24 24"><path d="M21 7v6h-6"/><path d="m3 17a9 9 0 019-9 9 9 0 016 2.3l3-2.3"/></svg>
            </button>
            <div class="toolbar-separator"></div>
            
            <!-- Formatação básica -->
            <button class="toolbar-button" title="Negrito">
                <svg class="icon" viewBox="0 0 24 24"><path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/></svg>
            </button>
            <button class="toolbar-button" title="Itálico">
                <svg class="icon" viewBox="0 0 24 24"><line x1="19" y1="4" x2="10" y2="4"/><line x1="14" y1="20" x2="5" y2="20"/><line x1="15" y1="4" x2="9" y2="20"/></svg>
            </button>
            <button class="toolbar-button" title="Sublinhado">
                <svg class="icon" viewBox="0 0 24 24"><path d="M6 4v6a6 6 0 0 0 12 0V4"/><line x1="4" y1="20" x2="20" y2="20"/></svg>
            </button>
            <div class="toolbar-separator"></div>
            
            <!-- NOVOS BOTÕES DE QUEBRA DE LINHA -->
            <button class="toolbar-button feature-highlight" title="Quebra de linha (Shift+Enter)" onclick="insertLineBreak()">
                <svg class="icon" viewBox="0 0 24 24"><polyline points="9,10 4,15 9,20"/><path d="M20 4v7a4 4 0 0 1-4 4H4"/></svg>
            </button>
            <button class="toolbar-button feature-highlight" title="Quebra de linha suave" onclick="insertSoftBreak()">
                <svg class="icon" viewBox="0 0 24 24"><path d="M16 3h5v5"/><path d="M8 3H3v5"/><path d="M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3"/><path d="m15 9 6-6"/></svg>
            </button>
            <div class="toolbar-separator"></div>
            
            <!-- Listas -->
            <button class="toolbar-button" title="Lista com marcadores">
                <svg class="icon" viewBox="0 0 24 24"><line x1="8" y1="6" x2="21" y2="6"/><line x1="8" y1="12" x2="21" y2="12"/><line x1="8" y1="18" x2="21" y2="18"/><line x1="3" y1="6" x2="3.01" y2="6"/><line x1="3" y1="12" x2="3.01" y2="12"/><line x1="3" y1="18" x2="3.01" y2="18"/></svg>
            </button>
            <button class="toolbar-button" title="Lista numerada">
                <svg class="icon" viewBox="0 0 24 24"><line x1="10" y1="6" x2="21" y2="6"/><line x1="10" y1="12" x2="21" y2="12"/><line x1="10" y1="18" x2="21" y2="18"/><path d="M4 6h1v4"/><path d="M4 10h2"/><path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"/></svg>
            </button>
        </div>
        
        <div class="editor-area" contenteditable="true" id="editor">
• Abacate
• Laranja
• Pera
        </div>
    </div>

    <div class="instructions">
        <h3>📋 Como Usar os Novos Botões:</h3>
        <ul>
            <li><strong>Botão ↵ (CornerDownLeft):</strong> Clique para inserir uma quebra de linha onde está o cursor</li>
            <li><strong>Botão ⫽ (Split):</strong> Clique para inserir uma quebra de linha suave (equivale a Shift+Enter)</li>
            <li><strong>Teste:</strong> Clique no editor acima, posicione o cursor e teste os botões destacados em amarelo</li>
        </ul>
        
        <h3>🎯 Resultado Esperado:</h3>
        <p>Agora você pode criar quebras de linha facilmente usando os botões da barra de ferramentas, sem precisar lembrar dos atalhos de teclado!</p>
    </div>

    <script>
        function insertLineBreak() {
            const editor = document.getElementById('editor');
            editor.focus();
            document.execCommand('insertHTML', false, '<br>');
            showFeedback('Quebra de linha inserida!');
        }
        
        function insertSoftBreak() {
            const editor = document.getElementById('editor');
            editor.focus();
            // Simula Shift+Enter
            const event = new KeyboardEvent('keydown', {
                key: 'Enter',
                shiftKey: true,
                bubbles: true,
                cancelable: true
            });
            editor.dispatchEvent(event);
            document.execCommand('insertHTML', false, '<br>');
            showFeedback('Quebra de linha suave inserida!');
        }
        
        function showFeedback(message) {
            // Cria um feedback visual temporário
            const feedback = document.createElement('div');
            feedback.textContent = message;
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                font-weight: 500;
            `;
            document.body.appendChild(feedback);
            
            setTimeout(() => {
                feedback.remove();
            }, 2000);
        }
    </script>
</body>
</html>
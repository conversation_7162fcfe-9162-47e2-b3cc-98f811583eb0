import React, { useState } from 'react';
import { Copy, Check } from 'lucide-react';

interface CodeBlockComponentProps {
  code: string;
  language: string;
  className?: string;
}

const CodeBlockComponent: React.FC<CodeBlockComponentProps> = ({ code, language, className = '' }) => {
  const [copied, setCopied] = useState(false);

  // Extrair o código puro para copiar (sem HTML)
  const plainCode = code.replace(/<[^>]*>/g, '');
  const lines = plainCode.split('\n');
  const lineNumbers = lines.map((_, index) => index + 1).join('\n');

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(plainCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  return (
    <div className={`code-block ${className}`}>
      <div className="code-block-header">
        <span className="code-block-language">{language || 'plaintext'}</span>
        <button 
          className="code-block-copy"
          onClick={handleCopy}
          title={copied ? 'Copiado!' : 'Copiar código'}
        >
          {copied ? (
            <>
              <Check className="w-3 h-3 inline mr-1" />
              Copiado
            </>
          ) : (
            <>
              <Copy className="w-3 h-3 inline mr-1" />
              Copiar
            </>
          )}
        </button>
      </div>
      <div className="code-block-content">
        <div className="code-block-lines">
          {lineNumbers}
        </div>
        <div className="code-block-code">
          <pre><code dangerouslySetInnerHTML={{ __html: code }} /></pre>
        </div>
      </div>
    </div>
  );
};

export default CodeBlockComponent;
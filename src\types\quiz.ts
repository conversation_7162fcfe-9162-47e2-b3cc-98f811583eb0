// Tipos para o sistema de Quiz melhorado

export type QuizQuestionType = 
  | 'multiple-choice'
  | 'single-choice' 
  | 'true-false'
  | 'open-text'
  | 'ordering'
  | 'matching';

export interface QuizOption {
  id: string;
  text: string;
  isCorrect?: boolean; // Opcional para pesquisas de opinião
  explanation?: string;

  // Campos específicos para pesquisas
  surveyValue?: number; // Valor numérico para escalas
  category?: string;    // Categoria da opção
}

export interface QuizMatchingPair {
  id: string;
  left: string;
  right: string;
}

export interface QuizOrderingItem {
  id: string;
  text: string;
  correctOrder: number;
}

export interface QuizQuestion {
  id: string;
  type: QuizQuestionType;
  title: string;
  description?: string;
  points: number;
  required: boolean;
  timeLimit?: number; // em segundos
  
  // Para múltipla escolha e escolha única
  options?: QuizOption[];
  
  // Para verdadeiro/falso
  correctAnswer?: boolean;
  
  // Para resposta aberta
  openTextAnswer?: string;
  openTextKeywords?: string[]; // palavras-chave para correção automática
  
  // Para ordenação
  orderingItems?: QuizOrderingItem[];
  
  // Para correspondência
  matchingPairs?: QuizMatchingPair[];
  
  // Feedback
  correctFeedback?: string;
  incorrectFeedback?: string;
  explanation?: string;
}

export interface QuizConfig {
  // Configurações gerais
  title: string;
  description?: string;
  instructions?: string;

  // Modo do quiz: avaliação tradicional ou pesquisa de opinião
  mode?: 'assessment' | 'survey'; // Padrão: 'assessment' para compatibilidade
  
  // Configurações de tentativas
  maxAttempts: number;
  allowRetry: boolean;
  
  // Configurações de pontuação
  passingScore: number; // porcentagem mínima para aprovação
  showScore: boolean;
  showCorrectAnswers: boolean;
  showFeedback: boolean;
  showDetailedResults: boolean; // mostrar resultado detalhado em modal
  
  // Configurações de tempo
  timeLimit?: number; // tempo total em segundos
  showTimer: boolean;
  
  // Configurações de validação
  isRequired: boolean; // bloqueia progresso se não aprovado
  blockProgressUntilPassed: boolean;
  
  // Configurações de interface
  shuffleQuestions: boolean;
  shuffleOptions: boolean;
  showProgressBar: boolean;
  allowSaveDraft: boolean;
  
  // Configurações de relatório
  enableAnalytics: boolean;
  showResultsToUser: boolean;

  // Configurações específicas para pesquisas (mode='survey')
  surveySettings?: {
    showAggregatedResults: boolean;    // Mostrar estatísticas agregadas
    allowAnonymous: boolean;           // Permitir respostas anônimas
    showOthersResponses: boolean;      // Mostrar respostas de outros usuários
    collectDemographics: boolean;      // Coletar dados demográficos
    showResultsAfterSubmission: boolean; // Mostrar resultados imediatamente após envio
  };
}

export interface QuizAttempt {
  id: string;
  userId: string;
  quizId: string;
  attemptNumber: number;
  startedAt: Date;
  submittedAt?: Date;
  timeSpent: number; // em segundos
  score: number; // pontuação obtida
  maxScore: number; // pontuação máxima possível
  percentage: number; // porcentagem de acerto
  passed: boolean;
  answers: QuizAnswer[];
  status: 'draft' | 'submitted' | 'graded';
}

export interface QuizAnswer {
  questionId: string;
  questionType: QuizQuestionType;
  
  // Para múltipla escolha e escolha única
  selectedOptions?: string[]; // IDs das opções selecionadas
  
  // Para verdadeiro/falso
  booleanAnswer?: boolean;
  
  // Para resposta aberta
  textAnswer?: string;
  
  // Para ordenação
  orderedItems?: string[]; // IDs dos itens na ordem escolhida
  
  // Para correspondência
  matchedPairs?: { leftId: string; rightId: string }[];
  
  // Metadados da resposta
  timeSpent: number;
  isCorrect?: boolean; // Opcional durante execução, definido na finalização
  pointsEarned?: number; // Opcional durante execução, definido na finalização
  feedback?: string;
}

export interface QuizContent {
  config: QuizConfig;
  questions: QuizQuestion[];
}

export interface QuizBlockContent {
  quiz: QuizContent;
}

// Tipos para relatórios e analytics
export interface QuizStatistics {
  quizId: string;
  totalAttempts: number;
  uniqueUsers: number;
  averageScore: number;
  passRate: number;
  averageTimeSpent: number;
  questionStats: QuestionStatistics[];
}

export interface QuestionStatistics {
  questionId: string;
  questionTitle: string;
  totalAnswers: number;
  correctAnswers: number;
  accuracyRate: number;
  averageTimeSpent: number;
  optionStats?: OptionStatistics[]; // para múltipla/única escolha
}

export interface OptionStatistics {
  optionId: string;
  optionText: string;
  timesSelected: number;
  selectionRate: number;
}

export interface UserQuizProgress {
  userId: string;
  quizId: string;
  attempts: QuizAttempt[];
  bestScore: number;
  bestPercentage: number;
  totalAttempts: number;
  passed: boolean;
  lastAttemptAt: Date;
}

// Tipos para exportação de dados
export interface QuizExportData {
  quiz: QuizContent;
  statistics: QuizStatistics;
  attempts: QuizAttempt[];
  userProgress: UserQuizProgress[];
}

// Tipos para validação
export interface QuizValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Tipos específicos para pesquisas de opinião
export interface SurveyResults {
  quizId: string;
  questionResults: SurveyQuestionResult[];
  totalResponses: number;
  collectedAt: Date;
  demographics?: Record<string, any>;
}

export interface SurveyQuestionResult {
  questionId: string;
  questionTitle: string;
  questionType: QuizQuestionType;
  totalResponses: number;

  // Estatísticas por opção (para múltipla escolha, única escolha, etc.)
  optionStats?: {
    optionId: string;
    optionText: string;
    responseCount: number;
    percentage: number;
    surveyValue?: number; // Para escalas numéricas
  }[];

  // Para perguntas abertas
  textResponses?: {
    response: string;
    timestamp: Date;
    userId?: string; // Opcional para respostas anônimas
  }[];

  // Para escalas numéricas e análises estatísticas
  numericStats?: {
    average: number;
    median: number;
    mode: number;
    standardDeviation: number;
    distribution: { value: number; count: number }[];
  };

  // Para perguntas verdadeiro/falso
  booleanStats?: {
    trueCount: number;
    falseCount: number;
    truePercentage: number;
    falsePercentage: number;
  };
}

// Tipos para o banco de perguntas
export interface QuestionTemplate {
  id: string;
  title: string;
  description?: string;
  type: QuizQuestionType;
  category: string;
  tags: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  question: Omit<QuizQuestion, 'id' | 'points'>;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  isPublic: boolean;
}

// Tipos para configuração do editor
export interface QuizEditorState {
  activeTab: 'general' | 'questions' | 'correction' | 'validation' | 'reports';
  selectedQuestionId?: string;
  isPreviewMode: boolean;
  isDirty: boolean;
}

// Funções utilitárias para compatibilidade
export const QuizUtils = {
  // Verificar se é uma pesquisa de opinião
  isSurveyMode: (config: QuizConfig): boolean => {
    return config.mode === 'survey';
  },

  // Verificar se é uma avaliação tradicional (padrão)
  isAssessmentMode: (config: QuizConfig): boolean => {
    return !config.mode || config.mode === 'assessment';
  },

  // Garantir compatibilidade com quizzes antigos
  ensureCompatibility: (config: Partial<QuizConfig>): QuizConfig => {
    return {
      mode: 'assessment', // Padrão para compatibilidade
      surveySettings: {
        showAggregatedResults: true,
        allowAnonymous: false,
        showOthersResponses: false,
        collectDemographics: false,
        showResultsAfterSubmission: true
      },
      ...config
    } as QuizConfig;
  },

  // Verificar se uma opção precisa de correção (tem isCorrect definido)
  optionNeedsCorrection: (option: QuizOption): boolean => {
    return option.isCorrect !== undefined;
  },

  // Criar template de pesquisa padrão
  createSurveyTemplate: (): QuizContent => {
    return {
      config: {
        title: 'Nova Pesquisa de Opinião',
        description: 'Colete opiniões e feedback dos usuários',
        instructions: 'Por favor, responda às perguntas com sinceridade. Suas respostas são importantes para nós.',
        mode: 'survey',
        maxAttempts: 1,
        allowRetry: false,
        passingScore: 0, // Não aplicável para pesquisas
        showScore: false,
        showCorrectAnswers: false,
        showFeedback: false,
        showDetailedResults: true,
        showTimer: false,
        isRequired: false,
        blockProgressUntilPassed: false,
        shuffleQuestions: false,
        shuffleOptions: false,
        showProgressBar: true,
        allowSaveDraft: true,
        enableAnalytics: true,
        showResultsToUser: true,
        surveySettings: {
          showAggregatedResults: true,
          allowAnonymous: false,
          showOthersResponses: false,
          collectDemographics: false,
          showResultsAfterSubmission: true
        }
      },
      questions: [
        {
          id: 'survey_q1',
          type: 'single-choice',
          title: 'Como você avalia nossa plataforma?',
          points: 1,
          required: true,
          options: [
            { id: 'opt1', text: 'Excelente', surveyValue: 5 },
            { id: 'opt2', text: 'Muito Bom', surveyValue: 4 },
            { id: 'opt3', text: 'Bom', surveyValue: 3 },
            { id: 'opt4', text: 'Regular', surveyValue: 2 },
            { id: 'opt5', text: 'Ruim', surveyValue: 1 }
          ]
        },
        {
          id: 'survey_q2',
          type: 'multiple-choice',
          title: 'Quais recursos você mais utiliza? (Selecione todos que se aplicam)',
          points: 1,
          required: false,
          options: [
            { id: 'opt1', text: 'Dashboard' },
            { id: 'opt2', text: 'Relatórios' },
            { id: 'opt3', text: 'Configurações' },
            { id: 'opt4', text: 'Suporte' },
            { id: 'opt5', text: 'Documentação' }
          ]
        },
        {
          id: 'survey_q3',
          type: 'open-text',
          title: 'Deixe seus comentários e sugestões:',
          points: 1,
          required: false
        }
      ]
    };
  },

  // Criar template de avaliação padrão
  createAssessmentTemplate: (): QuizContent => {
    return {
      config: {
        title: 'Nova Avaliação',
        description: 'Teste seus conhecimentos',
        instructions: 'Leia cada pergunta com atenção e selecione a melhor resposta.',
        mode: 'assessment',
        maxAttempts: 3,
        allowRetry: true,
        passingScore: 70,
        showScore: true,
        showCorrectAnswers: true,
        showFeedback: true,
        showDetailedResults: true,
        showTimer: false,
        isRequired: false,
        blockProgressUntilPassed: false,
        shuffleQuestions: false,
        shuffleOptions: false,
        showProgressBar: true,
        allowSaveDraft: true,
        enableAnalytics: true,
        showResultsToUser: true
      },
      questions: [
        {
          id: 'assessment_q1',
          type: 'single-choice',
          title: 'Qual é a capital do Brasil?',
          points: 1,
          required: true,
          options: [
            { id: 'opt1', text: 'Brasília', isCorrect: true },
            { id: 'opt2', text: 'São Paulo', isCorrect: false },
            { id: 'opt3', text: 'Rio de Janeiro', isCorrect: false },
            { id: 'opt4', text: 'Salvador', isCorrect: false }
          ],
          correctFeedback: 'Correto! Brasília é a capital do Brasil.',
          incorrectFeedback: 'Incorreto. A capital do Brasil é Brasília.'
        }
      ]
    };
  }
};

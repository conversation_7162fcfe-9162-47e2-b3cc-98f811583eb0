import { createClient } from '@supabase/supabase-js';

function getEnv(key: string, fallback?: string) {
  // Vite expõe as variáveis no frontend
  if (typeof import.meta.env !== "undefined" && import.meta.env[key]) {
    return import.meta.env[key];
  }
  // Node/scripts
  if (typeof process !== "undefined" && process.env && process.env[key]) {
    return process.env[key];
  }
  return fallback;
}

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Disponibilizar globalmente para debug
declare global {
  interface Window {
    supabase: typeof supabase;
  }
}

if (typeof window !== 'undefined') {
  window.supabase = supabase;
}
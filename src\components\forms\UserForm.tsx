import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/ui/use-toast';
import { supabase } from '@/lib/supabaseClient';
import { createProfileFromAuthUser, updateProfile } from '@/services/userService';
import { AvatarUpload } from '../ui/AvatarUpload';

const userSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  email: z.string().email('E-mail inválido'),
  position: z.string().min(1, 'Cargo é obrigatório'),
  phone: z.string().optional(),
  role: z.enum(['admin', 'manager', 'member']),
});

type UserFormData = z.infer<typeof userSchema>;

interface UserFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreated?: () => void;
  onUpdated?: () => void;
  userToEdit?: any;
}

export const UserForm: React.FC<UserFormProps> = ({ open, onOpenChange, onCreated, onUpdated, userToEdit }) => {
  console.log('UserForm render', { userToEdit });
  const { toast } = useToast();
  const form = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: userToEdit?.name || '',
      email: userToEdit?.email || '',
      position: userToEdit?.position || '',
      phone: userToEdit?.phone || '',
      role: userToEdit?.role || 'member',
    },
  });

  const [avatarUrl, setAvatarUrl] = React.useState<string | null>(userToEdit?.avatar_url || null);

  React.useEffect(() => {
    if (userToEdit) {
      setAvatarUrl(userToEdit.avatar_url || null);
      form.reset({
        name: userToEdit.name || '',
        email: userToEdit.email || '',
        position: userToEdit.position || '',
        phone: userToEdit.phone || '',
        role: userToEdit.role || 'member',
      });
    } else {
      setAvatarUrl(null);
      form.reset({ name: '', email: '', position: '', phone: '', role: 'member' });
    }
  }, [userToEdit, open]);

  const onSubmit = async (data: UserFormData) => {
    console.log('onSubmit chamado', { data, userToEdit });
    try {
      if (userToEdit) {
        // Edição
        console.log('Enviando updateProfile:', userToEdit.id, {
          name: data.name,
          position: data.position,
          phone: data.phone,
          role: data.role,
          avatar_url: avatarUrl || null,
        });
        const result = await updateProfile(userToEdit.id, {
          name: data.name,
          position: data.position,
          phone: data.phone,
          role: data.role,
          avatar_url: avatarUrl || null,
        });
        console.log('Resultado do updateProfile:', result);
        toast({ title: 'Usuário atualizado', description: 'Os dados do usuário foram atualizados.' });
        onOpenChange(false);
        form.reset();
        onUpdated?.();
        return;
      }
      // Criação (fluxo já existente)
      const tempPassword = Math.random().toString(36).slice(-10) + 'Aa!';
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: tempPassword,
        options: {
          data: {
            name: data.name,
            position: data.position,
            phone: data.phone,
            role: data.role,
            is_active: true,
          }
        }
      });
      console.log('authData:', authData, 'authError:', authError);

      // Verificar se houve erro na criação do usuário Auth
      if (authError) {
        console.error('Erro ao criar usuário no Supabase Auth:', authError);

        if (
          authError.message.includes('User already registered') ||
          authError.message.includes('already registered') ||
          authError.message.includes('email') && authError.message.includes('exists')
        ) {
          throw new Error('Já existe um usuário cadastrado com este e-mail.');
        }

        // Outros erros de Auth
        throw new Error(`Erro ao criar usuário: ${authError.message}`);
      }

      // Verificar se o usuário foi criado corretamente
      if (!authData?.user || !authData.user.id) {
        console.error('Usuário não foi criado corretamente:', authData);
        throw new Error('Erro ao criar usuário: dados de autenticação inválidos');
      }

      console.log('Usuário Auth criado com sucesso:', authData.user.id);

      // Aguardar mais tempo para garantir que o usuário foi persistido no auth.users
      console.log('Aguardando persistência do usuário no auth.users...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Verificar se o usuário realmente existe antes de criar perfil
      try {
        const { data: userCheck, error: userCheckError } = await supabase.auth.getUser();
        console.log('Verificação de usuário após criação:', userCheck, userCheckError);

        if (userCheckError || !userCheck.user) {
          throw new Error('Usuário não foi encontrado após criação. Tente fazer login primeiro.');
        }
      } catch (checkError) {
        console.error('Erro ao verificar usuário criado:', checkError);
        throw new Error('Não foi possível verificar se o usuário foi criado corretamente.');
      }

      await createProfileFromAuthUser(authData.user, {
        name: data.name,
        role: data.role,
        position: data.position,
        phone: data.phone,
        is_active: true,
        avatar_url: avatarUrl || null,
      });
      toast({ title: 'Usuário cadastrado', description: 'O usuário foi cadastrado e receberá um e-mail para ativar a conta.' });
      onOpenChange(false);
      form.reset();
      onCreated?.();
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error.message || 'Ocorreu um erro ao cadastrar o usuário.',
        variant: 'destructive',
      });
    }
  };

  // Atualiza avatarUrl com cache busting
  const handleAvatarChange = (url: string | null) => {
    if (url) {
      setAvatarUrl(url + '?t=' + Date.now());
    } else {
      setAvatarUrl(null);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md" aria-describedby="user-form-desc">
        <DialogHeader>
          <DialogTitle>{userToEdit ? 'Editar Usuário' : 'Novo Usuário'}</DialogTitle>
          <DialogDescription id="user-form-desc">
            Preencha os campos abaixo para {userToEdit ? 'editar' : 'cadastrar'} o usuário. O usuário receberá um e-mail para definir a senha.
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-center mb-4">
          <AvatarUpload
            userId={userToEdit?.id || ''}
            avatarUrl={avatarUrl}
            onAvatarChange={handleAvatarChange}
            disabled={form.formState.isSubmitting}
          />
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome completo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>E-mail</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} disabled={!!userToEdit} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="position"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cargo</FormLabel>
                  <FormControl>
                    <Input placeholder="Cargo ou função" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telefone</FormLabel>
                  <FormControl>
                    <Input placeholder="(99) 99999-9999" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Papel</FormLabel>
                  <FormControl>
                    <select {...field} className="w-full p-2 border rounded">
                      <option value="admin">Administrador</option>
                      <option value="manager">Gerente</option>
                      <option value="member">Membro</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" className="bg-project hover:bg-project-dark">
                {userToEdit ? 'Salvar' : 'Cadastrar'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
import CodeBlock from '@tiptap/extension-code-block';

// Lista de linguagens disponíveis com labels amigáveis
export const SUPPORTED_LANGUAGES = [
  { value: 'javascript', label: 'JavaScript', icon: '🟨' },
  { value: 'typescript', label: 'TypeScript', icon: '🔷' },
  { value: 'python', label: 'Python', icon: '🐍' },
  { value: 'java', label: 'Java', icon: '☕' },
  { value: 'cpp', label: 'C++', icon: '⚡' },
  { value: 'csharp', label: 'C#', icon: '🔵' },
  { value: 'php', label: 'PHP', icon: '🐘' },
  { value: 'ruby', label: 'Ruby', icon: '💎' },
  { value: 'go', label: 'Go', icon: '🐹' },
  { value: 'rust', label: 'Rust', icon: '🦀' },
  { value: 'sql', label: 'SQL', icon: '🗃️' },
  { value: 'json', label: 'JSON', icon: '📋' },
  { value: 'xml', label: 'XML', icon: '📄' },
  { value: 'html', label: 'HTML', icon: '🌐' },
  { value: 'css', label: 'CSS', icon: '🎨' },
  { value: 'scss', label: 'SCSS', icon: '💅' },
  { value: 'bash', label: 'Bash', icon: '💻' },
  { value: 'shell', label: 'Shell', icon: '🐚' },
  { value: 'yaml', label: 'YAML', icon: '⚙️' },
  { value: 'markdown', label: 'Markdown', icon: '📝' },
  { value: 'text', label: 'Texto Simples', icon: '📄' },
];

// Extensão customizada com syntax highlighting via CSS
export const CodeBlockExtension = CodeBlock.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      language: {
        default: 'text',
        parseHTML: element => element.getAttribute('data-language') || 'text',
        renderHTML: attributes => {
          if (!attributes.language) {
            return {};
          }
          return {
            'data-language': attributes.language,
          };
        },
      },
    };
  },
}).configure({
  HTMLAttributes: {
    class: 'code-block-container',
  },
});

export default CodeBlockExtension;

// Exemplo de código para testar syntax highlighting
export const EXAMPLE_CODE = {
  javascript: `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

const result = fibonacci(10);
console.log('Fibonacci(10):', result);`,

  python: `def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

result = fibonacci(10)
print(f"Fibonacci(10): {result}")`,

  typescript: `interface User {
  id: number;
  name: string;
  email: string;
}

const users: User[] = [
  { id: 1, name: 'John', email: '<EMAIL>' },
  { id: 2, name: 'Jane', email: '<EMAIL>' }
];

function findUser(id: number): User | undefined {
  return users.find(user => user.id === id);
}`
};

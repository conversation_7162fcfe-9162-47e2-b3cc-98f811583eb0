import React from 'react';
import { Card, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit } from 'lucide-react';

interface TaskHeaderProps {
  name: string;
  description: string;
  status: string;
  onEdit: () => void;
  onComplete: () => void;
}

export const TaskHeader: React.FC<TaskHeaderProps> = ({ name, description, status, onEdit, onComplete }) => {
  return (
    <Card className="border-task/20 bg-task-bg">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 w-full">
          <div className="space-y-2 flex-1 min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-task text-white hover:text-task hover:bg-white border-task transition-colors">
                Tarefa
              </Badge>
              <Badge variant="outline" className="border-task text-task">
                {status === 'in-progress' ? 'Em Andamento' : status}
              </Badge>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words truncate max-w-full">{name}</h1>
            <p className="text-gray-600 max-w-full break-words truncate">{description}</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button size="sm" variant="outline" onClick={onEdit} className="w-full sm:w-auto">
              <Edit className="w-4 h-4 mr-1" />
              Editar
            </Button>
            <Button 
              size="sm" 
              className="bg-task hover:bg-task-dark w-full sm:w-auto"
              onClick={onComplete}
            >
              Marcar como Concluída
            </Button>
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}; 
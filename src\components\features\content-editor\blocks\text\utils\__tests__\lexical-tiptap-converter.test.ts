import { 
  convertLexicalToTiptap, 
  convertTiptapToLexical, 
  isLexicalFormat,
  sanitizeHTML 
} from '../lexical-tiptap-converter';

describe('lexical-tiptap-converter', () => {
  describe('isLexicalFormat', () => {
    it('should detect Lexical JSON format', () => {
      const lexicalJson = JSON.stringify({
        root: {
          children: [
            {
              type: 'paragraph',
              children: [
                { type: 'text', text: 'Hello world' }
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      });

      expect(isLexicalFormat(lexicalJson)).toBe(true);
    });

    it('should not detect HTML as Lexical format', () => {
      const html = '<p>Hello world</p>';
      expect(isLexicalFormat(html)).toBe(false);
    });

    it('should not detect invalid JSON as Lexical format', () => {
      const invalidJson = '{ invalid json }';
      expect(isLexicalFormat(invalidJson)).toBe(false);
    });

    it('should not detect non-Lexical JSON as Lexical format', () => {
      const nonLexicalJson = JSON.stringify({ data: 'test' });
      expect(isLexicalFormat(nonLexicalJson)).toBe(false);
    });
  });

  describe('convertLexicalToTiptap', () => {
    it('should convert simple paragraph', () => {
      const lexicalData = {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [
                { type: 'text', text: 'Hello world' }
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      };

      const result = convertLexicalToTiptap(lexicalData);
      expect(result).toBe('<p>Hello world</p>');
    });

    it('should convert formatted text', () => {
      const lexicalData = {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [
                { type: 'text', text: 'Bold text', format: 1 }, // Bold = 1
                { type: 'text', text: ' and ' },
                { type: 'text', text: 'italic text', format: 2 } // Italic = 2
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      };

      const result = convertLexicalToTiptap(lexicalData);
      expect(result).toBe('<p><strong>Bold text</strong> and <em>italic text</em></p>');
    });

    it('should convert headings', () => {
      const lexicalData = {
        root: {
          children: [
            {
              type: 'heading',
              tag: 'h1',
              children: [
                { type: 'text', text: 'Main Title' }
              ]
            },
            {
              type: 'heading',
              tag: 'h2',
              children: [
                { type: 'text', text: 'Subtitle' }
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      };

      const result = convertLexicalToTiptap(lexicalData);
      expect(result).toBe('<h1>Main Title</h1><h2>Subtitle</h2>');
    });

    it('should convert lists', () => {
      const lexicalData = {
        root: {
          children: [
            {
              type: 'list',
              tag: 'ul',
              children: [
                {
                  type: 'listitem',
                  children: [
                    { type: 'text', text: 'First item' }
                  ]
                },
                {
                  type: 'listitem',
                  children: [
                    { type: 'text', text: 'Second item' }
                  ]
                }
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      };

      const result = convertLexicalToTiptap(lexicalData);
      expect(result).toBe('<ul><li>First item</li><li>Second item</li></ul>');
    });

    it('should convert links', () => {
      const lexicalData = {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'link',
                  url: 'https://example.com',
                  children: [
                    { type: 'text', text: 'Click here' }
                  ]
                }
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      };

      const result = convertLexicalToTiptap(lexicalData);
      expect(result).toBe('<p><a href="https://example.com">Click here</a></p>');
    });

    it('should handle already HTML content', () => {
      const htmlContent = '<p>Already HTML</p>';
      const result = convertLexicalToTiptap(htmlContent);
      expect(result).toBe(htmlContent);
    });

    it('should handle empty content', () => {
      const result = convertLexicalToTiptap('');
      expect(result).toBe('');
    });

    it('should handle invalid JSON gracefully', () => {
      const invalidJson = '{ invalid json }';
      const result = convertLexicalToTiptap(invalidJson);
      expect(result).toBe('<p>{ invalid json }</p>');
    });
  });

  describe('convertTiptapToLexical', () => {
    it('should return HTML as-is for compatibility', () => {
      const html = '<p>Test content</p>';
      const result = convertTiptapToLexical(html);
      expect(result).toBe(html);
    });
  });

  describe('sanitizeHTML', () => {
    it('should remove script tags', () => {
      const maliciousHtml = '<p>Safe content</p><script>alert("xss")</script>';
      const result = sanitizeHTML(maliciousHtml);
      expect(result).toBe('<p>Safe content</p>');
    });

    it('should remove event handlers', () => {
      const maliciousHtml = '<p onclick="alert(\'xss\')">Click me</p>';
      const result = sanitizeHTML(maliciousHtml);
      expect(result).toBe('<p>Click me</p>');
    });

    it('should remove javascript: URLs', () => {
      const maliciousHtml = '<a href="javascript:alert(\'xss\')">Link</a>';
      const result = sanitizeHTML(maliciousHtml);
      expect(result).toBe('<a href="">Link</a>');
    });

    it('should preserve safe HTML', () => {
      const safeHtml = '<p><strong>Bold</strong> and <em>italic</em> text</p>';
      const result = sanitizeHTML(safeHtml);
      expect(result).toBe(safeHtml);
    });
  });
});

import React from 'react';
import { Shield, Info } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { useProjectPermissions, type Permission } from '@/hooks/usePermissions';

interface QuickPermissionBadgeProps {
  projectId?: string;
  context?: {
    userId?: string;
    projectOwnerId?: string;
    taskResponsibleId?: string;
    taskExecutorIds?: string[];
    taskApproverIds?: string[];
    stageResponsibleIds?: string[];
  };
  screenType: 'project' | 'stage' | 'task';
}

// Filtrar permissões por contexto da tela
const getRelevantPermissions = (screenType: 'project' | 'stage' | 'task'): Permission[] => {
  switch (screenType) {
    case 'project':
      return [
        'create_project', 'view_project', 'edit_project', 'delete_project', 
        'manage_project_members', 'complete_project',
        'create_stage', 'view_stage'
      ];
    case 'stage':
      return [
        'view_project', 'create_stage', 'view_stage', 'edit_stage', 
        'delete_stage', 'manage_stage_members', 'complete_stage',
        'create_task', 'view_task'
      ];
    case 'task':
      return [
        'view_task', 'edit_task', 'delete_task', 'view_task_content', 
        'edit_task_content', 'execute_task', 'approve_task', 
        'manage_task_executors', 'manage_task_approvers', 'complete_task'
      ];
    default:
      return [];
  }
};

export const QuickPermissionBadge: React.FC<QuickPermissionBadgeProps> = ({
  projectId,
  context,
  screenType
}) => {
  const permissions = useProjectPermissions(projectId, context);
  
  const relevantPermissions = getRelevantPermissions(screenType);
  const userPermissions = relevantPermissions.filter(permission => 
    permissions.hasPermission(permission)
  );

  const permissionPercentage = Math.round((userPermissions.length / relevantPermissions.length) * 100);

  const getBadgeColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (percentage >= 60) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (percentage >= 40) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getTooltipContent = () => {
    const granted = userPermissions.length;
    const total = relevantPermissions.length;
    
    return (
      <div className="text-xs space-y-1">
        <div className="font-medium">Suas permissões nesta tela:</div>
        <div>{granted} de {total} disponíveis ({permissionPercentage}%)</div>
        <div className="text-gray-300">Clique no ícone de escudo para ver detalhes</div>
      </div>
    );
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant="outline" 
            className={`${getBadgeColor(permissionPercentage)} cursor-help flex items-center gap-1`}
          >
            <Shield className="w-3 h-3" />
            <span className="text-xs font-medium">{permissionPercentage}%</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="max-w-xs">
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

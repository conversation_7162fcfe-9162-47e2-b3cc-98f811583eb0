-- =====================================================
-- VERIFICAÇÃO FINAL: Por que ainda não funciona?
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR USUÁRIO ATUAL LOGADO NO FRONTEND
-- O console mostra: 4b09be1f-5187-44c0-9b53-87b7c57e45b4
-- Mas precisamos que seja: 3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee (Wellington)

-- 2. VERIFICAR SE WELLINGTON É EXECUTOR
SELECT 
    'WELLINGTON É EXECUTOR?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - É EXECUTOR'
        ELSE '❌ NÃO - NÃO É EXECUTOR'
    END as status
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee';

-- 3. VERIFICAR SE O USUÁRIO ATUAL (4b09be1f...) É EXECUTOR
SELECT 
    'USUÁRIO ATUAL É EXECUTOR?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - É EXECUTOR'
        ELSE '❌ NÃO - NÃO É EXECUTOR'
    END as status
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 4. VERIFICAR SE O USUÁRIO ATUAL EXISTE EM PROFILES
SELECT 
    'USUÁRIO ATUAL EXISTE EM PROFILES?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - EXISTE'
        ELSE '❌ NÃO - NÃO EXISTE (PROBLEMA!)'
    END as status
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 5. LISTAR TODOS OS EXECUTORES DA TAREFA
SELECT 
    'TODOS OS EXECUTORES' as info,
    te.user_id,
    p.name,
    p.email,
    CASE 
        WHEN te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4' THEN '👈 USUÁRIO ATUAL'
        WHEN te.user_id = '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee' THEN '👈 WELLINGTON'
        ELSE ''
    END as identificacao
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- =====================================================
-- DIAGNÓSTICO:
-- =====================================================
-- O problema é que o usuário ainda está logado como 
-- 4b09be1f-5187-44c0-9b53-87b7c57e45b4
-- 
-- Mas este usuário:
-- - NÃO existe na tabela profiles
-- - NÃO é executor da tarefa
-- 
-- SOLUÇÃO: Fazer logout e login como Wellington
-- Email: <EMAIL>
-- =====================================================

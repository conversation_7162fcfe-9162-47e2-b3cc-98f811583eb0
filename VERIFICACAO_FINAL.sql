-- =====================================================
-- VERIFICAÇÃO E CORREÇÃO FINAL: ADICIONAR COMO EXECUTOR
-- Execute este script para finalizar a configuração
-- =====================================================

-- STEP 1: CONFIRMAR QUE O PROFILE EXISTE
SELECT 
    'PROFILE CONFIRMADO' as status,
    p.id,
    p.name,
    p.email,
    p.role,
    p.is_active
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 2: VERIFICAR SE É EXECUTOR DA TAREFA
SELECT 
    'STATUS EXECUTOR ATUAL' as verificacao,
    COUNT(*) as total,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ JÁ É EXECUTOR'
        ELSE '❌ NÃO É EXECUTOR'
    END as status
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 3: ADICIONAR COMO EXECUTOR DA TAREFA
INSERT INTO task_executors (task_id, user_id, created_at)
VALUES (
    '7c606667-9391-4660-933d-90d6bd276e88',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    NOW()
)
ON CONFLICT (task_id, user_id) DO NOTHING;

-- STEP 4: CONFIRMAR QUE FOI ADICIONADO
SELECT 
    'EXECUTOR ADICIONADO' as resultado,
    te.task_id,
    te.user_id,
    p.name,
    p.email,
    te.created_at
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 5: LISTAR TODOS OS EXECUTORES DA TAREFA
SELECT 
    'TODOS OS EXECUTORES DA TAREFA' as info,
    te.user_id,
    p.name,
    p.email,
    p.role
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- STEP 6: VERIFICAR SE EXISTEM CONTENT BLOCKS NA TAREFA
SELECT 
    'CONTENT BLOCKS NA TAREFA' as verificacao,
    COUNT(*) as total_blocos,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ TEM CONTEÚDO'
        ELSE '❌ SEM CONTEÚDO'
    END as status
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- STEP 7: SIMULAR A QUERY QUE O APP FAZ
SELECT 
    'SIMULAÇÃO QUERY APP' as teste,
    tcb.id,
    tcb.type,
    LEFT(tcb.content::text, 50) as preview
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND tcb.task_id IN (
        SELECT te.task_id FROM task_executors te
        WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
    );

-- =====================================================
-- INSTRUÇÕES FINAIS:
-- =====================================================
-- 1. Execute este script no Supabase SQL Editor
-- 2. Recarregue a página da aplicação (F5)
-- 3. Navegue para a tarefa
-- 4. Clique na aba "Executar"
-- 5. O conteúdo deve aparecer!
-- =====================================================

# 🚀 Guia Rápido de Onboarding

Bem-vindo ao projeto! Siga este passo a passo para se integrar rapidamente, entender as regras e usar o Cursor.ai na máxima potência.

---

## 1. Central de Regras e Padrões
- **Leia SEMPRE** os arquivos em `.project-rules/`:
  - `cursorrules.md`: regras centrais de arquitetura, padrões, git, automação e IA.
  - `best-practices-cursor-ai.md`: dicas, exemplos de prompts e checklist de produtividade.
  - `ux-ui.md`, `rbac.md`, `padronizacao-blocos-editor.md`: padr<PERSON>es visuais, segurança e blocos do editor.

## 2. Uso do Cursor.ai
- Sempre selecione múltiplos arquivos e forneça contexto amplo nos prompts.
- Consulte os exemplos de prompts em `best-practices-cursor-ai.md`.
- Registre aprendizados e decisões importantes usando memories.

## 3. Padrão de Commit e Pull Request
- Use o padrão `[Data/hora] - [Título]` (data/hora do PowerShell) para commits.
- Siga o template de PR e preencha o checklist obrigatório.
- Nunca force push em branch compartilhada.

## 4. Automação e CI
- Todos os arquivos de regras são validados automaticamente.
- PRs e commits fora do padrão serão bloqueados.

## 5. Checklist Diário
- Revise código gerado pela IA antes de integrar.
- Atualize documentação e regras após decisões importantes.
- Compartilhe boas práticas e dúvidas com o time.

## 6. Dúvidas ou Problemas?
- Consulte o README principal e a central de regras.
- Use o Cursor.ai para troubleshooting, revisão e automação.
- Peça ajuda ao time e registre aprendizados para todos.

## 7. Manutenção e Boas Práticas
- Revise e documente alterações em arquivos de configuração (eslint, tsconfig, tailwind, vite, vitest, etc).
- Justifique e documente toda nova dependência no package.json.
- Mantenha exemplos/demos atualizados e use-os como base para testes.
- Consulte e atualize aliases em components.json conforme necessário.
- Documente alterações em schema e dados de exemplo (SQL, CSV).
- Utilize e documente scripts e utilitários de ambiente de teste.

## 8. Organização de Arquivos do Projeto
- **/demos/**: Exemplos, protótipos e arquivos HTML de demonstração.
- **/scripts/**: Scripts utilitários e de ambiente de desenvolvimento/teste.
- **Configurações principais**: Os arquivos de configuração do projeto (ESLint, Tailwind, Vite, Vitest, TypeScript, components.json, etc.) agora ficam na raiz do repositório, seguindo o padrão das ferramentas modernas.
- **/data/**: Schemas SQL, dados de exemplo e arquivos CSV.
- **/docs/**: Documentação complementar e planos de fases.
- Arquivos principais como `README.md`, `package.json` permanecem na raiz.

Consulte sempre essas pastas para encontrar recursos, exemplos e utilitários antes de criar novos arquivos na raiz do projeto.

---

**Bem-vindo(a)! Sua produtividade e qualidade de entrega vão decolar 🚀** 
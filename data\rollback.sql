-- =====================================================
-- SCRIPT DE ROLLBACK
-- =====================================================
-- Remove todas as tabelas, políticas e dados criados
-- Use apenas em caso de necessidade de limpeza total
-- Versão: 2.0 - Julho 2025

-- AVISO: Este script irá remover TODOS os dados!
DO $$
BEGIN
  RAISE NOTICE '⚠️ ATENÇÃO: Este script irá remover TODOS os dados!';
  RAISE NOTICE '📅 Iniciado em: %', NOW();
END $$;

-- =====================================================
-- REMOVER TRIGGERS (SAFE)
-- =====================================================

DO $$
BEGIN
  -- Remover triggers apenas se as tabelas existirem
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tasks' AND table_schema = 'public') THEN
    DROP TRIGGER IF EXISTS trigger_task_update_stage_progress ON public.tasks;
    DROP TRIGGER IF EXISTS trigger_tasks_update_timestamp ON public.tasks;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stages' AND table_schema = 'public') THEN
    DROP TRIGGER IF EXISTS trigger_stage_update_project_progress ON public.stages;
    DROP TRIGGER IF EXISTS trigger_stages_update_timestamp ON public.stages;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
    DROP TRIGGER IF EXISTS trigger_profiles_update_timestamp ON public.profiles;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects' AND table_schema = 'public') THEN
    DROP TRIGGER IF EXISTS trigger_projects_update_timestamp ON public.projects;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'quiz_attempts' AND table_schema = 'public') THEN
    DROP TRIGGER IF EXISTS trigger_quiz_attempt_stats ON public.quiz_attempts;
  END IF;
  
  RAISE NOTICE '✅ Triggers removidos (se existiam)';
END $$;

-- =====================================================
-- REMOVER FUNCTIONS (SAFE)
-- =====================================================

DO $$
BEGIN
  DROP FUNCTION IF EXISTS public.calculate_project_progress(uuid);
  DROP FUNCTION IF EXISTS public.calculate_stage_progress(uuid);
  DROP FUNCTION IF EXISTS public.create_notification(uuid, text, text, text, text);
  DROP FUNCTION IF EXISTS public.update_quiz_statistics(uuid);
  DROP FUNCTION IF EXISTS public.trigger_update_stage_progress();
  DROP FUNCTION IF EXISTS public.trigger_update_project_progress();
  DROP FUNCTION IF EXISTS public.trigger_update_timestamp();
  DROP FUNCTION IF EXISTS public.trigger_quiz_stats_update();
  DROP FUNCTION IF EXISTS public.cleanup_temp_files();
  DROP FUNCTION IF EXISTS public.get_signed_url(text, text, integer);
  DROP FUNCTION IF EXISTS public.can_access_file(text, text, uuid);
  RAISE NOTICE '✅ Functions removidas';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '⚠️ Erro ao remover functions: %', SQLERRM;
END $$;

-- =====================================================
-- REMOVER VIEWS (SAFE)
-- =====================================================

DO $$
BEGIN
  DROP VIEW IF EXISTS public.project_stats;
  DROP VIEW IF EXISTS public.task_details;
  DROP VIEW IF EXISTS public.user_stats;
  DROP VIEW IF EXISTS public.quiz_dashboard;
  RAISE NOTICE '✅ Views removidas';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '⚠️ Erro ao remover views: %', SQLERRM;
END $$;

-- =====================================================
-- REMOVER POLÍTICAS RLS (SAFE)
-- =====================================================

-- Function para remover todas as políticas de uma tabela (apenas se existir)
CREATE OR REPLACE FUNCTION public.drop_all_policies_rollback_safe(target_table text)
RETURNS void AS $$
DECLARE
  policy_record RECORD;
BEGIN
  -- Verificar se a tabela existe
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables t WHERE t.table_name = target_table AND t.table_schema = 'public') THEN
    RAISE NOTICE '⚠️ Tabela % não existe, pulando políticas', target_table;
    RETURN;
  END IF;
  
  -- Remover políticas se a tabela existir
  FOR policy_record IN
    SELECT policyname
    FROM pg_policies p
    WHERE p.tablename = target_table AND p.schemaname = 'public'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I', policy_record.policyname, target_table);
  END LOOP;
  
  RAISE NOTICE '✅ Políticas da tabela % removidas', target_table;
END;
$$ LANGUAGE plpgsql;

-- Remover políticas de todas as tabelas (apenas se existirem)
SELECT public.drop_all_policies_rollback_safe('profiles');
SELECT public.drop_all_policies_rollback_safe('projects');
SELECT public.drop_all_policies_rollback_safe('stages');
SELECT public.drop_all_policies_rollback_safe('tasks');
SELECT public.drop_all_policies_rollback_safe('project_members');
SELECT public.drop_all_policies_rollback_safe('task_executors');
SELECT public.drop_all_policies_rollback_safe('task_approvers');
SELECT public.drop_all_policies_rollback_safe('stage_responsibles');
SELECT public.drop_all_policies_rollback_safe('task_content_blocks');
SELECT public.drop_all_policies_rollback_safe('task_attachments');
SELECT public.drop_all_policies_rollback_safe('evidence');
SELECT public.drop_all_policies_rollback_safe('task_comments');
SELECT public.drop_all_policies_rollback_safe('project_history');
SELECT public.drop_all_policies_rollback_safe('user_notifications');
SELECT public.drop_all_policies_rollback_safe('quizzes');
SELECT public.drop_all_policies_rollback_safe('quiz_attempts');
SELECT public.drop_all_policies_rollback_safe('quiz_answers');
SELECT public.drop_all_policies_rollback_safe('user_quiz_progress');
SELECT public.drop_all_policies_rollback_safe('quiz_statistics');

-- Remover políticas de storage (se existirem)
DO $$
DECLARE
  policy_record RECORD;
  policy_count INTEGER := 0;
BEGIN
  -- Contar políticas de storage existentes
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE schemaname = 'storage' AND tablename = 'objects';
  
  IF policy_count > 0 THEN
    -- Log das políticas que serão removidas
    FOR policy_record IN 
      SELECT policyname 
      FROM pg_policies 
      WHERE schemaname = 'storage' AND tablename = 'objects'
      ORDER BY policyname
    LOOP
      RAISE NOTICE '🗑️ Removendo política de storage: %', policy_record.policyname;
      EXECUTE format('DROP POLICY IF EXISTS %I ON storage.objects', policy_record.policyname);
    END LOOP;
    
    RAISE NOTICE '✅ % políticas de storage removidas', policy_count;
  ELSE
    RAISE NOTICE '⚠️ Nenhuma política de storage encontrada';
  END IF;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '⚠️ Erro ao remover políticas de storage: %', SQLERRM;
END $$;

-- Remover function auxiliar
DROP FUNCTION IF EXISTS public.drop_all_policies_rollback_safe(text);

-- =====================================================
-- REMOVER BUCKETS DE STORAGE (SAFE)
-- =====================================================

DO $$
DECLARE
  bucket_record RECORD;
  bucket_count INTEGER := 0;
  object_count INTEGER := 0;
BEGIN
  -- Primeiro, contar e remover todos os objetos de todos os buckets
  SELECT COUNT(*) INTO object_count FROM storage.objects;
  
  IF object_count > 0 THEN
    DELETE FROM storage.objects;
    RAISE NOTICE '✅ % objetos do storage removidos', object_count;
  ELSE
    RAISE NOTICE '⚠️ Nenhum objeto encontrado no storage';
  END IF;
  
  -- Depois, remover todos os buckets existentes
  SELECT COUNT(*) INTO bucket_count FROM storage.buckets;
  
  IF bucket_count > 0 THEN
    -- Log dos buckets que serão removidos
    FOR bucket_record IN 
      SELECT id, name FROM storage.buckets ORDER BY created_at
    LOOP
      RAISE NOTICE '🗑️ Removendo bucket: % (%)', bucket_record.name, bucket_record.id;
    END LOOP;
    
    -- Remover todos os buckets
    DELETE FROM storage.buckets;
    RAISE NOTICE '✅ % buckets removidos', bucket_count;
  ELSE
    RAISE NOTICE '⚠️ Nenhum bucket encontrado no storage';
  END IF;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '⚠️ Erro ao remover storage: %', SQLERRM;
    RAISE NOTICE '🔍 Detalhes do erro: %', SQLSTATE;
END $$;

-- =====================================================
-- REMOVER TABELAS EM ORDEM REVERSA (SAFE)
-- =====================================================

DO $$
DECLARE
  target_table_names TEXT[] := ARRAY[
    'quiz_statistics', 'user_quiz_progress', 'quiz_answers', 'quiz_attempts', 
    'quizzes', 'user_notifications', 'project_history', 'task_comments', 
    'evidence', 'task_attachments', 'task_content_blocks', 'task_approvers', 
    'task_executors', 'stage_responsibles', 'project_members', 'tasks', 'stages', 'projects', 'profiles'
  ];
  target_table TEXT;
BEGIN
  -- Desabilitar RLS primeiro (apenas se as tabelas existirem)
  FOREACH target_table IN ARRAY target_table_names LOOP
    IF EXISTS (SELECT 1 FROM information_schema.tables t WHERE t.table_name = target_table AND t.table_schema = 'public') THEN
      EXECUTE format('ALTER TABLE public.%I DISABLE ROW LEVEL SECURITY', target_table);
    END IF;
  END LOOP;
  
  RAISE NOTICE '✅ RLS desabilitado';
  
  -- Remover tabelas (apenas se existirem)
  FOREACH target_table IN ARRAY target_table_names LOOP
    IF EXISTS (SELECT 1 FROM information_schema.tables t WHERE t.table_name = target_table AND t.table_schema = 'public') THEN
      EXECUTE format('DROP TABLE public.%I CASCADE', target_table);
      RAISE NOTICE '✅ Tabela % removida', target_table;
    ELSE
      RAISE NOTICE '⚠️ Tabela % não existe', target_table;
    END IF;
  END LOOP;
  
  RAISE NOTICE '✅ Processo de remoção de tabelas concluído';
END $$;

-- =====================================================
-- REMOVER TIPOS ENUM (SAFE)
-- =====================================================

DO $$
BEGIN
  DROP TYPE IF EXISTS public.project_member_role CASCADE;
  DROP TYPE IF EXISTS public.task_status CASCADE;
  DROP TYPE IF EXISTS public.stage_status CASCADE;
  DROP TYPE IF EXISTS public.project_status CASCADE;
  RAISE NOTICE '✅ Tipos ENUM removidos';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE '⚠️ Erro ao remover tipos ENUM: %', SQLERRM;
END $$;

-- =====================================================
-- REMOVER EXTENSÕES (OPCIONAL)
-- =====================================================

-- Descomente se quiser remover as extensões
-- DROP EXTENSION IF EXISTS pg_trgm;

-- =====================================================
-- LOGS FINAIS
-- =====================================================

DO $$
DECLARE
  remaining_tables INTEGER;
  remaining_policies INTEGER;
  remaining_buckets INTEGER;
  remaining_objects INTEGER;
BEGIN
  -- Contar tabelas restantes
  SELECT COUNT(*) INTO remaining_tables
  FROM information_schema.tables
  WHERE table_schema = 'public'
    AND table_name IN (
      'profiles', 'projects', 'stages', 'tasks', 'project_members',
      'task_executors', 'task_approvers', 'stage_responsibles', 'task_content_blocks',
      'task_attachments', 'evidence', 'task_comments', 'project_history',
      'user_notifications', 'quizzes', 'quiz_attempts', 'quiz_answers',
      'user_quiz_progress', 'quiz_statistics'
    );
  
  -- Contar políticas restantes
  SELECT COUNT(*) INTO remaining_policies
  FROM pg_policies
  WHERE schemaname = 'public';
  
  -- Contar buckets e objetos restantes
  SELECT COUNT(*) INTO remaining_buckets FROM storage.buckets;
  SELECT COUNT(*) INTO remaining_objects FROM storage.objects;
  
  RAISE NOTICE '🧹 Rollback concluído!';
  RAISE NOTICE '📊 Tabelas removidas: %', (19 - remaining_tables);
  RAISE NOTICE '🔒 Políticas removidas';
  RAISE NOTICE '🗂️ Buckets de storage removidos';
  RAISE NOTICE '📁 Objetos de storage removidos';
  RAISE NOTICE '⚙️ Functions e triggers removidos';
  RAISE NOTICE '📈 Views removidas';
  RAISE NOTICE '🎯 Tipos ENUM removidos';
  
  IF remaining_tables = 0 AND remaining_policies = 0 AND remaining_buckets = 0 AND remaining_objects = 0 THEN
    RAISE NOTICE '✅ Limpeza completa realizada com sucesso!';
  ELSE
    RAISE WARNING '⚠️ Alguns elementos podem não ter sido removidos completamente';
    RAISE NOTICE 'Tabelas restantes: %', remaining_tables;
    RAISE NOTICE 'Políticas restantes: %', remaining_policies;
    RAISE NOTICE 'Buckets restantes: %', remaining_buckets;
    RAISE NOTICE 'Objetos restantes: %', remaining_objects;
  END IF;
  
  RAISE NOTICE '📅 Finalizado em: %', NOW();
END $$;

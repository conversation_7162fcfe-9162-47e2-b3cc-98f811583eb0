import { BlockTypePreset } from '../constants/types';
import { StylePreset } from '../types';

/**
 * Converte um BlockTypePreset para StylePreset
 * Usado para compatibilidade com o sistema de presets existente
 */
export function convertBlockTypePresetToStylePreset(
  key: string,
  preset: BlockTypePreset,
  metadata: { name: string; description: string }
): StylePreset {
  return {
    id: key,
    name: metadata.name,
    description: metadata.description,
    config: {
      icon: preset.icon,
      button: preset.button,
      card: preset.card,
    },
  };
}

/**
 * Converte múltiplos BlockTypePresets para StylePresets
 */
export function convertBlockTypePresetsToStylePresets(
  presets: Record<string, BlockTypePreset>,
  metadataMap: Record<string, { name: string; description: string }>
): StylePreset[] {
  return Object.entries(presets).map(([key, preset]) =>
    convertBlockTypePresetToStylePreset(key, preset, metadataMap[key])
  );
}

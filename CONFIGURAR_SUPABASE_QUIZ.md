# 🗄️ Configurar Tabelas do Quiz no Supabase

## 🎯 **PROBLEMA IDENTIFICADO E SOLUÇÃO:**

### ❌ **Erro 400 (Bad Request):**
- Tentativa de criar quiz_attempt falhando
- Estrutura de dados incorreta no Supabase
- Tabelas não existem ou estão mal configuradas

### ✅ **Solução Implementada:**
- **Script SQL completo** para criar todas as tabelas
- **Estrutura corrigida** com relacionamentos corretos
- **Código atualizado** para usar IDs reais

---

## 🚀 **PASSO A PASSO PARA CONFIGURAR:**

### **1. 📊 Acesse o Painel do Supabase:**
```
1. V<PERSON> para: https://supabase.com/dashboard
2. Selecione seu projeto: "Gestão de Projeto"
3. Clique em "SQL Editor" no menu lateral
```

### **2. 📝 Execute o Script SQL:**
```
1. Clique em "New Query"
2. Copie TODO o conteúdo do arquivo: SUPABASE_QUIZ_TABLES.sql
3. Cole no editor SQL
4. Clique em "Run" (ou Ctrl+Enter)
5. Aguarde execução completa
```

### **3. ✅ Verificar Criação das Tabelas:**
```
1. Vá para "Table Editor" no menu lateral
2. Deve ver 4 novas tabelas:
   - quizzes
   - quiz_attempts  
   - quiz_answers
   - user_quiz_progress
```

### **4. 🔍 Verificar Estrutura:**
```sql
-- Execute esta query para verificar:
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename IN ('quizzes', 'quiz_attempts', 'quiz_answers', 'user_quiz_progress')
ORDER BY tablename;
```

---

## 📊 **ESTRUTURA DAS TABELAS CRIADAS:**

### **1. 🧩 Tabela `quizzes`:**
```sql
- id (UUID, PK) - ID único do quiz
- task_id (TEXT) - ID da tarefa
- block_id (TEXT) - ID do bloco
- content (JSONB) - Conteúdo completo do quiz
- is_active (BOOLEAN) - Se o quiz está ativo
- created_at, updated_at (TIMESTAMP)
- UNIQUE(task_id, block_id)
```

### **2. 🎯 Tabela `quiz_attempts`:**
```sql
- id (UUID, PK) - ID único da tentativa
- quiz_id (UUID, FK) - Referência para quizzes.id
- user_id (UUID) - ID do usuário
- started_at, submitted_at (TIMESTAMP)
- score, max_score (INTEGER)
- percentage (DECIMAL)
- passed (BOOLEAN)
- status (TEXT) - 'in_progress', 'submitted', 'graded'
- time_spent (INTEGER) - Tempo em segundos
```

### **3. 📝 Tabela `quiz_answers`:**
```sql
- id (UUID, PK) - ID único da resposta
- attempt_id (UUID, FK) - Referência para quiz_attempts.id
- question_id (TEXT) - ID da pergunta
- question_type (TEXT) - Tipo da pergunta
- selected_options (TEXT[]) - Opções selecionadas
- text_answer (TEXT) - Resposta de texto
- boolean_answer (BOOLEAN) - Resposta verdadeiro/falso
- ordering_answer (JSONB) - Resposta de ordenação
- matching_answer (JSONB) - Resposta de correspondência
- is_correct (BOOLEAN)
- points_earned (INTEGER)
- time_spent (INTEGER)
```

### **4. 📈 Tabela `user_quiz_progress`:**
```sql
- id (UUID, PK) - ID único do progresso
- quiz_id (UUID, FK) - Referência para quizzes.id
- user_id (UUID) - ID do usuário
- total_attempts (INTEGER) - Total de tentativas
- best_score, best_percentage - Melhor resultado
- passed (BOOLEAN) - Se foi aprovado
- first_attempt_at, last_attempt_at (TIMESTAMP)
- UNIQUE(quiz_id, user_id)
```

---

## 🔧 **RECURSOS INCLUÍDOS:**

### **✅ Índices para Performance:**
- Busca rápida por task_id e block_id
- Consultas eficientes por usuário
- Relacionamentos otimizados

### **✅ Triggers Automáticos:**
- updated_at atualizado automaticamente
- Timestamps precisos em todas as operações

### **✅ Constraints e Validações:**
- Relacionamentos garantidos por Foreign Keys
- Valores únicos onde necessário
- Tipos de dados apropriados

### **✅ Comentários e Documentação:**
- Cada tabela e coluna documentada
- Propósito e uso explicados
- Facilita manutenção futura

---

## 🧪 **TESTE APÓS CONFIGURAÇÃO:**

### **1. 🚀 Execute um Quiz:**
```
1. Vá para uma tarefa com quiz
2. Complete o quiz normalmente
3. Finalize o quiz
4. Observe logs no console
```

### **2. 📊 Logs Esperados:**
```
✅ "📝 Criando quiz no Supabase..."
✅ "✅ Quiz criado no Supabase com ID: [uuid]"
✅ "✅ Resultados salvos no Supabase com sucesso"
```

### **3. 🗄️ Verificar Dados Salvos:**
```sql
-- Verificar quiz criado:
SELECT * FROM quizzes ORDER BY created_at DESC LIMIT 1;

-- Verificar tentativa salva:
SELECT * FROM quiz_attempts ORDER BY created_at DESC LIMIT 1;

-- Verificar respostas salvas:
SELECT * FROM quiz_answers ORDER BY created_at DESC LIMIT 5;
```

### **4. 👁️ Notificação Esperada:**
```
Toast: "Dados salvos - Resultados salvos no servidor com sucesso"
```

---

## 🚨 **TROUBLESHOOTING:**

### **Se Script SQL Falhar:**
```
1. Verifique se está no projeto correto
2. Confirme permissões de administrador
3. Execute seções do script separadamente
4. Verifique logs de erro no Supabase
```

### **Se Tabelas Não Aparecem:**
```
1. Recarregue página do Supabase
2. Verifique aba "Table Editor"
3. Execute query de verificação
4. Confirme que script executou sem erros
```

### **Se Ainda Há Erro 400:**
```
1. Confirme que todas as tabelas foram criadas
2. Verifique estrutura das colunas
3. Teste com dados simples primeiro
4. Verifique logs detalhados no console
```

---

## 🎯 **RESULTADO ESPERADO:**

### **✅ Após Configuração:**
```
✅ 4 tabelas criadas no Supabase
✅ Relacionamentos funcionando
✅ Índices e triggers ativos
✅ Quiz salva dados automaticamente
✅ Sem mais erros 400
✅ Toast de sucesso aparece
✅ Dados visíveis no painel
```

### **📊 Fluxo Completo:**
```
1. Usuário executa quiz (modo local)
2. Quiz finaliza localmente (garantido)
3. Sistema cria quiz no Supabase (se necessário)
4. Sistema salva tentativa no Supabase
5. Sistema salva respostas no Supabase
6. Usuário recebe confirmação de sucesso
7. Dados ficam disponíveis no painel
```

---

## 📞 **PRÓXIMOS PASSOS:**

### **1. 🗄️ Execute o SQL:**
- Copie conteúdo de `SUPABASE_QUIZ_TABLES.sql`
- Execute no SQL Editor do Supabase
- Verifique criação das tabelas

### **2. 🧪 Teste o Sistema:**
- Execute um quiz completo
- Verifique logs no console
- Confirme dados no Supabase

### **3. ✅ Confirme Funcionamento:**
- Toast de sucesso aparece
- Dados salvos corretamente
- Sem mais erros 400

---

## 🎉 **CONCLUSÃO:**

**🎯 Após executar o script SQL, o sistema de Quiz terá:**
- **Estrutura completa** no Supabase
- **Salvamento automático** funcionando
- **Dados persistidos** no banco
- **Experiência fluida** para o usuário

**🗄️ Execute o script agora e teste o salvamento no Supabase!** ✅📊🚀

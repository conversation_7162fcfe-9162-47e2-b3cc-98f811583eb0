import { $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND } from 'lexical';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useCallback, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { FaBold, FaItalic, FaUnderline, FaStrikethrough, FaCode } from 'react-icons/fa';

// Tipo para as coordenadas do menu flutuante
type FloatingMenuCoords = { x: number; y: number } | undefined;

// Estado do menu flutuante
type FloatingMenuState = {
  isBold: boolean;
  isCode: boolean;
  isItalic: boolean;
  isStrikethrough: boolean;
  isUnderline: boolean;
};

const FloatingTextFormatToolbarPlugin = () => {
  const [editor] = useLexicalComposerContext();
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [coords, setCoords] = useState<FloatingMenuCoords>(undefined);
  const [state, setState] = useState<FloatingMenuState>({
    isBold: false,
    isCode: false,
    isItalic: false,
    isStrikethrough: false,
    isUnderline: false,
  });

  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      // Atualiza o estado com base na seleção atual
      setState({
        isBold: selection.hasFormat('bold'),
        isCode: selection.hasFormat('code'),
        isItalic: selection.hasFormat('italic'),
        isStrikethrough: selection.hasFormat('strikethrough'),
        isUnderline: selection.hasFormat('underline'),
      });

      // Só mostra o menu se houver texto selecionado
      const isCollapsed = selection.isCollapsed();
      if (isCollapsed) {
        setCoords(undefined);
        return;
      }

      // Calcula a posição do menu com base na seleção
      // Obtém o range da seleção do DOM nativo
      const nativeSelection = window.getSelection();
      if (!nativeSelection || nativeSelection.rangeCount === 0) {
        setCoords(undefined);
        return;
      }
      const domRange = nativeSelection.getRangeAt(0);

      const rect = domRange.getBoundingClientRect();
      if (!rect) {
        setCoords(undefined);
        return;
      }

      // Posiciona o menu acima da seleção
      const toolbarElem = toolbarRef.current;
      const toolbarHeight = toolbarElem?.offsetHeight || 0;
      const toolbarWidth = toolbarElem?.offsetWidth || 0;

      // Centraliza o menu horizontalmente sobre a seleção
      const x = rect.left + rect.width / 2 - toolbarWidth / 2;
      // Posiciona o menu acima da seleção com um pequeno espaço
      const y = rect.top - toolbarHeight - 8;

      // Garante que o menu não saia da tela
      const maxX = window.innerWidth - toolbarWidth;
      const adjustedX = Math.min(Math.max(0, x), maxX);
      const adjustedY = Math.max(0, y);

      setCoords({ x: adjustedX, y: adjustedY });
    }
  }, []);

  // Registra o listener para atualizar o menu quando a seleção mudar
  useEffect(() => {
    const unregisterListener = editor.registerUpdateListener(
      ({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }
    );

    // Atualiza o menu quando o mouse é liberado (após uma seleção)
    const handleMouseUp = () => {
      // Acessa o estado do editor dentro de um editor.update()
      editor.update(() => {
        updateToolbar();
      });
    };

    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      unregisterListener();
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [editor, updateToolbar]);

  // Manipuladores para os comandos de formatação
  const handleFormat = (format: string) => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  };

  // Não renderiza nada se não houver coordenadas
  if (!coords) {
    return null;
  }

  // Renderiza o menu flutuante usando um portal
  return createPortal(
    <div
      ref={toolbarRef}
      className="floating-text-format-toolbar"
      style={{
        position: 'fixed',
        top: `${coords.y}px`,
        left: `${coords.x}px`,
        zIndex: 10,
        display: 'flex',
        backgroundColor: 'var(--color-toolbar-bg)',
        borderRadius: '4px',
        boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)',
        padding: '4px',
      }}
    >
      <button
        className={`toolbar-item spaced ${state.isBold ? 'active' : ''}`}
        onClick={() => handleFormat('bold')}
        aria-label="Formatar texto como negrito"
      >
        <FaBold />
      </button>
      <button
        className={`toolbar-item spaced ${state.isItalic ? 'active' : ''}`}
        onClick={() => handleFormat('italic')}
        aria-label="Formatar texto como itálico"
      >
        <FaItalic />
      </button>
      <button
        className={`toolbar-item spaced ${state.isUnderline ? 'active' : ''}`}
        onClick={() => handleFormat('underline')}
        aria-label="Formatar texto como sublinhado"
      >
        <FaUnderline />
      </button>
      <button
        className={`toolbar-item spaced ${state.isStrikethrough ? 'active' : ''}`}
        onClick={() => handleFormat('strikethrough')}
        aria-label="Formatar texto como tachado"
      >
        <FaStrikethrough />
      </button>
      <button
        className={`toolbar-item spaced ${state.isCode ? 'active' : ''}`}
        onClick={() => handleFormat('code')}
        aria-label="Formatar texto como código"
      >
        <FaCode />
      </button>
    </div>,
    document.body
  );
};

export default FloatingTextFormatToolbarPlugin;
# Guia de Melhores Práticas para o Cursor.ai

> **Atenção:** Sempre referencie este arquivo e `.project-rules/cursorrules.md` ao utilizar o Cursor.ai para geração, refatoração ou revisão de código. Oriente novos membros a ler ambos os arquivos no onboarding.

---

# 🚀 Guia Completo: Maximizando o Potencial do Cursor.ai

## 1. Engajamento Eficaz com o Chat AI (Ctrl+K / Cmd+K)

### Estratégias para Prompts Otimizados

#### **Para Geração de Código**
```
❌ Prompt vago:
"crie um componente de formulário"

✅ Prompt otimizado:
"Crie um componente React TypeScript para formulário de projeto com os campos: nome, descrição, data_inicio, data_fim. Use react-hook-form, zod para validação, e shadcn/ui components. O componente deve ser reutilizável e seguir o padrão do projeto."
```

#### **Para Refatoração**
```
❌ Prompt genérico:
"refatore este código"

✅ Prompt específico:
"Refatore este componente para usar custom hooks, separar lógica de negócio da UI, e implementar error boundaries. Mantenha a compatibilidade com TypeScript e adicione testes unitários."
```

#### **Para Depuração**
```
❌ Prompt básico:
"tem um erro aqui"

✅ Prompt detalhado:
"Analise este erro de TypeScript relacionado ao Supabase query. O erro é: [cole o erro]. Contexto: estou tentando fazer um select com join entre projects e stages. Mostre a solução e explique o motivo do erro."
```

### Melhores Práticas para Fornecer Contexto

#### **Seleção Inteligente de Código**
```typescript
// ✅ Selecione o código relevante + contexto
// Exemplo: Para refatorar um service
// Selecione: projectService.ts (linhas 45-80)
// Adicione no prompt: "Este service está sendo usado em ProjectDetails.tsx e ProjectList.tsx"

// ❌ Não selecione apenas uma linha isolada
```

#### **Referência a Múltiplos Arquivos**
```
Prompt eficaz:
"Analise estes arquivos relacionados ao sistema de autenticação:
- src/auth/AuthProvider.tsx (linhas 1-50)
- src/auth/useAuth.ts (linhas 20-40)
- src/services/authService.ts (linhas 10-30)

Identifique inconsistências no gerenciamento de estado e sugira melhorias."
```

### Técnicas para Refinar Respostas

#### **Iteração Progressiva**
```
1ª interação: "Crie um hook personalizado para gerenciar upload de arquivos"
2ª interação: "Adicione validação de tipos de arquivo e limite de tamanho"
3ª interação: "Implemente progress bar e tratamento de erros"
4ª interação: "Adicione testes unitários para este hook"
```

#### **Especificação de Padrões**
```
"Use o padrão de nomenclatura do projeto:
- Components: PascalCase (ex: ProjectCard)
- Hooks: camelCase com prefixo 'use' (ex: useProjectStore)
- Services: camelCase (ex: projectService)
- Types: PascalCase com sufixo apropriado (ex: ProjectData, UserRole)"
```

## 2. Otimização do Uso de Comandos e Funcionalidades AI

### Ações de IA Dentro do Editor

#### **Comando `Cmd+L` (Chat Contextual)**
```typescript
// ✅ Selecione o código e use Cmd+L
const handleProjectSubmit = async (data: ProjectFormData) => {
  // Selecione esta função e digite: "Adicione validação de data e tratamento de erro"
};

// Resultado esperado:
const handleProjectSubmit = async (data: ProjectFormData) => {
  try {
    // Validação de data
    if (new Date(data.start_date) >= new Date(data.end_date)) {
      throw new Error('Data de início deve ser anterior à data de fim');
    }
    
    const result = await projectService.createProject(data);
    toast.success('Projeto criado com sucesso!');
  } catch (error) {
    toast.error(`Erro ao criar projeto: ${error.message}`);
  }
};
```

#### **Comando `Ctrl+G` (Geração Inteligente)**
```typescript
// ✅ Posicione o cursor e use Ctrl+G
// Digite: "generate Supabase query for fetching projects with stages count"

// Resultado esperado:
const fetchProjectsWithStagesCount = async () => {
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      stages:stages(count)
    `)
    .order('created_at', { ascending: false });
    
  if (error) throw error;
  return data;
};
```

### Dicas para Revisão de Código

#### **Identificação de Vulnerabilidades**
```
Prompt para segurança:
"Analise este código em busca de vulnerabilidades de segurança, especialmente:
- SQL injection (queries Supabase)
- XSS (renderização de dados do usuário)
- Exposição de dados sensíveis
- Validação de entrada inadequada"
```

#### **Otimização de Performance**
```
Prompt para performance:
"Otimize este componente React para melhor performance:
- Identifique re-renders desnecessários
- Sugira memoização onde apropriado
- Otimize queries Supabase
- Implemente lazy loading se necessário"
```

### Uso Inteligente do Contexto de Múltiplos Arquivos

#### **Análise de Dependências**
```
"Analise a dependência circular entre:
- src/store/useProjectStore.ts
- src/services/projectService.ts
- src/pages/ProjectDetails.tsx

Sugira uma arquitetura mais limpa."
```

## 3. Integração do AI ao Fluxo de Trabalho

### Momentos Ideais para Usar IA vs Codificação Manual

#### **✅ Use IA para:**
- **Prototipagem rápida**: "Crie um componente de dashboard com métricas"
- **Boilerplate**: "Gere CRUD completo para entidade Task"
- **Refatoração**: "Converta class component para functional component"
- **Testes**: "Gere testes unitários para este service"
- **Documentação**: "Crie JSDoc para estas funções"

#### **❌ Codifique manualmente:**
- **Lógica de negócio complexa**: Algoritmos específicos do domínio
- **Integrações críticas**: Configurações de Supabase, autenticação
- **Arquitetura**: Decisões de estrutura do projeto
- **Debugging complexo**: Problemas específicos de runtime

### Técnicas para Prototipagem Rápida

#### **Exploração de APIs**
```typescript
// Prompt: "Explore a API do Supabase para implementar real-time subscriptions"
// Resultado esperado:
const useRealtimeProjects = () => {
  const [projects, setProjects] = useState([]);
  
  useEffect(() => {
    const subscription = supabase
      .channel('projects_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'projects' },
        (payload) => {
          // Atualizar estado baseado no tipo de mudança
          console.log('Change received!', payload);
        }
      )
      .subscribe();
      
    return () => subscription.unsubscribe();
  }, []);
  
  return projects;
};
```

#### **Exploração de Bibliotecas**
```
Prompt: "Demonstre como usar @dnd-kit para drag and drop de tasks entre stages"
```

### Gerenciamento Eficiente do Código Gerado

#### **Revisão Sistemática**
```typescript
// ✅ Sempre revise o código gerado
// 1. Verifique tipos TypeScript
// 2. Teste a funcionalidade
// 3. Ajuste para padrões do projeto
// 4. Adicione testes

// Exemplo de revisão:
// Código gerado:
const handleSubmit = (data) => { ... }

// Após revisão:
const handleSubmit = async (data: ProjectFormData): Promise<void> => {
  try {
    await projectService.createProject(data);
    toast.success('Projeto criado!');
  } catch (error) {
    console.error('Erro ao criar projeto:', error);
    toast.error('Falha ao criar projeto');
  }
};
```

#### **Integração ao Projeto**
```
Checklist de integração:
□ Verificar imports e dependências
□ Ajustar para padrões de nomenclatura
□ Adicionar tipos TypeScript
□ Implementar error handling
□ Adicionar loading states
□ Criar testes unitários
□ Documentar funcionalidade
```

## 4. Configurações Avançadas e Personalização

### Configurações para Performance da IA

#### **Arquivo `.project-rules/cursorrules` (Raiz do Projeto)**
```json
{
  "rules": [
    "Sempre use TypeScript strict mode",
    "Prefira functional components com hooks",
    "Use shadcn/ui components quando possível",
    "Implemente error boundaries em componentes críticos",
    "Siga o padrão de nomenclatura: PascalCase para componentes, camelCase para funções",
    "Use react-hook-form + zod para validação de formulários",
    "Implemente loading states e error handling",
    "Use Supabase para backend operations",
    "Prefira custom hooks para lógica reutilizável",
    "Adicione JSDoc para funções complexas"
  ],
  "context": {
    "framework": "React + TypeScript + Vite",
    "ui": "shadcn/ui + Tailwind CSS",
    "backend": "Supabase",
    "state": "Zustand + React Query",
    "testing": "Vitest + Testing Library"
  }
}
```

#### **Configurações do Workspace**
```json
// .vscode/settings.json
{
  "cursor.chat.model": "claude-3.5-sonnet",
  "cursor.chat.contextLines": 50,
  "cursor.chat.maxTokens": 4000,
  "cursor.chat.temperature": 0.3,
  "cursor.chat.systemPrompt": "Você é um especialista em React, TypeScript e Supabase. Sempre sugira soluções escaláveis e mantenha o código limpo e bem documentado."
}
```

### Gerenciamento de Tokens e API

#### **Otimização de Uso**
```
Estratégias para economizar tokens:
1. Selecione apenas código relevante
2. Use prompts concisos e específicos
3. Evite repetir contexto desnecessário
4. Use .project-rules/cursorrules para contexto global
5. Limpe histórico de chat regularmente
```

#### **Configuração de Chaves**
```
Para projetos empresariais:
1. Use chaves de API dedicadas por projeto
2. Configure rate limits apropriados
3. Monitore uso de tokens
4. Implemente fallback para quando a IA não estiver disponível
```

## 5. Exemplos Práticos Baseados no Seu Projeto

### Exemplo 1: Criação de Componente de Dashboard

```
Prompt: "Crie um componente Dashboard que exiba métricas de projetos usando recharts. Inclua: total de projetos, projetos em andamento, projetos concluídos, e um gráfico de progresso por mês."
```

### Exemplo 2: Implementação de Sistema de Notificações

```
Prompt: "Implemente um sistema de notificações em tempo real usando Supabase real-time. Crie um hook useNotifications que escute mudanças na tabela notifications e exiba toasts usando sonner."
```

### Exemplo 3: Otimização de Performance

```
Prompt: "Analise o componente ProjectDetails.tsx e otimize para evitar re-renders desnecessários. Implemente React.memo, useMemo e useCallback onde apropriado."
```

## Exemplos de Prompts Otimizados para o Projeto

### Refatoração de Serviços
```
Refatore todos os arquivos de service para evitar duplicação de lógica, garantir tipagem forte e cobrir com testes unitários. Considere as regras em .project-rules/cursorrules.md.
```

### Geração de Componente com Validação
```
Crie um componente React TypeScript para formulário de etapa, usando react-hook-form, validação com zod e componentes do design system. Siga os padrões de UX definidos em .project-rules/ux-ui.md.
```

### Revisão de Segurança (RBAC)
```
Analise o fluxo de aprovação de tarefas e sugira melhorias de segurança, considerando as regras de RBAC em .project-rules/rbac.md e .project-rules/rbac-por-papeis.md.
```

### Prototipagem de Bloco do Editor
```
Implemente um novo tipo de bloco para o editor, seguindo as diretrizes de .project-rules/padronizacao-blocos-editor.md. Forneça exemplos de configuração visual e integração com o painel de blocos.
```

## 6. Checklist de Produtividade Diária

### ✅ Rotina Matutina
- [ ] Revisar código gerado na sessão anterior
- [ ] Verificar se há melhorias sugeridas pela IA
- [ ] Atualizar documentação se necessário

### ✅ Durante o Desenvolvimento
- [ ] Use Cmd+K para dúvidas rápidas
- [ ] Use Cmd+L para refatoração contextual
- [ ] Use Ctrl+G para geração de código
- [ ] Sempre revise e teste código gerado

### ✅ Rotina de Finalização
- [ ] Execute testes unitários
- [ ] Verifique TypeScript errors
- [ ] Commit com mensagem descritiva
- [ ] Documente funcionalidades complexas

## Checklist Diário para Uso do Cursor.ai

- [ ] Revisar código gerado pela IA antes de integrar
- [ ] Selecionar múltiplos arquivos para contexto amplo
- [ ] Fornecer objetivos claros e exemplos nos prompts
- [ ] Consultar `.project-rules/cursorrules.md` para regras e padrões
- [ ] Atualizar documentação e regras após decisões importantes
- [ ] Compartilhar aprendizados e boas práticas com o time
- [ ] Usar automações (mcp tools) para auditoria, logs e deploys
- [ ] Manter o ambiente de desenvolvimento alinhado com as recomendações do projeto

**Dica:** Use o Cursor.ai para acelerar tarefas repetitivas, refatorações em larga escala, prototipagem e revisão de padrões. Sempre revise, adapte e documente o que for gerado!

## Checklist de Revisão de Código Gerado pela IA

- [ ] O código segue as regras de `.project-rules/cursorrules.md`?
- [ ] O padrão de UX/UI está de acordo com `.project-rules/ux-ui.md`?
- [ ] O código está tipado corretamente (TypeScript)?
- [ ] Foram adicionados/atualizados testes automatizados?
- [ ] O código foi revisado manualmente e adaptado ao contexto do projeto?
- [ ] A documentação foi atualizada, se necessário?
- [ ] O commit segue o padrão definido?
- [ ] O PR descreve claramente contexto, motivação e impacto?

## Conclusão

O Cursor.ai é uma ferramenta poderosa que, quando utilizada com as estratégias corretas, pode aumentar significativamente sua produtividade. A chave está em:

1. **Formular prompts específicos e contextuais**
2. **Sempre revisar e adaptar o código gerado**
3. **Usar a IA para tarefas repetitivas e prototipagem**
4. **Manter o controle sobre decisões arquiteturais importantes**
5. **Configurar adequadamente o ambiente para seu projeto**

Com essas práticas, você conseguirá aproveitar ao máximo o potencial da IA enquanto mantém a qualidade e a manutenibilidade do seu código.

---

## Troubleshooting com Cursor.ai e mcp tools

### Exemplos de Prompts para Depuração

#### 1. Análise de Logs do Frontend
```
Use as mcp tools para buscar logs de erro do frontend e identificar a origem de um bug:

Prompt: "Rode getConsoleErrors e getConsoleLogs para analisar erros recentes ao salvar tarefas. Sugira logs adicionais se necessário."
```

#### 2. Auditoria de Segurança no Supabase
```
Prompt: "Rode auditoria de segurança no Supabase usando mcp tools e liste possíveis falhas de RLS ou policies."
```

#### 3. Diagnóstico de Performance
```
Prompt: "Execute um performance audit na página de dashboard e sugira otimizações baseadas nos resultados."
```

#### 4. Logs de Backend
```
Prompt: "Busque logs de erro do Supabase (api, postgres) para a última operação de aprovação de tarefa. Analise e sugira correções."
```

**Dica:** Sempre registre aprendizados e decisões importantes usando memories para facilitar troubleshooting futuro! 
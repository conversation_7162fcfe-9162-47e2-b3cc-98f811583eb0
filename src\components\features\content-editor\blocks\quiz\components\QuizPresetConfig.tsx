import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON>rkles, 
  Target,
  BarChart3,
  CheckSquare,
  ToggleLeft,
  MessageSquare,
  Palette,
  Zap
} from 'lucide-react';

import { BlockConfig } from '@/types';

interface QuizPresetConfigProps {
  config: BlockConfig;
  onChange: (config: BlockConfig) => void;
  quizMode?: 'assessment' | 'survey';
}

// Presets específicos para Quiz com configurações completas
const QUIZ_PRESETS = {
  assessment: {
    default: {
      name: 'Padrão Assessment',
      description: 'Estilo clássico para avaliações',
      icon: Target,
      config: {
        icon: {
          enabled: true,
          iconName: 'Target',
          position: 'left-title',
          type: 'predefined',
          backgroundColor: '#3b82f6',
          color: '#ffffff',
          format: 'circle',
          border: { enabled: true, color: '#3b82f6', width: 1 },
          shadow: { enabled: true, depth: 2 }
        },
        card: {
          backgroundColor: '#eff6ff',
          color: '#1e40af',
          format: 'rounded',
          border: { enabled: true, color: '#3b82f6', width: 1 },
          shadow: { enabled: true, depth: 1 }
        },
        button: {
          backgroundColor: '#3b82f6',
          color: '#ffffff',
          format: 'rounded',
          size: 'medium',
          border: { enabled: false, color: '#3b82f6', width: 1 },
          shadow: { enabled: true, depth: 2 },
          hover: { backgroundColor: '#2563eb', color: '#ffffff' }
        }
      }
    },
    serious: {
      name: 'Profissional',
      description: 'Visual sério para avaliações formais',
      icon: CheckSquare,
      config: {
        icon: {
          enabled: true,
          iconName: 'CheckSquare',
          position: 'left-title',
          type: 'predefined',
          backgroundColor: '#1f2937',
          color: '#ffffff',
          format: 'square',
          border: { enabled: true, color: '#1f2937', width: 1 },
          shadow: { enabled: true, depth: 2 }
        },
        card: {
          backgroundColor: '#f9fafb',
          color: '#1f2937',
          format: 'square',
          border: { enabled: true, color: '#374151', width: 1 },
          shadow: { enabled: true, depth: 1 }
        },
        button: {
          backgroundColor: '#1f2937',
          color: '#ffffff',
          format: 'square',
          size: 'medium',
          border: { enabled: false, color: '#1f2937', width: 1 },
          shadow: { enabled: true, depth: 2 },
          hover: { backgroundColor: '#374151', color: '#ffffff' }
        }
      }
    },
    success: {
      name: 'Sucesso',
      description: 'Tema verde para avaliações positivas',
      icon: Zap,
      config: {
        icon: {
          enabled: true,
          iconName: 'CircleHelp',
          position: 'left-title',
          type: 'predefined',
          backgroundColor: '#059669',
          color: '#ffffff',
          format: 'circle',
          border: { enabled: true, color: '#059669', width: 1 },
          shadow: { enabled: true, depth: 2 }
        },
        card: {
          backgroundColor: '#ecfdf5',
          color: '#065f46',
          format: 'rounded',
          border: { enabled: true, color: '#10b981', width: 1 },
          shadow: { enabled: true, depth: 1 }
        },
        button: {
          backgroundColor: '#059669',
          color: '#ffffff',
          format: 'rounded',
          size: 'medium',
          border: { enabled: false, color: '#059669', width: 1 },
          shadow: { enabled: true, depth: 2 },
          hover: { backgroundColor: '#047857', color: '#ffffff' }
        }
      }
    }
  },
  survey: {
    default: {
      name: 'Padrão Survey',
      description: 'Estilo amigável para pesquisas',
      icon: BarChart3,
      config: {
        icon: {
          enabled: true,
          iconName: 'BarChart3',
          position: 'left-title',
          type: 'predefined',
          backgroundColor: '#10b981',
          color: '#ffffff',
          format: 'circle',
          border: { enabled: true, color: '#10b981', width: 1 },
          shadow: { enabled: true, depth: 2 }
        },
        card: {
          backgroundColor: '#ecfdf5',
          color: '#065f46',
          format: 'rounded',
          border: { enabled: true, color: '#10b981', width: 1 },
          shadow: { enabled: true, depth: 1 }
        },
        button: {
          backgroundColor: '#10b981',
          color: '#ffffff',
          format: 'rounded',
          size: 'medium',
          border: { enabled: false, color: '#10b981', width: 1 },
          shadow: { enabled: true, depth: 2 },
          hover: { backgroundColor: '#059669', color: '#ffffff' }
        }
      }
    },
    analytics: {
      name: 'Analítico',
      description: 'Visual moderno para análise de dados',
      icon: Palette,
      config: {
        icon: {
          enabled: true,
          iconName: 'BarChart3',
          position: 'left-title',
          type: 'predefined',
          backgroundColor: '#8b5cf6',
          color: '#ffffff',
          format: 'square',
          border: { enabled: true, color: '#8b5cf6', width: 1 },
          shadow: { enabled: true, depth: 2 }
        },
        card: {
          backgroundColor: '#f5f3ff',
          color: '#5b21b6',
          format: 'rounded',
          border: { enabled: true, color: '#8b5cf6', width: 1 },
          shadow: { enabled: true, depth: 1 }
        },
        button: {
          backgroundColor: '#8b5cf6',
          color: '#ffffff',
          format: 'rounded',
          size: 'medium',
          border: { enabled: false, color: '#8b5cf6', width: 1 },
          shadow: { enabled: true, depth: 2 },
          hover: { backgroundColor: '#7c3aed', color: '#ffffff' }
        }
      }
    },
    friendly: {
      name: 'Amigável',
      description: 'Cores quentes para pesquisas casuais',
      icon: MessageSquare,
      config: {
        icon: {
          enabled: true,
          iconName: 'MessageSquare',
          position: 'left-title',
          type: 'predefined',
          backgroundColor: '#f59e0b',
          color: '#ffffff',
          format: 'circle',
          border: { enabled: true, color: '#f59e0b', width: 1 },
          shadow: { enabled: true, depth: 2 }
        },
        card: {
          backgroundColor: '#fffbeb',
          color: '#92400e',
          format: 'rounded',
          border: { enabled: true, color: '#f59e0b', width: 1 },
          shadow: { enabled: true, depth: 1 }
        },
        button: {
          backgroundColor: '#f59e0b',
          color: '#ffffff',
          format: 'rounded',
          size: 'medium',
          border: { enabled: false, color: '#f59e0b', width: 1 },
          shadow: { enabled: true, depth: 2 },
          hover: { backgroundColor: '#d97706', color: '#ffffff' }
        }
      }
    }
  }
};

export const QuizPresetConfig: React.FC<QuizPresetConfigProps> = ({
  config,
  onChange,
  quizMode = 'assessment'
}) => {
  const currentPresets = QUIZ_PRESETS[quizMode];

  const applyPreset = (presetKey: string) => {
    const preset = currentPresets[presetKey];
    if (!preset) return;

    onChange({
      ...config,
      ...preset.config
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Sparkles className="w-5 h-5" />
        <h3 className="text-lg font-semibold">
          Presets para {quizMode === 'survey' ? 'Pesquisas' : 'Avaliações'}
        </h3>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">
            Temas Otimizados para Quiz
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4">
            {Object.entries(currentPresets).map(([key, preset]) => {
              const IconComponent = preset.icon;
              const isActive = false; // TODO: Implementar detecção de preset ativo
              
              return (
                <Button
                  key={key}
                  variant={isActive ? "default" : "outline"}
                  className="justify-start h-auto p-4 relative"
                  onClick={() => applyPreset(key)}
                >
                  <div className="flex items-center gap-4 w-full">
                    {/* Preview do ícone */}
                    <div 
                      className="w-10 h-10 rounded-full flex items-center justify-center border-2"
                      style={{ 
                        backgroundColor: preset.config.icon.backgroundColor,
                        borderColor: preset.config.icon.backgroundColor 
                      }}
                    >
                      <IconComponent className="w-5 h-5 text-white" />
                    </div>

                    {/* Preview do card */}
                    <div 
                      className="w-16 h-10 rounded border-2 flex items-center justify-center"
                      style={{ 
                        backgroundColor: preset.config.card.backgroundColor,
                        borderColor: preset.config.card.border?.color,
                        color: preset.config.card.color
                      }}
                    >
                      <div className="text-xs font-medium">Card</div>
                    </div>

                    {/* Informações do preset */}
                    <div className="flex-1 text-left">
                      <div className="font-medium flex items-center gap-2">
                        {preset.name}
                        {isActive && <Badge variant="secondary" className="text-xs">Ativo</Badge>}
                      </div>
                      <div className="text-sm text-gray-500">
                        {preset.description}
                      </div>
                    </div>

                    {/* Preview do botão */}
                    <div 
                      className="px-3 py-1 rounded text-xs font-medium"
                      style={{ 
                        backgroundColor: preset.config.button.backgroundColor,
                        color: preset.config.button.color
                      }}
                    >
                      Botão
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-4">
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-2">💡 Dica sobre Presets</p>
            <ul className="space-y-1 text-xs">
              <li>• Cada preset aplica configurações completas de ícone, card e botão</li>
              <li>• Os presets são otimizados para o modo {quizMode === 'survey' ? 'pesquisa' : 'avaliação'}</li>
              <li>• Você pode personalizar qualquer configuração após aplicar um preset</li>
              <li>• As cores são escolhidas para máxima legibilidade e acessibilidade</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

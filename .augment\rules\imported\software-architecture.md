---
type: "manual"
---

# Princípios de Arquitetura de Software - Guia de Boas Práticas

**Siga rigorosamente os princípios de SRP, DRY, SSOT, KISS e YAGNI. Separe UI de lógica em serviços.**

Ao gerar código e arquitetar o projeto, siga rigorosamente os seguintes princípios de boas práticas de arquitetura de software para garantir escalabilidade, manutenibilidade e qualidade do código.

## Princípios Fundamentais a Serem Seguidos

### 1. Single Responsibility Principle (SRP) - Separação de Responsabilidades

Cada componente, módulo ou arquivo deve ter uma única e bem definida responsabilidade.

#### UI (Interface de Usuário) vs. Lógica de Negócio/Dados:
- **Componentes de UI** (ex: `Button.vue`, `ProductCard.tsx`) devem ser responsáveis apenas por sua apresentação e interação visual
- Eles devem emitir eventos quando ações ocorrem (ex: `@click="onClick"`) e não devem conter lógica de negócio, chamadas de API ou manipulação de dados diretamente

#### Serviços e Lógica:
- Crie serviços dedicados (ex: `apiService.ts`, `authService.ts`, `productService.ts`) para encapsular a lógica de negócio, chamadas de API, manipulação de dados e qualquer outra operação complexa

#### Estrutura de Pastas:
- Organize o projeto de forma que a separação de responsabilidades seja evidente (ex: `components/`, `services/`, `stores/`, `utils/`)

**Perguntas a considerar:** *"Este arquivo faz mais de uma coisa? Se sim, como posso dividi-lo?"*

---

### 2. Don't Repeat Yourself (DRY) - Não Repita-se

Evite qualquer duplicação de código, lógica ou configuração.

#### Componentes Reutilizáveis:
- Sempre que uma parte da UI (ex: um botão estilizado, um input de formulário, um card) ou uma lógica (ex: formatação de data, validação de e-mail) for usada em múltiplos lugares, crie um componente ou função reutilizável

#### Abstração:
- Identifique padrões e abstraia-os em funções, classes ou módulos genéricos que possam ser consumidos em diferentes contextos

**Perguntas a considerar:** *"Estou escrevendo o mesmo código em dois lugares? Se sim, como posso criar uma função/componente genérico?"*

---

### 3. Single Source of Truth (SSOT) - Fonte Única da Verdade

Todos os dados e estados críticos da aplicação devem residir em um único local acessível e centralizado.

#### Gerenciamento de Estado Global:
- Utilize um gerenciador de estado global (ex: Pinia para Vue, Redux/Zustand para React, Context API com cautela) para armazenar e gerenciar dados que são compartilhados entre múltiplos componentes ou telas

#### Dados Consistentes:
- Garanta que quando um dado é atualizado, essa atualização seja refletida consistentemente em todos os lugares que o utilizam, através da fonte única

#### Evite Estados Locais Redundantes:
- Não crie variáveis de estado locais em componentes diferentes que armazenam a mesma informação de forma independente

**Perguntas a considerar:** *"Onde está a fonte primária deste dado? Todos os lugares que usam este dado o acessam do mesmo lugar?"*

---

### 4. Keep It Simple, Stupid (KISS) - Mantenha Simples e Direto

Priorize a simplicidade e a clareza no design e na implementação.

#### Evite Engenharia Excessiva:
- Não adicione complexidade desnecessária ou "perfumarias" que não trazem valor imediato

#### Soluções Diretas:
- Busque a solução mais direta e legível para um problema. Complexidade deve ser justificada por requisitos claros

#### Código Legível:
- O código deve ser fácil de entender por outro desenvolvedor (e pelo próprio Lovable em interações futuras)

**Perguntas a considerar:** *"Existe uma maneira mais simples de fazer isso? Esta solução é excessivamente complexa para o problema atual?"*

---

### 5. You Aren't Gonna Need It (YAGNI) - Você Não Vai Precisar Disso (Agora)

Implemente apenas as funcionalidades que são estritamente necessárias no momento atual do desenvolvimento.

#### Desenvolvimento Iterativo:
- Construa o mínimo produto viável (MVP) e adicione funcionalidades incrementais à medida que elas se tornam realmente necessárias e validadas

#### Evite Previsões Desnecessárias:
- Não gaste tempo e esforço implementando recursos com base em suposições futuras

#### Foco no Core:
- Mantenha o foco nas funcionalidades essenciais que entregam valor imediato

**Perguntas a considerar:** *"Este recurso é realmente necessário AGORA? Ele resolve um problema existente ou é uma especulação futura?"*

---

## Princípios Complementares

### 6. Separation of Concerns (SoC) - Separação de Preocupações

- Mantenha diferentes aspectos do sistema (apresentação, lógica, dados) em módulos separados
- Organize arquivos por funcionalidade ou domínio, não apenas por tipo técnico
- Evite dependências circulares entre módulos

**Perguntas a considerar:** *"Este módulo está misturando responsabilidades? Como posso separar melhor as preocupações?"*

---

### 7. Fail Fast - Falhe Rapidamente

- Implemente validações e verificações de erro o mais cedo possível no fluxo de execução
- Use TypeScript e suas tipagens rigorosas para detectar erros em tempo de desenvolvimento
- Adicione guards e validações de entrada em funções críticas

**Perguntas a considerar:** *"Onde posso adicionar validações para detectar erros mais cedo?"*

---

### 8. Composition over Inheritance - Composição sobre Herança

- Prefira composição de componentes/funções menores ao invés de hierarquias complexas
- Use hooks customizados, composables ou utility functions para compartilhar lógica
- Evite componentes ou classes muito profundas na hierarquia

**Perguntas a considerar:** *"Posso usar composição ao invés de herança aqui? Como posso tornar isso mais modular?"*

---

### 9. Principle of Least Surprise - Princípio da Menor Surpresa

- O comportamento do código deve ser previsível e intuitivo
- Nomes de variáveis, funções e componentes devem ser descritivos e seguir convenções
- Mantenha consistência em padrões e estilos ao longo do projeto

**Perguntas a considerar:** *"Este código faz o que o nome sugere? É intuitivo para outros desenvolvedores?"*
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Palette, 
  Eye, 
  Sparkles, 
  RotateCcw,
  CircleHelp,
  CheckSquare,
  ToggleLeft,
  MessageSquare,
  BarChart3,
  Target
} from 'lucide-react';

import { BlockConfig } from '@/types';
import { ColorPicker } from '../../shared/ColorPicker';
import { QuizUtils } from '@/types/quiz';

interface QuizAppearanceConfigProps {
  config: BlockConfig;
  onChange: (config: BlockConfig) => void;
  quizMode?: 'assessment' | 'survey';
}

// Ícones específicos para Quiz baseados no modo
const QUIZ_ICONS = {
  assessment: {
    'CircleHelp': { icon: CircleHelp, label: 'Pergunta Geral' },
    'Target': { icon: Target, label: 'Objetivo/Meta' },
    'CheckSquare': { icon: CheckSquare, label: 'Múltipla Escolha' },
    'ToggleLeft': { icon: ToggleLeft, label: 'Verdadeiro/Falso' },
    'MessageSquare': { icon: MessageSquare, label: 'Resposta Aberta' },
  },
  survey: {
    'BarChart3': { icon: BarChart3, label: 'Estatísticas' },
    'CircleHelp': { icon: CircleHelp, label: 'Pergunta' },
    'CheckSquare': { icon: CheckSquare, label: 'Opções' },
    'MessageSquare': { icon: MessageSquare, label: 'Comentários' },
  }
};

// Presets otimizados para Quiz
const QUIZ_PRESETS = {
  assessment: {
    default: { name: 'Padrão Assessment', colors: { primary: '#3b82f6', bg: '#eff6ff' } },
    serious: { name: 'Sério', colors: { primary: '#1f2937', bg: '#f9fafb' } },
    success: { name: 'Sucesso', colors: { primary: '#059669', bg: '#ecfdf5' } },
  },
  survey: {
    default: { name: 'Padrão Survey', colors: { primary: '#10b981', bg: '#ecfdf5' } },
    analytics: { name: 'Analítico', colors: { primary: '#8b5cf6', bg: '#f5f3ff' } },
    friendly: { name: 'Amigável', colors: { primary: '#f59e0b', bg: '#fffbeb' } },
  }
};

export const QuizAppearanceConfig: React.FC<QuizAppearanceConfigProps> = ({
  config,
  onChange,
  quizMode = 'assessment'
}) => {
  const currentIcons = QUIZ_ICONS[quizMode];
  const currentPresets = QUIZ_PRESETS[quizMode];

  const handleIconChange = (field: string, value: any) => {
    const newConfig = {
      ...config,
      icon: {
        ...config.icon,
        [field]: value
      }
    };
    onChange(newConfig);
  };

  const handleCardChange = (field: string, value: any) => {
    const newConfig = {
      ...config,
      card: {
        ...config.card,
        [field]: value
      }
    };
    onChange(newConfig);
  };

  const applyPreset = (presetKey: string) => {
    const preset = currentPresets[presetKey];
    if (!preset) return;

    onChange({
      ...config,
      icon: {
        ...config.icon,
        backgroundColor: preset.colors.primary,
        color: '#ffffff',
        border: { ...config.icon?.border, color: preset.colors.primary }
      },
      card: {
        ...config.card,
        backgroundColor: preset.colors.bg,
        color: preset.colors.primary,
        border: { ...config.card?.border, color: preset.colors.primary }
      }
    });
  };

  const resetToDefault = () => {
    const defaultIcon = quizMode === 'survey' ? 'BarChart3' : 'CircleHelp';
    const defaultColors = currentPresets.default.colors;

    onChange({
      ...config,
      icon: {
        enabled: true,
        iconName: defaultIcon,
        position: 'left-title',
        type: 'predefined',
        backgroundColor: defaultColors.primary,
        color: '#ffffff',
        format: 'circle',
        border: { enabled: true, color: defaultColors.primary, width: 1 },
        shadow: { enabled: true, depth: 2 }
      },
      card: {
        backgroundColor: defaultColors.bg,
        color: defaultColors.primary,
        format: 'rounded',
        border: { enabled: true, color: defaultColors.primary, width: 1 },
        shadow: { enabled: true, depth: 1 }
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Palette className="w-5 h-5" />
          Aparência do Quiz
        </h3>
        <Button variant="outline" size="sm" onClick={resetToDefault}>
          <RotateCcw className="w-4 h-4 mr-2" />
          Restaurar Padrão
        </Button>
      </div>

      <Tabs defaultValue="appearance" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="appearance">Aparência</TabsTrigger>
          <TabsTrigger value="icon">Ícone</TabsTrigger>
          <TabsTrigger value="presets">Presets</TabsTrigger>
        </TabsList>

        <TabsContent value="appearance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Configurações do Card</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Cor de Fundo</Label>
                  <ColorPicker
                    value={config.card?.backgroundColor || '#ffffff'}
                    onChange={(color) => handleCardChange('backgroundColor', color)}
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Cor do Texto</Label>
                  <ColorPicker
                    value={config.card?.color || '#000000'}
                    onChange={(color) => handleCardChange('color', color)}
                  />
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Formato</Label>
                <Select
                  value={config.card?.format || 'rounded'}
                  onValueChange={(value) => handleCardChange('format', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rounded">Arredondado</SelectItem>
                    <SelectItem value="square">Quadrado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={config.card?.border?.enabled || false}
                  onCheckedChange={(enabled) => 
                    handleCardChange('border', { ...config.card?.border, enabled })
                  }
                />
                <Label className="text-sm">Mostrar borda</Label>
              </div>

              {config.card?.border?.enabled && (
                <div>
                  <Label className="text-sm font-medium">Cor da Borda</Label>
                  <ColorPicker
                    value={config.card?.border?.color || '#e5e5e5'}
                    onChange={(color) => 
                      handleCardChange('border', { ...config.card?.border, color })
                    }
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="icon" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Configurações do Ícone</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={config.icon?.enabled !== false}
                  onCheckedChange={(enabled) => handleIconChange('enabled', enabled)}
                />
                <Label className="text-sm">Mostrar ícone</Label>
              </div>

              {config.icon?.enabled !== false && (
                <>
                  <div>
                    <Label className="text-sm font-medium">Ícone</Label>
                    <Select
                      value={config.icon?.iconName || 'CircleHelp'}
                      onValueChange={(iconName) => handleIconChange('iconName', iconName)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(currentIcons).map(([key, iconData]) => {
                          const IconComponent = iconData.icon;
                          return (
                            <SelectItem key={key} value={key}>
                              <div className="flex items-center gap-2">
                                <IconComponent className="w-4 h-4" />
                                {iconData.label}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Cor de Fundo</Label>
                      <ColorPicker
                        value={config.icon?.backgroundColor || '#3b82f6'}
                        onChange={(color) => handleIconChange('backgroundColor', color)}
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Cor do Ícone</Label>
                      <ColorPicker
                        value={config.icon?.color || '#ffffff'}
                        onChange={(color) => handleIconChange('color', color)}
                      />
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Formato</Label>
                    <Select
                      value={config.icon?.format || 'circle'}
                      onValueChange={(value) => handleIconChange('format', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="circle">Circular</SelectItem>
                        <SelectItem value="square">Quadrado</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="presets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                Presets para {quizMode === 'survey' ? 'Pesquisas' : 'Avaliações'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3">
                {Object.entries(currentPresets).map(([key, preset]) => (
                  <Button
                    key={key}
                    variant="outline"
                    className="justify-start h-auto p-4"
                    onClick={() => applyPreset(key)}
                  >
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-6 h-6 rounded-full border-2"
                        style={{ 
                          backgroundColor: preset.colors.primary,
                          borderColor: preset.colors.primary 
                        }}
                      />
                      <div className="text-left">
                        <div className="font-medium">{preset.name}</div>
                        <div className="text-xs text-gray-500">
                          Otimizado para {quizMode === 'survey' ? 'pesquisas' : 'avaliações'}
                        </div>
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos para listas do Lexical Editor */
@layer base {
  /* G<PERSON>tir que as listas sejam exibidas corretamente */
  [data-lexical-editor] ul,
  [data-lexical-editor] ol {
    list-style-position: outside !important;
    padding-left: 1.5rem !important;
  }
  
  [data-lexical-editor] ul {
    list-style-type: disc !important;
  }
  
  [data-lexical-editor] ol {
    list-style-type: decimal !important;
  }
  
  [data-lexical-editor] li {
    display: list-item !important;
    margin-bottom: 0.25rem !important;
  }
  
  /* Para o preview das listas */
  .prose ul,
  .prose ol {
    list-style-position: outside !important;
    padding-left: 1.5rem !important;
  }
  
  .prose ul {
    list-style-type: disc !important;
  }
  
  .prose ol {
    list-style-type: decimal !important;
  }
  
  .prose li {
    display: list-item !important;
  }
  
  /* Garantir que os headings sejam exibidos corretamente */
  [data-lexical-editor] h1,
  .prose h1 {
    font-size: 1.875rem !important;
    font-weight: bold !important;
    margin-bottom: 1rem !important;
    line-height: 1.2 !important;
  }
  
  [data-lexical-editor] h2,
  .prose h2 {
    font-size: 1.5rem !important;
    font-weight: bold !important;
    margin-bottom: 0.75rem !important;
    line-height: 1.3 !important;
  }
  
  [data-lexical-editor] h3,
  .prose h3 {
    font-size: 1.25rem !important;
    font-weight: bold !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.4 !important;
  }
  
  [data-lexical-editor] h4,
  .prose h4 {
    font-size: 1.125rem !important;
    font-weight: bold !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.4 !important;
  }
  
  [data-lexical-editor] h5,
  .prose h5 {
    font-size: 1rem !important;
    font-weight: bold !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.5 !important;
  }
  
  [data-lexical-editor] h6,
   .prose h6 {
     font-size: 0.875rem !important;
     font-weight: bold !important;
     margin-bottom: 0.5rem !important;
     line-height: 1.5 !important;
   }
   
   /* Garantir que o código seja exibido corretamente */
   [data-lexical-editor] code {
     background-color: #0f172a !important;
     color: #f9fafb !important;
     padding: 0.5rem 0.75rem !important;
     border-radius: 0.375rem !important;
     font-family: 'Courier New', Courier, monospace !important;
     font-size: 0.875rem !important;
     font-weight: normal !important;
   }
   
   .prose code {
     background-color: #ffffff !important;
     color: #374151 !important;
     padding: 0.5rem 0.75rem !important;
     border: 1px solid #e5e7eb !important;
     border-radius: 0.375rem !important;
     font-family: 'Courier New', Courier, monospace !important;
     font-size: 0.875rem !important;
     font-weight: normal !important;
     box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
   }
   
   [data-lexical-editor] pre {
     background-color: #0f172a !important;
     color: #f9fafb !important;
     padding: 1rem !important;
     border-radius: 0.5rem !important;
     overflow-x: auto !important;
     margin-bottom: 1rem !important;
     white-space: pre !important;
     font-family: monospace !important;
     display: block !important;
     width: 100% !important;
     line-height: 1.4 !important;
   }
   
   [data-lexical-editor] .code {
     background-color: #0f172a !important;
     color: #f9fafb !important;
     padding: 1rem !important;
     border-radius: 0.5rem !important;
     overflow-x: auto !important;
     margin-bottom: 1rem !important;
     white-space: pre !important;
     font-family: monospace !important;
     display: block !important;
     width: 100% !important;
     line-height: 1.4 !important;
   }
   
   /* Estilos específicos para CodeNode do Lexical */
   [data-lexical-editor] [data-lexical-code] {
     background-color: #0f172a !important;
     color: #f9fafb !important;
     padding: 1rem !important;
     border-radius: 0.5rem !important;
     overflow-x: auto !important;
     margin-bottom: 1rem !important;
     white-space: pre !important;
     font-family: 'Courier New', Courier, monospace !important;
     display: block !important;
     width: 100% !important;
     line-height: 1.4 !important;
     font-size: 0.875rem !important;
   }
   
   /* Remove espaçamento entre linhas nos blocos de código */
   [data-lexical-editor] [data-lexical-code] span {
     display: inline !important;
     margin: 0 !important;
     padding: 0 !important;
     line-height: inherit !important;
   }
   
   .prose pre {
     background-color: #0f172a !important;
     color: #f9fafb !important;
     padding: 1rem !important;
     border: 1px solid #374151 !important;
     border-radius: 0.5rem !important;
     overflow-x: auto !important;
     margin-bottom: 1rem !important;
     box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
     line-height: 1.4 !important;
   }
   
   /* Estilos para elementos code gerados pelo Lexical */
   [data-lexical-editor] code {
     background-color: #0f172a !important;
     color: #f9fafb !important;
     padding: 1rem !important;
     border-radius: 0.5rem !important;
     overflow-x: auto !important;
     margin-bottom: 1rem !important;
     white-space: pre !important;
     font-family: 'Courier New', Courier, monospace !important;
     display: block !important;
     width: 100% !important;
     line-height: 1.4 !important;
     font-size: 0.875rem !important;
   }
   
   /* Força estilos para todos os elementos que podem ser blocos de código */
   [data-lexical-editor] [class*="code"],
   [data-lexical-editor] [data-lexical-text="true"][style*="font-family"] {
     background-color: #0f172a !important;
     color: #f9fafb !important;
     padding: 1rem !important;
     border-radius: 0.5rem !important;
     overflow-x: auto !important;
     margin-bottom: 1rem !important;
     white-space: pre !important;
     font-family: 'Courier New', Courier, monospace !important;
     display: block !important;
     width: 100% !important;
     line-height: 1.4 !important;
     font-size: 0.875rem !important;
   }
   
   [data-lexical-editor] pre code {
     background-color: transparent !important;
     color: inherit !important;
     padding: 0 !important;
     border-radius: 0 !important;
   }
   
   .prose pre code {
     background-color: transparent !important;
     color: inherit !important;
     padding: 0 !important;
     border-radius: 0 !important;
     border: none !important;
     box-shadow: none !important;
   }
 }

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* CORREÇÃO CRÍTICA: Sobrescrever prose para tabelas com especificidade máxima */
@layer utilities {
  /* Garantir que estilos inline de tabela sempre funcionem */
  .tiptap-editor table[style],
  .tiptap-editor td[style],
  .tiptap-editor th[style],
  .tiptap-editor tr[style],
  .tiptap-editor-content table[style],
  .tiptap-editor-content td[style],
  .tiptap-editor-content th[style],
  .tiptap-editor-content tr[style] {
    all: revert !important;
  }

  /* Resetar prose apenas para tabelas */
  .prose table,
  .prose td,
  .prose th {
    all: unset !important;
    display: table-cell !important;
  }

  .prose table {
    display: table !important;
    border-collapse: collapse !important;
  }
}

@media (max-width: 640px) {
  .dialog-content {
    width: 100vw !important;
    min-width: 100vw !important;
    max-width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    border-radius: 0 !important;
    height: 100dvh !important;
    max-height: 100dvh !important;
    transform: none !important;
    margin: 0 !important;
  }
}

/* Forçar estilos do Quill para títulos e elementos no preview */
.ql-editor h1 {
  font-size: 2em !important;
  font-weight: bold !important;
  margin: 0.67em 0 !important;
}
.ql-editor h2 {
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.75em 0 !important;
}
.ql-editor h3 {
  font-size: 1.17em !important;
  font-weight: bold !important;
  margin: 0.83em 0 !important;
}
.ql-editor h4 {
  font-size: 1em !important;
  font-weight: bold !important;
  margin: 1.12em 0 !important;
}
.ql-editor h5 {
  font-size: 0.83em !important;
  font-weight: bold !important;
  margin: 1.5em 0 !important;
}
.ql-editor h6 {
  font-size: 0.67em !important;
  font-weight: bold !important;
  margin: 1.67em 0 !important;
}
.ql-editor ul,
.ql-editor ol {
  margin: 1em 0 1em 2em !important;
}
.ql-editor li {
  margin: 0.2em 0 !important;
}
.ql-editor strong {
  font-weight: bold !important;
}
.ql-editor em {
  font-style: italic !important;
}
/* ... outros ajustes se necessário ... */
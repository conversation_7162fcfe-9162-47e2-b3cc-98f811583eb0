-- =====================================================
-- DIAGNÓSTICO DETALHADO - ERRO 406 TASKS
-- =====================================================

-- 1. VERIFICAR ESTADO DAS POLÍTICAS RLS
SELECT 
    '=== POLÍTICAS RLS ATIVAS PARA TASKS ===' as secao;

SELECT 
    policyname as politica,
    cmd as definicao
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'tasks';

-- 2. VERIFICAR SE RLS ESTÁ HABILITADO
SELECT 
    '=== STATUS RLS ===' as secao;

SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_habilitado
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'tasks';

-- 3. VERIFICAR PERFIL DO USUÁRIO ATUAL
SELECT 
    '=== PERFIL USUÁRIO ATUAL ===' as secao;

SELECT 
    id,
    email,
    role,
    full_name
FROM public.profiles 
WHERE id = auth.uid();

-- 4. VERIFICAR PROJETOS ACESSÍVEIS
SELECT 
    '=== PROJETOS ACESSÍVEIS ===' as secao;

-- Como proprietário
SELECT 
    'proprietário' as tipo_acesso,
    p.id,
    p.title,
    p.owner_id
FROM public.projects p
WHERE p.owner_id = auth.uid();

-- Como membro
SELECT 
    'membro' as tipo_acesso,
    p.id,
    p.title,
    pm.role as papel_no_projeto
FROM public.projects p
JOIN public.project_members pm ON pm.project_id = p.id
WHERE pm.user_id = auth.uid();

-- 5. VERIFICAR STAGES ACESSÍVEIS
SELECT 
    '=== STAGES ACESSÍVEIS ===' as secao;

SELECT 
    s.id as stage_id,
    s.title as stage_title,
    s.project_id,
    p.title as project_title,
    'owner' as acesso_tipo
FROM public.stages s
JOIN public.projects p ON s.project_id = p.id
WHERE p.owner_id = auth.uid()

UNION

SELECT 
    s.id as stage_id,
    s.title as stage_title,
    s.project_id,
    p.title as project_title,
    'member' as acesso_tipo
FROM public.stages s
JOIN public.projects p ON s.project_id = p.id
JOIN public.project_members pm ON pm.project_id = p.id
WHERE pm.user_id = auth.uid();

-- 6. VERIFICAR TASKS DIRETAMENTE (SEM RLS)
SELECT 
    '=== TASKS TOTAIS (SEM RLS) ===' as secao;

SELECT 
    COUNT(*) as total_tasks
FROM public.tasks;

-- 7. VERIFICAR TASKS ACESSÍVEIS (COM RLS)
SELECT 
    '=== TASKS ACESSÍVEIS (COM RLS) ===' as secao;

SELECT 
    COUNT(*) as tasks_acessiveis
FROM public.tasks;

-- 8. VERIFICAR TASK_EXECUTORS
SELECT 
    '=== TASK EXECUTORS ===' as secao;

SELECT 
    te.task_id,
    te.user_id,
    p.email as executor_email,
    t.title as task_title
FROM public.task_executors te
JOIN public.profiles p ON te.user_id = p.id
LEFT JOIN public.tasks t ON te.task_id = t.id
WHERE te.user_id = auth.uid() OR t.id IS NOT NULL
LIMIT 10;

-- 9. VERIFICAR TASK_APPROVERS
SELECT 
    '=== TASK APPROVERS ===' as secao;

SELECT 
    ta.task_id,
    ta.user_id,
    p.email as approver_email,
    t.title as task_title
FROM public.task_approvers ta
JOIN public.profiles p ON ta.user_id = p.id
LEFT JOIN public.tasks t ON ta.task_id = t.id
WHERE ta.user_id = auth.uid() OR t.id IS NOT NULL
LIMIT 10;

-- 10. TESTE ESPECÍFICO - BUSCAR UMA TASK ESPECÍFICA
SELECT 
    '=== TESTE TASK ESPECÍFICA ===' as secao;

-- Tentar buscar a primeira task disponível
SELECT 
    t.id,
    t.title,
    t.stage_id,
    s.project_id,
    p.title as project_title,
    p.owner_id,
    t.assigned_to,
    t.created_by
FROM public.tasks t
JOIN public.stages s ON t.stage_id = s.id
JOIN public.projects p ON s.project_id = p.id
LIMIT 1;

-- 11. VERIFICAR ERROS DE MULTIPLICIDADE
SELECT 
    '=== VERIFICAR DUPLICATAS ===' as secao;

-- Verificar se há tasks duplicadas por algum critério
SELECT 
    id,
    title,
    COUNT(*) as ocorrencias
FROM public.tasks
GROUP BY id, title
HAVING COUNT(*) > 1;

import { supabase } from '@/lib/supabaseClient';
import { EvidenceBlockContent, Evidence } from '@/types';
import { v4 as uuidv4 } from 'uuid';

export interface MigrationResult {
  success: boolean;
  tasksProcessed: number;
  blocksCreated: number;
  errors: string[];
}

export class EvidenceMigrationService {
  /**
   * Migra evidências existentes para o novo formato de bloco
   * Adiciona um bloco de evidências para tarefas que possuem anexos mas não possuem bloco de evidências
   */
  static async migrateExistingEvidences(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      tasksProcessed: 0,
      blocksCreated: 0,
      errors: []
    };

    try {
      // 1. Buscar todas as tarefas que possuem anexos
      const { data: tasksWithAttachments, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          task_attachments (
            id,
            file_url,
            description,
            created_at,
            uploaded_by,
            profiles:uploaded_by (
              id,
              name,
              email
            )
          )
        `)
        .not('task_attachments', 'is', null);

      if (tasksError) {
        throw new Error(`Erro ao buscar tarefas: ${tasksError.message}`);
      }

      if (!tasksWithAttachments || tasksWithAttachments.length === 0) {
        console.log('Nenhuma tarefa com anexos encontrada');
        return result;
      }

      // 2. Para cada tarefa, verificar se já possui bloco de evidências
      for (const task of tasksWithAttachments) {
        try {
          result.tasksProcessed++;

          // Verificar se já existe bloco de evidências
          const { data: existingBlocks, error: blocksError } = await supabase
            .from('task_content_blocks')
            .select('id')
            .eq('task_id', task.id)
            .eq('type', 'evidence');

          if (blocksError) {
            result.errors.push(`Erro ao verificar blocos da tarefa ${task.id}: ${blocksError.message}`);
            continue;
          }

          // Se já existe bloco de evidências, pular
          if (existingBlocks && existingBlocks.length > 0) {
            console.log(`Tarefa ${task.id} já possui bloco de evidências`);
            continue;
          }

          // 3. Converter anexos para formato Evidence
          const evidences: Evidence[] = task.task_attachments.map(attachment => ({
            id: attachment.id,
            type: 'file',
            name: attachment.description || 'Arquivo',
            url: attachment.file_url,
            uploadedBy: attachment.profiles ? {
              id: attachment.profiles.id,
              name: attachment.profiles.name || attachment.profiles.email,
              email: attachment.profiles.email
            } : attachment.uploaded_by,
            uploadedAt: attachment.created_at
          }));

          // 4. Criar conteúdo do bloco de evidências
          const evidenceContent: EvidenceBlockContent = {
            title: 'Evidências/Anexos',
            description: 'Arquivos anexados a esta tarefa.',
            allowUpload: true,
            allowedFileTypes: ['image/*', 'video/*', '.pdf', '.doc', '.docx', '.txt'],
            maxFileSize: 10,
            maxFiles: 10,
            showUploadedBy: true,
            showUploadDate: true,
            evidences: evidences
          };

          // 5. Obter a ordem máxima dos blocos existentes
          const { data: maxOrderBlock, error: orderError } = await supabase
            .from('task_content_blocks')
            .select('order')
            .eq('task_id', task.id)
            .order('order', { ascending: false })
            .limit(1);

          if (orderError) {
            result.errors.push(`Erro ao obter ordem dos blocos da tarefa ${task.id}: ${orderError.message}`);
            continue;
          }

          const nextOrder = maxOrderBlock && maxOrderBlock.length > 0 
            ? maxOrderBlock[0].order + 1 
            : 0;

          // 6. Criar bloco de evidências
          const { error: insertError } = await supabase
            .from('task_content_blocks')
            .insert({
              id: uuidv4(),
              task_id: task.id,
              type: 'evidence',
              content: evidenceContent,
              order: nextOrder,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            result.errors.push(`Erro ao criar bloco de evidências para tarefa ${task.id}: ${insertError.message}`);
            continue;
          }

          result.blocksCreated++;
          console.log(`Bloco de evidências criado para tarefa ${task.id} com ${evidences.length} evidências`);

        } catch (taskError) {
          result.errors.push(`Erro ao processar tarefa ${task.id}: ${taskError instanceof Error ? taskError.message : 'Erro desconhecido'}`);
          continue;
        }
      }

      if (result.errors.length > 0) {
        result.success = false;
      }

      console.log('Migração concluída:', result);
      return result;

    } catch (error) {
      result.success = false;
      result.errors.push(`Erro geral na migração: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      return result;
    }
  }

  /**
   * Verifica se uma tarefa específica precisa de migração
   */
  static async checkTaskNeedsMigration(taskId: string): Promise<boolean> {
    try {
      // Verificar se tem anexos
      const { data: attachments, error: attachError } = await supabase
        .from('task_attachments')
        .select('id')
        .eq('task_id', taskId)
        .limit(1);

      if (attachError || !attachments || attachments.length === 0) {
        return false;
      }

      // Verificar se já tem bloco de evidências
      const { data: evidenceBlocks, error: blockError } = await supabase
        .from('task_content_blocks')
        .select('id')
        .eq('task_id', taskId)
        .eq('type', 'evidence')
        .limit(1);

      if (blockError) {
        return false;
      }

      // Precisa de migração se tem anexos mas não tem bloco de evidências
      return evidenceBlocks.length === 0;

    } catch (error) {
      console.error('Erro ao verificar necessidade de migração:', error);
      return false;
    }
  }

  /**
   * Migra uma tarefa específica
   */
  static async migrateTask(taskId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const needsMigration = await this.checkTaskNeedsMigration(taskId);
      
      if (!needsMigration) {
        return { success: true };
      }

      const result = await this.migrateExistingEvidences();
      
      return {
        success: result.success,
        error: result.errors.length > 0 ? result.errors.join(', ') : undefined
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Obtém estatísticas de migração
   */
  static async getMigrationStats(): Promise<{
    totalTasksWithAttachments: number;
    tasksWithEvidenceBlocks: number;
    tasksNeedingMigration: number;
  }> {
    try {
      // Contar tarefas com anexos
      const { count: totalWithAttachments } = await supabase
        .from('task_attachments')
        .select('task_id', { count: 'exact', head: true })
        .not('task_id', 'is', null);

      // Contar tarefas com blocos de evidências
      const { count: withEvidenceBlocks } = await supabase
        .from('task_content_blocks')
        .select('task_id', { count: 'exact', head: true })
        .eq('type', 'evidence');

      return {
        totalTasksWithAttachments: totalWithAttachments || 0,
        tasksWithEvidenceBlocks: withEvidenceBlocks || 0,
        tasksNeedingMigration: (totalWithAttachments || 0) - (withEvidenceBlocks || 0)
      };

    } catch (error) {
      console.error('Erro ao obter estatísticas de migração:', error);
      return {
        totalTasksWithAttachments: 0,
        tasksWithEvidenceBlocks: 0,
        tasksNeedingMigration: 0
      };
    }
  }
}

export default EvidenceMigrationService;

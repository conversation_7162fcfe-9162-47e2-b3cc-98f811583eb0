import React, { useState } from 'react';
import { Shield, Eye, Edit, Trash2, Plus, Users, CheckCircle, Play, UserCheck, Crown, Settings, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useProjectPermissions, type Permission } from '@/hooks/usePermissions';
import { useAuth } from '@/auth/useAuth';

interface PermissionIndicatorProps {
  projectId?: string;
  context?: {
    userId?: string;
    projectOwnerId?: string;
    taskResponsibleId?: string;
    taskExecutorIds?: string[];
    taskApproverIds?: string[];
    stageResponsibleIds?: string[];
  };
  screenType: 'project' | 'stage' | 'task';
}

// Mapeamento de papéis globais para nomes em português
const getGlobalRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'Administrador';
    case 'manager':
      return 'Gerente';
    case 'member':
      return 'Membro';
    default:
      return role;
  }
};

// Ícones para papéis globais
const getGlobalRoleIcon = (role: string) => {
  switch (role) {
    case 'admin':
      return <Crown className="w-3 h-3" />;
    case 'manager':
      return <Settings className="w-3 h-3" />;
    case 'member':
      return <User className="w-3 h-3" />;
    default:
      return <User className="w-3 h-3" />;
  }
};

// Mapeamento de papéis de projeto para nomes em português
const getProjectRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'Administrador';
    case 'manager':
      return 'Gerente';
    case 'editor':
      return 'Editor';
    case 'executor':
      return 'Executor';
    case 'approver':
      return 'Aprovador';
    case 'member':
      return 'Membro';
    default:
      return role;
  }
};
const getPermissionIcon = (permission: Permission) => {
  switch (permission) {
    case 'create_project':
    case 'create_stage':
    case 'create_task':
      return <Plus className="w-4 h-4" />;
    case 'view_project':
    case 'view_stage':
    case 'view_task':
    case 'view_task_content':
      return <Eye className="w-4 h-4" />;
    case 'edit_project':
    case 'edit_stage':
    case 'edit_task':
    case 'edit_task_content':
      return <Edit className="w-4 h-4" />;
    case 'delete_project':
    case 'delete_stage':
    case 'delete_task':
      return <Trash2 className="w-4 h-4" />;
    case 'manage_project_members':
    case 'manage_stage_members':
    case 'manage_task_executors':
    case 'manage_task_approvers':
      return <Users className="w-4 h-4" />;
    case 'complete_project':
    case 'complete_stage':
    case 'complete_task':
      return <CheckCircle className="w-4 h-4" />;
    case 'execute_task':
      return <Play className="w-4 h-4" />;
    case 'approve_task':
      return <UserCheck className="w-4 h-4" />;
    default:
      return <Shield className="w-4 h-4" />;
  }
};

// Descrições das permissões
const getPermissionDescription = (permission: Permission): string => {
  const descriptions: Record<Permission, string> = {
    // Projetos
    'create_project': 'Criar novos projetos',
    'view_project': 'Visualizar projetos',
    'edit_project': 'Editar informações do projeto',
    'delete_project': 'Excluir projetos',
    'manage_project_members': 'Gerenciar membros do projeto',
    'complete_project': 'Concluir projetos',
    // Etapas
    'create_stage': 'Criar novas etapas',
    'view_stage': 'Visualizar etapas',
    'edit_stage': 'Editar informações da etapa',
    'delete_stage': 'Excluir etapas',
    'manage_stage_members': 'Gerenciar membros da etapa',
    'complete_stage': 'Concluir etapas',
    // Tarefas
    'create_task': 'Criar novas tarefas',
    'view_task': 'Visualizar tarefas',
    'edit_task': 'Editar informações da tarefa',
    'delete_task': 'Excluir tarefas',
    'view_task_content': 'Visualizar conteúdo da tarefa',
    'edit_task_content': 'Editar conteúdo da tarefa',
    'execute_task': 'Executar tarefas',
    'approve_task': 'Aprovar tarefas',
    'manage_task_executors': 'Gerenciar executores da tarefa',
    'manage_task_approvers': 'Gerenciar aprovadores da tarefa',
    'complete_task': 'Concluir tarefas'
  };
  
  return descriptions[permission] || permission;
};

// Filtrar permissões por contexto da tela
const getRelevantPermissions = (screenType: 'project' | 'stage' | 'task'): Permission[] => {
  switch (screenType) {
    case 'project':
      return [
        'create_project', 'view_project', 'edit_project', 'delete_project', 
        'manage_project_members', 'complete_project',
        'create_stage', 'view_stage'
      ];
    case 'stage':
      return [
        'view_project', 'create_stage', 'view_stage', 'edit_stage', 
        'delete_stage', 'manage_stage_members', 'complete_stage',
        'create_task', 'view_task'
      ];
    case 'task':
      return [
        'view_task', 'edit_task', 'delete_task', 'view_task_content', 
        'edit_task_content', 'execute_task', 'approve_task', 
        'manage_task_executors', 'manage_task_approvers', 'complete_task'
      ];
    default:
      return [];
  }
};

const getScreenTitle = (screenType: 'project' | 'stage' | 'task'): string => {
  switch (screenType) {
    case 'project':
      return 'Projetos';
    case 'stage':
      return 'Etapas';
    case 'task':
      return 'Tarefas';
    default:
      return 'Tela';
  }
};

export const PermissionIndicator: React.FC<PermissionIndicatorProps> = ({
  projectId,
  context,
  screenType
}) => {
  const [open, setOpen] = useState(false);
  const { user, profile } = useAuth();
  const permissions = useProjectPermissions(projectId, context);
  
  const relevantPermissions = getRelevantPermissions(screenType);
  const userPermissions = relevantPermissions.filter(permission => 
    permissions.hasPermission(permission)
  );
  const deniedPermissions = relevantPermissions.filter(permission => 
    !permissions.hasPermission(permission)
  );

  const screenTitle = getScreenTitle(screenType);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
          title={`Ver permissões nesta tela de ${screenTitle.toLowerCase()}`}
        >
          <Shield className="w-4 h-4" />
          <span className="hidden sm:inline">Minhas Permissões</span>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {userPermissions.length}
          </Badge>
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-600" />
            Suas Permissões na Tela de {screenTitle}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Informações do usuário */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-gray-600">Informações do Usuário</CardTitle>
            </CardHeader>
            <CardContent className="pt-0 space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-700">
                    {user?.email?.[0]?.toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="font-medium">{user?.email}</p>
                  <p className="text-sm text-gray-600">
                    Usuário ativo no sistema
                  </p>
                </div>
              </div>
              
              {/* Seção de Papéis */}
              <div className="border-t pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Papel Global */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Papel Global no Sistema
                    </div>
                    <Badge 
                      variant="outline" 
                      className="bg-blue-50 text-blue-800 border-blue-200 flex items-center gap-1"
                    >
                      {getGlobalRoleIcon(permissions.globalRole)}
                      {getGlobalRoleName(permissions.globalRole)}
                    </Badge>
                    <div className="text-xs text-gray-500">
                      Nível de acesso geral
                    </div>
                  </div>
                  
                  {/* Papel no Contexto */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Papel neste {screenTitle.slice(0, -1)} {/* Remove 's' do final */}
                    </div>
                    <Badge 
                      variant="outline" 
                      className="bg-green-50 text-green-800 border-green-200 flex items-center gap-1"
                    >
                      <Shield className="w-3 h-3" />
                      {permissions.contextualRole}
                    </Badge>
                    <div className="text-xs text-gray-500">
                      Função específica aqui
                    </div>
                  </div>
                </div>
              </div>

              {/* Relacionamentos específicos */}
              {(permissions.isProjectOwner || permissions.isTaskResponsible || 
                permissions.isTaskExecutor || permissions.isTaskApprover || 
                permissions.isStageResponsible) && (
                <div className="border-t pt-4">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
                    Relacionamentos Especiais
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {permissions.isProjectOwner && (
                      <Badge variant="secondary" className="bg-purple-100 text-purple-800 text-xs">
                        📋 Proprietário do Projeto
                      </Badge>
                    )}
                    {permissions.isTaskResponsible && (
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800 text-xs">
                        👤 Responsável pela Tarefa
                      </Badge>
                    )}
                    {permissions.isTaskExecutor && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">
                        ⚡ Executor da Tarefa
                      </Badge>
                    )}
                    {permissions.isTaskApprover && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                        ✅ Aprovador da Tarefa
                      </Badge>
                    )}
                    {permissions.isStageResponsible && (
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
                        🎯 Responsável pela Etapa
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Permissões permitidas */}
          {userPermissions.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-green-700 flex items-center gap-2">
                  <CheckCircle className="w-4 h-4" />
                  Permissões Concedidas ({userPermissions.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 gap-2">
                  {userPermissions.map((permission) => (
                    <div
                      key={permission}
                      className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg"
                    >
                      <div className="text-green-600">
                        {getPermissionIcon(permission)}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-green-800">
                          {getPermissionDescription(permission)}
                        </p>
                        <p className="text-xs text-green-600 font-mono">
                          {permission}
                        </p>
                      </div>
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Permissões negadas */}
          {deniedPermissions.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-red-700 flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  Permissões Restritas ({deniedPermissions.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 gap-2">
                  {deniedPermissions.map((permission) => (
                    <div
                      key={permission}
                      className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg opacity-60"
                    >
                      <div className="text-gray-400">
                        {getPermissionIcon(permission)}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-600">
                          {getPermissionDescription(permission)}
                        </p>
                        <p className="text-xs text-gray-500 font-mono">
                          {permission}
                        </p>
                      </div>
                      <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Resumo */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-blue-800">
                    Resumo de Permissões
                  </p>
                  <p className="text-sm text-blue-600">
                    {userPermissions.length} de {relevantPermissions.length} permissões disponíveis
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-700">
                    {Math.round((userPermissions.length / relevantPermissions.length) * 100)}%
                  </div>
                  <p className="text-xs text-blue-600">Acesso</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

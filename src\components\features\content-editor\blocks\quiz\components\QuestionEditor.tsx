import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { 
  Plus, 
  Trash2, 
  GripVertical,
  Clock,
  AlertCircle,
  CheckCircle,
  X,
  Save
} from 'lucide-react';
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { QuizQuestion, QuizQuestionType, QuizOption, QuizOrderingItem, QuizMatchingPair } from '@/types/quiz';

interface QuestionEditorProps {
  question: QuizQuestion;
  onChange: (question: QuizQuestion) => void;
  onSave?: () => void;
  onCancel?: () => void;
}

// Componente para opção arrastável
function SortableOption({ option, onUpdate, onDelete }: {
  option: QuizOption;
  onUpdate: (option: QuizOption) => void;
  onDelete: () => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: option.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} className="flex items-center gap-2 p-3 border rounded-lg bg-white">
      <Button
        variant="ghost"
        size="sm"
        className="cursor-grab hover:cursor-grabbing p-1"
        {...attributes}
        {...listeners}
      >
        <GripVertical className="w-4 h-4 text-gray-400" />
      </Button>
      
      <Checkbox
        checked={option.isCorrect}
        onCheckedChange={(checked) => onUpdate({ ...option, isCorrect: !!checked })}
      />
      
      <Input
        value={option.text}
        onChange={(e) => onUpdate({ ...option, text: e.target.value })}
        placeholder="Texto da opção"
        className="flex-1 standard-field"
      />
      
      <Button
        variant="ghost"
        size="sm"
        onClick={onDelete}
        className="text-red-600 hover:text-red-700"
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  );
}

export const QuestionEditor: React.FC<QuestionEditorProps> = ({
  question,
  onChange,
  onSave,
  onCancel
}) => {
  const [localQuestion, setLocalQuestion] = useState<QuizQuestion>(question);
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleQuestionChange = (updates: Partial<QuizQuestion>) => {
    const updatedQuestion = { ...localQuestion, ...updates };
    setLocalQuestion(updatedQuestion);
    onChange(updatedQuestion);
  };

  const handleAddOption = () => {
    const newOption: QuizOption = {
      id: `opt_${Date.now()}`,
      text: '',
      isCorrect: false
    };
    
    const updatedOptions = [...(localQuestion.options || []), newOption];
    handleQuestionChange({ options: updatedOptions });
  };

  const handleUpdateOption = (optionId: string, updatedOption: QuizOption) => {
    const updatedOptions = localQuestion.options?.map(opt => 
      opt.id === optionId ? updatedOption : opt
    ) || [];
    handleQuestionChange({ options: updatedOptions });
  };

  const handleDeleteOption = (optionId: string) => {
    const updatedOptions = localQuestion.options?.filter(opt => opt.id !== optionId) || [];
    handleQuestionChange({ options: updatedOptions });
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    if (active.id !== over.id && localQuestion.options) {
      const oldIndex = localQuestion.options.findIndex(opt => opt.id === active.id);
      const newIndex = localQuestion.options.findIndex(opt => opt.id === over.id);
      
      const reorderedOptions = arrayMove(localQuestion.options, oldIndex, newIndex);
      handleQuestionChange({ options: reorderedOptions });
    }
  };

  const renderQuestionTypeSpecificFields = () => {
    switch (localQuestion.type) {
      case 'single-choice':
      case 'multiple-choice':
        return (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className="standard-label">
                Opções de Resposta {localQuestion.type === 'multiple-choice' && '(múltipla seleção)'}
              </Label>
              <Button onClick={handleAddOption} size="sm" variant="outline">
                <Plus className="w-4 h-4 mr-1" />
                Adicionar Opção
              </Button>
            </div>
            
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={localQuestion.options?.map(opt => opt.id) || []} strategy={verticalListSortingStrategy}>
                <div className="space-y-2">
                  {localQuestion.options?.map((option) => (
                    <SortableOption
                      key={option.id}
                      option={option}
                      onUpdate={(updatedOption) => handleUpdateOption(option.id, updatedOption)}
                      onDelete={() => handleDeleteOption(option.id)}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
            
            {(!localQuestion.options || localQuestion.options.length === 0) && (
              <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                <p>Nenhuma opção adicionada</p>
                <Button onClick={handleAddOption} size="sm" className="mt-2">
                  Adicionar Primeira Opção
                </Button>
              </div>
            )}
          </div>
        );

      case 'true-false':
        return (
          <div className="space-y-4">
            <Label className="standard-label">Resposta Correta</Label>
            <RadioGroup
              value={localQuestion.correctAnswer?.toString() || 'true'}
              onValueChange={(value) => handleQuestionChange({ correctAnswer: value === 'true' })}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="true" />
                <Label htmlFor="true">Verdadeiro</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="false" />
                <Label htmlFor="false">Falso</Label>
              </div>
            </RadioGroup>
          </div>
        );

      case 'open-text':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="standard-label">Resposta Esperada (opcional)</Label>
              <Textarea
                value={localQuestion.openTextAnswer || ''}
                onChange={(e) => handleQuestionChange({ openTextAnswer: e.target.value })}
                placeholder="Digite a resposta esperada para correção automática"
                className="standard-field"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label className="standard-label">Palavras-chave (separadas por vírgula)</Label>
              <Input
                value={localQuestion.openTextKeywords?.join(', ') || ''}
                onChange={(e) => {
                  const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                  handleQuestionChange({ openTextKeywords: keywords });
                }}
                placeholder="palavra1, palavra2, palavra3"
                className="standard-field"
              />
              <p className="text-xs text-gray-500">
                Palavras-chave para correção automática parcial
              </p>
            </div>
          </div>
        );

      case 'ordering':
        return (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className="standard-label">Itens para Ordenação</Label>
              <Button 
                onClick={() => {
                  const newItem: QuizOrderingItem = {
                    id: `item_${Date.now()}`,
                    text: '',
                    correctOrder: (localQuestion.orderingItems?.length || 0) + 1
                  };
                  const updatedItems = [...(localQuestion.orderingItems || []), newItem];
                  handleQuestionChange({ orderingItems: updatedItems });
                }}
                size="sm" 
                variant="outline"
              >
                <Plus className="w-4 h-4 mr-1" />
                Adicionar Item
              </Button>
            </div>
            
            <div className="space-y-2">
              {localQuestion.orderingItems?.map((item, index) => (
                <div key={item.id} className="flex items-center gap-2 p-3 border rounded-lg">
                  <span className="text-sm font-medium w-8">{index + 1}.</span>
                  <Input
                    value={item.text}
                    onChange={(e) => {
                      const updatedItems = localQuestion.orderingItems?.map(i => 
                        i.id === item.id ? { ...i, text: e.target.value } : i
                      ) || [];
                      handleQuestionChange({ orderingItems: updatedItems });
                    }}
                    placeholder="Texto do item"
                    className="flex-1 standard-field"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const updatedItems = localQuestion.orderingItems?.filter(i => i.id !== item.id) || [];
                      handleQuestionChange({ orderingItems: updatedItems });
                    }}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        );

      case 'matching':
        return (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className="standard-label">Pares para Correspondência</Label>
              <Button 
                onClick={() => {
                  const newPair: QuizMatchingPair = {
                    id: `pair_${Date.now()}`,
                    left: '',
                    right: ''
                  };
                  const updatedPairs = [...(localQuestion.matchingPairs || []), newPair];
                  handleQuestionChange({ matchingPairs: updatedPairs });
                }}
                size="sm" 
                variant="outline"
              >
                <Plus className="w-4 h-4 mr-1" />
                Adicionar Par
              </Button>
            </div>
            
            <div className="space-y-2">
              {localQuestion.matchingPairs?.map((pair, index) => (
                <div key={pair.id} className="grid grid-cols-2 gap-2 p-3 border rounded-lg">
                  <Input
                    value={pair.left}
                    onChange={(e) => {
                      const updatedPairs = localQuestion.matchingPairs?.map(p => 
                        p.id === pair.id ? { ...p, left: e.target.value } : p
                      ) || [];
                      handleQuestionChange({ matchingPairs: updatedPairs });
                    }}
                    placeholder="Item da coluna A"
                    className="standard-field"
                  />
                  <div className="flex gap-2">
                    <Input
                      value={pair.right}
                      onChange={(e) => {
                        const updatedPairs = localQuestion.matchingPairs?.map(p => 
                          p.id === pair.id ? { ...p, right: e.target.value } : p
                        ) || [];
                        handleQuestionChange({ matchingPairs: updatedPairs });
                      }}
                      placeholder="Item da coluna B"
                      className="flex-1 standard-field"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const updatedPairs = localQuestion.matchingPairs?.filter(p => p.id !== pair.id) || [];
                        handleQuestionChange({ matchingPairs: updatedPairs });
                      }}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Editar Pergunta</span>
          <div className="flex gap-2">
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                <X className="w-4 h-4 mr-2" />
                Cancelar
              </Button>
            )}
            {onSave && (
              <Button onClick={onSave}>
                <Save className="w-4 h-4 mr-2" />
                Salvar
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Configurações Básicas */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="standard-label">Tipo de Pergunta</Label>
            <Select 
              value={localQuestion.type} 
              onValueChange={(value: QuizQuestionType) => handleQuestionChange({ type: value })}
            >
              <SelectTrigger className="standard-field">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="single-choice">Escolha Única</SelectItem>
                <SelectItem value="multiple-choice">Múltipla Escolha</SelectItem>
                <SelectItem value="true-false">Verdadeiro/Falso</SelectItem>
                <SelectItem value="open-text">Resposta Aberta</SelectItem>
                <SelectItem value="ordering">Ordenação</SelectItem>
                <SelectItem value="matching">Correspondência</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label className="standard-label">Pontuação</Label>
            <Input
              type="number"
              value={localQuestion.points}
              onChange={(e) => handleQuestionChange({ points: parseInt(e.target.value) || 1 })}
              min="1"
              max="100"
              className="standard-field"
            />
          </div>
        </div>

        {/* Título e Descrição */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="standard-label">Título da Pergunta</Label>
            <Input
              value={localQuestion.title}
              onChange={(e) => handleQuestionChange({ title: e.target.value })}
              placeholder="Digite o título da pergunta"
              className="standard-field"
            />
          </div>
          
          <div className="space-y-2">
            <Label className="standard-label">Descrição (opcional)</Label>
            <Textarea
              value={localQuestion.description || ''}
              onChange={(e) => handleQuestionChange({ description: e.target.value })}
              placeholder="Descrição adicional ou contexto"
              className="standard-field"
              rows={3}
            />
          </div>
        </div>

        {/* Configurações Específicas do Tipo */}
        {renderQuestionTypeSpecificFields()}

        {/* Configurações Avançadas */}
        <div className="space-y-4 border-t pt-4">
          <h4 className="font-medium">Configurações Avançadas</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={localQuestion.required}
                onCheckedChange={(checked) => handleQuestionChange({ required: checked })}
              />
              <Label htmlFor="required" className="standard-label">
                Pergunta obrigatória
              </Label>
            </div>
            
            <div className="space-y-2">
              <Label className="standard-label">Tempo limite (segundos)</Label>
              <Input
                type="number"
                value={localQuestion.timeLimit || ''}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || undefined;
                  handleQuestionChange({ timeLimit: value });
                }}
                placeholder="Sem limite"
                className="standard-field"
                min="10"
                max="600"
              />
            </div>
          </div>
        </div>

        {/* Feedback */}
        <div className="space-y-4 border-t pt-4">
          <h4 className="font-medium">Feedback</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="standard-label">Feedback para resposta correta</Label>
              <Textarea
                value={localQuestion.correctFeedback || ''}
                onChange={(e) => handleQuestionChange({ correctFeedback: e.target.value })}
                placeholder="Mensagem quando acertar"
                className="standard-field"
                rows={2}
              />
            </div>
            
            <div className="space-y-2">
              <Label className="standard-label">Feedback para resposta incorreta</Label>
              <Textarea
                value={localQuestion.incorrectFeedback || ''}
                onChange={(e) => handleQuestionChange({ incorrectFeedback: e.target.value })}
                placeholder="Mensagem quando errar"
                className="standard-field"
                rows={2}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label className="standard-label">Explicação (opcional)</Label>
            <Textarea
              value={localQuestion.explanation || ''}
              onChange={(e) => handleQuestionChange({ explanation: e.target.value })}
              placeholder="Explicação detalhada da resposta"
              className="standard-field"
              rows={3}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

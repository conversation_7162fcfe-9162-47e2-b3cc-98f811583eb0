/* CSS para melhorar responsividade dos blocos no mobile */

/* Container principal dos blocos */
.content-blocks-container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Cards dos blocos - responsividade melhorada */
.block-card-mobile {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  margin-bottom: 1rem;
}

/* Header do bloco - layout flexível */
.block-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  min-height: 56px;
  padding: 0.75rem;
}

/* Badge do tipo de bloco - responsivo */
.block-type-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
  margin-right: 0.5rem;
  padding: 0.25rem 0.75rem;
  background-color: #f3f4f6;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
}

/* Ícone do tipo de bloco */
.block-type-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Label do tipo de bloco */
.block-type-label {
  flex-shrink: 0;
  white-space: nowrap;
}

/* Título/descrição do bloco */
.block-content-title {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #6b7280;
  font-weight: 400;
}

/* Container dos botões de ação */
.block-actions {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  flex-shrink: 0;
}

/* Botões de ação - mobile first */
.block-action-button {
  height: 2.25rem;
  width: 2.25rem;
  padding: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.block-action-button:hover {
  background-color: #f3f4f6;
}

.block-action-button:active {
  background-color: #e5e7eb;
}

/* Botão de arrastar */
.block-drag-button {
  cursor: grab;
  touch-action: manipulation;
}

.block-drag-button:active {
  cursor: grabbing;
}

/* Botão de excluir */
.block-delete-button {
  color: #dc2626;
}

.block-delete-button:hover {
  color: #b91c1c;
  background-color: #fef2f2;
}

/* Ícones dos botões */
.block-action-icon {
  width: 1rem;
  height: 1rem;
}

.block-drag-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsividade para tablets e desktop */
@media (min-width: 768px) {
  .block-action-button {
    height: 2rem;
    width: 2rem;
  }
  
  .block-action-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .block-drag-icon {
    width: 1rem;
    height: 1rem;
  }
}

/* Melhorias para telas muito pequenas */
@media (max-width: 480px) {
  .block-header {
    padding: 0.5rem;
    gap: 0.25rem;
  }
  
  .block-type-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
    margin-right: 0.25rem;
  }
  
  .block-actions {
    gap: 0.125rem;
  }
  
  .block-content-title {
    max-width: 120px;
  }
}

/* Estados de colapso */
.block-collapsed {
  background-color: #f9fafb;
  opacity: 0.9;
}

.block-collapsed .block-header {
  min-height: 56px;
  margin-bottom: 0;
}

/* Animações suaves */
.block-transition {
  transition: all 0.2s ease-in-out;
}

/* Drag and drop states */
.block-dragging {
  opacity: 0.5;
  z-index: 10;
}

.block-drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* Acessibilidade - foco visível */
.block-action-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Garantir que os botões sejam sempre visíveis */
.block-actions {
  min-width: fit-content;
}

/* Overflow protection */
.block-card-mobile {
  overflow-x: auto;
  overflow-y: visible;
}

/* Scroll horizontal suave se necessário */
.block-header {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.block-header::-webkit-scrollbar {
  display: none;
}

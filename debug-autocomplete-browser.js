// =====================================================
// DEBUG AUTOCOMPLETE - EXECUTAR NO CONSOLE DO BROWSER
// =====================================================
// Cole este código no console quando estiver na tela de detalhes da tarefa

console.log('🔍 INICIANDO DEBUG DO AUTOCOMPLETE...');

// Função para verificar estado dos projectMembers
function debugProjectMembers() {
  console.log('📊 VERIFICANDO PROJECT MEMBERS...');
  
  // Verificar se há dados no React DevTools (se disponível)
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('⚛️ React DevTools detectado');
  }
  
  // Verificar localStorage/sessionStorage
  const storageKeys = Object.keys(localStorage).filter(key => 
    key.includes('project') || key.includes('member') || key.includes('user')
  );
  console.log('💾 Chaves relevantes no localStorage:', storageKeys);
  
  // Verificar se há elementos UserAutocomplete na página
  const autoCompleteInputs = document.querySelectorAll('input[placeholder*="Buscar"]');
  console.log('🔍 Inputs de autocomplete encontrados:', autoCompleteInputs.length);
  
  autoCompleteInputs.forEach((input, index) => {
    console.log(`📝 Input ${index + 1}:`, {
      placeholder: input.placeholder,
      value: input.value,
      parent: input.closest('[class*="card"]')?.querySelector('p')?.textContent
    });
  });
}

// Função para testar chamadas de API
async function debugApiCalls() {
  console.log('🌐 TESTANDO CHAMADAS DE API...');
  
  try {
    // Verificar se supabase está disponível
    if (typeof window.supabase !== 'undefined') {
      console.log('✅ Supabase disponível');
      
      // Testar busca de profiles
      const { data: profiles, error: profilesError } = await window.supabase
        .from('profiles')
        .select('id, name, email, is_active')
        .eq('is_active', true)
        .limit(5);
        
      if (profilesError) {
        console.error('❌ Erro ao buscar profiles:', profilesError);
      } else {
        console.log('✅ Profiles encontrados:', profiles?.length || 0, profiles);
      }
      
      // Testar busca de project_members
      const { data: members, error: membersError } = await window.supabase
        .from('project_members')
        .select('id, project_id, user_id, role')
        .limit(5);
        
      if (membersError) {
        console.error('❌ Erro ao buscar project_members:', membersError);
      } else {
        console.log('✅ Project members encontrados:', members?.length || 0, members);
      }
      
    } else {
      console.log('❌ Supabase não está disponível no window');
    }
  } catch (error) {
    console.error('❌ Erro nas chamadas de API:', error);
  }
}

// Função para simular eventos no autocomplete
function debugAutocompleteEvents() {
  console.log('🎯 TESTANDO EVENTOS DO AUTOCOMPLETE...');
  
  const inputs = document.querySelectorAll('input[placeholder*="Buscar"]');
  
  inputs.forEach((input, index) => {
    console.log(`🎮 Testando input ${index + 1}...`);
    
    // Simular foco
    input.focus();
    console.log(`  Focus aplicado no input ${index + 1}`);
    
    // Simular digitação
    setTimeout(() => {
      input.value = 'a';
      input.dispatchEvent(new Event('input', { bubbles: true }));
      console.log(`  Texto 'a' inserido no input ${index + 1}`);
      
      // Verificar se dropdown apareceu
      setTimeout(() => {
        const dropdown = input.parentElement?.querySelector('ul');
        if (dropdown) {
          console.log(`  ✅ Dropdown encontrado para input ${index + 1}:`, dropdown.children.length, 'itens');
        } else {
          console.log(`  ❌ Dropdown não encontrado para input ${index + 1}`);
        }
      }, 500);
    }, 100);
  });
}

// Função para verificar console logs
function debugConsoleLogs() {
  console.log('📝 VERIFICANDO LOGS DO CONSOLE...');
  
  // Interceptar console.log para capturar logs do UserAutocomplete
  const originalLog = console.log;
  const autocompleteLogs = [];
  
  console.log = function(...args) {
    if (args.some(arg => typeof arg === 'string' && arg.includes('[UserAutocomplete]'))) {
      autocompleteLogs.push(args);
    }
    originalLog.apply(console, args);
  };
  
  // Restaurar após 5 segundos
  setTimeout(() => {
    console.log = originalLog;
    console.log('📊 LOGS DO USERAUTOCOMPLETE CAPTURADOS:', autocompleteLogs);
  }, 5000);
}

// Executar todos os debugs
async function runFullDebug() {
  console.log('🚀 EXECUTANDO DEBUG COMPLETO...');
  
  debugProjectMembers();
  await debugApiCalls();
  debugConsoleLogs();
  
  // Aguardar um pouco antes de testar eventos
  setTimeout(() => {
    debugAutocompleteEvents();
  }, 1000);
  
  console.log('✅ DEBUG INICIADO - Verifique os logs acima e abaixo');
  console.log('💡 Dica: Tente focar nos inputs de busca para ver se os logs aparecem');
}

// Executar
runFullDebug();

// Função auxiliar para executar individualmente
window.debugAutocomplete = {
  members: debugProjectMembers,
  api: debugApiCalls,
  events: debugAutocompleteEvents,
  logs: debugConsoleLogs,
  full: runFullDebug
};

console.log('🛠️ FUNÇÕES DE DEBUG DISPONÍVEIS:');
console.log('  debugAutocomplete.members() - Verificar project members');
console.log('  debugAutocomplete.api() - Testar chamadas de API');
console.log('  debugAutocomplete.events() - Testar eventos do autocomplete');
console.log('  debugAutocomplete.logs() - Monitorar logs');
console.log('  debugAutocomplete.full() - Executar tudo');

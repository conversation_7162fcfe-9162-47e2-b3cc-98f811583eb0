-- =====================
-- CORREÇÃO: ERRO 500 EM CONSULTAS DE PROFILES
-- Data: 2024-12-19
-- Problema: Políticas RLS causando erro interno do servidor
-- =====================

-- PASSO 1: Remover políticas problemáticas temporariamente
-- =====================
drop policy if exists "Allow users to view profiles for collaboration" on public.profiles;
drop policy if exists "Temporary allow authenticated users to view profiles" on public.profiles;

-- PASSO 2: Criar política mais simples e segura
-- =====================
create policy "Simple authenticated users can view profiles"
  on public.profiles
  for select
  using (
    -- Usuário pode ver próprio perfil
    id = auth.uid() or
    -- Admin pode ver todos (se função is_admin funcionar)
    (
      exists(select 1 from pg_proc where proname = 'is_admin') and
      public.is_admin(auth.uid())
    ) or
    -- Qualquer usuário autenticado pode ver outros (temporário)
    auth.uid() is not null
  );

-- PASSO 3: Verificar se as políticas básicas existem
-- =====================
-- Política para usuário ver próprio perfil
drop policy if exists "Users can view their own profile" on public.profiles;
create policy "Users can view their own profile"
  on public.profiles
  for select
  using (id = auth.uid());

-- Política para inserção de próprio perfil
drop policy if exists "Users can insert their own profile" on public.profiles;
create policy "Users can insert their own profile"
  on public.profiles
  for insert
  with check (id = auth.uid());

-- Política para atualização de próprio perfil
drop policy if exists "Users can update their own profile" on public.profiles;
create policy "Users can update their own profile"
  on public.profiles
  for update
  using (id = auth.uid());

-- PASSO 4: Políticas de admin (se função is_admin existir)
-- =====================
do $$
begin
  -- Verificar se função is_admin existe
  if exists(select 1 from pg_proc where proname = 'is_admin') then
    -- Política para admin ver todos
    execute 'drop policy if exists "Admin can view all profiles" on public.profiles';
    execute 'create policy "Admin can view all profiles"
      on public.profiles
      for select
      using (public.is_admin(auth.uid()))';
    
    -- Política para admin inserir qualquer perfil
    execute 'drop policy if exists "Admin can insert any profile" on public.profiles';
    execute 'create policy "Admin can insert any profile"
      on public.profiles
      for insert
      with check (public.is_admin(auth.uid()))';
    
    -- Política para admin atualizar qualquer perfil
    execute 'drop policy if exists "Admin can update all profiles" on public.profiles';
    execute 'create policy "Admin can update all profiles"
      on public.profiles
      for update
      using (public.is_admin(auth.uid()))';
    
    raise notice 'Políticas de admin criadas com sucesso';
  else
    raise notice 'Função is_admin não encontrada, políticas de admin não criadas';
  end if;
end $$;

-- PASSO 5: Garantir que RLS está ativado
-- =====================
alter table public.profiles enable row level security;

-- PASSO 6: Testar se as políticas funcionam
-- =====================
-- Verificar políticas criadas
select 
    'Políticas de profiles:' as info,
    policyname,
    cmd,
    permissive
from pg_policies 
where tablename = 'profiles'
order by policyname;

-- Verificar se função is_admin existe
select 
    case 
        when exists(select 1 from pg_proc where proname = 'is_admin') 
        then 'Função is_admin existe'
        else 'Função is_admin NÃO existe'
    end as status;

-- PASSO 7: Instruções para teste
-- =====================
select 'CORREÇÃO APLICADA - Teste agora:' as status;
select '1. Acesse UserManagement ou TaskDetailsV2' as instrucao;
select '2. Verifique se erro 500 foi resolvido' as instrucao;
select '3. Se ainda houver erro, execute fix_rls_emergency.sql' as instrucao;

# 🔐 Sistema de Permissões Centralizado

## 📋 Visão Geral

Este documento explica como usar o sistema de permissões centralizado implementado para o projeto.

## 🔐 Matriz Completa de Permissões

### **Permissões de Projeto**
- `create_project` - Criar novos projetos
- `view_project` - Visualizar projetos 
- `edit_project` - Editar informações do projeto
- `delete_project` - Excluir projetos
- `manage_project_members` - Gerenciar membros do projeto
- `complete_project` - Concluir projetos

### **Permissões de Etapas**
- `create_stage` - Criar novas etapas
- `view_stage` - Visualizar etapas
- `edit_stage` - Editar informações da etapa
- `delete_stage` - Excluir etapas
- `manage_stage_members` - Gerenciar membros da etapa
- `complete_stage` - Concluir etapas

### **Permissões de Tarefas**
- `create_task` - Criar novas tarefas
- `view_task` - Visual<PERSON>r tarefas
- `edit_task` - Editar informações da tarefa
- `delete_task` - Excluir tarefas
- `view_task_content` - Visualizar conteúdo da tarefa
- `edit_task_content` - Editar conteúdo da tarefa
- `execute_task` - Executar tarefas
- `approve_task` - Aprovar tarefas
- `manage_task_executors` - Gerenciar executores da tarefa
- `manage_task_approvers` - Gerenciar aprovadores da tarefa
## 🛠️ Como Usar as Permissões

### 1. Hook usePermissions

```typescript
import { usePermissions } from '@/hooks/usePermissions';

function MyComponent() {
  const { hasPermission, loading } = usePermissions();
  
  // Verificar permissão específica
  if (hasPermission('create_project')) {
    // Usuário pode criar projetos
  }
  
  // Verificar múltiplas permissões
  if (hasPermission(['edit_task', 'delete_task'])) {
    // Usuário pode editar E excluir tarefas
  }
}
```

### 2. Componente de Proteção

```typescript
import { ProtectedComponent } from '@/components/ProtectedComponent';

<ProtectedComponent permission="create_project">
  <CreateProjectButton />
</ProtectedComponent>
```

### 3. Verificação por Papel (Role)

```typescript
// Verificar se usuário tem papel específico
const isAdmin = userRole === 'admin';
const isManager = userRole === 'manager';
```

## 📊 Matriz de Permissões por Papel

| Permissão | Admin | Manager | Coordinator | Member | Observer |
|-----------|-------|---------|-------------|---------|----------|
| **Projetos** |
| create_project | ✅ | ✅ | ❌ | ❌ | ❌ |
| view_project | ✅ | ✅ | ✅ | ✅ | ✅ |
| edit_project | ✅ | ✅ | ✅ | ❌ | ❌ |
| delete_project | ✅ | ❌ | ❌ | ❌ | ❌ |
| manage_project_members | ✅ | ✅ | ❌ | ❌ | ❌ |
| complete_project | ✅ | ✅ | ✅ | ❌ | ❌ |
| **Etapas** |
| create_stage | ✅ | ✅ | ✅ | ❌ | ❌ |
| view_stage | ✅ | ✅ | ✅ | ✅ | ✅ |
| edit_stage | ✅ | ✅ | ✅ | ❌ | ❌ |
| delete_stage | ✅ | ✅ | ❌ | ❌ | ❌ |
| manage_stage_members | ✅ | ✅ | ✅ | ❌ | ❌ |
| complete_stage | ✅ | ✅ | ✅ | ❌ | ❌ |
| **Tarefas** |
| create_task | ✅ | ✅ | ✅ | ✅ | ❌ |
| view_task | ✅ | ✅ | ✅ | ✅ | ✅ |
| edit_task | ✅ | ✅ | ✅ | ✅ | ❌ |
| delete_task | ✅ | ✅ | ✅ | ❌ | ❌ |
| view_task_content | ✅ | ✅ | ✅ | ✅ | ✅ |
| edit_task_content | ✅ | ✅ | ✅ | ✅ | ❌ |
| execute_task | ✅ | ✅ | ✅ | ✅ | ❌ |
| approve_task | ✅ | ✅ | ✅ | ❌ | ❌ |
| manage_task_executors | ✅ | ✅ | ✅ | ❌ | ❌ |
| manage_task_approvers | ✅ | ✅ | ❌ | ❌ | ❌ |
| complete_task | ✅ | ✅ | ✅ | ✅ | ❌ |

## 🚨 Segurança e Boas Práticas

### Princípios de Segurança

1. **Princípio do Menor Privilégio**: Usuários devem ter apenas as permissões mínimas necessárias
2. **Verificação Dupla**: Validação tanto no frontend quanto no backend
3. **Auditoria**: Log de todas as ações sensíveis

### Implementação Segura

```typescript
// ❌ ERRADO - Apenas verificação frontend
if (userRole === 'admin') {
  deleteProject();
}

// ✅ CORRETO - Verificação com hook centralizado
if (hasPermission('delete_project')) {
  deleteProject(); // Backend também valida
}
```

## 🔧 Manutenção do Sistema

### Adicionando Novas Permissões

1. Atualizar `PermissionType` em `usePermissions.ts`
2. Adicionar à matriz `PROJECT_PERMISSIONS`
3. Implementar validação no backend
4. Atualizar documentação

### Testando Permissões

```typescript
// Teste unitário exemplo
describe('usePermissions', () => {
  it('should allow admin to delete project', () => {
    const { hasPermission } = renderHook(() => 
      usePermissions({ role: 'admin' })
    );
    
    expect(hasPermission('delete_project')).toBe(true);
  });
});
```

## 🎯 Componentes Principais

### 1. Hook de Permissões de Projeto
```tsx
import { useProjectPermissions } from '@/hooks/usePermissions';

const MyComponent = ({ projectId, task }) => {
  const permissionContext = {
    userId: user?.id,
    projectOwnerId: project?.owner_id,
    taskResponsibleId: task?.responsible?.id,
    taskExecutorIds: task?.executors?.map(e => e.id),
    taskApproverIds: task?.approvers?.map(a => a.id)
  };

  const {
    canEditProject,
    canManageMembers,
    canEditExecutors,
    canEditTask,
    hasPermission
  } = useProjectPermissions(projectId, permissionContext);

  // Usar as permissões
  if (canEditProject) {
    // Mostrar botão de editar projeto
  }
};
```

### 2. Hook de Permissões Globais
```tsx
import { useGlobalPermissions } from '@/hooks/usePermissions';

const MyComponent = () => {
  const { 
    canManageUsers, 
    canCreateProjects, 
    isAdmin, 
    isManager 
  } = useGlobalPermissions();

  if (canManageUsers) {
    // Mostrar interface de gerenciamento de usuários
  }
};
```

### 3. Wrappers de Proteção

#### RequirePermission
```tsx
import { RequirePermission } from '@/components/auth/PermissionWrappers';

<RequirePermission 
  permissions={['edit_task', 'manage_task_executors']} 
  projectId={projectId}
  userId={user?.id}
  taskResponsibleId={task?.responsible?.id}
>
  <Button>Editar Tarefa</Button>
</RequirePermission>
```

#### RequireProjectRole
```tsx
import { RequireProjectRole } from '@/components/auth/PermissionWrappers';

<RequireProjectRole 
  roles={['admin', 'manager']} 
  projectId={projectId}
  projectOwnerId={project?.owner_id}
>
  <Button>Gerenciar Projeto</Button>
</RequireProjectRole>
```

#### RequireGlobalRole
```tsx
import { RequireGlobalRole } from '@/components/auth/PermissionWrappers';

<RequireGlobalRole roles="admin">
  <UserManagementInterface />
</RequireGlobalRole>
```

## 📊 Matriz de Permissões

### Permissões Disponíveis
- `view_project` - Ver projeto
- `edit_project` - Editar projeto
- `manage_project_members` - Gerenciar membros
- `create_stage` - Criar etapas
- `edit_stage` - Editar etapas
- `create_task` - Criar tarefas
- `edit_task` - Editar tarefas
- `execute_task` - Executar tarefas
- `approve_task` - Aprovar tarefas
- `manage_task_executors` - Gerenciar executores
- `manage_task_approvers` - Gerenciar aprovadores

### Roles de Projeto
- **admin**: Todas as permissões
- **manager**: Gerência completa (exceto delete_project)
- **editor**: Criação e edição de conteúdo
- **executor**: Execução de tarefas
- **approver**: Aprovação de tarefas
- **member**: Visualização apenas

## 🔄 Migração de Código Antigo

### Antes (Hardcoded)
```tsx
// ❌ EVITAR - Lógica hardcoded
const canEdit = user?.role === 'admin' || 
               user?.role === 'manager' || 
               user?.id === project?.owner_id;

{canEdit && <EditButton />}
```

### Depois (Centralizado)
```tsx
// ✅ USAR - Sistema centralizado
const { canEditProject } = useProjectPermissions(projectId, context);

<RequirePermission permissions="edit_project" projectId={projectId}>
  <EditButton />
</RequirePermission>
```

## 🚨 Importante

1. **Sempre usar contexto**: Forneça o máximo de contexto possível para as verificações de permissão
2. **Prefer wrappers**: Use componentes `Require*` em vez de condicionais inline
3. **Não misturar sistemas**: Evite misturar verificações hardcoded com o sistema centralizado

## 🔮 Próximos Passos

1. **Integração com RLS**: As permissões serão sincronizadas com políticas RLS no banco
2. **Cache de roles**: Implementar cache para roles de projeto por usuário
3. **Auditoria**: Adicionar logs de tentativas de acesso negado

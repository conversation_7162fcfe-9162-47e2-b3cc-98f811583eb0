-- =====================================================
-- VERIFICAÇÃO RÁPIDA DO ENUM project_member_role
-- =====================================================

-- 1. Ver se o enum existe e seus valores
SELECT 
    'ENUM EXISTE?' as verificacao,
    typname as nome_enum,
    typtype as tipo
FROM pg_type 
WHERE typname = 'project_member_role';

-- 2. Ver valores do enum (método alternativo)
SELECT 
    'VALORES DO ENUM' as tipo,
    unnest(enum_range(NULL::project_member_role)) as valor_valido;

-- 3. Testar inserção manual simples
-- Vamos ver se conseguimos inserir um membro manualmente
SELECT 'TESTE DE INSERÇÃO' as teste;

-- Primeiro, ver dados disponíveis
SELECT 
    'DADOS PARA TESTE' as info,
    (SELECT id FROM projects ORDER BY created_at DESC LIMIT 1) as project_id,
    (SELECT id FROM profiles WHERE is_active = true LIMIT 1) as user_id;

-- 4. Verificar se existe algum membro atual
SELECT 
    'MEMBROS ATUAIS' as tipo,
    COUNT(*) as total
FROM project_members;

-- 5. Ver estrutura completa da tabela
SELECT 
    'ESTRUTURA DA TABELA' as tipo,
    column_name,
    data_type,
    udt_name,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'project_members'
AND table_schema = 'public'
ORDER BY ordinal_position;

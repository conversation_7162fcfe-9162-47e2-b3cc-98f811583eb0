import React, { useState, useEffect, useRef, useCallback } from 'react';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
));
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

interface ResizableDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  children: React.ReactNode;
  className?: string;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export const ResizableDialog: React.FC<ResizableDialogProps> = ({
  open,
  onOpenChange,
  title,
  children,
  className,
  minWidth = 400,
  minHeight = 300,
  maxWidth = window.innerWidth * 0.9,
  maxHeight = window.innerHeight * 0.9,
}) => {
  // Calcular largura inicial responsiva
  const getInitialWidth = () => {
    const screenWidth = window.innerWidth;
    if (screenWidth < 768) { // Mobile
      return Math.min(screenWidth * 0.95, 400);
    } else if (screenWidth < 1024) { // Tablet
      return Math.min(screenWidth * 0.8, 700);
    } else { // Desktop
      return Math.min(screenWidth * 0.7, 900);
    }
  };
  
  const initialWidth = getInitialWidth();
  const initialHeight = 500;
  
  const [size, setSize] = useState({ width: initialWidth, height: initialHeight });
  const [position, setPosition] = useState({ 
    x: (window.innerWidth - initialWidth) / 2, 
    y: (window.innerHeight - initialHeight) / 2 
  });
  const [isResizing, setIsResizing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);
  const startPos = useRef({ x: 0, y: 0 });
  const startSize = useRef({ width: 0, height: 0 });
  const startPosition = useRef({ x: 0, y: 0 });

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setIsResizing(true);
      startPos.current = { x: e.clientX, y: e.clientY };
      startSize.current = { ...size };
      e.preventDefault();
    }
  }, [size]);

  const handleDragStart = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    startPos.current = { x: e.clientX, y: e.clientY };
    startPosition.current = { ...position };
    e.preventDefault();
  }, [position]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isResizing) {
      const deltaX = e.clientX - startPos.current.x;
      const deltaY = e.clientY - startPos.current.y;

      const newWidth = Math.max(minWidth, Math.min(maxWidth, startSize.current.width + deltaX));
      const newHeight = Math.max(minHeight, Math.min(maxHeight, startSize.current.height + deltaY));

      setSize({ width: newWidth, height: newHeight });
    } else if (isDragging) {
      const deltaX = e.clientX - startPos.current.x;
      const deltaY = e.clientY - startPos.current.y;

      const newX = startPosition.current.x + deltaX;
      const newY = startPosition.current.y + deltaY;

      // Limitar a posição para manter o modal visível
      const maxX = window.innerWidth - 100; // Manter pelo menos 100px visível
      const maxY = window.innerHeight - 100;
      const minX = -size.width + 100;
      const minY = -size.height + 100; // Permitir movimento para cima

      setPosition({
        x: Math.max(minX, Math.min(maxX, newX)),
        y: Math.max(minY, Math.min(maxY, newY))
      });
    }
  }, [isResizing, isDragging, minWidth, maxWidth, minHeight, maxHeight, size.width]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    setIsDragging(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove]);

  const handleResize = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    startPos.current = { x: e.clientX, y: e.clientY };
    startSize.current = { width: size.width, height: size.height };
  }, [size]);

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // Ajustar tamanho do modal quando a janela for redimensionada
  useEffect(() => {
    const handleWindowResize = () => {
      const newInitialWidth = getInitialWidth();
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      
      // Ajustar tamanho se necessário (apenas se o modal for maior que a tela)
      setSize(prevSize => ({
        width: Math.min(prevSize.width, screenWidth * 0.9),
        height: Math.min(prevSize.height, screenHeight * 0.9)
      }));
      
      // Recentrar o modal se ele sair da tela
      setPosition(prevPos => ({
        x: Math.max(0, Math.min(prevPos.x, screenWidth - size.width)),
        y: Math.max(0, Math.min(prevPos.y, screenHeight - size.height))
      }));
    };

    window.addEventListener('resize', handleWindowResize);
    return () => window.removeEventListener('resize', handleWindowResize);
  }, [size.width, size.height]);

  return (
    <DialogPrimitive.Root open={open} onOpenChange={onOpenChange}>
      <DialogPrimitive.Portal>
        <DialogOverlay />
        <DialogPrimitive.Content
          ref={dialogRef}
          className={cn(
            'fixed left-0 top-0 z-50 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg',
            'resize-none overflow-hidden flex flex-col',
            className
          )}
          style={{
            width: `${size.width}px`,
            height: `${size.height}px`,
            maxWidth: 'none',
            maxHeight: 'none',
            transform: `translate(${position.x}px, ${position.y}px)`,
            cursor: isDragging ? 'grabbing' : 'default',
          }}
          aria-describedby="resizable-dialog-description"
        >
          {/* Hidden description for accessibility */}
          <DialogPrimitive.Description id="resizable-dialog-description" className="sr-only">
            Modal redimensionável e arrastável para edição de conteúdo
          </DialogPrimitive.Description>
          {/* Header */}
          <div 
            className="flex items-center justify-between p-6 border-b flex-shrink-0 cursor-grab active:cursor-grabbing select-none bg-gray-50 hover:bg-gray-100 transition-colors"
            onMouseDown={handleDragStart}
          >
            {/* Drag indicator */}
            <div className="absolute left-1/2 top-3 transform -translate-x-1/2 flex space-x-1">
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
              <div className="w-1 h-1 bg-gray-500 rounded-full"></div>
            </div>
            <DialogPrimitive.Title className="text-lg font-semibold leading-none tracking-tight">
              {title}
            </DialogPrimitive.Title>
            <DialogPrimitive.Close className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogPrimitive.Close>
          </div>
          
          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {children}
          </div>

          {/* Resize handle */}
          <div
            className="absolute bottom-0 right-0 w-6 h-6 cursor-se-resize hover:bg-gray-200 transition-colors flex items-center justify-center"
            style={{
              background: 'linear-gradient(-45deg, transparent 30%, #9ca3af 30%, #9ca3af 40%, transparent 40%, transparent 60%, #9ca3af 60%, #9ca3af 70%, transparent 70%)',
            }}
            onMouseDown={handleResize}
            title="Arrastar para redimensionar"
          />
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </DialogPrimitive.Root>
  );
};

export default ResizableDialog;
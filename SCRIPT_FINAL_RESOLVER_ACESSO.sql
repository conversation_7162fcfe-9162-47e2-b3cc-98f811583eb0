-- =====================================================
-- SCRIPT FINAL: RESOLVER PROBLEMA DE ACESSO AGORA
-- Execute este script para resolver o problema imediato
-- =====================================================

-- STEP 1: IDENTIFICAR O PROBLEMA
SELECT 
    'DIAGNÓSTICO' as fase,
    'Usuário atual: 4b09be1f-5187-44c0-9b53-87b7c57e45b4' as info;

-- STEP 2: VERIFICAR SE O USUÁRIO EXISTE EM AUTH.USERS
SELECT 
    'USUÁRIO EM AUTH.USERS' as verificacao,
    u.id,
    u.email,
    u.created_at
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 3: VERIFICAR SE JÁ TEM PROFILE
SELECT 
    'PROFILE EXISTENTE' as verificacao,
    p.id,
    p.name,
    p.email,
    p.role
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 4: CRIAR PROFILE COM EMAIL ÚNICO (GARANTIDO)
-- Usando timestamp para garantir unicidade total
INSERT INTO profiles (id, name, email, role, is_active, created_at, updated_at)
SELECT 
    u.id,
    COALESCE(
        u.raw_user_meta_data->>'name',
        u.raw_user_meta_data->>'full_name',
        SPLIT_PART(u.email, '@', 1),
        'Usuário'
    ) as name,
    -- Email único com timestamp
    SPLIT_PART(u.email, '@', 1) || '_' || SUBSTRING(u.id::text, 1, 8) || '@' || SPLIT_PART(u.email, '@', 2) as email,
    'member' as role,
    true as is_active,
    u.created_at,
    NOW()
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
    AND NOT EXISTS (
        SELECT 1 FROM profiles p WHERE p.id = u.id
    );

-- STEP 5: VERIFICAR SE PROFILE FOI CRIADO
SELECT 
    'PROFILE CRIADO COM SUCESSO' as resultado,
    p.id,
    p.name,
    p.email,
    p.role,
    p.is_active
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 6: VERIFICAR SE JÁ É EXECUTOR DA TAREFA
SELECT 
    'STATUS EXECUTOR' as verificacao,
    CASE 
        WHEN COUNT(*) > 0 THEN 'JÁ É EXECUTOR'
        ELSE 'NÃO É EXECUTOR'
    END as status
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 7: ADICIONAR COMO EXECUTOR DA TAREFA
INSERT INTO task_executors (task_id, user_id, created_at)
SELECT 
    '7c606667-9391-4660-933d-90d6bd276e88',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM task_executors 
    WHERE task_id = '7c606667-9391-4660-933d-90d6bd276e88' 
      AND user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
);

-- STEP 8: VERIFICAÇÃO FINAL COMPLETA
SELECT 
    'VERIFICAÇÃO FINAL' as fase,
    u.id as user_id,
    u.email as user_email,
    p.id as profile_id,
    p.email as profile_email,
    p.name as profile_name,
    p.role as profile_role,
    p.is_active as profile_ativo,
    CASE 
        WHEN te.user_id IS NOT NULL THEN 'SIM'
        ELSE 'NÃO'
    END as e_executor
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
LEFT JOIN task_executors te ON te.user_id = u.id AND te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 9: TESTAR ACESSO AOS CONTENT BLOCKS
SELECT 
    'TESTE ACESSO CONTENT BLOCKS' as teste,
    COUNT(*) as blocos_acessiveis
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND tcb.task_id IN (
        SELECT te.task_id FROM task_executors te
        WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
    );

-- =====================================================
-- INSTRUÇÕES FINAIS:
-- =====================================================
-- 1. Execute este script completo no Supabase SQL Editor
-- 2. Recarregue a página da aplicação (F5 ou Ctrl+R)
-- 3. Navegue para a tarefa 7c606667-9391-4660-933d-90d6bd276e88
-- 4. Clique na aba "Executar"
-- 5. O conteúdo deve aparecer agora!
-- 
-- Se ainda não funcionar, verifique:
-- - Se o console mostra o novo user_id sendo usado
-- - Se a policy RLS está ativa para task_content_blocks
-- - Se existem blocos de conteúdo na tarefa
-- =====================================================

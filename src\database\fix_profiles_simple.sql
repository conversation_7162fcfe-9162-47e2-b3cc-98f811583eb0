-- Correção simples para permitir listagem de usuários
-- Execute este script no SQL Editor do Supabase

-- 1. <PERSON><PERSON><PERSON> função is_admin se não existir
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND role = 'admin'
  );
$$;

-- 2. Política temporária mais permissiva para resolver o erro 403
DROP POLICY IF EXISTS "Allow users to view profiles for collaboration" ON public.profiles;
CREATE POLICY "Allow users to view profiles for collaboration"
  ON public.profiles
  FOR SELECT
  USING (
    -- Admin pode ver todos
    public.is_admin(auth.uid()) OR
    -- Qualquer usuário autenticado pode ver outros usuários (temporário)
    auth.uid() IS NOT NULL
  );

-- 3. <PERSON><PERSON><PERSON><PERSON> que RLS está ativado
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

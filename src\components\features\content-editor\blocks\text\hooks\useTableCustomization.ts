import { useState, useEffect, useCallback, useRef } from 'react';
import { Editor } from '@tiptap/react';
import { TableConfig, TableConfigPersistence } from '../utils/tableConfigPersistence';

export interface UseTableCustomizationProps {
  editor: Editor | null;
  tableElement?: HTMLTableElement | null;
}

export interface TableCustomizationState {
  config: TableConfig | null;
  isLoading: boolean;
  isDirty: boolean;
  selectedElement: 'table' | 'cell' | 'row' | 'column' | null;
  selectedCellId: string | null;
  selectedRowIndex: number | null;
  selectedColumnIndex: number | null;
}

export const useTableCustomization = ({ editor, tableElement }: UseTableCustomizationProps) => {
  const [state, setState] = useState<TableCustomizationState>({
    config: null,
    isLoading: false,
    isDirty: false,
    selectedElement: null,
    selectedCellId: null,
    selectedRowIndex: null,
    selectedColumnIndex: null,
  });

  const configRef = useRef<TableConfig | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Carregar configuração inicial
  useEffect(() => {
    if (tableElement) {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const config = TableConfigPersistence.loadTableConfig(tableElement);
      configRef.current = config;
      
      setState(prev => ({
        ...prev,
        config,
        isLoading: false,
        isDirty: false
      }));

      // Adicionar IDs às células se não existirem
      TableConfigPersistence.addCellIds(tableElement);
    }
  }, [tableElement]);

  // Salvar configuração automaticamente quando houver mudanças
  const saveConfig = useCallback((newConfig: TableConfig) => {
    if (!tableElement) return;

    // Cancelar salvamento anterior se pendente
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Salvar após debounce para evitar muitas operações
    saveTimeoutRef.current = setTimeout(() => {
      const optimizedConfig = TableConfigPersistence.optimizeConfig(newConfig);
      TableConfigPersistence.saveTableConfig(tableElement, optimizedConfig);
      
      setState(prev => ({ ...prev, isDirty: false }));
      
      // Notificar editor sobre mudanças
      if (editor) {
        editor.commands.focus();
      }
    }, 500);

  }, [tableElement, editor]);

  // Atualizar configuração
  const updateConfig = useCallback((updates: Partial<TableConfig>) => {
    const currentConfig = configRef.current || {
      id: `table-${Date.now()}`,
      dimensions: {},
      colors: {},
      styles: {},
      responsive: {}
    };

    const newConfig: TableConfig = {
      ...currentConfig,
      dimensions: { ...currentConfig.dimensions, ...updates.dimensions },
      colors: { ...currentConfig.colors, ...updates.colors },
      styles: { ...currentConfig.styles, ...updates.styles },
      responsive: { ...currentConfig.responsive, ...updates.responsive },
    };

    configRef.current = newConfig;
    
    setState(prev => ({
      ...prev,
      config: newConfig,
      isDirty: true
    }));

    saveConfig(newConfig);
  }, [saveConfig]);

  // Aplicar cor a célula específica
  const applyCellColor = useCallback((cellId: string, color: string) => {
    updateConfig({
      colors: {
        cellColors: {
          ...configRef.current?.colors.cellColors,
          [cellId]: color
        }
      }
    });
  }, [updateConfig]);

  // Aplicar cor a linha
  const applyRowColor = useCallback((rowIndex: number, color: string) => {
    updateConfig({
      colors: {
        rowColors: {
          ...configRef.current?.colors.rowColors,
          [rowIndex.toString()]: color
        }
      }
    });
  }, [updateConfig]);

  // Aplicar cor a coluna
  const applyColumnColor = useCallback((columnIndex: number, color: string) => {
    updateConfig({
      colors: {
        columnColors: {
          ...configRef.current?.colors.columnColors,
          [columnIndex.toString()]: color
        }
      }
    });
  }, [updateConfig]);

  // Redimensionar coluna
  const resizeColumn = useCallback((columnIndex: number, width: string) => {
    const currentWidths = configRef.current?.dimensions.columnWidths || [];
    const newWidths = [...currentWidths];
    newWidths[columnIndex] = width;

    updateConfig({
      dimensions: {
        columnWidths: newWidths
      }
    });
  }, [updateConfig]);

  // Redimensionar linha
  const resizeRow = useCallback((rowIndex: number, height: string) => {
    const currentHeights = configRef.current?.dimensions.rowHeights || [];
    const newHeights = [...currentHeights];
    newHeights[rowIndex] = height;

    updateConfig({
      dimensions: {
        rowHeights: newHeights
      }
    });
  }, [updateConfig]);

  // Selecionar elemento
  const selectElement = useCallback((
    type: 'table' | 'cell' | 'row' | 'column',
    cellId?: string,
    rowIndex?: number,
    columnIndex?: number
  ) => {
    setState(prev => ({
      ...prev,
      selectedElement: type,
      selectedCellId: cellId || null,
      selectedRowIndex: rowIndex ?? null,
      selectedColumnIndex: columnIndex ?? null,
    }));
  }, []);

  // Resetar configuração
  const resetConfig = useCallback(() => {
    if (!tableElement) return;

    const defaultConfig: TableConfig = {
      id: `table-${Date.now()}`,
      dimensions: {},
      colors: {},
      styles: {},
      responsive: {}
    };

    configRef.current = defaultConfig;
    
    setState(prev => ({
      ...prev,
      config: defaultConfig,
      isDirty: true
    }));

    saveConfig(defaultConfig);
  }, [tableElement, saveConfig]);

  // Exportar configuração
  const exportConfig = useCallback(() => {
    if (!tableElement) return '';
    return TableConfigPersistence.exportConfig(tableElement);
  }, [tableElement]);

  // Importar configuração
  const importConfig = useCallback((jsonConfig: string) => {
    if (!tableElement) return false;
    
    const success = TableConfigPersistence.importConfig(tableElement, jsonConfig);
    if (success) {
      const config = TableConfigPersistence.loadTableConfig(tableElement);
      configRef.current = config;
      
      setState(prev => ({
        ...prev,
        config,
        isDirty: false
      }));
    }
    
    return success;
  }, [tableElement]);

  // Aplicar configuração responsiva
  const applyResponsiveConfig = useCallback(() => {
    if (!configRef.current) return;

    const responsiveConfig = TableConfigPersistence.generateResponsiveConfig(configRef.current);
    
    setState(prev => ({
      ...prev,
      config: responsiveConfig
    }));
  }, []);

  // Cleanup
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Listener para mudanças de tamanho da tela
  useEffect(() => {
    const handleResize = () => {
      applyResponsiveConfig();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [applyResponsiveConfig]);

  return {
    // Estado
    ...state,
    
    // Ações
    updateConfig,
    applyCellColor,
    applyRowColor,
    applyColumnColor,
    resizeColumn,
    resizeRow,
    selectElement,
    resetConfig,
    exportConfig,
    importConfig,
    applyResponsiveConfig,
    
    // Utilitários
    isTableSelected: state.selectedElement === 'table',
    isCellSelected: state.selectedElement === 'cell',
    isRowSelected: state.selectedElement === 'row',
    isColumnSelected: state.selectedElement === 'column',
  };
};

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ButtonConfig } from '../config-panel/components/ButtonConfig';
import { defaultBlockConfig } from '../config-panel/constants/migration';

describe('ButtonConfig', () => {
  const mockOnChange = vi.fn();
  const mockOnReset = vi.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
    mockOnReset.mockClear();
  });

  it('should render with default configuration', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    expect(screen.getByText('Configuração do Botão')).toBeInTheDocument();
    expect(screen.getByText('Restaurar Padrão')).toBeInTheDocument();
  });

  it('should call onChange when background color changes', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    // Pegar todos os inputs de cor e usar o primeiro (cor de fundo)
    const colorInputs = screen.getAllByLabelText('Selecionar cor');
    const backgroundColorInput = colorInputs[0]; // Primeiro é cor de fundo
    fireEvent.change(backgroundColorInput, { target: { value: '#ff0000' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, backgroundColor: '#ff0000' }
    });
  });

  it('should call onReset when reset button is clicked', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const resetButton = screen.getByText('Restaurar Padrão');
    fireEvent.click(resetButton);

    expect(mockOnReset).toHaveBeenCalled();
  });

  it('should toggle border settings', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const borderCheckbox = screen.getByLabelText(/Borda/i);
    fireEvent.click(borderCheckbox);

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: {
        ...defaultBlockConfig.button,
        border: { ...defaultBlockConfig.button?.border, enabled: true }
      }
    });
  });

  it('should change button format', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const formatSelect = screen.getByDisplayValue('Arredondado');
    fireEvent.change(formatSelect, { target: { value: 'pill' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, style: 'pill' }
    });
  });

  it('should change button size', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const sizeSelect = screen.getByDisplayValue('Médio');
    fireEvent.change(sizeSelect, { target: { value: 'large' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, size: 'large' }
    });
  });

  it('should have correct default size value', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const sizeSelect = screen.getByDisplayValue('Médio');
    expect(sizeSelect).toBeInTheDocument();
    expect(defaultBlockConfig.button?.size).toBe('medium');
  });

  it('should change button size to small (auto width)', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const sizeSelect = screen.getByDisplayValue('Médio');
    fireEvent.change(sizeSelect, { target: { value: 'small' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, size: 'small' }
    });
  });

  it('should change button size to large (full width)', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const sizeSelect = screen.getByDisplayValue('Médio');
    fireEvent.change(sizeSelect, { target: { value: 'large' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, size: 'large' }
    });
  });

  it('should maintain size configuration when position changes', () => {
    const configWithLargeButton = {
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, size: 'large' }
    };

    render(
      <ButtonConfig
        config={configWithLargeButton}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    // Verificar se o tamanho grande está selecionado
    const sizeSelect = screen.getByDisplayValue('Grande');
    expect(sizeSelect).toBeInTheDocument();

    // Mudar posição e verificar se tamanho se mantém
    const positionSelect = screen.getByDisplayValue('Inferior centro');
    fireEvent.change(positionSelect, { target: { value: 'top-left' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...configWithLargeButton,
      button: { ...configWithLargeButton.button, position: 'top-left' }
    });
  });

  it('should use correct button text sources for different block types', () => {
    // Teste conceitual dos campos de texto do botão por tipo de bloco
    const buttonTextSources = {
      video: { field: 'buttonText', fallback: 'Assistir Agora' },
      coloredBlock: { field: 'actionButton.text', fallback: null },
      alert: { field: 'actionLabel', fallback: null },
      image: { field: null, fallback: null }, // Não tem botão
      file: { field: null, fallback: null }, // Não tem botão
      quiz: { field: null, fallback: null }, // Não tem botão
    };

    expect(buttonTextSources.video.field).toBe('buttonText');
    expect(buttonTextSources.coloredBlock.field).toBe('actionButton.text');
    expect(buttonTextSources.alert.field).toBe('actionLabel');
    expect(buttonTextSources.video.fallback).toBe('Assistir Agora');
  });

  it('should have specific reset buttons for each section', () => {
    // Teste conceitual dos botões de reset específicos
    const resetButtons = {
      appearance: {
        component: 'CardAppearanceConfig',
        button: 'Restaurar Padrão',
        scope: 'Apenas configurações de aparência do card',
        restores: [
          'cor do fundo do card',
          'cor da fonte do card',
          'formato do card',
          'borda do card',
          'cor da borda do card',
          'largura da borda do card',
          'sombra do card',
          'profundidade da sombra do card',
          'hover do card',
          'profundidade do hover do card'
        ]
      },
      icon: {
        component: 'IconAppearanceConfig',
        button: 'Restaurar Padrão',
        scope: 'Apenas configurações do ícone',
        restores: [
          'ícone selecionado',
          'cor do ícone',
          'tamanho do ícone',
          'posição do ícone',
          'hover do ícone',
          'configurações de aparência do ícone'
        ]
      },
      button: {
        component: 'ButtonConfig',
        button: 'Restaurar Padrão',
        scope: 'Apenas configurações do botão',
        restores: [
          'cor de fundo do botão',
          'cor da fonte do botão',
          'formato do botão',
          'tamanho do botão',
          'posição do botão',
          'borda do botão',
          'sombra do botão',
          'hover do botão'
        ]
      },
      general: {
        component: 'BlockConfigPanel',
        button: 'Restaurar Padrão',
        scope: 'Todas as configurações (Aparência, Ícone e Botão)',
        restores: ['todas as configurações acima']
      }
    };

    expect(resetButtons.appearance.scope).toBe('Apenas configurações de aparência do card');
    expect(resetButtons.icon.scope).toBe('Apenas configurações do ícone');
    expect(resetButtons.button.scope).toBe('Apenas configurações do botão');
    expect(resetButtons.general.scope).toBe('Todas as configurações (Aparência, Ícone e Botão)');

    // Verificar que cada seção restaura apenas suas próprias configurações
    expect(resetButtons.appearance.restores).toContain('cor do fundo do card');
    expect(resetButtons.appearance.restores).toContain('sombra do card');
    expect(resetButtons.icon.restores).toContain('ícone selecionado');
    expect(resetButtons.icon.restores).toContain('cor do ícone');
    expect(resetButtons.button.restores).toContain('cor de fundo do botão');
    expect(resetButtons.button.restores).toContain('posição do botão');
  });

  it('should ensure general reset uses exact same logic as specific resets', () => {
    // Teste conceitual da equivalência exata entre reset geral e específicos
    const resetLogicEquivalence = {
      cardLogic: {
        specific: 'handleResetCard() - usa coloredVariant || "info" fallback',
        general: 'handleResetAll() - usa exatamente o mesmo fallback para card',
        equivalent: true
      },
      iconLogic: {
        specific: 'handleResetIcon() - usa coloredVariant || "info" fallback',
        general: 'handleResetAll() - usa exatamente o mesmo fallback para icon',
        equivalent: true
      },
      buttonLogic: {
        specific: 'handleResetButton() - usa coloredVariant || "info" fallback',
        general: 'handleResetAll() - usa exatamente o mesmo fallback para button',
        equivalent: true
      },
      fallbackConsistency: {
        description: 'Todos usam coloredVariant || "info" para colored-block',
        cardAppearanceConfig: 'coloredVariant || "info"',
        blockConfigPanel: 'coloredVariant || "info"',
        equivalent: true
      }
    };

    expect(resetLogicEquivalence.cardLogic.equivalent).toBe(true);
    expect(resetLogicEquivalence.iconLogic.equivalent).toBe(true);
    expect(resetLogicEquivalence.buttonLogic.equivalent).toBe(true);
    expect(resetLogicEquivalence.fallbackConsistency.equivalent).toBe(true);
  });

  it('should change button position', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const positionSelect = screen.getByDisplayValue('Inferior centro');
    fireEvent.change(positionSelect, { target: { value: 'top-center' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, position: 'top-center' }
    });
  });

  it('should have correct default position value', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const positionSelect = screen.getByDisplayValue('Inferior centro');
    expect(positionSelect).toBeInTheDocument();
    expect(defaultBlockConfig.button?.position).toBe('bottom-center');
  });

  it('should change button position to top-left (absolute positioning)', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const positionSelect = screen.getByDisplayValue('Inferior centro');
    fireEvent.change(positionSelect, { target: { value: 'top-left' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, position: 'top-left' }
    });
  });

  it('should change button position to bottom-right (absolute positioning)', () => {
    render(
      <ButtonConfig
        config={defaultBlockConfig}
        onChange={mockOnChange}
        onReset={mockOnReset}
      />
    );

    const positionSelect = screen.getByDisplayValue('Inferior centro');
    fireEvent.change(positionSelect, { target: { value: 'bottom-right' } });

    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultBlockConfig,
      button: { ...defaultBlockConfig.button, position: 'bottom-right' }
    });
  });
});

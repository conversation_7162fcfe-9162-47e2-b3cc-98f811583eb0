-- =====================================================
-- SCRIPT COMPLETO DE DADOS DE TESTE - SISTEMA DE QUIZ
-- =====================================================
-- ATUALIZADO: 2025-07-12 - Suporte a Assessment e Survey
-- 
-- Este script cria dados de teste completos para demonstrar:
-- 1. ASSESSMENTS (Avaliações) - Modo tradicional com correção
-- 2. SURVEYS (Pesquisas) - Novo modo para coleta de opinião
-- 3. Tentativas e respostas variadas para análise
-- 4. Estrutura completa: usuários, projetos, tarefas, quizzes
-- 
-- COMPATIBILIDADE: 100% com sistema existente
-- =====================================================

-- =====================================================
-- 1. LIMPEZA COMPLETA DA BASE DE DADOS DE TESTE
-- =====================================================

-- Remover dados de quiz em ordem correta (respeitando foreign keys)
DELETE FROM quiz_statistics WHERE quiz_id IN (
  SELECT id FROM quizzes WHERE task_id IN (
    SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
  )
);

DELETE FROM user_quiz_progress WHERE quiz_id IN (
  SELECT id FROM quizzes WHERE task_id IN (
    SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
  )
);

DELETE FROM quiz_answers WHERE attempt_id IN (
  SELECT id FROM quiz_attempts WHERE quiz_id IN (
    SELECT id FROM quizzes WHERE task_id IN (
      SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
    )
  )
);

DELETE FROM quiz_attempts WHERE quiz_id IN (
  SELECT id FROM quizzes WHERE task_id IN (
    SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
  )
);

DELETE FROM quizzes WHERE task_id IN (
  SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
);

-- Remover estrutura de tarefas e projetos de teste
DELETE FROM task_content_blocks WHERE task_id IN (
  SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
);

DELETE FROM tasks WHERE title LIKE 'TESTE:%';
DELETE FROM stages WHERE name LIKE '%Teste%Quiz%';
DELETE FROM projects WHERE name LIKE '%Teste%Quiz%';

-- Remover usuários de teste (opcional - manter comentado para preservar usuários reais)
-- DELETE FROM profiles WHERE email LIKE '%teste.quiz%';


-- Definir UUIDs fixos para facilitar testes e referências
WITH test_data AS (
  SELECT 
    -- Usuários de teste
    '50580ad5-7c45-4d7f-9446-69f069205ad0'::uuid as user_id_1,
    
    -- Estrutura do projeto
    gen_random_uuid() as project_id,
    gen_random_uuid() as stage_id,
    
    -- Tarefas para ASSESSMENTS (Avaliações)
    gen_random_uuid() as task_assessment_basic_id,
    gen_random_uuid() as task_assessment_advanced_id,
    gen_random_uuid() as task_assessment_mandatory_id,
    
    -- Tarefas para SURVEYS (Pesquisas)
    gen_random_uuid() as task_survey_satisfaction_id,
    gen_random_uuid() as task_survey_feedback_id,
    gen_random_uuid() as task_survey_preferences_id,
    
    -- Tarefas mistas para demonstração
    gen_random_uuid() as task_mixed_demo_id
),


-- 2. Inserir projeto
project_insert AS (
  INSERT INTO projects (id, name, description, status, progress, owner_id, start_date, end_date, created_at, updated_at)
  SELECT 
    td.project_id,
    'Projeto de Testes - Sistema de Quiz Completo',
    'Projeto para demonstrar Assessments (avaliações) e Surveys (pesquisas de opinião) com dados de teste completos',
    'active',
    0,
    td.user_id_1,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '30 days',
    NOW(),
    NOW()
  FROM test_data td
  ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW()
  RETURNING id
),

-- 3. Inserir etapa
stage_insert AS (
  INSERT INTO stages (id, project_id, name, description, status, progress, start_date, end_date, created_at, updated_at)
  SELECT 
    td.stage_id,
    td.project_id,
    'Etapa de Testes - Quiz Completo',
    'Etapa com tarefas de Assessment (avaliações) e Survey (pesquisas) para demonstração completa',
    'not-started',
    0,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '30 days',
    NOW(),
    NOW()
  FROM test_data td
  ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW()
  RETURNING id
),

-- 4. Inserir tarefas (Assessments e Surveys)
tasks_insert AS (
  INSERT INTO tasks (id, stage_id, title, description, status, priority, assigned_to, created_by, due_date, created_at, updated_at)
  SELECT * FROM (
    -- ===== ASSESSMENTS (Avaliações) =====
    SELECT
      td.task_assessment_basic_id as id,
      td.stage_id,
      'TESTE: Assessment Básico' as title,
      'Avaliação tradicional com correção automática - 3 tentativas, 70% aprovação, feedback completo' as description,
      'todo'::task_status as status,
      1 as priority,
      td.user_id_1 as assigned_to,
      td.user_id_1 as created_by,
      CURRENT_DATE + INTERVAL '7 days' as due_date,
      NOW() as created_at,
      NOW() as updated_at
    FROM test_data td

    UNION ALL

    -- ===== SURVEYS (Pesquisas de Opinião) =====
    SELECT
      td.task_survey_satisfaction_id,
      td.stage_id,
      'TESTE: Survey Satisfação',
      'Pesquisa de satisfação com escalas numéricas - mode=survey, sem correção automática',
      'todo',
      1,
      td.user_id_1,
      td.user_id_1,
      CURRENT_DATE + INTERVAL '14 days',
      NOW(),
      NOW()
    FROM test_data td

    UNION ALL

    SELECT
      td.task_survey_preferences_id,
      td.stage_id,
      'TESTE: Survey Preferências',
      'Pesquisa de preferências do usuário - múltiplas opções, sem respostas certas/erradas',
      'todo',
      1,
      td.user_id_1,
      td.user_id_1,
      CURRENT_DATE + INTERVAL '14 days',
      NOW(),
      NOW()
    FROM test_data td
  ) tasks_data
  ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    updated_at = NOW()
  RETURNING id, title
),

-- 5. Inserir quizzes na tabela quizzes
quiz_insert AS (
  INSERT INTO quizzes (id, task_id, block_id, content, created_at, updated_at, created_by, is_active)
  SELECT * FROM (
    -- ===== ASSESSMENT BÁSICO =====
    SELECT
      gen_random_uuid() as id,
      td.task_assessment_basic_id as task_id,
      'quiz_block_1' as block_id,
      jsonb_build_object(
        'config', jsonb_build_object(
          'title', 'Assessment Básico - Conhecimentos Gerais',
          'description', 'Avaliação tradicional com correção automática',
          'instructions', 'Responda às perguntas. Você tem 3 tentativas e precisa de 70% para aprovação.',
          'mode', 'assessment',
          'maxAttempts', 3,
          'allowRetry', true,
          'passingScore', 70,
          'showScore', true,
          'showCorrectAnswers', true,
          'showFeedback', true,
          'showDetailedResults', true,
          'showTimer', false,
          'isRequired', false,
          'blockProgressUntilPassed', false,
          'shuffleQuestions', false,
          'shuffleOptions', false,
          'showProgressBar', true,
          'allowSaveDraft', true,
          'enableAnalytics', true,
          'showResultsToUser', true
        ),
        'questions', jsonb_build_array(
          jsonb_build_object(
            'id', 'assess_basic_q1',
            'type', 'single-choice',
            'title', 'Qual é a capital do Brasil?',
            'points', 2,
            'required', true,
            'options', jsonb_build_array(
              jsonb_build_object('id', 'opt1', 'text', 'Brasília', 'isCorrect', true),
              jsonb_build_object('id', 'opt2', 'text', 'São Paulo', 'isCorrect', false),
              jsonb_build_object('id', 'opt3', 'text', 'Rio de Janeiro', 'isCorrect', false),
              jsonb_build_object('id', 'opt4', 'text', 'Salvador', 'isCorrect', false)
            ),
            'correctFeedback', 'Correto! Brasília é a capital do Brasil desde 1960.',
            'incorrectFeedback', 'Incorreto. A capital do Brasil é Brasília.',
            'explanation', 'Brasília foi inaugurada em 21 de abril de 1960 como a nova capital do Brasil.'
          ),
          jsonb_build_object(
            'id', 'assess_basic_q2',
            'type', 'true-false',
            'title', 'O TypeScript é um superset do JavaScript.',
            'points', 2,
            'required', true,
            'correctAnswer', true,
            'correctFeedback', 'Correto! TypeScript adiciona tipagem estática ao JavaScript.',
            'incorrectFeedback', 'Incorreto. TypeScript é realmente um superset do JavaScript.',
            'explanation', 'TypeScript estende JavaScript adicionando definições de tipo estático.'
          )
        )
      ) as content,
      NOW() as created_at,
      NOW() as updated_at,
      td.user_id_1 as created_by,
      true as is_active
    FROM test_data td

    UNION ALL

    -- ===== SURVEY SATISFAÇÃO =====
    SELECT
      gen_random_uuid() as id,
      td.task_survey_satisfaction_id as task_id,
      'survey_block_1' as block_id,
      jsonb_build_object(
        'config', jsonb_build_object(
          'title', 'Pesquisa de Satisfação - Plataforma',
          'description', 'Sua opinião é importante para melhorarmos nossos serviços',
          'instructions', 'Por favor, avalie nossa plataforma. Suas respostas nos ajudam a melhorar.',
          'mode', 'survey',
          'maxAttempts', 1,
          'allowRetry', false,
          'passingScore', 0,
          'showScore', false,
          'showCorrectAnswers', false,
          'showFeedback', false,
          'showDetailedResults', true,
          'showTimer', false,
          'isRequired', false,
          'blockProgressUntilPassed', false,
          'shuffleQuestions', false,
          'shuffleOptions', false,
          'showProgressBar', true,
          'allowSaveDraft', true,
          'enableAnalytics', true,
          'showResultsToUser', true,
          'surveySettings', jsonb_build_object(
            'showAggregatedResults', true,
            'allowAnonymous', true,
            'showOthersResponses', false,
            'collectDemographics', false,
            'showResultsAfterSubmission', true
          )
        ),
        'questions', jsonb_build_array(
          jsonb_build_object(
            'id', 'survey_sat_q1',
            'type', 'single-choice',
            'title', 'Como você avalia nossa plataforma em geral?',
            'points', 1,
            'required', true,
            'options', jsonb_build_array(
              jsonb_build_object('id', 'opt1', 'text', 'Excelente', 'surveyValue', 5),
              jsonb_build_object('id', 'opt2', 'text', 'Muito Bom', 'surveyValue', 4),
              jsonb_build_object('id', 'opt3', 'text', 'Bom', 'surveyValue', 3),
              jsonb_build_object('id', 'opt4', 'text', 'Regular', 'surveyValue', 2),
              jsonb_build_object('id', 'opt5', 'text', 'Ruim', 'surveyValue', 1)
            )
          ),
          jsonb_build_object(
            'id', 'survey_sat_q2',
            'type', 'multiple-choice',
            'title', 'Quais recursos você mais utiliza? (Selecione todos)',
            'points', 1,
            'required', false,
            'options', jsonb_build_array(
              jsonb_build_object('id', 'opt1', 'text', 'Dashboard', 'category', 'interface'),
              jsonb_build_object('id', 'opt2', 'text', 'Relatórios', 'category', 'analytics'),
              jsonb_build_object('id', 'opt3', 'text', 'Sistema de Quiz', 'category', 'content'),
              jsonb_build_object('id', 'opt4', 'text', 'Gestão de Projetos', 'category', 'management')
            )
          ),
          jsonb_build_object(
            'id', 'survey_sat_q3',
            'type', 'open-text',
            'title', 'Deixe seus comentários e sugestões:',
            'points', 1,
            'required', false
          )
        )
      ) as content,
      NOW() as created_at,
      NOW() as updated_at,
      td.user_id_1 as created_by,
      true as is_active
    FROM test_data td

    UNION ALL

    -- ===== SURVEY PREFERÊNCIAS =====
    SELECT
      gen_random_uuid() as id,
      td.task_survey_preferences_id as task_id,
      'survey_block_2' as block_id,
      jsonb_build_object(
        'config', jsonb_build_object(
          'title', 'Pesquisa de Preferências - Desenvolvimento',
          'description', 'Queremos conhecer suas preferências de desenvolvimento',
          'instructions', 'Compartilhe suas preferências em desenvolvimento de software.',
          'mode', 'survey',
          'maxAttempts', 1,
          'allowRetry', false,
          'passingScore', 0,
          'showScore', false,
          'showCorrectAnswers', false,
          'showFeedback', false,
          'showDetailedResults', true,
          'showTimer', false,
          'isRequired', false,
          'blockProgressUntilPassed', false,
          'shuffleQuestions', false,
          'shuffleOptions', true,
          'showProgressBar', true,
          'allowSaveDraft', true,
          'enableAnalytics', true,
          'showResultsToUser', true,
          'surveySettings', jsonb_build_object(
            'showAggregatedResults', true,
            'allowAnonymous', false,
            'showOthersResponses', true,
            'collectDemographics', true,
            'showResultsAfterSubmission', true
          )
        ),
        'questions', jsonb_build_array(
          jsonb_build_object(
            'id', 'survey_pref_q1',
            'type', 'single-choice',
            'title', 'Qual é sua linguagem de programação favorita?',
            'points', 1,
            'required', true,
            'options', jsonb_build_array(
              jsonb_build_object('id', 'opt1', 'text', 'JavaScript/TypeScript', 'category', 'web'),
              jsonb_build_object('id', 'opt2', 'text', 'Python', 'category', 'general'),
              jsonb_build_object('id', 'opt3', 'text', 'Java', 'category', 'enterprise'),
              jsonb_build_object('id', 'opt4', 'text', 'C#', 'category', 'microsoft'),
              jsonb_build_object('id', 'opt5', 'text', 'Outra', 'category', 'other')
            )
          ),
          jsonb_build_object(
            'id', 'survey_pref_q2',
            'type', 'single-choice',
            'title', 'Há quantos anos você programa?',
            'points', 1,
            'required', true,
            'options', jsonb_build_array(
              jsonb_build_object('id', 'opt1', 'text', 'Menos de 1 ano', 'surveyValue', 0.5),
              jsonb_build_object('id', 'opt2', 'text', '1-2 anos', 'surveyValue', 1.5),
              jsonb_build_object('id', 'opt3', 'text', '3-5 anos', 'surveyValue', 4),
              jsonb_build_object('id', 'opt4', 'text', '6-10 anos', 'surveyValue', 8),
              jsonb_build_object('id', 'opt5', 'text', 'Mais de 10 anos', 'surveyValue', 15)
            )
          )
        )
      ) as content,
      NOW() as created_at,
      NOW() as updated_at,
      td.user_id_1 as created_by,
      true as is_active
    FROM test_data td
  ) quiz_data
  ON CONFLICT (task_id, block_id) DO UPDATE SET
    content = EXCLUDED.content,
    updated_at = NOW()
  RETURNING id, task_id
),

-- 6. Criar task_content_blocks para que os quizzes apareçam na execução
content_blocks_insert AS (
  INSERT INTO task_content_blocks (id, task_id, type, content, config, "order", created_at)
  SELECT * FROM (
    -- Assessment Básico
    SELECT
      gen_random_uuid() as id,
      td.task_assessment_basic_id as task_id,
      'quiz' as type,
      jsonb_build_object(
        'quiz', jsonb_build_object(
          'config', jsonb_build_object(
            'title', 'Assessment Básico - Conhecimentos Gerais',
            'description', 'Avaliação tradicional com correção automática',
            'instructions', 'Responda às perguntas. Você tem 3 tentativas e precisa de 70% para aprovação.',
            'mode', 'assessment',
            'maxAttempts', 3,
            'allowRetry', true,
            'passingScore', 70,
            'showScore', true,
            'showCorrectAnswers', true,
            'showFeedback', true,
            'showDetailedResults', true,
            'showTimer', false,
            'isRequired', false,
            'blockProgressUntilPassed', false,
            'shuffleQuestions', false,
            'shuffleOptions', false,
            'showProgressBar', true,
            'allowSaveDraft', true,
            'enableAnalytics', true,
            'showResultsToUser', true
          ),
          'questions', jsonb_build_array(
            jsonb_build_object(
              'id', 'assess_basic_q1',
              'type', 'single-choice',
              'title', 'Qual é a capital do Brasil?',
              'points', 2,
              'required', true,
              'options', jsonb_build_array(
                jsonb_build_object('id', 'opt1', 'text', 'Brasília', 'isCorrect', true),
                jsonb_build_object('id', 'opt2', 'text', 'São Paulo', 'isCorrect', false),
                jsonb_build_object('id', 'opt3', 'text', 'Rio de Janeiro', 'isCorrect', false),
                jsonb_build_object('id', 'opt4', 'text', 'Salvador', 'isCorrect', false)
              ),
              'correctFeedback', 'Correto! Brasília é a capital do Brasil desde 1960.',
              'incorrectFeedback', 'Incorreto. A capital do Brasil é Brasília.',
              'explanation', 'Brasília foi inaugurada em 21 de abril de 1960 como a nova capital do Brasil.'
            ),
            jsonb_build_object(
              'id', 'assess_basic_q2',
              'type', 'true-false',
              'title', 'O TypeScript é um superset do JavaScript.',
              'points', 2,
              'required', true,
              'correctAnswer', true,
              'correctFeedback', 'Correto! TypeScript adiciona tipagem estática ao JavaScript.',
              'incorrectFeedback', 'Incorreto. TypeScript é realmente um superset do JavaScript.',
              'explanation', 'TypeScript estende JavaScript adicionando definições de tipo estático.'
            )
          )
        )
      ) as content,
      jsonb_build_object('icon', jsonb_build_object('iconName', 'Target', 'color', '#3b82f6')) as config,
      0 as "order",
      NOW() as created_at
    FROM test_data td

    UNION ALL

    -- Survey Satisfação
    SELECT
      gen_random_uuid() as id,
      td.task_survey_satisfaction_id as task_id,
      'quiz' as type,
      jsonb_build_object(
        'quiz', jsonb_build_object(
          'config', jsonb_build_object(
            'title', 'Pesquisa de Satisfação - Plataforma',
            'description', 'Sua opinião é importante para melhorarmos nossos serviços',
            'instructions', 'Por favor, avalie nossa plataforma. Suas respostas nos ajudam a melhorar.',
            'mode', 'survey',
            'maxAttempts', 1,
            'allowRetry', false,
            'passingScore', 0,
            'showScore', false,
            'showCorrectAnswers', false,
            'showFeedback', false,
            'showDetailedResults', true,
            'showTimer', false,
            'isRequired', false,
            'blockProgressUntilPassed', false,
            'shuffleQuestions', false,
            'shuffleOptions', false,
            'showProgressBar', true,
            'allowSaveDraft', true,
            'enableAnalytics', true,
            'showResultsToUser', true,
            'surveySettings', jsonb_build_object(
              'showAggregatedResults', true,
              'allowAnonymous', true,
              'showOthersResponses', false,
              'collectDemographics', false,
              'showResultsAfterSubmission', true
            )
          ),
          'questions', jsonb_build_array(
            jsonb_build_object(
              'id', 'survey_sat_q1',
              'type', 'single-choice',
              'title', 'Como você avalia nossa plataforma em geral?',
              'points', 1,
              'required', true,
              'options', jsonb_build_array(
                jsonb_build_object('id', 'opt1', 'text', 'Excelente', 'surveyValue', 5),
                jsonb_build_object('id', 'opt2', 'text', 'Muito Bom', 'surveyValue', 4),
                jsonb_build_object('id', 'opt3', 'text', 'Bom', 'surveyValue', 3),
                jsonb_build_object('id', 'opt4', 'text', 'Regular', 'surveyValue', 2),
                jsonb_build_object('id', 'opt5', 'text', 'Ruim', 'surveyValue', 1)
              )
            ),
            jsonb_build_object(
              'id', 'survey_sat_q2',
              'type', 'multiple-choice',
              'title', 'Quais recursos você mais utiliza? (Selecione todos)',
              'points', 1,
              'required', false,
              'options', jsonb_build_array(
                jsonb_build_object('id', 'opt1', 'text', 'Dashboard', 'category', 'interface'),
                jsonb_build_object('id', 'opt2', 'text', 'Relatórios', 'category', 'analytics'),
                jsonb_build_object('id', 'opt3', 'text', 'Sistema de Quiz', 'category', 'content'),
                jsonb_build_object('id', 'opt4', 'text', 'Gestão de Projetos', 'category', 'management')
              )
            ),
            jsonb_build_object(
              'id', 'survey_sat_q3',
              'type', 'open-text',
              'title', 'Deixe seus comentários e sugestões:',
              'points', 1,
              'required', false
            )
          )
        )
      ) as content,
      jsonb_build_object('icon', jsonb_build_object('iconName', 'BarChart3', 'color', '#10b981')) as config,
      0 as "order",
      NOW() as created_at
    FROM test_data td

    UNION ALL

    -- Survey Preferências
    SELECT
      gen_random_uuid() as id,
      td.task_survey_preferences_id as task_id,
      'quiz' as type,
      jsonb_build_object(
        'quiz', jsonb_build_object(
          'config', jsonb_build_object(
            'title', 'Pesquisa de Preferências - Desenvolvimento',
            'description', 'Queremos conhecer suas preferências de desenvolvimento',
            'instructions', 'Compartilhe suas preferências em desenvolvimento de software.',
            'mode', 'survey',
            'maxAttempts', 1,
            'allowRetry', false,
            'passingScore', 0,
            'showScore', false,
            'showCorrectAnswers', false,
            'showFeedback', false,
            'showDetailedResults', true,
            'showTimer', false,
            'isRequired', false,
            'blockProgressUntilPassed', false,
            'shuffleQuestions', false,
            'shuffleOptions', true,
            'showProgressBar', true,
            'allowSaveDraft', true,
            'enableAnalytics', true,
            'showResultsToUser', true,
            'surveySettings', jsonb_build_object(
              'showAggregatedResults', true,
              'allowAnonymous', false,
              'showOthersResponses', true,
              'collectDemographics', true,
              'showResultsAfterSubmission', true
            )
          ),
          'questions', jsonb_build_array(
            jsonb_build_object(
              'id', 'survey_pref_q1',
              'type', 'single-choice',
              'title', 'Qual é sua linguagem de programação favorita?',
              'points', 1,
              'required', true,
              'options', jsonb_build_array(
                jsonb_build_object('id', 'opt1', 'text', 'JavaScript/TypeScript', 'category', 'web'),
                jsonb_build_object('id', 'opt2', 'text', 'Python', 'category', 'general'),
                jsonb_build_object('id', 'opt3', 'text', 'Java', 'category', 'enterprise'),
                jsonb_build_object('id', 'opt4', 'text', 'C#', 'category', 'microsoft'),
                jsonb_build_object('id', 'opt5', 'text', 'Outra', 'category', 'other')
              )
            ),
            jsonb_build_object(
              'id', 'survey_pref_q2',
              'type', 'single-choice',
              'title', 'Há quantos anos você programa?',
              'points', 1,
              'required', true,
              'options', jsonb_build_array(
                jsonb_build_object('id', 'opt1', 'text', 'Menos de 1 ano', 'surveyValue', 0.5),
                jsonb_build_object('id', 'opt2', 'text', '1-2 anos', 'surveyValue', 1.5),
                jsonb_build_object('id', 'opt3', 'text', '3-5 anos', 'surveyValue', 4),
                jsonb_build_object('id', 'opt4', 'text', '6-10 anos', 'surveyValue', 8),
                jsonb_build_object('id', 'opt5', 'text', 'Mais de 10 anos', 'surveyValue', 15)
              )
            )
          )
        )
      ) as content,
      jsonb_build_object('icon', jsonb_build_object('iconName', 'Code', 'color', '#8b5cf6')) as config,
      0 as "order",
      NOW() as created_at
    FROM test_data td
  ) content_data
  RETURNING id, task_id, type
),

-- 7. Criar tentativas de teste para os surveys
survey_attempts_insert AS (
  INSERT INTO quiz_attempts (id, quiz_id, user_id, attempt_number, started_at, submitted_at, time_spent, score, max_score, percentage, passed, status)
  SELECT * FROM (
    -- Tentativas para Survey Satisfação (3 usuários)
    SELECT
      gen_random_uuid() as id,
      q.id as quiz_id,
      td.user_id_1 as user_id,
      1 as attempt_number,
      NOW() - INTERVAL '2 hours' as started_at,
      NOW() - INTERVAL '1 hour 50 minutes' as submitted_at,
      600 as time_spent,
      3 as score,
      3 as max_score,
      100.0 as percentage,
      true as passed,
      'graded' as status
    FROM test_data td
    JOIN quizzes q ON q.task_id = td.task_survey_satisfaction_id

    UNION ALL

    SELECT
      gen_random_uuid() as id,
      q.id as quiz_id,
      td.user_id_1 as user_id,
      1 as attempt_number,
      NOW() - INTERVAL '1 day' as started_at,
      NOW() - INTERVAL '23 hours 45 minutes' as submitted_at,
      480 as time_spent,
      3 as score,
      3 as max_score,
      100.0 as percentage,
      true as passed,
      'graded' as status
    FROM test_data td
    JOIN quizzes q ON q.task_id = td.task_survey_satisfaction_id

    UNION ALL

    SELECT
      gen_random_uuid() as id,
      q.id as quiz_id,
      td.user_id_1 as user_id,
      1 as attempt_number,
      NOW() - INTERVAL '3 hours' as started_at,
      NOW() - INTERVAL '2 hours 45 minutes' as submitted_at,
      720 as time_spent,
      3 as score,
      3 as max_score,
      100.0 as percentage,
      true as passed,
      'graded' as status
    FROM test_data td
    JOIN quizzes q ON q.task_id = td.task_survey_satisfaction_id
  ) attempts_data
  RETURNING id, quiz_id, user_id
),

-- 8. Criar respostas variadas para demonstrar estatísticas reais
survey_answers_insert AS (
  INSERT INTO quiz_answers (id, attempt_id, question_id, question_type, selected_options, text_answer, time_spent, points_earned, created_at)
  SELECT * FROM (
    -- Respostas do User 1 para Survey Satisfação
    SELECT
      gen_random_uuid() as id,
      sa.id as attempt_id,
      'survey_sat_q1' as question_id,
      'single-choice' as question_type,
      ARRAY['opt4'] as selected_options, -- "Muito Bom"
      null as text_answer,
      120 as time_spent,
      1 as points_earned,
      NOW() - INTERVAL '1 hour 50 minutes' as created_at
    FROM survey_attempts_insert sa
    JOIN test_data td ON sa.user_id = td.user_id_1
    WHERE sa.quiz_id IN (SELECT id FROM quizzes WHERE task_id = td.task_survey_satisfaction_id)

    UNION ALL

    SELECT
      gen_random_uuid() as id,
      sa.id as attempt_id,
      'survey_sat_q2' as question_id,
      'multiple-choice' as question_type,
      ARRAY['opt1', 'opt3'] as selected_options, -- Dashboard + Sistema de Quiz
      null as text_answer,
      90 as time_spent,
      1 as points_earned,
      NOW() - INTERVAL '1 hour 48 minutes' as created_at
    FROM survey_attempts_insert sa
    JOIN test_data td ON sa.user_id = td.user_id_1
    WHERE sa.quiz_id IN (SELECT id FROM quizzes WHERE task_id = td.task_survey_satisfaction_id)

    UNION ALL

    SELECT
      gen_random_uuid() as id,
      sa.id as attempt_id,
      'survey_sat_q3' as question_id,
      'open-text' as question_type,
      null as selected_options,
      'A plataforma está muito boa! Gostaria de ver mais recursos de relatórios.' as text_answer,
      180 as time_spent,
      1 as points_earned,
      NOW() - INTERVAL '1 hour 45 minutes' as created_at
    FROM survey_attempts_insert sa
    JOIN test_data td ON sa.user_id = td.user_id_1
    WHERE sa.quiz_id IN (SELECT id FROM quizzes WHERE task_id = td.task_survey_satisfaction_id)

    UNION ALL

    -- Respostas do User 2 para Survey Satisfação
    SELECT
      gen_random_uuid() as id,
      sa.id as attempt_id,
      'survey_sat_q1' as question_id,
      'single-choice' as question_type,
      ARRAY['opt1'] as selected_options, -- "Excelente"
      null as text_answer,
      90 as time_spent,
      1 as points_earned,
      NOW() - INTERVAL '23 hours 45 minutes' as created_at
    FROM survey_attempts_insert sa
    JOIN test_data td ON sa.user_id = td.user_id_1
    WHERE sa.quiz_id IN (SELECT id FROM quizzes WHERE task_id = td.task_survey_satisfaction_id)

    UNION ALL

    SELECT
      gen_random_uuid() as id,
      sa.id as attempt_id,
      'survey_sat_q2' as question_id,
      'multiple-choice' as question_type,
      ARRAY['opt2', 'opt4'] as selected_options, -- Relatórios + Gestão de Projetos
      null as text_answer,
      120 as time_spent,
      1 as points_earned,
      NOW() - INTERVAL '23 hours 43 minutes' as created_at
    FROM survey_attempts_insert sa
    JOIN test_data td ON sa.user_id = td.user_id_1
    WHERE sa.quiz_id IN (SELECT id FROM quizzes WHERE task_id = td.task_survey_satisfaction_id)

    UNION ALL

    SELECT
      gen_random_uuid() as id,
      sa.id as attempt_id,
      'survey_sat_q3' as question_id,
      'open-text' as question_type,
      null as selected_options,
      'Excelente ferramenta! Muito intuitiva e fácil de usar.' as text_answer,
      150 as time_spent,
      1 as points_earned,
      NOW() - INTERVAL '23 hours 40 minutes' as created_at
    FROM survey_attempts_insert sa
    JOIN test_data td ON sa.user_id = td.user_id_1
    WHERE sa.quiz_id IN (SELECT id FROM quizzes WHERE task_id = td.task_survey_satisfaction_id)
  ) answers_data
  RETURNING id, attempt_id, question_id
)

SELECT 'DADOS DE TESTE CRIADOS COM SUCESSO!' as resultado,
       NOW() as timestamp_execucao,
       (SELECT COUNT(*) FROM tasks WHERE title LIKE 'TESTE:%') as tarefas_criadas,
       (SELECT COUNT(*) FROM quizzes WHERE task_id IN (
         SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
       )) as quizzes_criados,
       (SELECT COUNT(*) FROM task_content_blocks WHERE task_id IN (
         SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
       ) AND type = 'quiz') as content_blocks_criados,
       (SELECT COUNT(*) FROM quiz_attempts WHERE quiz_id IN (
         SELECT id FROM quizzes WHERE task_id IN (
           SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
         )
       )) as tentativas_criadas,
       (SELECT COUNT(*) FROM quiz_answers WHERE attempt_id IN (
         SELECT id FROM quiz_attempts WHERE quiz_id IN (
           SELECT id FROM quizzes WHERE task_id IN (
             SELECT id FROM tasks WHERE title LIKE 'TESTE:%'
           )
         )
       )) as respostas_criadas;

-- Mostrar resumo dos quizzes por modo
SELECT
  COALESCE(content->'config'->>'mode', 'assessment') as modo_quiz,
  COUNT(*) as quantidade,
  string_agg(content->'config'->>'title', ', ') as titulos
FROM quizzes
WHERE task_id IN (SELECT id FROM tasks WHERE title LIKE 'TESTE:%')
GROUP BY COALESCE(content->'config'->>'mode', 'assessment')
ORDER BY modo_quiz;

-- =====================================================
-- INSTRUÇÕES PARA USO
-- =====================================================

/*
DADOS DE TESTE CRIADOS COM SUCESSO!

ASSESSMENTS (Avaliações):
- Assessment Básico: Quiz tradicional com correção automática
- Perguntas com respostas certas/erradas, feedback e explicações

SURVEYS (Pesquisas):
- Survey Satisfação: Pesquisa com escalas numéricas e NPS
- Survey Preferências: Pesquisa sobre tecnologias de desenvolvimento
- Sem correção automática, foco em coleta de opiniões

USUÁRIOS DE TESTE:
- Ana Silva (<EMAIL>)
- Carlos Santos (<EMAIL>)
- Maria Oliveira (<EMAIL>)

PARA TESTAR:
1. Acesse as tarefas que começam com "TESTE:"
2. Execute os quizzes para ver a diferença entre Assessment e Survey
3. Verifique os resultados e estatísticas
4. Teste as funcionalidades específicas de cada modo

COMPATIBILIDADE:
- Quizzes antigos sem campo 'mode' são tratados como 'assessment'
- Todas as funcionalidades existentes são preservadas
- Sistema suporta ambos os modos simultaneamente
*/

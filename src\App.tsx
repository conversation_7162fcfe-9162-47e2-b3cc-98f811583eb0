import React from "react";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { AuthProvider } from "@/auth/AuthProvider";
import AuthRoutes from "@/auth/AuthRoutes";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import { ProjectDetails } from "./pages/ProjectDetails";
import { StageDetails } from "./pages/StageDetails";
import { TaskDetails } from "./pages/TaskDetails";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <AuthRoutes />
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;


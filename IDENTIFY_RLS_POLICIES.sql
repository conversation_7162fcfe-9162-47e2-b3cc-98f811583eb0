-- =====================================================
-- IDENTIFICAR POLÍTICAS RLS RESTANTES
-- =====================================================
-- Execute primeiro para ver quais políticas ainda existem

SELECT 
    tablename as tabela,
    policyname as politica,
    cmd as operacao,
    'DROP POLICY IF EXISTS "' || policyname || '" ON public.' || tablename || ';' as comando_para_remover
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- VERIFICAR SE O USUÁRIO EXISTE NA TABELA PROFILES
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR SE O USUÁRIO LOGADO EXISTE NA TABELA PROFILES
SELECT 
    'TESTE 1: USUÁRIO LOGADO EXISTE?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - EXISTE EM PROFILES'
        ELSE '❌ NÃO - NÃO EXISTE EM PROFILES'
    END as status
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 2. VERIFICAR DADOS DO USUÁRIO LOGADO (se existir)
SELECT 
    'TESTE 2: DADOS DO USUÁRIO LOGADO' as teste,
    p.id,
    p.name,
    p.email,
    p.role,
    p.created_at
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 3. VERIFICAR SE O USUÁRIO EXISTE NA TABELA AUTH.USERS
SELECT 
    'TESTE 3: USUÁRIO EM AUTH.USERS?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - EXISTE EM AUTH'
        ELSE '❌ NÃO - NÃO EXISTE EM AUTH'
    END as status
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 4. VERIFICAR DADOS DO USUÁRIO EM AUTH.USERS (se existir)
SELECT 
    'TESTE 4: DADOS EM AUTH.USERS' as teste,
    u.id,
    u.email,
    u.created_at,
    u.email_confirmed_at,
    u.last_sign_in_at
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 5. LISTAR TODOS OS USUÁRIOS EM PROFILES (para comparar)
SELECT 
    'TESTE 5: TODOS OS PROFILES' as teste,
    p.id,
    p.name,
    p.email,
    p.role
FROM profiles p
ORDER BY p.created_at DESC
LIMIT 10;

-- =====================================================
-- INTERPRETAÇÃO DOS RESULTADOS:
-- =====================================================
-- TESTE 1 = ❌ → Usuário não existe em profiles (problema!)
-- TESTE 3 = ✅ → Usuário existe em auth.users (trigger não funcionou)
-- TESTE 1 = ✅ → Usuário existe em profiles (podemos continuar)
-- =====================================================

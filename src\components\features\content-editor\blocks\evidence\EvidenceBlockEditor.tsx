import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { generateUUID } from '@/lib/utils';
import { 
  Paperclip, 
  Upload, 
  Eye, 
  Download, 
  Trash2, 
  FileIcon, 
  ImageIcon,
  Settings,
  Plus,
  X
} from 'lucide-react';

import { EvidenceBlockContent, BlockConfig, defaultBlockConfig, Evidence } from '@/types';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { BlockCard } from '@/components/ui/BlockCard';
import { BlockConfigPanel } from '../shared/BlockConfigPanel';
import { evidenceBlockPresets } from '../shared/config-panel/constants/block-types/evidence/presets';
import { convertBlockTypePresetToStylePreset } from '../shared/config-panel/utils/preset-converter';

export interface EvidenceBlockEditorProps {
  content: EvidenceBlockContent;
  onChange: (content: EvidenceBlockContent) => void;
  mode: 'edit' | 'preview';
  config?: BlockConfig;
  setConfig?: (config: BlockConfig) => void;
}

const DEFAULT_EVIDENCE_CONTENT: EvidenceBlockContent = {
  title: 'Evidências/Anexos',
  description: 'Envie arquivos como evidência ou anexo para esta tarefa.',
  allowUpload: true,
  allowedFileTypes: ['image/*', 'video/*', '.pdf', '.doc', '.docx', '.txt'],
  maxFileSize: 10, // 10MB
  maxFiles: 10,
  showUploadedBy: true,
  showUploadDate: true,
  evidences: []
};

const FILE_TYPE_OPTIONS = [
  { value: 'image/*', label: 'Imagens' },
  { value: 'video/*', label: 'Vídeos' },
  { value: '.pdf', label: 'PDF' },
  { value: '.doc', label: 'Word (.doc)' },
  { value: '.docx', label: 'Word (.docx)' },
  { value: '.txt', label: 'Texto (.txt)' },
  { value: '.xlsx', label: 'Excel (.xlsx)' },
  { value: '.pptx', label: 'PowerPoint (.pptx)' },
  { value: '.zip', label: 'ZIP' },
  { value: '.rar', label: 'RAR' }
];

export const EvidenceBlockEditor: React.FC<EvidenceBlockEditorProps> = ({
  content,
  onChange,
  mode,
  config,
  setConfig
}) => {
  const safeConfig = config || defaultBlockConfig;
  const safeContent = { ...DEFAULT_EVIDENCE_CONTENT, ...content };

  const [newFileType, setNewFileType] = useState<string>('');

  const handleContentChange = useCallback((field: keyof EvidenceBlockContent, value: any) => {
    onChange({
      ...safeContent,
      [field]: value
    });
  }, [safeContent, onChange]);

  const handleAddFileType = () => {
    if (newFileType && !safeContent.allowedFileTypes?.includes(newFileType)) {
      const updatedTypes = [...(safeContent.allowedFileTypes || []), newFileType];
      handleContentChange('allowedFileTypes', updatedTypes);
      setNewFileType('');
    }
  };

  const handleRemoveFileType = (typeToRemove: string) => {
    const updatedTypes = safeContent.allowedFileTypes?.filter(type => type !== typeToRemove) || [];
    handleContentChange('allowedFileTypes', updatedTypes);
  };

  const handleAddEvidence = () => {
    const newEvidence: Evidence = {
      id: generateUUID(), // Use our UUID generator function
      type: 'file',
      fileName: 'Arquivo de exemplo.pdf',
      content: '#',
      uploadedBy: { 
        id: 'user-1', 
        name: 'Usuário Exemplo',
        email: '<EMAIL>',
        role: 'member',
        isActive: true
      },
      uploadedAt: new Date().toISOString(),
      taskId: '',
      status: 'pending'
    };
    
    const updatedEvidences = [...(safeContent.evidences || []), newEvidence];
    handleContentChange('evidences', updatedEvidences);
  };

  const handleRemoveEvidence = (evidenceId: string) => {
    const updatedEvidences = safeContent.evidences?.filter(e => e.id !== evidenceId) || [];
    handleContentChange('evidences', updatedEvidences);
  };

  // Configurações de estilo do card para preview
  const cardBgColor = safeConfig.card?.backgroundColor || '#ffffff';
  const cardTextColor = safeConfig.card?.color || '#000000';
  const cardFormat = safeConfig.card?.format || 'rounded';
  const cardBorder = safeConfig.card?.border?.enabled;
  const cardBorderColor = safeConfig.card?.border?.color || '#e5e5e5';
  const cardBorderWidth = safeConfig.card?.border?.width || 1;
  const cardShadow = safeConfig.card?.shadow?.enabled;
  const cardShadowDepth = safeConfig.card?.shadow?.depth || 1;
  const cardHover = safeConfig.card?.hover?.enabled;
  const cardHoverShadowDepth = safeConfig.card?.hover?.shadowDepth || 2;

  // Função para gerar box-shadow
  const getBoxShadow = (depth: number) => `0 ${depth * 2}px ${depth * 4}px rgba(0,0,0,${depth * 0.1})`;

  // Estilos do card
  const cardStyles: React.CSSProperties = {
    background: cardBgColor,
    color: cardTextColor,
    borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
    border: cardBorder ? `${cardBorderWidth}px solid ${cardBorderColor}` : 'none',
    boxShadow: cardShadow ? getBoxShadow(cardShadowDepth) : 'none',
    transition: cardHover ? 'box-shadow 0.3s ease, transform 0.2s ease' : 'none',
    cursor: cardHover ? 'pointer' : 'default'
  };

  // Handlers de hover
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = getBoxShadow(cardHoverShadowDepth);
      e.currentTarget.style.transform = 'translateY(-2px)';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = cardShadow ? getBoxShadow(cardShadowDepth) : 'none';
      e.currentTarget.style.transform = 'translateY(0)';
    }
  };

  if (mode === 'preview') {
    return (
      <BlockCard
        className="flex flex-col gap-1 p-4"
        style={cardStyles}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <BlockCardIcon
          config={safeConfig.icon}
          title={safeContent.title || 'Evidências/Anexos'}
          textColor={cardTextColor}
          content={
            <div className="space-y-3">
              {safeContent.description && (
                <p className="text-sm opacity-80">{safeContent.description}</p>
              )}
              
              <div className="flex items-center justify-between">
                <span className="text-sm opacity-60">
                  {safeContent.evidences?.length || 0} arquivo(s)
                </span>
                {safeContent.allowUpload && (
                  <Button size="sm" variant="outline" disabled>
                    <Upload className="w-4 h-4 mr-1" />
                    Enviar Arquivo
                  </Button>
                )}
              </div>

              {safeContent.evidences && safeContent.evidences.length > 0 && (
                <div className="space-y-2">
                  {safeContent.evidences.slice(0, 3).map((evidence) => (
                    <div key={evidence.id} className="flex items-center gap-3 p-2 border rounded opacity-60">
                      <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                        <FileIcon className="w-4 h-4 text-gray-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium truncate">{evidence.name}</p>
                        {safeContent.showUploadedBy && evidence.uploadedBy && (
                          <p className="text-xs opacity-60">
                            Por {typeof evidence.uploadedBy === 'string' ? evidence.uploadedBy : evidence.uploadedBy.name}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                  {safeContent.evidences.length > 3 && (
                    <p className="text-xs opacity-60 text-center">
                      ... e mais {safeContent.evidences.length - 3} arquivo(s)
                    </p>
                  )}
                </div>
              )}
            </div>
          }
        />
      </BlockCard>
    );
  }

  // Função para obter presets de estilo
  const getStylePresets = () => {
    return Object.entries(evidenceBlockPresets).map(([key, preset]) =>
      convertBlockTypePresetToStylePreset(key, preset, {
        name: key === 'default' ? 'Padrão' :
              key === 'professional' ? 'Profissional' :
              key === 'modern' ? 'Moderno' :
              key === 'secure' ? 'Seguro' :
              key === 'creative' ? 'Criativo' : key,
        description: key === 'default' ? 'Estilo padrão para evidências' :
                    key === 'professional' ? 'Visual sério para documentos formais' :
                    key === 'modern' ? 'Design moderno com cores vibrantes' :
                    key === 'secure' ? 'Visual seguro para documentos confidenciais' :
                    key === 'creative' ? 'Design criativo e colorido' : `Preset ${key}`
      })
    );
  };

  // Função para aplicar preset
  const handleApplyPreset = (preset: any) => {
    if (setConfig) {
      setConfig({
        ...safeConfig,
        ...preset.config
      });
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="content" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content">Conteúdo</TabsTrigger>
          <TabsTrigger value="settings">Configurações</TabsTrigger>
          <TabsTrigger value="config">Configuração</TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Paperclip className="w-5 h-5" />
                Configuração do Bloco de Evidências
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="evidence-title">Título</Label>
                <Input
                  id="evidence-title"
                  value={safeContent.title || ''}
                  onChange={(e) => handleContentChange('title', e.target.value)}
                  placeholder="Evidências/Anexos"
                />
              </div>

              <div>
                <Label htmlFor="evidence-description">Descrição</Label>
                <Textarea
                  id="evidence-description"
                  value={safeContent.description || ''}
                  onChange={(e) => handleContentChange('description', e.target.value)}
                  placeholder="Descrição opcional para orientar os usuários"
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="allow-upload"
                  checked={safeContent.allowUpload ?? true}
                  onCheckedChange={(checked) => handleContentChange('allowUpload', checked)}
                />
                <Label htmlFor="allow-upload">Permitir upload de arquivos</Label>
              </div>
            </CardContent>
          </Card>

          {/* Preview de evidências existentes */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Evidências de Exemplo</CardTitle>
              <Button size="sm" variant="outline" onClick={handleAddEvidence}>
                <Plus className="w-4 h-4 mr-1" />
                Adicionar Exemplo
              </Button>
            </CardHeader>
            <CardContent>
              {safeContent.evidences && safeContent.evidences.length > 0 ? (
                <div className="space-y-2">
                  {safeContent.evidences.map((evidence) => (
                    <div key={evidence.id} className="flex items-center gap-3 p-3 border rounded">
                      <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                        <FileIcon className="w-5 h-5 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{evidence.name}</p>
                        <p className="text-xs text-gray-600">Arquivo de exemplo</p>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveEvidence(evidence.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  Nenhuma evidência de exemplo. Clique em "Adicionar Exemplo" para visualizar como ficará.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Configurações de Upload
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="max-file-size">Tamanho máximo por arquivo (MB)</Label>
                <Input
                  id="max-file-size"
                  type="number"
                  min="1"
                  max="100"
                  value={safeContent.maxFileSize || 10}
                  onChange={(e) => handleContentChange('maxFileSize', parseInt(e.target.value) || 10)}
                />
              </div>

              <div>
                <Label htmlFor="max-files">Número máximo de arquivos</Label>
                <Input
                  id="max-files"
                  type="number"
                  min="1"
                  max="50"
                  value={safeContent.maxFiles || 10}
                  onChange={(e) => handleContentChange('maxFiles', parseInt(e.target.value) || 10)}
                />
              </div>

              <div>
                <Label>Tipos de arquivo permitidos</Label>
                <div className="flex gap-2 mt-2">
                  <Select value={newFileType} onValueChange={setNewFileType}>
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="Selecionar tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      {FILE_TYPE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button onClick={handleAddFileType} disabled={!newFileType}>
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="flex flex-wrap gap-2 mt-3">
                  {safeContent.allowedFileTypes?.map((type) => (
                    <Badge key={type} variant="secondary" className="flex items-center gap-1">
                      {FILE_TYPE_OPTIONS.find(opt => opt.value === type)?.label || type}
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-4 w-4 p-0"
                        onClick={() => handleRemoveFileType(type)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-uploaded-by"
                    checked={safeContent.showUploadedBy ?? true}
                    onCheckedChange={(checked) => handleContentChange('showUploadedBy', checked)}
                  />
                  <Label htmlFor="show-uploaded-by">Mostrar quem enviou o arquivo</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-upload-date"
                    checked={safeContent.showUploadDate ?? true}
                    onCheckedChange={(checked) => handleContentChange('showUploadDate', checked)}
                  />
                  <Label htmlFor="show-upload-date">Mostrar data de envio</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config">
          <BlockConfigPanel
            config={safeConfig}
            onChange={setConfig || (() => {})}
            getStylePresets={getStylePresets}
            onApplyPreset={handleApplyPreset}
            blockType="evidence"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

-- =====================
-- CORREÇÃO MANUAL: LIMPAR PERFIS DUPLICADOS
-- Data: 2024-12-19
-- Execute este script MANUALMENTE no Supabase SQL Editor
-- =====================

-- PASSO 1: Verificar duplicatas existentes
SELECT 'VERIFICANDO DUPLICATAS ANTES DA LIMPEZA:' as status;
SELECT 
    id,
    email,
    name,
    count(*) as count,
    array_agg(created_at order by created_at) as created_dates
FROM public.profiles 
GROUP BY id, email, name
HAVING count(*) > 1
ORDER BY count(*) DESC;

-- PASSO 2: Remover perfis duplicados mantendo apenas o mais recente
DO $$
DECLARE
    duplicate_record RECORD;
    deleted_count INTEGER := 0;
BEGIN
    -- Para cada ID duplicado
    FOR duplicate_record IN 
        SELECT id, count(*) as count
        FROM public.profiles 
        GROUP BY id 
        HAVING count(*) > 1
    LOOP
        -- Deletar todos exceto o mais recente
        DELETE FROM public.profiles 
        WHERE id = duplicate_record.id 
        AND created_at < (
            SELECT max(created_at) 
            FROM public.profiles 
            WHERE id = duplicate_record.id
        );
        
        GET DIAGNOSTICS deleted_count = ROW_COUNT;
        RAISE NOTICE 'Removidos % perfis duplicados para ID: %', deleted_count, duplicate_record.id;
    END LOOP;
END $$;

-- PASSO 3: Verificar se ainda há duplicatas
SELECT 'VERIFICANDO DUPLICATAS APÓS LIMPEZA:' as status;
SELECT 
    id,
    email,
    name,
    count(*) as count
FROM public.profiles 
GROUP BY id, email, name
HAVING count(*) > 1;

-- PASSO 4: Se não houver duplicatas, deve retornar vazio
SELECT 
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM public.profiles 
            GROUP BY id 
            HAVING count(*) > 1
        ) THEN '✅ NENHUMA DUPLICATA ENCONTRADA'
        ELSE '❌ AINDA HÁ DUPLICATAS'
    END as resultado;

-- PASSO 5: Contar total de perfis
SELECT 'TOTAL DE PERFIS APÓS LIMPEZA:' as status;
SELECT count(*) as total_profiles FROM public.profiles;

-- PASSO 6: Verificar perfil específico que estava dando erro
SELECT 'VERIFICANDO PERFIL ESPECÍFICO:' as status;
SELECT 
    id,
    email,
    name,
    created_at,
    updated_at
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
ORDER BY created_at;

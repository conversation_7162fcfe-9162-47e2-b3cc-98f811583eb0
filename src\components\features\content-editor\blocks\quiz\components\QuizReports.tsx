import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  Download, 
  Users, 
  Target, 
  Clock, 
  TrendingUp,
  FileText,
  PieChart,
  Activity,
  Calendar
} from 'lucide-react';

import { QuizContent, QuizStatistics, QuestionStatistics } from '@/types/quiz';

interface QuizReportsProps {
  quizContent: QuizContent;
}

// Dados mockados para demonstração
const mockStatistics: QuizStatistics = {
  quizId: 'quiz_1',
  totalAttempts: 45,
  uniqueUsers: 32,
  averageScore: 78.5,
  passRate: 75.0,
  averageTimeSpent: 420, // 7 minutos
  questionStats: [
    {
      questionId: 'q1',
      questionTitle: 'Qual é a capital do Brasil?',
      totalAnswers: 32,
      correctAnswers: 28,
      accuracyRate: 87.5,
      averageTimeSpent: 45,
      optionStats: [
        { optionId: 'opt1', optionText: 'Brasília', timesSelected: 28, selectionRate: 87.5 },
        { optionId: 'opt2', optionText: 'São Paulo', timesSelected: 3, selectionRate: 9.4 },
        { optionId: 'opt3', optionText: 'Rio de Janeiro', timesSelected: 1, selectionRate: 3.1 }
      ]
    },
    {
      questionId: 'q2',
      questionTitle: 'Quantos estados tem o Brasil?',
      totalAnswers: 32,
      correctAnswers: 24,
      accuracyRate: 75.0,
      averageTimeSpent: 60,
      optionStats: [
        { optionId: 'opt1', optionText: '26', timesSelected: 24, selectionRate: 75.0 },
        { optionId: 'opt2', optionText: '27', timesSelected: 6, selectionRate: 18.8 },
        { optionId: 'opt3', optionText: '25', timesSelected: 2, selectionRate: 6.2 }
      ]
    }
  ]
};

export const QuizReports: React.FC<QuizReportsProps> = ({ quizContent }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedQuestion, setSelectedQuestion] = useState<string | null>(null);

  const handleExportData = (format: 'csv' | 'excel') => {
    // Implementar exportação de dados
    console.log(`Exportando dados em formato ${format}`);
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getAccuracyColor = (rate: number): string => {
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Relatórios do Quiz</h3>
          <p className="text-sm text-gray-600">
            Análise de performance e estatísticas detalhadas
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => handleExportData('csv')}>
            <Download className="w-4 h-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleExportData('excel')}>
            <Download className="w-4 h-4 mr-2" />
            Excel
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="questions">Por Pergunta</TabsTrigger>
          <TabsTrigger value="users">Por Usuário</TabsTrigger>
          <TabsTrigger value="trends">Tendências</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Métricas Principais */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total de Tentativas</p>
                    <p className="text-2xl font-bold">{mockStatistics.totalAttempts}</p>
                  </div>
                  <Activity className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Usuários Únicos</p>
                    <p className="text-2xl font-bold">{mockStatistics.uniqueUsers}</p>
                  </div>
                  <Users className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Nota Média</p>
                    <p className="text-2xl font-bold">{mockStatistics.averageScore.toFixed(1)}%</p>
                  </div>
                  <Target className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Taxa de Aprovação</p>
                    <p className="text-2xl font-bold">{mockStatistics.passRate.toFixed(1)}%</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Gráfico de Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Performance por Pergunta
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockStatistics.questionStats.map((stat, index) => (
                  <div key={stat.questionId} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <p className="font-medium text-sm">
                          {index + 1}. {stat.questionTitle}
                        </p>
                        <p className="text-xs text-gray-500">
                          {stat.correctAnswers}/{stat.totalAnswers} respostas corretas
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${getAccuracyColor(stat.accuracyRate)}`}>
                          {stat.accuracyRate.toFixed(1)}%
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatTime(stat.averageTimeSpent)}
                        </p>
                      </div>
                    </div>
                    <Progress value={stat.accuracyRate} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Tempo Médio */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Análise de Tempo
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {formatTime(mockStatistics.averageTimeSpent)}
                  </p>
                  <p className="text-sm text-gray-600">Tempo Médio Total</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {formatTime(Math.min(...mockStatistics.questionStats.map(q => q.averageTimeSpent)))}
                  </p>
                  <p className="text-sm text-gray-600">Pergunta Mais Rápida</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">
                    {formatTime(Math.max(...mockStatistics.questionStats.map(q => q.averageTimeSpent)))}
                  </p>
                  <p className="text-sm text-gray-600">Pergunta Mais Lenta</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-6">
          <div className="grid gap-6">
            {mockStatistics.questionStats.map((stat, index) => (
              <Card key={stat.questionId}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        Pergunta {index + 1}: {stat.questionTitle}
                      </CardTitle>
                      <div className="flex gap-2 mt-2">
                        <Badge variant="outline">
                          {stat.totalAnswers} respostas
                        </Badge>
                        <Badge variant={stat.accuracyRate >= 70 ? 'default' : 'destructive'}>
                          {stat.accuracyRate.toFixed(1)}% de acerto
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Tempo médio</p>
                      <p className="font-semibold">{formatTime(stat.averageTimeSpent)}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {stat.optionStats && (
                    <div className="space-y-3">
                      <h4 className="font-medium">Distribuição de Respostas:</h4>
                      {stat.optionStats.map((option) => (
                        <div key={option.optionId} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">{option.optionText}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">
                                {option.timesSelected} ({option.selectionRate.toFixed(1)}%)
                              </span>
                            </div>
                          </div>
                          <Progress value={option.selectionRate} className="h-2" />
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Relatório por Usuário
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Relatórios Individuais</h3>
                <p className="text-sm">
                  Esta funcionalidade será implementada quando o quiz estiver ativo e houver dados de usuários reais.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Análise de Tendências
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-12 h-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Tendências Temporais</h3>
                <p className="text-sm">
                  Gráficos de performance ao longo do tempo serão exibidos aqui quando houver dados suficientes.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

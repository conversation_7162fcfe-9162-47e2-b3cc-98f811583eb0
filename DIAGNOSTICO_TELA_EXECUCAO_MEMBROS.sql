-- =====================================================
-- DIAGNOSTICAR PROBLEMA: TELA EXECUÇÃO PARA MEMBROS
-- =====================================================

-- STEP 1: VERIFICAR USUÁRIO ATUAL COMO EXECUTOR
SELECT 
    '🔍 VERIFICANDO USUÁRIO COMO EXECUTOR' as diagnostico,
    u.id as user_id,
    u.email as user_email,
    p.name as profile_name,
    p.role as profile_role,
    COUNT(te.task_id) as total_tasks_executor
FROM auth.users u
JOIN profiles p ON u.id = p.id
LEFT JOIN task_executors te ON u.id = te.user_id
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
GROUP BY u.id, u.email, p.name, p.role;

-- STEP 2: LISTAR TODAS AS TAREFAS QUE O USUÁRIO É EXECUTOR
SELECT 
    '📋 TAREFAS COMO EXECUTOR' as categoria,
    t.id as task_id,
    t.title as task_title,
    t.status as task_status,
    s.name as stage_name,
    proj.name as project_name,
    te.created_at as added_as_executor_at
FROM task_executors te
JOIN tasks t ON te.task_id = t.id
JOIN stages s ON t.stage_id = s.id
JOIN projects proj ON s.project_id = proj.id
WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
ORDER BY te.created_at DESC;

-- STEP 3: VERIFICAR PERMISSÕES DE ACESSO AOS CONTENT BLOCKS
SELECT 
    '🔒 VERIFICANDO ACESSO CONTENT BLOCKS' as categoria,
    tcb.task_id,
    t.title as task_title,
    COUNT(tcb.id) as total_content_blocks,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM task_executors te 
            WHERE te.task_id = tcb.task_id 
            AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
        ) THEN '✅ ACESSO LIBERADO'
        ELSE '❌ ACESSO NEGADO'
    END as acesso_status
FROM task_content_blocks tcb
JOIN tasks t ON tcb.task_id = t.id
WHERE tcb.task_id IN (
    SELECT te.task_id FROM task_executors te
    WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
)
GROUP BY tcb.task_id, t.title
ORDER BY t.title;

-- STEP 4: LISTAR TODAS AS TAREFAS DISPONÍVEIS NO SISTEMA
SELECT 
    '📊 RESUMO GERAL TAREFAS' as categoria,
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4' THEN 1 END) as tasks_user_executor,
    COUNT(CASE WHEN t.assigned_to = '4b09be1f-5187-44c0-9b53-87b7c57e45b4' THEN 1 END) as tasks_user_responsible
FROM tasks t
LEFT JOIN task_executors te ON t.id = te.task_id AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 5: VERIFICAR PROJETOS ONDE O USUÁRIO É MEMBRO
SELECT 
    '👥 PROJETOS COMO MEMBRO' as categoria,
    p.id as project_id,
    p.name as project_name,
    pm.role as member_role,
    COUNT(DISTINCT s.id) as total_stages,
    COUNT(DISTINCT t.id) as total_tasks
FROM project_members pm
JOIN projects p ON pm.project_id = p.id
LEFT JOIN stages s ON p.id = s.project_id
LEFT JOIN tasks t ON s.id = t.stage_id
WHERE pm.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
GROUP BY p.id, p.name, pm.role
ORDER BY p.name;

-- =====================================================
-- INSTRUÇÃO:
-- Execute este diagnóstico no Supabase SQL Editor
-- para identificar por que as tarefas não aparecem
-- na tela de execução para membros
-- =====================================================

import React, { useState } from 'react';
import {
  Briefcase, Building, Calendar, ChartBar, BarChart3, PieChart, DollarSign, FileText, Globe, Users, User, UserCog, UserPlus, UserCheck, UserX, UserSquare, AtSign, Mail, Phone, MessageCircle, MessageSquare, Star, Award, Shield, Clipboard, Target, CheckCircle, XCircle, List, CheckSquare, ClipboardList, Edit, Flag, Folder, FolderOpen, Layers, Tag, Timer, AlarmClock, Play, Pause, Repeat, RefreshCcw, Upload, Download, Link, Share, Paperclip, Handshake, Banknote, Receipt, Wallet, Scale, Gavel, ClipboardCheck, ClipboardEdit, ClipboardX, CalendarCheck, CalendarX, CalendarPlus, CalendarMinus, Trophy, Medal, FlagTriangleRight, Gem, Puzzle, Brain, Workflow, KanbanSquare,
  UserRound, UserSearch, User2, UserSquare2, UserCheck2, UserPlus2, UserMinus2, UserX2, UserCog2, UsersRound,
  Code, Code2, CodeSquare, Monitor, MonitorSmartphone, Smartphone, Tablet, Cpu, Database, Cloud, Camera, Image, Video, Music, Mic, Palette, PenTool, Brush, Lightbulb, Rocket, Zap, Server, Terminal, Bug, ShieldCheck, ShieldAlert, LockKeyhole, Globe2, Wifi, Bluetooth, Satellite, CloudUpload, CloudDownload, CloudLightning, DatabaseBackup, DatabaseZap, GitBranch, GitCommit, GitMerge, GitPullRequest, GitCompare, GitFork, Github,
  GraduationCap, Book, BookOpen, BookOpenCheck, BookKey, BookLock, BookUser, BookHeart, BookPlus, BookMinus, BookX, BookCheck, BookOpenText,
  Home, Map, Compass, Search, Lock, Unlock, Eye, EyeOff, Sun, Moon, Bell, Settings, HelpCircle, Info, AlertCircle, ThumbsUp, ThumbsDown, Bookmark, BookDown,
  PlayCircle
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { BlockPicker } from 'react-color';
import * as Popover from '@radix-ui/react-popover';
import { VideoBlockContent, BlockConfig, defaultBlockConfig } from '@/types';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { BlockCard } from '@/components/ui/BlockCard';
import './VideoBlockEditor.css';

export const ICONS = {
  Briefcase, Building, Calendar, ChartBar, BarChart3, PieChart, DollarSign, FileText, Globe, Users, User, UserCog, UserPlus, UserCheck, UserX, UserSquare, AtSign, Mail, Phone, MessageCircle, MessageSquare, Star, Award, Shield, Clipboard, Target, CheckCircle, XCircle, List, CheckSquare, ClipboardList, Edit, Flag, Folder, FolderOpen, Layers, Tag, Timer, AlarmClock, Play, Pause, Repeat, RefreshCcw, Upload, Download, Link, Share, Paperclip, Handshake, Banknote, Receipt, Wallet, Scale, Gavel, ClipboardCheck, ClipboardEdit, ClipboardX, CalendarCheck, CalendarX, CalendarPlus, CalendarMinus, Trophy, Medal, FlagTriangleRight, Gem, Puzzle, Brain, Workflow, KanbanSquare,
  UserRound, UserSearch, User2, UserSquare2, UserCheck2, UserPlus2, UserMinus2, UserX2, UserCog2, UsersRound,
  Code, Code2, CodeSquare, Monitor, MonitorSmartphone, Smartphone, Tablet, Cpu, Database, Cloud, Camera, Image, Video, Music, Mic, Palette, PenTool, Brush, Lightbulb, Rocket, Zap, Server, Terminal, Bug, ShieldCheck, ShieldAlert, LockKeyhole, Globe2, Wifi, Bluetooth, Satellite, CloudUpload, CloudDownload, CloudLightning, DatabaseBackup, DatabaseZap, GitBranch, GitCommit, GitMerge, GitPullRequest, GitCompare, GitFork, Github,
  GraduationCap, Book, BookOpen, BookOpenCheck, BookKey, BookLock, BookUser, BookHeart, BookPlus, BookMinus, BookX, BookCheck, BookOpenText,
  Home, Map, Compass, Search, Lock, Unlock, Eye, EyeOff, Sun, Moon, Bell, Settings, HelpCircle, Info, AlertCircle, ThumbsUp, ThumbsDown, Bookmark, BookDown,
  PlayCircle
};

function truncateText(text: string, maxLength: number) {
  if (!text) return '';
  return text.length > maxLength ? text.slice(0, maxLength).trim() + '...' : text;
}

function isYoutubeUrl(url: string) {
  return /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\//.test(url || '');
}
function getYoutubeEmbedUrl(url: string) {
  if (!url) return '';
  const match = url.match(/[?&]v=([^&#]+)/) || url.match(/youtu\.be\/([^?&#]+)/);
  const videoId = match ? match[1] : '';
  return videoId ? `https://www.youtube.com/embed/${videoId}` : '';
}
function isCanvaUrl(url: string) {
  return /^(https?:\/\/)?(www\.)?canva\.com\//.test(url || '');
}
function isCanvaEmbedUrl(url: string) {
  return /canva\.com\/design\/.+\/(watch|view)\?embed/.test(url || '');
}

// Função utilitária para ajustar link do Canva
function autoEmbedCanvaUrl(url) {
  if (/canva\.com\/design\/.+\/(watch|view)/.test(url) && !/[?&]embed($|&)/.test(url)) {
    return url + (url.includes('?') ? '&embed' : '?embed');
  }
  return url;
}

export const DEFAULTS = {
  title: 'Novo Vídeo',
  description: '',
  url: '',
  showInline: false,
  buttonText: 'Assistir Agora',
  icon: 'PlayCircle',
  iconType: 'lucide',
  customImageUrl: '',
  iconColor: '#312e81',
  iconBgColor: '#f5f3ff',
  iconBorder: false,
  iconBorderColor: '#312e81',
  iconBorderWidth: 2,
  iconShadow: false,
  iconHover: false,
  iconShape: 'circle',
  cardBgColor: '#f5f3ff',
  cardTextColor: '#312e81',
  cardStyle: 'rounded-shadow',
  cardBorder: false,
  cardBorderColor: '#312e81',
  cardBorderWidth: 2,
  cardShadow: false,
  cardHover: false,
  buttonBgColor: '#7c3aed',
  buttonTextColor: '#fff',
  buttonStyle: 'rounded',
  buttonBorder: false,
  buttonBorderColor: '#7c3aed',
  buttonBorderWidth: 2,
  buttonShadow: false,
  buttonHover: true,
};

/**
 * Editor para blocos de vídeo.
 * @param editContent Conteúdo do bloco (tipado)
 * @param setEditContent Callback para atualizar o conteúdo
 * @param mode 'edit' para edição, 'preview' para visualização
 * @param config Configurações do bloco
 * @param isPreview Indica se é uma visualização prévia
 */
export interface VideoBlockEditorProps {
  editContent: VideoBlockContent;
  setEditContent: (c: VideoBlockContent) => void;
  mode: 'edit' | 'preview';
  config?: BlockConfig;
  isPreview?: boolean;
  className?: string;
}

export const VideoBlockEditor: React.FC<VideoBlockEditorProps> = ({
  editContent,
  setEditContent,
  mode,
  config,
  isPreview = false,
  className
}) => {
  const [iconPickerOpen, setIconPickerOpen] = useState(false);
  const [iconSearch, setIconSearch] = useState('');
  const [useCustomImage, setUseCustomImage] = useState(!!editContent.customImageUrl);
  const safeConfig = config || defaultBlockConfig;

  // Função para restaurar apenas o card
  const restoreCardDefaults = () => {
    setEditContent({
      ...editContent,
      cardBgColor: DEFAULTS.cardBgColor,
      cardTextColor: DEFAULTS.cardTextColor,
      cardStyle: DEFAULTS.cardStyle,
      cardBorder: DEFAULTS.cardBorder,
      cardBorderColor: DEFAULTS.cardBorderColor,
      cardBorderWidth: DEFAULTS.cardBorderWidth,
      cardShadow: DEFAULTS.cardShadow,
      cardHover: DEFAULTS.cardHover,
    });
  };
  // Função para restaurar apenas o ícone
  const restoreIconDefaults = () => {
    setEditContent({
      ...editContent,
      icon: DEFAULTS.icon,
      iconType: DEFAULTS.iconType,
      iconColor: DEFAULTS.iconColor,
      iconBgColor: DEFAULTS.iconBgColor,
      customImageUrl: DEFAULTS.customImageUrl,
      iconBorder: DEFAULTS.iconBorder,
      iconBorderColor: DEFAULTS.iconBorderColor,
      iconBorderWidth: DEFAULTS.iconBorderWidth,
      iconShadow: DEFAULTS.iconShadow,
      iconHover: DEFAULTS.iconHover,
      iconShape: DEFAULTS.iconShape,
    });
  };
  // Função para restaurar apenas o botão
  const restoreButtonDefaults = () => {
    if (typeof safeConfig === 'object' && safeConfig) {
      safeConfig.button = {
        backgroundColor: '#7c3aed',
        color: '#ffffff',
        style: 'rounded',
        size: 'medium',
        position: 'bottom-center',
        border: { enabled: false, color: '#e5e5e5', width: 1 },
        shadow: { enabled: false, depth: 2 },
        hover: { enabled: false, shadowDepth: 3 },
      };
    }
  };

  // Picker visual de ícones
  const iconNames = Object.keys(ICONS);
  const filteredIcons = iconNames.filter((k) =>
    k.toLowerCase().includes(iconSearch.toLowerCase())
  );

  // Preview do bloco de vídeo
  const renderPreview = () => {
    const iconName = editContent.icon;
    const Icon = ICONS[iconName] || ICONS['PlayCircle'];
    const cardBgColor = safeConfig.card?.backgroundColor || '#f5f3ff';
    const cardTextColor = safeConfig.card?.font?.color || '#312e81';
    const cardFormat = safeConfig.card?.format || 'rounded';
    const cardClass = `p-6`;
    const buttonConfig = safeConfig.button || {};
    const buttonBgColor = buttonConfig.backgroundColor || '#7c3aed';
    const buttonTextColor = buttonConfig.color || '#fff';
    const buttonStyle = buttonConfig.style || 'rounded';
    const buttonBorder = buttonConfig.border?.enabled;
    const buttonBorderColor = buttonConfig.border?.color || buttonBgColor;
    const buttonBorderWidth = buttonConfig.border?.width || 2;
    const buttonShadow = buttonConfig.shadow?.enabled;
    const buttonHover = buttonConfig.hover?.enabled;
    const buttonText = buttonConfig.text || editContent.buttonText || 'Assistir Agora';
    // Estilos do botão
    const buttonStyleObj = {
      background: buttonBgColor,
      color: buttonTextColor,
      borderRadius: buttonStyle === 'rounded' ? '9999px' : buttonStyle === 'flat' ? '0' : '9999px',
      padding: '0.5rem 1.5rem',
      fontWeight: 600,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: buttonShadow ? '0 6px 24px rgba(60,60,60,0.25), 0 1.5px 4px rgba(60,60,60,0.10)' : undefined,
      border: buttonBorder ? `${buttonBorderWidth}px solid ${buttonBorderColor}` : 'none',
      transition: buttonHover ? 'transform 0.15s, box-shadow 0.15s, filter 0.15s' : undefined,
      cursor: 'pointer',
    };
    const iconPosition = safeConfig.icon?.position || 'left-title';
    const player = null; // Substitua por seu player real, se necessário
    const actionButton = null; // Substitua pelo botão real, se necessário

    if (iconPosition === 'left-content' || iconPosition === 'right-content') {
    return (
        <div className={`p-4 w-full rounded-lg border shadow-sm flex flex-row items-stretch gap-2 ${className || ''}`}
            style={{
            background: cardBgColor,
            color: cardTextColor,
            borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
            boxShadow: safeConfig.card?.shadow?.enabled
              ? `0 2px ${2 * (safeConfig.card?.shadow?.depth || 1)}px #0002`
              : 'none',
          }}
        >
          <BlockCardIcon
            config={{ ...safeConfig.icon, position: iconPosition }}
            title={editContent.title}
            description={editContent.description}
            content={
              <>
                {editContent.title && (
                  <h4 className="font-semibold text-lg truncate" style={{ color: cardTextColor }}>{truncateText(editContent.title, 22)}</h4>
                )}
                {editContent.description && (
                  <div className="text-sm opacity-80 truncate" style={{ color: cardTextColor }}>{truncateText(editContent.description, 40)}</div>
                )}
                {player}
                {actionButton}
              </>
            }
          />
        </div>
      );
    }

    // Demais posições: ícone ao lado do título/descrição
    return (
      <div className={`p-4 w-full rounded-lg border shadow-sm flex flex-col gap-1 ${className || ''}`}
                style={{
          background: cardBgColor,
          color: cardTextColor,
          borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
          boxShadow: safeConfig.card?.shadow?.enabled
            ? `0 2px ${2 * (safeConfig.card?.shadow?.depth || 1)}px #0002`
            : 'none',
        }}
      >
        <BlockCardIcon
          config={safeConfig.icon}
          title={truncateText(editContent.title, 22)}
          description={truncateText(editContent.description, 40)}
          textColor={cardTextColor}
        />
        {/* Player e botão fora do BlockCardIcon */}
        {player}
        {actionButton}
      </div>
    );
  };

  if (mode === 'edit') {
    return (
      <div className="space-y-2 min-w-0">
        {/* Apenas campos de edição, sem header visual duplicado */}
        <div className="space-y-2">
          <Input
            value={editContent.title || ''}
            onChange={e => setEditContent({ ...editContent, title: e.target.value })}
            placeholder="Título do vídeo"
            className="mt-2"
          />
          <Textarea
            value={editContent.description || ''}
            onChange={e => setEditContent({ ...editContent, description: e.target.value })}
            placeholder="Descrição do vídeo"
            className="mt-2"
          />
          <Input
            value={editContent.url || ''}
            onChange={e => {
              const rawUrl = e.target.value;
              const adjustedUrl = autoEmbedCanvaUrl(rawUrl);
              setEditContent({ ...editContent, url: adjustedUrl });
            }}
            placeholder="URL do vídeo"
            className="mt-2"
          />
          <div className="flex items-center gap-2 mt-2">
            <Checkbox
              checked={!!editContent.showInline}
              onCheckedChange={v => setEditContent({ ...editContent, showInline: !!v })}
              id="showInline"
            />
            <label htmlFor="showInline" className="text-sm cursor-pointer select-none">Exibir vídeo inline (embed)</label>
          </div>
        </div>

        <Dialog open={iconPickerOpen} onOpenChange={setIconPickerOpen}>
          <DialogContent className="max-w-2xl">
            <DialogTitle>Escolher ícone</DialogTitle>
            <Input
              value={iconSearch}
              onChange={e => setIconSearch(e.target.value)}
              placeholder="Buscar ícone..."
              className="mb-2"
            />
            <div className="grid grid-cols-6 md:grid-cols-8 gap-2 max-h-64 overflow-y-auto">
              {filteredIcons.length === 0 && (
                <span className="col-span-full text-center text-gray-400 text-xs">Nenhum ícone encontrado</span>
              )}
              {filteredIcons.map((icon) => {
                const IconComp = ICONS[icon];
                if (!IconComp) return null;
                const selected = editContent.icon === icon && editContent.iconType === 'lucide';
                return (
                  <button
                    key={icon}
                    type="button"
                    className={`flex flex-col items-center justify-center border rounded p-1 transition-all ${selected ? 'border-blue-600 bg-blue-50' : 'border-gray-200 hover:border-blue-400'} focus:outline-none`}
                    onClick={() => {
                      setEditContent({ ...editContent, icon: icon, iconType: 'lucide' });
                      setIconPickerOpen(false);
                    }}
                  >
                    <IconComp className="w-6 h-6" color={editContent.iconColor || safeConfig.icon?.appearance?.color || '#312e81'} />
                    <span className="text-[10px] mt-1">{icon}</span>
                  </button>
                );
              })}
            </div>
          </DialogContent>
        </Dialog>

        {mode === 'edit' && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Texto do botão</label>
            <input
              type="text"
              className="w-full border rounded px-3 py-2"
              value={editContent.buttonText || ''}
              onChange={e => setEditContent({ ...editContent, buttonText: e.target.value })}
              placeholder="Ex: Assistir Agora"
              maxLength={40}
            />
          </div>
        )}
      </div>
    );
  }
  if (mode === 'preview') {
    return <VideoBlockPreview editContent={editContent} config={config} className={className} />;
  }
};

export const VideoBlockPreview: React.FC<{ editContent: VideoBlockContent; config?: BlockConfig; className?: string }> = ({ editContent, config, className }) => {
  const safeConfig = config || defaultBlockConfig;
  // LOGS DETALHADOS PARA DEPURAÇÃO
  console.log('VideoBlockPreview config.card:', safeConfig.card);
  console.log('Borda:', safeConfig.card?.border);
  console.log('Sombra:', safeConfig.card?.shadow);
  console.log('Hover:', safeConfig.card?.hover);

  // Forçar valores para testar visualmente
  const borderEnabled = safeConfig.card?.border?.enabled;
  const borderWidth = borderEnabled ? (safeConfig.card?.border?.width ?? 4) : 0;
  const borderColor = borderEnabled ? (safeConfig.card?.border?.color ?? '#ff0000') : 'transparent';
  const shadowEnabled = safeConfig.card?.shadow?.enabled;
  const shadowDepth = shadowEnabled ? (safeConfig.card?.shadow?.depth ?? 4) : 0;
  const hoverEnabled = safeConfig.card?.hover?.enabled;
  const hoverShadowDepth = hoverEnabled ? (safeConfig.card?.hover?.shadowDepth ?? 8) : 0;

  const cardBgColor = safeConfig.card?.backgroundColor || '#f5f3ff';
  const cardTextColor = safeConfig.card?.font?.color || '#312e81';
  const cardFormat = safeConfig.card?.format || 'rounded';
  const iconPosition = safeConfig.icon?.position || 'left-title';

  // Player de vídeo
  let player = null;
  if (editContent.showInline) {
    if (editContent.url && /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\//.test(editContent.url)) {
      // YouTube embed
      const match = editContent.url.match(/[?&]v=([^&#]+)/) || editContent.url.match(/youtu\.be\/([^?&#]+)/);
      const videoId = match ? match[1] : '';
      const embedUrl = videoId ? `https://www.youtube.com/embed/${videoId}` : '';
      player = (
        <div style={{ position: 'relative', width: '100%', height: 0, paddingTop: '56.25%', borderRadius: 10, overflow: 'hidden' }}>
          <iframe
            src={embedUrl}
            style={{ position: 'absolute', width: '100%', height: '100%', top: 0, left: 0, border: 'none' }}
            allow="fullscreen"
            title="Vídeo"
          />
        </div>
      );
    } else if (editContent.url && /^(https?:\/\/)?(www\.)?canva\.com\//.test(editContent.url)) {
      // Canva embed
      const isEmbed = /canva\.com\/design\/.+\/(watch|view)\?embed/.test(editContent.url);
      if (isEmbed) {
        player = (
          <div style={{ position: 'relative', width: '100%', height: 0, paddingTop: '56.25%', boxShadow: '0 2px 8px 0 rgba(63,69,81,0.16)', overflow: 'hidden', borderRadius: 10, willChange: 'transform' }}>
            <iframe
              loading="lazy"
              style={{ position: 'absolute', width: '100%', height: '100%', top: 0, left: 0, border: 'none', padding: 0, margin: 0 }}
              src={editContent.url}
              allow="fullscreen"
              title="Canva Embed"
            />
          </div>
        );
      } else {
        player = (
          <div className="flex flex-col items-center justify-center p-4 bg-yellow-50 border border-yellow-200 rounded">
            <span className="text-yellow-800 text-sm mb-2">
              O Canva não permite visualização inline para este link. Peça o link de compartilhamento com <b>?embed</b> ou abra em nova aba.
            </span>
            <a
              href={editContent.url}
              target="_blank"
              rel="noopener noreferrer"
              className="px-4 py-2 bg-yellow-600 text-white rounded font-semibold hover:bg-yellow-700 transition"
            >
              Abrir no Canva
            </a>
          </div>
        );
      }
    } else if (editContent.url) {
      // Vídeo direto
      player = <video src={editContent.url} controls style={{ width: '100%', borderRadius: 10 }} />;
    }
  }

  // Botão de ação
  let actionButton = null;
  if (!editContent.showInline) {
    const buttonConfig = safeConfig.button || {};
    const buttonBgColor = buttonConfig.backgroundColor || '#7c3aed';
    const buttonTextColor = buttonConfig.color || '#fff';
    const buttonStyle = buttonConfig.style || 'rounded';
    const buttonBorder = buttonConfig.border?.enabled;
    const buttonBorderColor = buttonConfig.border?.color || buttonBgColor;
    const buttonBorderWidth = buttonConfig.border?.width || 2;
    const buttonShadow = buttonConfig.shadow?.enabled;
    const buttonHover = buttonConfig.hover?.enabled;
    const buttonText = buttonConfig.text || editContent.buttonText || 'Assistir Agora';
    const style = {
      background: buttonBgColor,
      color: buttonTextColor,
      borderRadius: buttonStyle === 'rounded' ? '9999px' : buttonStyle === 'flat' ? '0' : '9999px',
      padding: '0.5rem 1.5rem',
      fontWeight: 600,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: buttonShadow ? '0 6px 24px rgba(60,60,60,0.25), 0 1.5px 4px rgba(60,60,60,0.10)' : undefined,
      border: buttonBorder ? `${buttonBorderWidth}px solid ${buttonBorderColor}` : 'none',
      transition: buttonHover ? 'transform 0.15s, box-shadow 0.15s, filter 0.15s' : undefined,
      cursor: 'pointer',
    };
    actionButton = (
      <div className="flex gap-2 mt-2">
        <button
          style={style}
          className={buttonHover ? 'hover:scale-105 hover:shadow-2xl hover:brightness-105 transition-all' : ''}
        >
          {(() => {
            const IconComp = (editContent.icon && (ICONS as any)[editContent.icon]);
            return IconComp ? <IconComp className="w-4 h-4 mr-1" color={editContent.iconColor || safeConfig.icon?.appearance?.color || '#312e81'} /> : null;
          })()}
          {buttonText}
        </button>
      </div>
    );
  }

  if (iconPosition === 'left-content' || iconPosition === 'right-content') {
    const iconElement = (
      <div className="flex flex-col justify-center flex-shrink-0">
        <BlockCardIcon config={{ ...safeConfig.icon, position: iconPosition }} />
      </div>
    );
    const contentElement = (
      <div className="flex-1 flex flex-col justify-center h-full min-w-0 max-w-full gap-2 overflow-x-hidden">
        {editContent.title && (
          <div className="font-bold text-lg min-w-0 max-w-full break-words line-clamp-2" title={editContent.title}>{editContent.title}</div>
        )}
        {editContent.description && (
          <div className="text-sm font-normal opacity-80 min-w-0 max-w-full break-words line-clamp-3" title={editContent.description}>{editContent.description}</div>
        )}
        {player}
        {actionButton}
      </div>
    );
    return (
      <div className={`p-4 w-full rounded-lg border shadow-sm flex flex-row items-stretch gap-2 ${className || ''}`}
        style={{
          background: cardBgColor,
          color: cardTextColor,
          borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
          boxShadow: safeConfig.card?.shadow?.enabled
            ? `0 2px ${2 * (safeConfig.card?.shadow?.depth || 1)}px #0002`
            : 'none',
        }}
      >
        {iconPosition === 'left-content' && iconElement}
        {contentElement}
        {iconPosition === 'right-content' && iconElement}
      </div>
    );
  }

  // Demais posições: ícone ao lado do título/descrição
  return (
    <BlockCard
      className={`flex flex-col gap-1 ${className || ''}`}
      style={{
        background: safeConfig.card?.backgroundColor || '#f5f3ff',
        color: safeConfig.card?.font?.color || '#312e81',
        borderRadius: safeConfig.card?.format === 'pill' ? 9999 : safeConfig.card?.format === 'square' ? 0 : 12,
        border: borderEnabled ? `${borderWidth}px solid ${borderColor}` : 'none',
        boxShadow: shadowEnabled ? `0 2px ${2 * shadowDepth}px #0002` : 'none',
        padding: '1.5rem',
        transition: hoverEnabled ? 'box-shadow 0.2s, filter 0.2s' : undefined,
      }}
      onMouseEnter={e => {
        if (hoverEnabled) {
          (e.currentTarget as HTMLElement).style.boxShadow = `0 2px ${2 * hoverShadowDepth}px #0004`;
        }
      }}
      onMouseLeave={e => {
        if (hoverEnabled) {
          (e.currentTarget as HTMLElement).style.boxShadow = shadowEnabled
            ? `0 2px ${2 * shadowDepth}px #0002`
            : 'none';
        }
      }}
    >
      <BlockCardIcon
        config={safeConfig.icon}
        title={editContent.title}
        description={editContent.description}
      />
      {player}
      {actionButton}
    </BlockCard>
  );
};

/* Adicione este bloco ao final do arquivo ou em um arquivo CSS importado */

// Se estiver usando CSS-in-JS, pode usar styled-components ou similar. Caso contrário, crie um arquivo CSS e importe.

// Exemplo para CSS global:
// src/components/features/content-editor/blocks/video/VideoBlockEditor.css

/*
.force-preview-style {
  border: 4px solid #00ff00 !important;
  box-shadow: 0 0 24px 8px #ff00ff !important;
  padding: 1.5rem !important;
}
*/

// Se preferir, posso criar o arquivo CSS e importar no topo do VideoBlockEditor.tsx.
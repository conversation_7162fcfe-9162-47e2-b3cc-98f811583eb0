-- =====================================================
-- SCRIPT PARA ADICIONAR MEMBROS AO PROJETO E TESTAR
-- =====================================================

-- 1. VER O ESTADO ATUAL
SELECT 'ESTADO ATUAL' as status;

SELECT 
    'PROFILES DISPONÍVEIS' as tipo,
    id,
    name,
    email,
    is_active
FROM profiles 
WHERE is_active = true
ORDER BY name;

SELECT 
    'PROJECT ATUAL' as tipo,
    id,
    name,
    status
FROM projects
ORDER BY created_at DESC
LIMIT 1;

SELECT 
    'MEMBROS ATUAIS' as tipo,
    pm.id,
    pm.user_id,
    pm.role,
    prof.name as user_name
FROM project_members pm
JOIN profiles prof ON prof.id = pm.user_id
WHERE pm.project_id = (SELECT id FROM projects ORDER BY created_at DESC LIMIT 1);

-- =====================================================
-- 2. ADICIONAR MEMBROS MANUALMENTE PARA TESTE
-- =====================================================

-- Primeiro, vamos adicionar todos os profiles como membros
-- Pegar o project_id mais recente
DO $$
DECLARE
    project_uuid uuid;
    profile_record RECORD;
BEGIN
    -- Pegar o projeto mais recente
    SELECT id INTO project_uuid 
    FROM projects 
    ORDER BY created_at DESC 
    LIMIT 1;
    
    RAISE NOTICE 'Usando projeto: %', project_uuid;
    
    -- Para cada profile ativo
    FOR profile_record IN 
        SELECT id, name, email 
        FROM profiles 
        WHERE is_active = true
    LOOP
        -- Verificar se já é membro
        IF NOT EXISTS (
            SELECT 1 FROM project_members 
            WHERE project_id = project_uuid 
            AND user_id = profile_record.id
        ) THEN
            -- Adicionar como membro
            INSERT INTO project_members (project_id, user_id, role)
            VALUES (project_uuid, profile_record.id, 'member');
            
            RAISE NOTICE 'Adicionado: % (%)', profile_record.name, profile_record.email;
        ELSE
            RAISE NOTICE 'Já é membro: % (%)', profile_record.name, profile_record.email;
        END IF;
    END LOOP;
END $$;

-- =====================================================
-- 3. VERIFICAR RESULTADO
-- =====================================================

SELECT 'RESULTADO FINAL' as status;

SELECT 
    'MEMBROS APÓS INSERÇÃO' as tipo,
    pm.id,
    pm.user_id,
    pm.role,
    prof.name as user_name,
    prof.email
FROM project_members pm
JOIN profiles prof ON prof.id = pm.user_id
WHERE pm.project_id = (SELECT id FROM projects ORDER BY created_at DESC LIMIT 1)
ORDER BY prof.name;

-- Contagem final
SELECT 
    'RESUMO FINAL' as categoria,
    'Total de profiles ativos' as item,
    COUNT(*)::TEXT as valor
FROM profiles 
WHERE is_active = true

UNION ALL

SELECT 
    '',
    'Total de membros no projeto',
    COUNT(*)::TEXT
FROM project_members 
WHERE project_id = (SELECT id FROM projects ORDER BY created_at DESC LIMIT 1);

import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Header } from '@/components/layout/Header';
import { TaskForm } from '@/components/forms/TaskForm';
import { RichContentEditor } from '@/components/features/content-editor';
import { TextEditorWrapper } from '@/components/features/content-editor/blocks/text';
import { isLexicalJson, convertTextToLexicalJson, EMPTY_LEXICAL_STATE } from '@/lib/utils';
import { useToast } from '@/hooks/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ContentBlock } from '@/types';
import * as LucideIcons from 'lucide-react';
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Play,
  Pause,
  Upload,
  MessageCircle,
  FileText,
  Video,
  Image as ImageIcon,
  Paperclip,
  Send,
  Eye,
  Edit,
  Trash2,
  Plus,
  Settings,
  PlayCircle,
  FolderOpen,
  Target,
  Loader2,
  Users,
  BarChart3,
  Flag,
  Timer,
  Star,
  Bookmark,
  Share2,
  Download,
  MoreVertical
} from 'lucide-react';
import { taskService } from '@/services/taskService';
import { userService } from '@/services/userService';
import { stageService } from '@/services/stageService';
import { projectService } from '@/services/projectService';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { useAuth } from '@/auth/useAuth';
import { SidebarProvider, Sidebar, SidebarTrigger } from '@/components/ui/sidebar';
import { useSidebarMenu } from '@/components/ui/sidebar';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { TaskHeader } from '@/pages/TaskDetails/TaskHeader';
import { TaskQuickActions } from '@/pages/TaskDetails/TaskQuickActions';
import { TaskTeamPanel } from '@/pages/TaskDetails/TaskTeamPanel';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';

// Importar todas as funções utilitárias da versão original
function isYoutubeUrl(url: string): boolean {
  return /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/.test(url);
}

function getYoutubeEmbedUrl(url: string): string {
  const match = url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
  return match ? `https://www.youtube.com/embed/${match[1]}` : url;
}

function isCanvaEmbedUrl(url: string): boolean {
  return /canva\.com\/design\/.+\/(watch|view)/.test(url);
}

function autoEmbedCanvaUrl(url: string): string {
  if (/canva\.com\/design\/.+\/(watch|view)/.test(url) && !/[?&]embed($|&)/.test(url)) {
    return url + (url.includes('?') ? '&embed' : '?embed');
  }
  return url;
}

function hashBlocks(blocks: ContentBlock[]): string {
  try {
    return btoa(unescape(encodeURIComponent(JSON.stringify(blocks)))).slice(0, 12);
  } catch {
    return String(Date.now());
  }
}

function sortBlocks(blocks: ContentBlock[]): ContentBlock[] {
  return [...(blocks || [])].sort((a, b) => a.order - b.order);
}

function formatDateBR(dateString?: string): string {
  if (!dateString) return '';
  const d = new Date(dateString);
  if (isNaN(d.getTime())) return dateString;
  return d.toLocaleDateString('pt-BR');
}

export const TaskDetailsV2 = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const [newComment, setNewComment] = useState('');
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [taskData, setTaskData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [taskStatus, setTaskStatus] = useState('in-progress');
  const { toast } = useToast();
  const [contentBlocks, setContentBlocks] = useState<ContentBlock[] | null>(null);
  const { user, profile } = useAuth();
  console.log('🔍 [TaskDetailsV2] Debug autenticação:', { 
    user, 
    profile, 
    userRole: user?.role,
    profileRole: profile?.role,
    hasUser: !!user,
    hasProfile: !!profile,
    shouldShowEditTab: profile?.role && ['admin', 'manager'].includes(profile.role)
  });
  const [executors, setExecutors] = useState<any[]>([]);
  const [executorsLoading, setExecutorsLoading] = useState(false);
  const [approvers, setApprovers] = useState<any[]>([]);
  const [approversLoading, setApproversLoading] = useState(false);
  const menuItems = useSidebarMenu();
  const [projectMembers, setProjectMembers] = useState<any[]>([]);
  const [savingBlocks, setSavingBlocks] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const saveTimeout = useRef<NodeJS.Timeout | null>(null);
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);
  const [inlineVideoBlockId, setInlineVideoBlockId] = useState<string | null>(null);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  // Verificar e redirecionar membros que tentam acessar a tab de edição
  useEffect(() => {
    if (profile?.role === 'member' && activeTab === 'edit') {
      setActiveTab('overview');
      toast({
        title: 'Acesso restrito',
        description: 'Como membro, você não tem permissão para editar o conteúdo da tarefa. Você pode fazer upload de evidências na aba "Executar Tarefa".',
        variant: 'destructive'
      });
    }
  }, [profile?.role, activeTab, toast]);

  // Função para buscar dados da tarefa (mesma lógica da versão original)
  const fetchTask = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await taskService.getFullById(taskId!);
      let responsible = null;
      if (data.assigned_to) responsible = await userService.getById(data.assigned_to);
      const approvers = data.approvers?.length
        ? await Promise.all(data.approvers.map((a: any) => userService.getById(a.user_id)))
        : [];
      const comments = data.comments?.length
        ? await Promise.all(data.comments.map(async (c: any) => ({
            ...c,
            author: await userService.getById(c.author),
            replies: data.comments.filter((r: any) => r.parent_id === c.id)
          })))
        : [];

      const stage = await stageService.getById(data.stage_id);
      const project = await projectService.getById(stage.project_id);
      const projectMembersData = await projectService.getProjectMembers(project.id);

      // Mapear membros do projeto para o formato esperado
      const mappedMembers = (projectMembersData || []).map((m: any) => {
        const profile = m.profile || m.profiles?.[0] || m;
        return profile && profile.id ? {
          id: profile.id,
          name: profile.name,
          email: profile.email,
          avatar_url: profile.avatar_url
        } : null;
      }).filter(Boolean);

      console.log('[TaskDetailsV2] Membros do projeto mapeados:', mappedMembers);
      setProjectMembers(mappedMembers);

      const taskWithDetails = {
        ...data,
        responsible,
        approvers,
        comments: comments.filter((c: any) => !c.parent_id),
        stage,
        project
      };

      setTaskData(taskWithDetails);
      setTaskStatus(data.status || 'in-progress');

      // Processar contentBlocks para garantir estrutura correta (igual à V1)
      const processedContentBlocks = (data.content_blocks || data.contentBlocks || []).map((block: any) => {
        let content = block.content;
        // Se vier como string, faz o parse
        if (typeof content === 'string') {
          try {
            content = JSON.parse(content);
          } catch (e) {
            console.warn('Erro ao fazer parse do conteúdo do bloco:', e);
            content = { text: content };
          }
        }
        // Garantir que content seja um objeto válido
        if (!content || typeof content !== 'object') {
          content = { text: '' };
        }
        return { ...block, content };
      });

      console.log('[TaskDetailsV2] Blocos carregados do banco:', JSON.stringify(processedContentBlocks, null, 2));
      setContentBlocks(sortBlocks(processedContentBlocks));

      // Carrega executores e aprovadores a partir dos dados já buscados na getFullById
      if (data.executors?.length) {
        console.log('[TaskDetailsV2] Carregando executores a partir dos dados:', data.executors);
        setExecutors(data.executors);
      } else {
        setExecutors([]);
      }

      if (data.approvers?.length) {
        console.log('[TaskDetailsV2] Carregando aprovadores a partir dos dados:', data.approvers);
        setApprovers(data.approvers);
      } else {
        setApprovers([]);
      }

    } catch (error) {
      console.error('Erro ao carregar tarefa:', error);
      setError('Erro ao carregar dados da tarefa');
    } finally {
      setLoading(false);
    }
  };

  // useEffect para carregar dados iniciais
  React.useEffect(() => {
    if (taskId) {
      console.log('[TaskDetailsV2] Carregando tarefa:', taskId);
      fetchTask();
    }
  }, [taskId]);

  // Debug: Log quando contentBlocks mudam
  React.useEffect(() => {
    console.log('[TaskDetailsV2] ContentBlocks atualizados:', {
      count: contentBlocks?.length || 0,
      blocks: contentBlocks
    });
  }, [contentBlocks]);

  // Todas as outras funções de manipulação (mesma lógica da versão original)
  const handleStatusChange = async (newStatus: string) => {
    try {
      await taskService.updateStatus(taskId!, newStatus);
      setTaskStatus(newStatus);
      toast({
        title: "Status atualizado",
        description: "O status da tarefa foi atualizado com sucesso.",
      });
      fetchTask();
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar status da tarefa.",
        variant: "destructive",
      });
    }
  };

  const handleProgressUpdate = async (progress: number) => {
    try {
      await taskService.updateProgress(taskId!, progress);
      toast({
        title: "Progresso atualizado",
        description: "O progresso da tarefa foi atualizado com sucesso.",
      });
      fetchTask();
    } catch (error) {
      console.error('Erro ao atualizar progresso:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar progresso da tarefa.",
        variant: "destructive",
      });
    }
  };

  const handleAddExecutors = async (userIds: string[]) => {
    try {
      await taskService.addExecutors(taskId!, userIds);
      toast({
        title: "Executores adicionados",
        description: "Os executores foram adicionados com sucesso.",
      });
      fetchTask();
    } catch (error) {
      console.error('Erro ao adicionar executores:', error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar executores.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveExecutor = async (userId: string) => {
    try {
      await taskService.removeExecutor(taskId!, userId);
      toast({
        title: "Executor removido",
        description: "O executor foi removido com sucesso.",
      });
      fetchTask();
    } catch (error) {
      console.error('Erro ao remover executor:', error);
      toast({
        title: "Erro",
        description: "Erro ao remover executor.",
        variant: "destructive",
      });
    }
  };

  const handleAddApprovers = async (userIds: string[]) => {
    try {
      await taskService.addApprovers(taskId!, userIds);
      toast({
        title: "Aprovadores adicionados",
        description: "Os aprovadores foram adicionados com sucesso.",
      });
      fetchTask();
    } catch (error) {
      console.error('Erro ao adicionar aprovadores:', error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar aprovadores.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveApprover = async (userId: string) => {
    try {
      await taskService.removeApprover(taskId!, userId);
      toast({
        title: "Aprovador removido",
        description: "O aprovador foi removido com sucesso.",
      });
      fetchTask();
    } catch (error) {
      console.error('Erro ao remover aprovador:', error);
      toast({
        title: "Erro",
        description: "Erro ao remover aprovador.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center h-96">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !taskData) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar tarefa</h2>
            <p className="text-gray-600 mb-4">{error || 'Tarefa não encontrada'}</p>
            <Button onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const task = taskData;
  const canEditExecutors = user?.id === task.assigned_to || user?.role === 'admin';

  return (
    <>
      <Header showSidebarButton={true} />
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`w-full min-w-0 max-w-full bg-gray-50 transition-all duration-300 ${sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'} md:pl-0 px-4 sm:px-8 py-4 space-y-6 overflow-x-hidden lg:ml-6`}>
        {/* Conteúdo principal da tarefa */}
        <div className="flex flex-col min-h-screen gap-6 min-w-0">
          <main className="flex-1 min-w-0">
            {/* Breadcrumb Hierárquico Melhorado */}
            <div className="w-full min-w-0 overflow-x-auto mb-2">
              <div className="flex flex-col text-sm mt-3 ml-2 mb-4 gap-0.5">
                <button
                  className="flex items-center font-normal text-project hover:bg-blue-50 rounded px-2 py-1 transition"
                  onClick={() => navigate(`/project/${task.project?.id}`)}
                  title={task.project?.name}
                  type="button"
                >
                  <FolderOpen className="w-4 h-4 mr-2 text-project" />
                  <span className="truncate">{task.project?.name}</span>
                </button>
                <div className="flex items-center font-normal ml-6 border-l-2 border-gray-200 pl-3 relative text-stage hover:bg-green-50 rounded px-2 py-1 transition">
                  <span className="absolute -left-6 top-1.5 w-4 h-4 flex items-center justify-center">
                    <span className="block w-0.5 h-6 bg-gray-200"></span>
                  </span>
                  <button
                    className="flex items-center text-stage font-normal"
                    onClick={() => navigate(`/stage/${task.stage?.id}`)}
                    title={task.stage?.name}
                    type="button"
                  >
                    <Target className="w-4 h-4 mr-2 text-stage" />
                    <span className="truncate">{task.stage?.name}</span>
                  </button>
                </div>
                <div className="flex items-center font-bold ml-12 border-l-2 border-gray-200 pl-3 relative text-task rounded px-2 py-1 shadow-sm">
                  <span className="absolute -left-6 top-1.5 w-4 h-4 flex items-center justify-center">
                    <span className="block w-0.5 h-6 bg-gray-200"></span>
                  </span>
                  <FileText className="w-4 h-4 mr-2 text-task" />
                  <span className="truncate">{task.title}</span>
                </div>
              </div>
            </div>

            {/* Header da Tarefa Melhorado */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm mb-6">
              <div className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900">{task.title}</h1>
                      <div className="flex items-center space-x-4 mt-2">
                        <Badge
                          variant={task.status === 'completed' ? 'default' : 'secondary'}
                          className={task.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}
                        >
                          {task.status === 'completed' ? 'Concluída' : 'Em Progresso'}
                        </Badge>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDateBR(task.due_date)}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Clock className="h-4 w-4" />
                          <span>{task.estimated_hours}h estimadas</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowTaskForm(true)}
                      className="border-blue-200 text-blue-700 hover:bg-blue-50"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Editar
                    </Button>
                    <Button
                      variant={task.status === 'completed' ? 'outline' : 'default'}
                      size="sm"
                      onClick={() => handleStatusChange(task.status === 'completed' ? 'in-progress' : 'completed')}
                      className={task.status === 'completed'
                        ? 'border-orange-200 text-orange-700 hover:bg-orange-50'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                      }
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {task.status === 'completed' ? 'Reabrir' : 'Concluir'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Grid Principal com Sidebar */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Coluna Principal */}
              <div className="lg:col-span-2 space-y-6">
                {/* Tabs de Navegação Melhoradas */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className={`grid w-full ${profile?.role === 'member' ? 'grid-cols-2' : 'grid-cols-3'} bg-white border border-gray-200 rounded-lg p-1`}>
                    <TabsTrigger
                      value="overview"
                      className="flex items-center space-x-2 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700 data-[state=active]:shadow-sm"
                    >
                      <Eye className="h-4 w-4" />
                      <span>Visão Geral</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="execute"
                      className="flex items-center space-x-2 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 data-[state=active]:shadow-sm"
                    >
                      <PlayCircle className="h-4 w-4" />
                      <span>Executar Tarefa</span>
                    </TabsTrigger>
                    {/* Tab de Editar Conteúdo - apenas para admin e manager */}
                    {profile?.role && ['admin', 'manager'].includes(profile.role) && (
                      <TabsTrigger
                        value="edit"
                        className="flex items-center space-x-2 data-[state=active]:bg-orange-100 data-[state=active]:text-orange-700 data-[state=active]:shadow-sm"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Editar Conteúdo</span>
                      </TabsTrigger>
                    )}
                  </TabsList>

                {/* Conteúdo das Tabs */}
                <TabsContent value="overview" className="space-y-6 mt-6">
                  {/* Card de Informações Principais */}
                  <Card className="border-blue-200 shadow-lg">
                    <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 border-b border-blue-200">
                      <CardTitle className="flex items-center space-x-2 text-blue-800">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <span>Informações da Tarefa</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 p-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                          <MessageCircle className="h-4 w-4 mr-2 text-blue-500" />
                          Descrição
                        </h4>
                        <p className="text-gray-700 leading-relaxed bg-gray-50 p-3 rounded-lg">{task.description}</p>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Data de Entrega</h4>
                          <div className="flex items-center space-x-2 text-gray-600">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(task.due_date).toLocaleDateString('pt-BR')}</span>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Horas Estimadas</h4>
                          <div className="flex items-center space-x-2 text-gray-600">
                            <Clock className="h-4 w-4" />
                            <span>{task.estimated_hours}h</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Progresso</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              {task.progress || 0}% concluído
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const newProgress = prompt('Digite o novo progresso (0-100):', String(task.progress || 0));
                                if (newProgress && !isNaN(Number(newProgress))) {
                                  handleProgressUpdate(Math.min(100, Math.max(0, Number(newProgress))));
                                }
                              }}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                          </div>
                          <Progress value={task.progress || 0} className="h-2" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Card de Comentários */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <MessageCircle className="h-5 w-5" />
                        <span>Comentários</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {task.comments?.length > 0 ? (
                          task.comments.map((comment: any) => (
                            <div key={comment.id} className="flex space-x-3 p-3 bg-gray-50 rounded-lg">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={comment.author?.avatar_url} />
                                <AvatarFallback>
                                  {comment.author?.name?.charAt(0) || 'U'}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="font-medium text-sm">{comment.author?.name}</span>
                                  <span className="text-xs text-gray-500">
                                    {new Date(comment.created_at).toLocaleDateString('pt-BR')}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-700">{comment.content}</p>
                              </div>
                            </div>
                          ))
                        ) : (
                          <p className="text-gray-500 text-center py-4">Nenhum comentário ainda</p>
                        )}
                        
                        {/* Adicionar novo comentário */}
                        <div className="flex space-x-3 pt-4 border-t">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user?.avatar_url} />
                            <AvatarFallback>{user?.name?.charAt(0) || 'U'}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1 space-y-2">
                            <Textarea
                              placeholder="Adicionar um comentário..."
                              value={newComment}
                              onChange={(e) => setNewComment(e.target.value)}
                              className="min-h-[80px]"
                            />
                            <Button
                              size="sm"
                              disabled={!newComment.trim()}
                              onClick={async () => {
                                try {
                                  await taskService.addComment(taskId!, newComment);
                                  setNewComment('');
                                  fetchTask();
                                  toast({
                                    title: "Comentário adicionado",
                                    description: "Seu comentário foi adicionado com sucesso.",
                                  });
                                } catch (error) {
                                  toast({
                                    title: "Erro",
                                    description: "Erro ao adicionar comentário.",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Send className="h-4 w-4 mr-2" />
                              Comentar
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="execute" className="mt-6">
                  {contentBlocks !== null && (
                    <div className="space-y-6">
                      {/* Header da Execução */}
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 p-6 shadow-lg">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-xl font-bold text-green-800 flex items-center">
                            <PlayCircle className="h-6 w-6 mr-3 text-green-600" />
                            Executar Tarefa
                          </h3>
                          <div className="flex items-center space-x-2">
                            <Badge className="bg-green-100 text-green-800 border-green-300">
                              Modo Execução
                            </Badge>
                            {/* Botão Editar Conteúdo - apenas para admin e manager */}
                            {profile?.role && ['admin', 'manager'].includes(profile.role) && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-green-300 text-green-700 hover:bg-green-100"
                                onClick={() => setActiveTab('edit')}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Editar Conteúdo
                              </Button>
                            )}
                          </div>
                        </div>
                        <p className="text-green-700 text-sm">
                          Visualize e execute as instruções da tarefa. O conteúdo está em modo somente leitura para execução.
                        </p>
                      </div>

                      {/* Conteúdo da Execução */}
                      <div className="bg-white rounded-lg border border-green-200 shadow-lg overflow-hidden">
                        <div className="bg-green-50 px-6 py-3 border-b border-green-200">
                          <h4 className="font-medium text-green-800 flex items-center">
                            <FileText className="h-4 w-4 mr-2" />
                            Instruções da Tarefa
                          </h4>
                        </div>
                        <div className="p-6">
                          {contentBlocks && contentBlocks.length > 0 ? (
                            <RichContentEditor
                              blocks={contentBlocks}
                              onBlocksChange={() => {}} // Não permite mudanças estruturais
                              editable={false} // Remove toolbar e drag&drop
                              mode="execution" // Modo execution - mantém funcionalidades interativas
                              taskId={taskId} // Necessário para quiz e evidências
                            />
                          ) : (
                            <div className="text-center py-12 text-gray-500">
                              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                              <h3 className="text-lg font-medium mb-2">Nenhum conteúdo disponível</h3>
                              <p className="text-sm">Esta tarefa ainda não possui conteúdo para execução.</p>
                              {/* Botão Adicionar Conteúdo - apenas para admin e manager */}
                              {profile?.role && ['admin', 'manager'].includes(profile.role) && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-4 border-orange-300 text-orange-700 hover:bg-orange-50"
                                  onClick={() => setActiveTab('edit')}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Adicionar Conteúdo
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </TabsContent>

                {/* Tab de Editar Conteúdo - apenas para admin e manager */}
                {profile?.role && ['admin', 'manager'].includes(profile.role) && (
                  <TabsContent value="edit" className="mt-6">
                    {contentBlocks !== null && (
                      <div className="space-y-6">
                        {/* Header da Edição */}
                        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-orange-200 p-6 shadow-lg">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-xl font-bold text-orange-800 flex items-center">
                              <Settings className="h-6 w-6 mr-3 text-orange-600" />
                              Editar Conteúdo da Tarefa
                            </h3>
                            <div className="flex items-center space-x-3">
                              {saveStatus === 'saving' && (
                                <div className="flex items-center text-orange-600 bg-orange-100 px-3 py-1 rounded-full">
                                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                  Salvando...
                                </div>
                              )}
                              {saveStatus === 'saved' && (
                                <div className="flex items-center text-green-600 bg-green-100 px-3 py-1 rounded-full">
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Salvo automaticamente
                                </div>
                              )}
                              {saveStatus === 'error' && (
                                <div className="flex items-center text-red-600 bg-red-100 px-3 py-1 rounded-full">
                                  <AlertCircle className="h-4 w-4 mr-2" />
                                  Erro ao salvar
                                </div>
                              )}
                              <Badge className="bg-orange-100 text-orange-800 border-orange-300">
                                Modo Edição
                              </Badge>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-orange-300 text-orange-700 hover:bg-orange-100"
                                onClick={() => setActiveTab('execute')}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                Visualizar
                              </Button>
                            </div>
                        </div>
                        <p className="text-orange-700 text-sm">
                          Edite o conteúdo da tarefa usando blocos de texto, vídeo, imagens e outros elementos. As alterações são salvas automaticamente.
                        </p>
                      </div>

                      {/* Editor de Conteúdo */}
                      <div className="bg-white rounded-lg border border-orange-200 shadow-lg overflow-hidden">
                        <div className="bg-orange-50 px-6 py-3 border-b border-orange-200">
                          <h4 className="font-medium text-orange-800 flex items-center">
                            <Edit className="h-4 w-4 mr-2" />
                            Editor de Blocos de Conteúdo
                          </h4>
                        </div>
                        <div className="p-6">
                          <RichContentEditor
                            blocks={contentBlocks}
                            onBlocksChange={async (blocks) => {
                              console.log('[TaskDetailsV2] Salvando blocos:', JSON.stringify(blocks, null, 2));
                              setContentBlocks(blocks);
                              setUnsavedChanges(true);
                              setSaveStatus('saving');

                              if (saveTimeout.current) {
                                clearTimeout(saveTimeout.current);
                              }

                              saveTimeout.current = setTimeout(async () => {
                                try {
                                  await taskService.saveContentBlocks(taskId!, blocks);
                                  setSaveStatus('saved');
                                  setUnsavedChanges(false);
                                  setTimeout(() => setSaveStatus('idle'), 2000);
                                } catch (error) {
                                  setSaveStatus('error');
                                  console.error('Erro ao salvar blocos:', error);
                                }
                              }, 1000);
                            }}
                            editable={true} // Modo de edição completo
                            mode="edit" // Modo edit - toolbar e drag&drop
                            taskId={taskId} // Para compatibilidade
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  </TabsContent>
                )}
              </Tabs>
            </div>

              {/* Sidebar Direita */}
              <div className="space-y-6">
                {/* Card de Status e Ações Rápidas */}
                <Card className="border-purple-200 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 border-b border-purple-200">
                    <CardTitle className="flex items-center space-x-2 text-purple-800">
                      <Target className="h-5 w-5 text-purple-600" />
                      <span>Status da Tarefa</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-6">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Status atual:</span>
                      <Badge
                        variant={task.status === 'completed' ? 'default' : 'secondary'}
                        className={task.status === 'completed'
                          ? 'bg-green-100 text-green-800 border-green-300'
                          : 'bg-blue-100 text-blue-800 border-blue-300'
                        }
                      >
                        {task.status === 'completed' ? 'Concluída' : 'Em Progresso'}
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <Button
                        variant={task.status === 'completed' ? 'outline' : 'default'}
                        size="sm"
                        className={`w-full ${task.status === 'completed'
                          ? 'border-orange-300 text-orange-700 hover:bg-orange-50'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                        }`}
                        onClick={() => handleStatusChange(task.status === 'completed' ? 'in-progress' : 'completed')}
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        {task.status === 'completed' ? 'Reabrir Tarefa' : 'Marcar como Concluída'}
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full border-red-300 text-red-700 hover:bg-red-50"
                      >
                        <Flag className="h-4 w-4 mr-2" />
                        Reportar Problema
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        Compartilhar
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Card de Equipe */}
                <Card className="border-teal-200 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 border-b border-teal-200">
                    <CardTitle className="flex items-center space-x-2 text-teal-800">
                      <Users className="h-5 w-5 text-teal-600" />
                      <span>Equipe da Tarefa</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <TaskTeamPanel
                      executors={executors}
                      approvers={approvers}
                      canEditExecutors={canEditExecutors}
                      executorsLoading={executorsLoading}
                      approversLoading={approversLoading}
                      onRemoveExecutor={handleRemoveExecutor}
                      onAddExecutor={(user) => handleAddExecutors([user.id])}
                      onRemoveApprover={handleRemoveApprover}
                      onAddApprover={(user) => handleAddApprovers([user.id])}
                      projectMembers={projectMembers}
                    />
                  </CardContent>
                </Card>

                {/* Card de Informações Adicionais */}
                <Card className="border-indigo-200 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b border-indigo-200">
                    <CardTitle className="flex items-center space-x-2 text-indigo-800">
                      <BarChart3 className="h-5 w-5 text-indigo-600" />
                      <span>Detalhes da Tarefa</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-6">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-indigo-500" />
                        Criada em:
                      </span>
                      <span className="font-medium text-gray-800">{formatDateBR(task.created_at)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-indigo-500" />
                        Atualizada em:
                      </span>
                      <span className="font-medium text-gray-800">{formatDateBR(task.updated_at)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 flex items-center">
                        <FolderOpen className="h-4 w-4 mr-2 text-project" />
                        Projeto:
                      </span>
                      <span className="font-medium text-project">{task.project?.name}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 flex items-center">
                        <Target className="h-4 w-4 mr-2 text-stage" />
                        Etapa:
                      </span>
                      <span className="font-medium text-stage">{task.stage?.name}</span>
                    </div>
                    {task.responsible && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 flex items-center">
                          <User className="h-4 w-4 mr-2 text-indigo-500" />
                          Responsável:
                        </span>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6 border-2 border-indigo-200">
                            <AvatarImage src={task.responsible.avatar_url} />
                            <AvatarFallback className="text-xs bg-indigo-100 text-indigo-700">
                              {task.responsible.name?.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium text-gray-800">{task.responsible.name}</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>

      {/* Modal de Formulário de Edição */}
      <TaskForm
        open={showTaskForm}
        onOpenChange={setShowTaskForm}
        task={{
          ...task,
          title: task.title, // O campo correto no banco é title, não name
          executors: Array.isArray(executors) ? executors.filter(Boolean) : [], // Usar lista de executores carregada
          approvers: Array.isArray(approvers) ? approvers.filter(Boolean) : [] // Incluir aprovadores
        }}
        mode="edit"
        stageId={task.stage.id}
        onCreated={fetchTask}
        projectMembers={projectMembers}
      />

      {/* Modal de Vídeo */}
      <Dialog open={videoModalOpen} onOpenChange={setVideoModalOpen}>
        <DialogContent className="max-w-2xl" aria-describedby="video-modal-desc">
          <DialogTitle>Visualizar vídeo</DialogTitle>
          <div id="video-modal-desc" className="sr-only">Modal para visualização de vídeo da tarefa. Use Tab para navegar e Esc para fechar.</div>
          {currentVideoUrl ? (
            isYoutubeUrl(currentVideoUrl) ? (
              <iframe
                width="100%"
                height="400"
                src={getYoutubeEmbedUrl(currentVideoUrl)}
                frameBorder="0"
                allow="autoplay; encrypted-media"
                allowFullScreen
                title="Vídeo"
              />
            ) : isCanvaEmbedUrl(currentVideoUrl) ? (
              <div style={{
                position: 'relative',
                width: '100%',
                height: 0,
                paddingTop: '56.25%',
                boxShadow: '0 2px 8px 0 rgba(63,69,81,0.16)',
                marginTop: '1.6em',
                marginBottom: '0.9em',
                overflow: 'hidden',
                borderRadius: '8px',
                willChange: 'transform'
              }}>
                <iframe
                  loading="lazy"
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    top: 0,
                    left: 0,
                    border: 'none',
                    padding: 0,
                    margin: 0
                  }}
                  src={autoEmbedCanvaUrl(currentVideoUrl)}
                  allowFullScreen
                  allow="fullscreen"
                  title="Vídeo Canva"
                />
              </div>
            ) : (
              <video src={currentVideoUrl} controls style={{ width: '100%' }} />
            )
          ) : null}
        </DialogContent>
      </Dialog>
    </>
  );
};

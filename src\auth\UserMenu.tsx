import React, { useState } from "react";
import { useAuth } from "./useAuth";
import { AvatarUpload } from "@/components/ui/AvatarUpload";
import { updateProfile } from "@/services/userService";

const UserMenu: React.FC = () => {
  const { user, profile, logout, loading } = useAuth();
  const [avatarUrl, setAvatarUrl] = useState<string | null>(profile?.avatar_url || null);
  const [saving, setSaving] = useState(false);

  if (!user || !profile) return null;

  const handleAvatarChange = async (url: string | null) => {
    setSaving(true);
    try {
      await updateProfile(profile.id, { avatar_url: url });
      setAvatarUrl(url);
      // Forçar reload do perfil após update
      window.location.reload();
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="flex items-center gap-4">
      <AvatarUpload
        userId={profile.id}
        avatarUrl={avatarUrl}
        onAvatarChange={handleAvatarChange}
        disabled={loading || saving}
      />
      <span className="text-sm text-gray-700">{user.email}</span>
      <button
        onClick={logout}
        className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-1 rounded"
        disabled={loading}
      >
        Sair
      </button>
    </div>
  );
};

export default UserMenu; 
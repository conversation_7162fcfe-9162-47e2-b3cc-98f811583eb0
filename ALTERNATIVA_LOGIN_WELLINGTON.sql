-- =====================================================
-- ALTERNATIVA: FAZER LOGIN COMO WELLINGTON VASQUE
-- Use se preferir não criar novo profile
-- =====================================================

-- DADOS PARA LOGIN MANUAL:
-- Email: <EMAIL>
-- User ID: 3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee
-- Role: member

-- VERIFICAR SE WELLINGTON JÁ É EXECUTOR
SELECT 
    'WELLINGTON É EXECUTOR?' as teste,
    COUNT(*) as resultado,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - JÁ É EXECUTOR'
        ELSE '❌ NÃO - PRECISA ADICIONAR'
    END as status
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee';

-- SE NÃO FOR EXECUTOR, ADICIONAR:
-- Primeiro verificar se já existe
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM task_executors 
        WHERE task_id = '7c606667-9391-4660-933d-90d6bd276e88' 
        AND user_id = '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee'
    ) THEN
        INSERT INTO task_executors (task_id, user_id, created_at)
        VALUES (
            '7c606667-9391-4660-933d-90d6bd276e88',
            '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee',
            NOW()
        );
        RAISE NOTICE '✅ Wellington adicionado como executor';
    ELSE
        RAISE NOTICE '✅ Wellington já é executor';
    END IF;
END $$;

-- VERIFICAR TODOS OS EXECUTORES DA TAREFA
SELECT 
    'TODOS OS EXECUTORES ATUAIS' as info,
    te.user_id,
    p.name,
    p.email,
    p.role
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- =====================================================
-- INSTRUÇÕES PARA O FRONTEND:
-- =====================================================
-- 1. Fazer logout do usuário atual
-- 2. Fazer login com: <EMAIL>
-- 3. Navegar para a tarefa
-- 4. Verificar se a aba "Executar" mostra o conteúdo
-- =====================================================

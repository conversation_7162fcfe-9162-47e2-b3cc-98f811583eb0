-- =====================
-- CORREÇÃO: ERRO 409 CONFLICT EM PROFILES
-- Data: 2024-12-19
-- Problema: Conflito em operações POST para profiles
-- =====================

-- PASSO 1: Verificar políticas atuais que podem estar causando conflito
-- =====================
select 
    'Políticas atuais em profiles:' as info,
    policyname,
    cmd,
    permissive,
    qual
from pg_policies 
where tablename = 'profiles'
order by cmd, policyname;

-- PASSO 2: Remover TODAS as políticas existentes para evitar conflitos
-- =====================
drop policy if exists "Users can insert their own profile" on public.profiles;
drop policy if exists "Admin can insert any profile" on public.profiles;
drop policy if exists "Allow users to view profiles for collaboration" on public.profiles;
drop policy if exists "Temporary allow authenticated users to view profiles" on public.profiles;
drop policy if exists "Simple authenticated users can view profiles" on public.profiles;
drop policy if exists "Basic users can view profiles" on public.profiles;
drop policy if exists "Users can create their own profile" on public.profiles;
drop policy if exists "Users can update their own profile" on public.profiles;
drop policy if exists "Users can view their own profile" on public.profiles;
drop policy if exists "Admin can view all profiles" on public.profiles;
drop policy if exists "Admin can update all profiles" on public.profiles;

-- PASSO 3: Criar políticas básicas e seguras
-- =====================

-- Política para SELECT (visualização)
create policy "Basic users can view profiles"
  on public.profiles
  for select
  using (
    -- Usuário pode ver próprio perfil
    id = auth.uid() or
    -- Qualquer usuário autenticado pode ver outros (temporário para resolver 409)
    auth.uid() is not null
  );

-- Política para INSERT (criação de perfil)
create policy "Users can create their own profile"
  on public.profiles
  for insert
  with check (
    -- Apenas o próprio usuário pode criar seu perfil
    id = auth.uid()
  );

-- Política para UPDATE (atualização)
create policy "Users can update their own profile"
  on public.profiles
  for update
  using (
    -- Apenas o próprio usuário pode atualizar seu perfil
    id = auth.uid()
  );

-- PASSO 4: Verificar constraints que podem estar causando conflito
-- =====================
select 
    'Constraints em profiles:' as info,
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as definition
from pg_constraint 
where conrelid = 'public.profiles'::regclass
order by contype, conname;

-- PASSO 5: Verificar se há registros duplicados
-- =====================
select 
    'Verificando duplicatas por email:' as info,
    email,
    count(*) as count
from public.profiles 
group by email 
having count(*) > 1;

select 
    'Verificando duplicatas por id:' as info,
    id,
    count(*) as count
from public.profiles 
group by id 
having count(*) > 1;

-- PASSO 6: Garantir que RLS está ativado com políticas corretas
-- =====================
alter table public.profiles enable row level security;

-- PASSO 7: Testar se as políticas funcionam
-- =====================
select 'CORREÇÃO 409 APLICADA - Verificações:' as status;

-- Verificar políticas criadas
select 
    'Novas políticas:' as info,
    policyname,
    cmd
from pg_policies 
where tablename = 'profiles'
order by cmd, policyname;

-- PASSO 8: Instruções para teste
-- =====================
select 'TESTE AGORA:' as instrucao;
select '1. Acesse UserManagement' as passo;
select '2. Tente adicionar usuário (se aplicável)' as passo;
select '3. Verifique se erro 409 foi resolvido' as passo;
select '4. Se persistir, execute fix_rls_emergency.sql' as passo;

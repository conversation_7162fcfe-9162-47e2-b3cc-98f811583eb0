import React from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { ProjectForm } from '@/components/forms/ProjectForm';
import { StageForm } from '@/components/forms/StageForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Clock, 
  Plus,
  ChevronRight
} from 'lucide-react';
import { projectService } from '@/services/projectService';
import { stageService } from '@/services/stageService';
import { supabase } from '@/lib/supabaseClient';
import { useAuth } from '@/auth/useAuth';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { Header } from '@/components/layout/Header';
import { ContentSection } from '@/components/features/content-editor/blocks/shared/ContentSection';
import { ContentBlock } from '@/types';
import { useToast } from '@/hooks/ui/use-toast';
import { PermissionIndicator } from '@/components/auth/PermissionIndicator';

export const ProjectDetails = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const [showProjectForm, setShowProjectForm] = React.useState(false);
  const [showStageForm, setShowStageForm] = React.useState(false);
  const [project, setProject] = React.useState<any>(null);
  const [stages, setStages] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [reloadFlag, setReloadFlag] = React.useState(0);
  const [members, setMembers] = React.useState<any[]>([]);
  const { user } = useAuth();
  const [membersLoading, setMembersLoading] = React.useState(false);
  const { toast } = useToast();

  // Estado local para blocos de conteúdo do projeto
  const [projectContentBlocks, setProjectContentBlocks] = React.useState<ContentBlock[]>(project?.content || []);

  const handleProjectUpdated = () => setReloadFlag(f => f + 1);
  const handleStageCreated = () => setReloadFlag(f => f + 1);

  React.useEffect(() => {
    const fetchData = async () => {
      console.log('ProjectDetails: projectId', projectId);
      setLoading(true);
      setError(null);
      try {
        const projectData = await projectService.getById(projectId);
        console.log('ProjectDetails: projectData', projectData);
        // Usar método que considera vínculos de tarefas
      const stagesData = await stageService.getMyStagesByProjectId(projectId);
        console.log('ProjectDetails: stagesData', stagesData);
        setProject({ ...projectData, members });
        setStages(stagesData);
        const { data: projectMembers } = await supabase
          .from('project_members')
          .select('user_id, role')
          .eq('project_id', projectId);
        if (projectMembers && projectMembers.length > 0) {
          const userIds = projectMembers.map((m: any) => m.user_id);
          const { data: profiles } = await supabase
            .from('profiles')
            .select('*')
            .in('id', userIds);
          const membersFull = profiles?.map((p: any) => ({
            ...p,
            role: projectMembers.find((m: any) => m.user_id === p.id)?.role || 'member',
          })) || [];
          setMembers(membersFull);
        } else {
          setMembers([]);
        }
      } catch (err: any) {
        console.error('ProjectDetails: erro ao buscar dados', err);
        setError('Erro ao carregar projeto ou etapas.');
      } finally {
        setLoading(false);
      }
    };
    if (projectId) fetchData();
  }, [projectId]);

  // Sincronizar blocos de conteúdo ao carregar novo projeto
  React.useEffect(() => {
    setProjectContentBlocks(project?.content || []);
  }, [project?.content]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-status-success';
      case 'in-progress': return 'bg-stage';
      case 'not-started': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const handleStageClick = (stage: any) => {
    navigate(`/stage/${stage.id}`);
  };

  const handleEditProject = async () => {
    if (!projectId) return;
    // Buscar membros do projeto do Supabase
    const membersData = await projectService.getProjectMembers(projectId);
    // Mapear para o formato esperado pelo ProjectForm
    const mappedMembers = (membersData || []).map(m => {
      const profile = m.profiles?.[0];
      return profile ? { id: profile.id, name: profile.name, email: profile.email, avatar_url: profile.avatar_url, role: m.role } : null;
    }).filter(Boolean);
    setProject((prev: any) => prev ? { ...prev, members: mappedMembers } : prev);
    setShowProjectForm(true);
  };

  const handleNewStage = () => {
    setShowStageForm(true);
  };

  const statusLabels: Record<string, string> = {
    planning: 'Planejamento',
    active: 'Ativo',
    'on-hold': 'Pausado',
    completed: 'Concluído',
    cancelled: 'Cancelado',
  };

  const formatDateBR = (dateString?: string) => {
    if (!dateString) return '';
    const [year, month, day] = dateString.split('-').map(Number);
    if (!year || !month || !day) return '';
    const d = new Date(year, month - 1, day);
    return d.toLocaleDateString('pt-BR');
  };

  // Função para salvar os blocos de conteúdo do projeto
  async function handleSaveProjectContentBlocks(blocks: ContentBlock[]) {
    try {
      await projectService.saveContentBlocks(project.id, blocks);
      setProjectContentBlocks(blocks);
      toast({ title: 'Conteúdo salvo', description: 'O conteúdo do projeto foi salvo com sucesso.' });
    } catch (err: any) {
      toast({ title: 'Erro ao salvar conteúdo', description: err?.message || 'Erro desconhecido', variant: 'destructive' });
    }
  }

  // Função para cancelar edição (recarrega do backend)
  function handleCancelProjectContentEdit() {
    setProjectContentBlocks(project?.content || []);
  }

  // Definir permissão de edição: admin, manager ou owner
  const canEditProjectContent = user?.role === 'admin' || user?.role === 'manager' || user?.id === project?.owner_id;

  if (loading) {
    return <div className="p-8 text-center">Carregando...</div>;
  }
  if (error) {
    return <div className="p-8 text-center text-red-600">{error}</div>;
  }

  return (
    <>
      <Header showSidebarButton={true} />
      <SidebarStandalone />
      <div className="max-w-none mx-auto p-6 space-y-6" style={{ marginLeft: '16rem' }}>
        {/* Navegação */}
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
          <Link to="/" className="flex items-center gap-1 hover:text-project">
            <ArrowLeft className="w-4 h-4" />
            Voltar ao Dashboard
          </Link>
          <span>/</span>
          <span className="text-project font-medium">{project?.name || 'Projeto'}</span>
        </div>

        {/* Header do Projeto */}
        <div>
          <Card className="border-project/20 bg-project-bg mb-6">
            <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-project text-white hover:bg-project-dark">
                    Projeto
                  </Badge>
                  <Badge variant="outline" className="border-project text-project bg-white">
                    {statusLabels[project?.status] || project?.status}
                  </Badge>
                </div>
                <h1 className="text-3xl font-bold text-gray-900">{project?.name}</h1>
                <p className="text-gray-600 max-w-2xl">{project?.description}</p>
              </div>
              <Button 
                size="sm" 
                className="bg-project hover:bg-project-dark"
                onClick={handleEditProject}
              >
                Editar Projeto
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6">
              <div className="flex items-center gap-3">
                <User className="w-5 h-5 text-project" />
                <div>
                  <p className="text-sm font-medium">Equipe</p>
                  <div className="flex items-center -space-x-2 mt-1">
                    {members.slice(0, 3).map(member => (
                      <Avatar key={member.id} className="w-6 h-6 border-2 border-white">
                        <AvatarImage src={member.avatar_url || '/placeholder.svg'} />
                        <AvatarFallback>{member.name?.[0] || '?'}</AvatarFallback>
                      </Avatar>
                    ))}
                    {members.length > 3 && (
                       <div className="w-6 h-6 rounded-full bg-gray-200 text-gray-600 text-xs flex items-center justify-center border-2 border-white">
                          +{members.length - 3}
                       </div>
                    )}
                     {members.length === 0 && (
                       <p className="text-xs text-gray-500 ml-2">Nenhum membro</p>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-project" />
                <div>
                  <p className="text-sm font-medium">Prazo</p>
                  <p className="text-sm text-gray-600">{formatDateBR(project?.start_date)} - {formatDateBR(project?.end_date)}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-project" />
                <div>
                  <p className="text-sm font-medium">Progresso</p>
                  <div className="flex items-center gap-2">
                    <Progress value={project?.progress ?? 0} className="w-32" />
                    <span className="text-sm font-medium text-gray-700">{project?.progress ?? 0}%</span>
                  </div>
                </div>
              </div>
            </div>
            </CardHeader>
          </Card>
        </div>

        {/* Lista de Etapas */}
        <div>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="text-xl">Etapas do Projeto ({stages.length})</CardTitle>
                <PermissionIndicator screenType="project" projectId={projectId} />
              </div>
            <Button 
              size="sm" 
              variant="outline" 
              className="border-stage text-stage hover:bg-stage-bg"
              onClick={handleNewStage}
            >
              <Plus className="w-4 h-4 mr-1" />
              Nova Etapa
            </Button>
            </CardHeader>
            <CardContent className="space-y-4">
            {stages.length > 0 ? stages.map((stage) => (
              <div 
                key={stage.id} 
                className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleStageClick(stage)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 flex-1">
                    <div className={`w-3 h-3 rounded-full mt-1.5 ${getStatusColor(stage.status)}`} />
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg text-gray-900">{stage?.name}</h3>
                      <p className="text-sm text-gray-600 mb-2">{stage?.description}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>Ana Silva</span>
                        <span>{stage?.completedTasks ?? 0}/{stage?.taskCount ?? 0} tarefas</span>
                        <div className="flex items-center gap-2 flex-1 max-w-xs">
                           <Progress value={stage?.progress ?? 0} className="w-full" />
                           <span className="text-stage font-medium">{stage?.progress ?? 0}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            )) : (
              <div className="text-center py-8 text-gray-500">
                Nenhuma etapa foi criada para este projeto ainda.
              </div>
            )}
            </CardContent>
          </Card>
        </div>

        {/* Seção de Conteúdo do Projeto */}
        <div className="mt-8">
          <ContentSection
            blocks={projectContentBlocks}
            onSave={handleSaveProjectContentBlocks}
            onCancel={handleCancelProjectContentEdit}
            editable={canEditProjectContent}
            title="Conteúdo do Projeto"
            description="Adicione, edite ou reordene blocos de conteúdo para este projeto."
          />
        </div>

        <ProjectForm
          open={showProjectForm}
          onOpenChange={setShowProjectForm}
          project={project}
          mode="edit"
          onUpdated={handleProjectUpdated}
        />

        <StageForm
          open={showStageForm}
          onOpenChange={setShowStageForm}
          mode="create"
          projectId={projectId!}
          onCreated={handleStageCreated}
        />
      </div>
    </>
  );
};

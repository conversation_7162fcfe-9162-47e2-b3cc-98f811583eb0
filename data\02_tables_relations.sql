-- =====================================================
-- TABELAS DE RELACIONAMENTO
-- =====================================================
-- Contém: project_members, task_executors, task_approvers, stage_responsibles
-- Dependências: 01_tables_core.sql
-- Versão: 2.0 - Julho 2025

-- Verificar dependências
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles') THEN
    RAISE EXCEPTION 'Tabela profiles não encontrada. Execute 01_tables_core.sql primeiro.';
  END IF;
  RAISE NOTICE '✅ Dependências verificadas';
END $$;

-- =====================================================
-- TABELA: project_members
-- =====================================================
-- Relaciona usuários com projetos e define seus papéis

CREATE TABLE public.project_members (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  project_id uuid NOT NULL,
  user_id uuid NOT NULL,
  role public.project_member_role DEFAULT 'member'::public.project_member_role,
  created_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT project_members_pkey PRIMARY KEY (id),
  CONSTRAINT project_members_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects (id) ON DELETE CASCADE,
  CONSTRAINT project_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles (id) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX idx_project_members_project ON public.project_members(project_id);
CREATE INDEX idx_project_members_user ON public.project_members(user_id);
CREATE INDEX idx_project_members_role ON public.project_members(role);

-- Índice único composto para evitar duplicatas
CREATE UNIQUE INDEX idx_project_members_unique ON public.project_members(project_id, user_id);

-- Comentários
COMMENT ON TABLE public.project_members IS 'Membros dos projetos e seus papéis';
COMMENT ON COLUMN public.project_members.role IS 'Papel no projeto: admin, manager, editor, executor, approver, member';

-- =====================================================
-- TABELA: task_executors
-- =====================================================
-- Relaciona usuários com tarefas como executores

CREATE TABLE public.task_executors (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  task_id uuid NOT NULL,
  user_id uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT task_executors_pkey PRIMARY KEY (id),
  CONSTRAINT task_executors_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks (id) ON DELETE CASCADE,
  CONSTRAINT task_executors_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles (id) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX idx_task_executors_task ON public.task_executors(task_id);
CREATE INDEX idx_task_executors_user ON public.task_executors(user_id);

-- Índice único composto para evitar duplicatas
CREATE UNIQUE INDEX idx_task_executors_unique ON public.task_executors(task_id, user_id);

-- Comentários
COMMENT ON TABLE public.task_executors IS 'Executores das tarefas';

-- =====================================================
-- TABELA: task_approvers
-- =====================================================
-- Relaciona usuários com tarefas como aprovadores

CREATE TABLE public.task_approvers (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  task_id uuid NOT NULL,
  user_id uuid NOT NULL,
  approved_at timestamp with time zone NULL,
  rejection_reason text NULL,
  created_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT task_approvers_pkey PRIMARY KEY (id),
  CONSTRAINT task_approvers_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks (id) ON DELETE CASCADE,
  CONSTRAINT task_approvers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles (id) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX idx_task_approvers_task ON public.task_approvers(task_id);
CREATE INDEX idx_task_approvers_user ON public.task_approvers(user_id);
CREATE INDEX idx_task_approvers_approved ON public.task_approvers(approved_at);

-- Índice único composto para evitar duplicatas
CREATE UNIQUE INDEX idx_task_approvers_unique ON public.task_approvers(task_id, user_id);

-- Comentários
COMMENT ON TABLE public.task_approvers IS 'Aprovadores das tarefas';
COMMENT ON COLUMN public.task_approvers.approved_at IS 'Data da aprovação (NULL = pendente)';

-- =====================================================
-- TABELA: stage_responsibles
-- =====================================================
-- Relaciona usuários com estágios como responsáveis

CREATE TABLE public.stage_responsibles (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  stage_id uuid NOT NULL,
  user_id uuid NOT NULL,
  assigned_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT stage_responsibles_pkey PRIMARY KEY (id),
  CONSTRAINT stage_responsibles_stage_id_fkey FOREIGN KEY (stage_id) REFERENCES public.stages (id) ON DELETE CASCADE,
  CONSTRAINT stage_responsibles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles (id) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX idx_stage_responsibles_stage ON public.stage_responsibles(stage_id);
CREATE INDEX idx_stage_responsibles_user ON public.stage_responsibles(user_id);

-- Índice único composto para evitar duplicatas
CREATE UNIQUE INDEX idx_stage_responsibles_unique ON public.stage_responsibles(stage_id, user_id);

-- Comentários
COMMENT ON TABLE public.stage_responsibles IS 'Responsáveis pelos estágios';

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE '✅ Tabelas de relacionamento criadas com sucesso!';
  RAISE NOTICE '🔗 Criadas: project_members, task_executors, task_approvers, stage_responsibles';
  RAISE NOTICE '📊 Índices únicos criados para evitar duplicatas';
END $$;

import { useMemo } from 'react';
import { useAuth } from '@/auth/useAuth';

// Tipos de permissão
export type Permission = 
  // Permissões de Projeto
  | 'create_project'
  | 'view_project'
  | 'edit_project'
  | 'delete_project'
  | 'manage_project_members'
  | 'complete_project'
  // Permissões de Etapas
  | 'create_stage'
  | 'view_stage'
  | 'edit_stage'
  | 'delete_stage'
  | 'manage_stage_members'
  | 'complete_stage'
  // Permissões de Tarefas
  | 'create_task'
  | 'view_task'
  | 'edit_task'
  | 'delete_task'
  | 'view_task_content'
  | 'edit_task_content'
  | 'execute_task'
  | 'approve_task'
  | 'manage_task_executors'
  | 'manage_task_approvers'
  | 'complete_task';

// Roles do projeto
export type ProjectRole = 'admin' | 'manager' | 'editor' | 'executor' | 'approver' | 'member';

// Roles globais
export type GlobalRole = 'admin' | 'manager' | 'member';

// Matriz de permissões por role de projeto
const PROJECT_PERMISSIONS: Record<ProjectRole, Permission[]> = {
  admin: [
    // Projetos
    'create_project', 'view_project', 'edit_project', 'delete_project', 'manage_project_members', 'complete_project',
    // Etapas
    'create_stage', 'view_stage', 'edit_stage', 'delete_stage', 'manage_stage_members', 'complete_stage',
    // Tarefas
    'create_task', 'view_task', 'edit_task', 'delete_task', 'view_task_content', 'edit_task_content',
    'execute_task', 'approve_task', 'manage_task_executors', 'manage_task_approvers', 'complete_task'
  ],
  manager: [
    // Projetos
    'view_project', 'edit_project', 'manage_project_members', 'complete_project',
    // Etapas
    'create_stage', 'view_stage', 'edit_stage', 'delete_stage', 'manage_stage_members', 'complete_stage',
    // Tarefas
    'create_task', 'view_task', 'edit_task', 'delete_task', 'view_task_content', 'edit_task_content',
    'execute_task', 'approve_task', 'manage_task_executors', 'manage_task_approvers', 'complete_task'
  ],
  editor: [
    // Projetos
    'view_project',
    // Etapas
    'create_stage', 'view_stage', 'edit_stage', 'manage_stage_members',
    // Tarefas
    'create_task', 'view_task', 'edit_task', 'view_task_content', 'edit_task_content'
  ],
  executor: [
    // Projetos
    'view_project',
    // Etapas
    'view_stage',
    // Tarefas
    'view_task', 'execute_task', 'view_task_content'
  ],
  approver: [
    // Projetos
    'view_project',
    // Etapas
    'view_stage',
    // Tarefas
    'view_task', 'approve_task', 'view_task_content'
  ],
  member: [
    // Projetos
    'view_project',
    // Etapas
    'view_stage',
    // Tarefas
    'view_task', 'view_task_content'
  ]
};

// Permissões extras baseadas em relacionamentos
interface PermissionContext {
  userId?: string;
  projectOwnerId?: string;
  taskResponsibleId?: string;
  taskExecutorIds?: string[];
  taskApproverIds?: string[];
  stageResponsibleIds?: string[];
}

/**
 * Hook para verificar permissões do usuário em um projeto específico
 */
export const useProjectPermissions = (projectId?: string, context?: PermissionContext) => {
  const { user, profile } = useAuth();

  return useMemo(() => {
    if (!user || !profile) {
      return {
        hasPermission: () => false,
        hasAnyPermission: () => false,
        // Permissões de Projeto
        canCreateProject: false,
        canViewProject: false,
        canEditProject: false,
        canDeleteProject: false,
        canManageMembers: false,
        canCompleteProject: false,
        // Permissões de Etapas
        canCreateStage: false,
        canViewStage: false,
        canEditStage: false,
        canDeleteStage: false,
        canManageStageMembers: false,
        canCompleteStage: false,
        // Permissões de Tarefas
        canCreateTask: false,
        canViewTask: false,
        canEditTask: false,
        canDeleteTask: false,
        canViewTaskContent: false,
        canEditTaskContent: false,
        canExecuteTask: false,
        canApproveTask: false,
        canEditExecutors: false,
        canEditApprovers: false,
        canCompleteTask: false,
        roles: [] as ProjectRole[],
        // Informações de papéis
        globalRole: 'member' as GlobalRole,
        projectRoles: [] as ProjectRole[],
        contextualRole: 'Não autenticado',
        isProjectOwner: false,
        isTaskResponsible: false,
        isTaskExecutor: false,
        isTaskApprover: false,
        isStageResponsible: false
      };
    }

    // Roles do usuário (inicialmente vazio, será preenchido via props ou context)
    const userProjectRoles: ProjectRole[] = [];

    // Se é admin global, tem role admin no projeto
    if (profile.role === 'admin') {
      userProjectRoles.push('admin');
    }

    // Se é manager global e owner do projeto, tem role manager
    if (profile.role === 'manager' && context?.projectOwnerId === user.id) {
      userProjectRoles.push('manager');
    }

    // TODO: Buscar roles específicos do projeto da tabela project_members
    // Por enquanto, usar lógica atual baseada no role global

    /**
     * Determina o papel específico no contexto atual
     */
    const getContextualRole = (): string => {
      const roles: string[] = [];
      
      // Papel baseado em roles do projeto
      if (userProjectRoles.length > 0) {
        roles.push(...userProjectRoles);
      }
      
      // Papéis baseados em relacionamentos específicos
      if (context?.projectOwnerId === user.id) {
        roles.push('Proprietário do Projeto');
      }
      
      if (context?.taskResponsibleId === user.id) {
        roles.push('Responsável pela Tarefa');
      }
      
      if (context?.taskExecutorIds?.includes(user.id)) {
        roles.push('Executor da Tarefa');
      }
      
      if (context?.taskApproverIds?.includes(user.id)) {
        roles.push('Aprovador da Tarefa');
      }
      
      if (context?.stageResponsibleIds?.includes(user.id)) {
        roles.push('Responsável pela Etapa');
      }
      
      // Se não tem papéis específicos, usar papel base
      if (roles.length === 0) {
        switch (profile.role) {
          case 'admin':
            return 'Administrador';
          case 'manager':
            return 'Gerente';
          default:
            return 'Membro';
        }
      }
      
      return roles.join(', ');
    };

    /**
     * Verifica se o usuário tem uma permissão específica
     */
    const hasPermission = (permission: Permission): boolean => {
      // Admin global tem todas as permissões
      if (profile.role === 'admin') return true;

      // Verifica permissões baseadas nos roles do projeto
      const hasRolePermission = userProjectRoles.some(role => 
        PROJECT_PERMISSIONS[role]?.includes(permission)
      );

      if (hasRolePermission) return true;

      // Verifica permissões especiais baseadas em contexto
      switch (permission) {
        // Permissões de Projeto
        case 'create_project':
          return profile.role === 'admin' || profile.role === 'manager';
        case 'edit_project':
        case 'manage_project_members':
        case 'complete_project':
          return profile.role === 'admin' || (profile.role === 'manager' && context?.projectOwnerId === user.id);

        // Permissões de Etapas
        case 'create_stage':
        case 'edit_stage':
        case 'delete_stage':
        case 'manage_stage_members':
        case 'complete_stage':
          return profile.role === 'admin' || (profile.role === 'manager' && context?.projectOwnerId === user.id);

        // Permissões de Tarefas
        case 'create_task':
        case 'edit_task':
        case 'delete_task':
        case 'complete_task':
        case 'manage_task_executors':
        case 'manage_task_approvers':
          return (
            profile.role === 'admin' ||
            (profile.role === 'manager' && context?.projectOwnerId === user.id) ||
            (context?.taskResponsibleId === user.id)
          );

        case 'execute_task':
          return context?.taskExecutorIds?.includes(user.id) || false;

        case 'approve_task':
          return context?.taskApproverIds?.includes(user.id) || false;

        default:
          return false;
      }
    };

    /**
     * Verifica se o usuário tem pelo menos uma das permissões
     */
    const hasAnyPermission = (permissions: Permission[]): boolean => {
      return permissions.some(permission => hasPermission(permission));
    };

    // Permissões derivadas para facilitar uso
    const canCreateProject = hasPermission('create_project');
    const canViewProject = hasPermission('view_project');
    const canEditProject = hasPermission('edit_project');
    const canDeleteProject = hasPermission('delete_project');
    const canManageMembers = hasPermission('manage_project_members');
    const canCompleteProject = hasPermission('complete_project');
    
    const canCreateStage = hasPermission('create_stage');
    const canViewStage = hasPermission('view_stage');
    const canEditStage = hasPermission('edit_stage');
    const canDeleteStage = hasPermission('delete_stage');
    const canManageStageMembers = hasPermission('manage_stage_members');
    const canCompleteStage = hasPermission('complete_stage');
    
    const canCreateTask = hasPermission('create_task');
    const canViewTask = hasPermission('view_task');
    const canEditTask = hasPermission('edit_task');
    const canDeleteTask = hasPermission('delete_task');
    const canViewTaskContent = hasPermission('view_task_content');
    const canEditTaskContent = hasPermission('edit_task_content');
    const canExecuteTask = hasPermission('execute_task');
    const canApproveTask = hasPermission('approve_task');
    const canEditExecutors = hasPermission('manage_task_executors');
    const canEditApprovers = hasPermission('manage_task_approvers');
    const canCompleteTask = hasPermission('complete_task');

    return {
      hasPermission,
      hasAnyPermission,
      // Permissões de Projeto
      canCreateProject,
      canViewProject,
      canEditProject,
      canDeleteProject,
      canManageMembers,
      canCompleteProject,
      // Permissões de Etapas
      canCreateStage,
      canViewStage,
      canEditStage,
      canDeleteStage,
      canManageStageMembers,
      canCompleteStage,
      // Permissões de Tarefas
      canCreateTask,
      canViewTask,
      canEditTask,
      canDeleteTask,
      canViewTaskContent,
      canEditTaskContent,
      canExecuteTask,
      canApproveTask,
      canEditExecutors,
      canEditApprovers,
      canCompleteTask,
      roles: userProjectRoles,
      // Informações de papéis
      globalRole: profile.role,
      projectRoles: userProjectRoles,
      contextualRole: getContextualRole(),
      isProjectOwner: context?.projectOwnerId === user.id,
      isTaskResponsible: context?.taskResponsibleId === user.id,
      isTaskExecutor: context?.taskExecutorIds?.includes(user.id) || false,
      isTaskApprover: context?.taskApproverIds?.includes(user.id) || false,
      isStageResponsible: context?.stageResponsibleIds?.includes(user.id) || false
    };
  }, [user, profile, projectId, context]);
};

/**
 * Hook para verificar permissões globais do sistema
 */
export const useGlobalPermissions = () => {
  const { profile } = useAuth();

  return useMemo(() => {
    if (!profile) {
      return {
        canManageUsers: false,
        canCreateProjects: false,
        canViewAllProjects: false,
        isAdmin: false,
        isManager: false
      };
    }

    const isAdmin = profile.role === 'admin';
    const isManager = profile.role === 'manager';

    return {
      canManageUsers: isAdmin,
      canCreateProjects: isAdmin || isManager,
      canViewAllProjects: isAdmin,
      isAdmin,
      isManager
    };
  }, [profile]);
};

/**
 * Hook simplificado para roles de projeto
 * TODO: Implementar busca real dos roles do usuário no projeto
 */
export const useProjectRoles = (projectId: string) => {
  const { user, profile } = useAuth();

  return useMemo(() => {
    const roles: ProjectRole[] = [];

    if (profile?.role === 'admin') {
      roles.push('admin');
    } else if (profile?.role === 'manager') {
      roles.push('manager');
    } else {
      roles.push('member');
    }

    return roles;
  }, [user, profile, projectId]);
};

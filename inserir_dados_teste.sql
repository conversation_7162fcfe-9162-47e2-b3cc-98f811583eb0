-- =====================================================
-- SCRIPT: INSERIR DADOS DE TESTE - EXECUTORES
-- =====================================================
-- Execute este script no Supabase SQL Editor se não houver dados

-- Primeiro, vamos verificar se já existem dados
SELECT 
    'Tarefas no sistema' as info,
    COUNT(*) as total
FROM tasks;

SELECT 
    'Executores no sistema' as info,
    COUNT(*) as total
FROM task_executors;

-- Se não houver tarefas, vamos criar algumas para teste
-- ATENÇÃO: Substitua os IDs pelos IDs reais do seu banco

-- Exemplo de inserção de tarefas (apenas se não existirem)
-- Substitua os IDs reais de projeto e estágio
/*
INSERT INTO tasks (title, description, status, progress, stage_id, assigned_to, created_at)
VALUES 
(
    'Tarefa de Teste 1',
    'Descrição da tarefa de teste 1',
    'pending',
    0,
    'ID_DO_STAGE_REAL',
    'ID_DO_USUARIO_RESPONSAVEL',
    NOW()
),
(
    'Tarefa de Teste 2',
    'Descrição da tarefa de teste 2',
    'in-progress',
    50,
    'ID_DO_STAGE_REAL',
    'ID_DO_USUARIO_RESPONSAVEL',
    NOW()
);
*/

-- Exemplo de inserção de executores (apenas se não existirem)
-- Substitua pelos IDs reais de usuário e tarefa
/*
INSERT INTO task_executors (user_id, task_id, created_at)
VALUES 
(
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    'ID_DA_TAREFA_REAL',
    NOW()
),
(
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    'ID_DA_SEGUNDA_TAREFA_REAL',
    NOW()
);
*/

-- Verificar os dados após inserção
SELECT 
    'Verificação final' as info,
    COUNT(DISTINCT t.id) as total_tarefas,
    COUNT(DISTINCT te.user_id) as total_executores,
    COUNT(te.id) as total_atribuicoes
FROM tasks t
LEFT JOIN task_executors te ON t.id = te.task_id;

-- Verificar se o usuário atual tem tarefas como executor
SELECT 
    'Tarefas do usuário atual' as info,
    u.email,
    COUNT(te.task_id) as total_tarefas_executor
FROM auth.users u
LEFT JOIN task_executors te ON u.id = te.user_id
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
GROUP BY u.id, u.email;

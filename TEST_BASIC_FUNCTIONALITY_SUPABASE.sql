-- =====================================================
-- TESTE BÁSICO DE FUNCIONALIDADES (VERSÃO SUPABASE)
-- =====================================================
-- Versão compatível com Supabase SQL Editor
-- Execute APÓS executar REMOVE_ALL_RLS.sql

-- =====================================================
-- TESTE 1: VERIFICAR LIMPEZA RLS
-- =====================================================

-- Verificar políticas restantes
SELECT 
    'POLÍTICAS RLS RESTANTES' as teste,
    COUNT(*) as quantidade,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ PASSOU'
        ELSE '❌ FALHOU'
    END as resultado
FROM pg_policies 
WHERE schemaname = 'public';

-- Verificar tabelas com RLS habilitado
SELECT 
    'TABELAS COM RLS HABILITADO' as teste,
    COUNT(*) as quantidade,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ PASSOU'
        ELSE '❌ FALHOU'
    END as resultado
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public' 
AND c.relkind = 'r' 
AND c.relrowsecurity = true;

-- =====================================================
-- TESTE 2: ACESSO BÁSICO A TABELAS
-- =====================================================

-- Testar acesso a todas as tabelas principais
SELECT 
    'profiles' as tabela,
    COUNT(*) as registros,
    '✅ ACESSÍVEL' as status
FROM public.profiles

UNION ALL

SELECT 
    'projects' as tabela,
    COUNT(*) as registros,
    '✅ ACESSÍVEL' as status
FROM public.projects

UNION ALL

SELECT 
    'stages' as tabela,
    COUNT(*) as registros,
    '✅ ACESSÍVEL' as status
FROM public.stages

UNION ALL

SELECT 
    'tasks' as tabela,
    COUNT(*) as registros,
    '✅ ACESSÍVEL' as status
FROM public.tasks

UNION ALL

SELECT 
    'project_members' as tabela,
    COUNT(*) as registros,
    '✅ ACESSÍVEL' as status
FROM public.project_members

UNION ALL

SELECT 
    'task_executors' as tabela,
    COUNT(*) as registros,
    '✅ ACESSÍVEL' as status
FROM public.task_executors

UNION ALL

SELECT 
    'task_approvers' as tabela,
    COUNT(*) as registros,
    '✅ ACESSÍVEL' as status
FROM public.task_approvers;

-- =====================================================
-- TESTE 3: AUTOCOMPLETE DE USUÁRIOS
-- =====================================================

-- Verificar usuários ativos para autocomplete
SELECT 
    'USUÁRIOS ATIVOS PARA AUTOCOMPLETE' as teste,
    COUNT(*) as quantidade,
    '✅ DISPONÍVEL' as status
FROM profiles 
WHERE is_active = true;

-- Exemplo de dados para autocomplete
SELECT 
    id,
    name,
    email,
    role,
    'AMOSTRA AUTOCOMPLETE' as tipo
FROM profiles 
WHERE is_active = true 
AND name IS NOT NULL
LIMIT 5;

-- =====================================================
-- TESTE 4: RELACIONAMENTOS HIERÁRQUICOS
-- =====================================================

-- Verificar hierarquia projeto -> etapa -> tarefa
SELECT 
    p.name as projeto,
    s.name as etapa,
    t.title as tarefa,
    'HIERARQUIA OK' as status
FROM projects p
LEFT JOIN stages s ON s.project_id = p.id
LEFT JOIN tasks t ON t.stage_id = s.id
WHERE p.name IS NOT NULL
LIMIT 5;

-- =====================================================
-- TESTE 5: PERFORMANCE BÁSICA
-- =====================================================

-- Query complexa para testar performance
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    p.id,
    p.name,
    COUNT(DISTINCT s.id) as total_stages,
    COUNT(DISTINCT t.id) as total_tasks,
    COUNT(DISTINCT pm.user_id) as total_members
FROM projects p
LEFT JOIN stages s ON s.project_id = p.id
LEFT JOIN tasks t ON t.stage_id = s.id
LEFT JOIN project_members pm ON pm.project_id = p.id
GROUP BY p.id, p.name
ORDER BY p.created_at DESC
LIMIT 10;

-- =====================================================
-- TESTE 6: VERIFICAR ESTRUTURA DE SEGURANÇA
-- =====================================================

-- Verificar que auth.uid() funciona (mesmo sem usuário autenticado)
SELECT 
    'AUTH.UID() TEST' as teste,
    CASE 
        WHEN auth.uid() IS NULL THEN 'NULL (esperado sem autenticação)'
        ELSE auth.uid()::TEXT
    END as resultado;

-- Verificar extensões necessárias
SELECT 
    extname as extensao,
    '✅ INSTALADA' as status
FROM pg_extension 
WHERE extname IN ('uuid-ossp', 'pgcrypto');

-- =====================================================
-- RELATÓRIO FINAL RESUMIDO
-- =====================================================

-- Resumo geral do sistema
SELECT 
    'RESUMO FINAL' as secao,
    'Tabela' as item,
    'Registros' as valor
WHERE 1=0  -- Header

UNION ALL

SELECT 
    '', 'profiles', COUNT(*)::TEXT
FROM profiles

UNION ALL

SELECT 
    '', 'projects', COUNT(*)::TEXT
FROM projects

UNION ALL

SELECT 
    '', 'stages', COUNT(*)::TEXT
FROM stages

UNION ALL

SELECT 
    '', 'tasks', COUNT(*)::TEXT
FROM tasks

UNION ALL

SELECT 
    '', 'project_members', COUNT(*)::TEXT
FROM project_members

UNION ALL

SELECT 
    'STATUS', 'RLS Policies', 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ TODAS REMOVIDAS'
        ELSE '❌ ' || COUNT(*)::TEXT || ' RESTANTES'
    END
FROM pg_policies WHERE schemaname = 'public'

UNION ALL

SELECT 
    '', 'Sistema', '✅ FUNCIONANDO SEM RLS'
WHERE NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public');

-- Verificação final de tabelas críticas
SELECT 
    table_name,
    CASE 
        WHEN c.relrowsecurity THEN '❌ RLS HABILITADO'
        ELSE '✅ RLS DESABILITADO'
    END as rls_status
FROM information_schema.tables t
LEFT JOIN pg_class c ON c.relname = t.table_name
WHERE t.table_schema = 'public' 
AND t.table_type = 'BASE TABLE'
AND t.table_name IN ('profiles', 'projects', 'stages', 'tasks', 'project_members')
ORDER BY t.table_name;

-- TESTE COMPLETO PARA CORREÇÃO DE TODOS OS ERROS
-- Execute APÓS aplicar o fix-rls-issue.sql atualizado

-- 1. Verificar se o RLS está desabilitado nas três tabelas problemáticas
SELECT 
    schemaname, 
    tablename, 
    rowsecurity,
    CASE 
        WHEN rowsecurity = true THEN '❌ RLS AINDA ATIVO - PROBLEMA!'
        ELSE '✅ RLS DESABILITADO - OK'
    END as status
FROM pg_tables 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') AND schemaname = 'public'
ORDER BY tablename;

-- 2. Verificar se ainda existem políticas ativas (deve retornar vazio)
SELECT 
    tablename,
    policyname,
    '❌ POLÍTICA AINDA ATIVA' as problema
FROM pg_policies 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') AND schemaname = 'public'
ORDER BY tablename, policyname;

-- 3. NOVO: Verificar se o usuário problemático existe na tabela profiles
SELECT 
    id,
    email,
    name,
    role,
    is_active,
    'Usu<PERSON>rio encontrado para foreign key' as status
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 4. Testar se a tarefa específica existe
SELECT 
    id, 
    title, 
    'Tarefa encontrada' as status
FROM public.tasks 
WHERE id = 'd6fb6099-a751-4bc0-acdc-50e1716bc8e8';

-- 5. Reproduzir a consulta que estava falhando (tasks)
SELECT 
    id,
    title,
    description,
    status,
    priority,
    assigned_to,
    created_by,
    estimated_hours,
    actual_hours,
    due_date,
    completed_at,
    created_at,
    updated_at,
    stage_id
FROM public.tasks 
WHERE id = 'd6fb6099-a751-4bc0-acdc-50e1716bc8e8';

-- 6. NOVO: Testar inserção em task_content_blocks (simular criação de bloco)
SELECT 
    gen_random_uuid() as id,
    'd6fb6099-a751-4bc0-acdc-50e1716bc8e8'::uuid as task_id,
    'evidence'::text as type,
    '{}'::jsonb as content,
    '{}'::jsonb as config,
    0 as "order",
    '✅ ESTRUTURA OK PARA INSERÇÃO EM CONTENT_BLOCKS' as status;

-- 7. NOVO: Testar inserção na tabela evidence (simular o upload)
SELECT 
    gen_random_uuid() as id,
    'd6fb6099-a751-4bc0-acdc-50e1716bc8e8'::uuid as task_id,
    'block-123'::text as block_id,
    'file'::text as type,
    'evidence/test-file.pdf'::text as content,
    'test-file.pdf'::text as file_name,
    'application/pdf'::text as mime_type,
    1024::bigint as file_size,
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4'::uuid as uploaded_by,
    'pending'::text as status,
    '✅ ESTRUTURA OK PARA INSERÇÃO EM EVIDENCE' as status_evidence;

-- 8. Contar registros nas tabelas para verificar acesso
SELECT 
    'TASKS' as tabela,
    count(*) as total_registros
FROM public.tasks 
WHERE created_at > CURRENT_DATE - INTERVAL '30 days'

UNION ALL

SELECT 
    'EVIDENCE' as tabela,
    count(*) as total_registros
FROM public.evidence
WHERE created_at > CURRENT_DATE - INTERVAL '30 days'

UNION ALL

SELECT 
    'TASK_CONTENT_BLOCKS' as tabela,
    count(*) as total_registros
FROM public.task_content_blocks
WHERE created_at > CURRENT_DATE - INTERVAL '30 days'

UNION ALL

SELECT 
    'PROFILES' as tabela,
    count(*) as total_registros
FROM public.profiles
WHERE created_at > CURRENT_DATE - INTERVAL '30 days'

ORDER BY tabela;

-- INTERPRETAÇÃO DOS RESULTADOS:
-- ✅ Teste 1: Todas as três tabelas devem mostrar "RLS DESABILITADO - OK"
-- ✅ Teste 2: Não deve retornar nenhuma linha (sem políticas ativas)
-- ✅ Teste 3: Deve mostrar o usuário criado para foreign key
-- ✅ Teste 4: Deve encontrar a tarefa ou mostrar se não existe
-- ✅ Teste 5: Deve funcionar sem erro 406
-- ✅ Teste 6: Estrutura dos content_blocks deve estar OK
-- ✅ Teste 7: Estrutura da evidence deve estar OK
-- ✅ Teste 8: Deve mostrar contagem de todas as tabelas sem erro

-- PRÓXIMOS PASSOS APÓS SUCESSO:
-- 1. Reiniciar aplicação (npm run dev) 
-- 2. Testar upload de arquivo na interface
-- 3. Verificar se todos os erros foram resolvidos:
--    - 406 Not Acceptable (tasks) ✅
--    - 403 Forbidden (evidence) ✅  
--    - 403 Forbidden (task_content_blocks) ✅
--    - 409 Conflict (foreign key) ✅
--    - 22P02 Invalid UUID (código) ✅

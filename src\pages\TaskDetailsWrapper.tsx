import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { TaskDetails } from './TaskDetails';
import { TaskDetailsV2 } from './TaskDetailsV2';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Header } from '@/components/layout/Header';
import { 
  Eye, 
  Sparkles, 
  ArrowLeft, 
  Settings,
  Monitor,
  Smartphone,
  Palette,
  Layout,
  Zap
} from 'lucide-react';

/**
 * Wrapper component que permite escolher entre TaskDetails (versão atual) 
 * e TaskDetailsV2 (versão melhorada) com toggle de interface
 */
export const TaskDetailsWrapper = () => {
  const { taskId } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();

  // Obter versão dos parâmetros da URL ou localStorage
  const getInitialVersion = () => {
    const urlVersion = searchParams.get('version');
    if (urlVersion === 'v1' || urlVersion === 'v2') {
      return urlVersion;
    }

    // Fallback para localStorage
    const savedVersion = localStorage.getItem('taskDetailsVersion');
    return savedVersion === 'v2' ? 'v2' : 'v1';
  };

  // Verificar se é primeira visita (sem versão salva) ou se foi forçado via URL
  const shouldShowSelector = () => {
    const urlVersion = searchParams.get('version');
    const savedVersion = localStorage.getItem('taskDetailsVersion');
    const forceSelector = searchParams.get('selector') === 'true';
    const isVersionSelectorRoute = window.location.pathname.includes('/version-selector');

    // Mostrar seletor se:
    // 1. É primeira visita (sem versão salva E sem versão na URL)
    // 2. Foi forçado via URL (?selector=true)
    // 3. É a rota específica do seletor
    return (!urlVersion && !savedVersion) || forceSelector || isVersionSelectorRoute;
  };

  const [selectedVersion, setSelectedVersion] = useState<'v1' | 'v2'>(getInitialVersion);
  const [showVersionSelector, setShowVersionSelector] = useState(shouldShowSelector());

  // Debug logs
  React.useEffect(() => {
    console.log('🔍 TaskDetailsWrapper Debug:', {
      taskId,
      urlVersion: searchParams.get('version'),
      savedVersion: localStorage.getItem('taskDetailsVersion'),
      forceSelector: searchParams.get('selector'),
      isVersionSelectorRoute: window.location.pathname.includes('/version-selector'),
      selectedVersion,
      showVersionSelector,
      shouldShow: shouldShowSelector()
    });
  }, [taskId, searchParams, selectedVersion, showVersionSelector]);

  // Atualizar URL e localStorage quando a versão muda
  useEffect(() => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('version', selectedVersion);
      return newParams;
    });
    localStorage.setItem('taskDetailsVersion', selectedVersion);
  }, [selectedVersion, setSearchParams]);

  const handleVersionChange = (version: 'v1' | 'v2') => {
    setSelectedVersion(version);
    setShowVersionSelector(false);
  };

  // Se não estiver mostrando o seletor, renderizar a versão escolhida
  if (!showVersionSelector) {
    return (
      <>
        {/* Botão flutuante para alternar versão */}
        <div className="fixed bottom-6 right-6 z-50">
          <Button
            onClick={() => setShowVersionSelector(true)}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg rounded-full p-3 transition-all duration-300 hover:scale-110"
            size="sm"
            title="Alternar versão da interface"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Renderizar versão selecionada */}
        {selectedVersion === 'v1' ? <TaskDetails /> : <TaskDetailsV2 />}
      </>
    );
  }

  // Tela de seleção de versão
  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-blue-600 rounded-full p-3 mr-4">
                <Eye className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900">
                Escolha a Versão da Interface
              </h1>
            </div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Selecione qual versão da tela de detalhes da tarefa você prefere usar. 
              Ambas mantêm 100% das funcionalidades.
            </p>
          </div>

          {/* Cards de Comparação */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Versão Atual (V1) */}
            <Card className={`cursor-pointer transition-all duration-300 hover:shadow-xl ${
              selectedVersion === 'v1' ? 'ring-2 ring-blue-500 shadow-lg' : ''
            }`}>
              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center mb-3">
                  <div className="bg-gray-600 rounded-full p-3 mr-3">
                    <Monitor className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-2xl">Versão Atual</CardTitle>
                </div>
                <Badge variant="secondary" className="mx-auto">
                  Estável e Testada
                </Badge>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                  <div className="text-center">
                    <Layout className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">Interface Tradicional</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900">Características:</h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Interface familiar e estável
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Layout vertical tradicional
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Todas as funcionalidades atuais
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Testada em produção
                    </li>
                  </ul>
                </div>

                <Button
                  variant={selectedVersion === 'v1' ? 'default' : 'outline'}
                  className="w-full"
                  onClick={() => handleVersionChange('v1')}
                >
                  {selectedVersion === 'v1' ? 'Versão Selecionada' : 'Usar Versão Atual'}
                </Button>
              </CardContent>
            </Card>

            {/* Versão Melhorada (V2) */}
            <Card className={`cursor-pointer transition-all duration-300 hover:shadow-xl ${
              selectedVersion === 'v2' ? 'ring-2 ring-blue-500 shadow-lg' : ''
            }`}>
              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center mb-3">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-full p-3 mr-3">
                    <Sparkles className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-2xl">Versão Melhorada</CardTitle>
                </div>
                <Badge className="mx-auto bg-gradient-to-r from-blue-600 to-purple-600">
                  Nova e Otimizada
                </Badge>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center mb-4">
                  <div className="text-center">
                    <Palette className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                    <p className="text-sm text-blue-600">Interface Moderna</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900">Melhorias:</h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center">
                      <Zap className="w-4 h-4 text-green-500 mr-2" />
                      Layout em grid responsivo
                    </li>
                    <li className="flex items-center">
                      <Zap className="w-4 h-4 text-green-500 mr-2" />
                      Hierarquia visual melhorada
                    </li>
                    <li className="flex items-center">
                      <Zap className="w-4 h-4 text-green-500 mr-2" />
                      Navegação por tabs otimizada
                    </li>
                    <li className="flex items-center">
                      <Zap className="w-4 h-4 text-green-500 mr-2" />
                      Sidebar com informações contextuais
                    </li>
                    <li className="flex items-center">
                      <Zap className="w-4 h-4 text-green-500 mr-2" />
                      UX/UI mais intuitiva
                    </li>
                  </ul>
                </div>

                <Button
                  variant={selectedVersion === 'v2' ? 'default' : 'outline'}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  onClick={() => handleVersionChange('v2')}
                >
                  {selectedVersion === 'v2' ? 'Versão Selecionada' : 'Experimentar Nova Versão'}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Comparação de Funcionalidades */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="text-center">Comparação de Funcionalidades</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Funcionalidade</th>
                      <th className="text-center py-3 px-4">Versão Atual</th>
                      <th className="text-center py-3 px-4">Versão Melhorada</th>
                    </tr>
                  </thead>
                  <tbody className="space-y-2">
                    <tr className="border-b">
                      <td className="py-3 px-4">Editar tarefa</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Marcar como concluída</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Gerenciar progresso</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Gerenciar membros</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Executar tarefa</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Editar conteúdos</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Comentários</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Layout responsivo</td>
                      <td className="text-center py-3 px-4">✅</td>
                      <td className="text-center py-3 px-4">✅ Melhorado</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4">Hierarquia visual</td>
                      <td className="text-center py-3 px-4">Básica</td>
                      <td className="text-center py-3 px-4">✨ Otimizada</td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4">Navegação intuitiva</td>
                      <td className="text-center py-3 px-4">Padrão</td>
                      <td className="text-center py-3 px-4">✨ Melhorada</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Botões de Ação */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              onClick={() => setShowVersionSelector(false)}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Continuar com {selectedVersion === 'v1' ? 'Versão Atual' : 'Versão Melhorada'}
            </Button>
          </div>

          {/* Nota */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              💡 Você pode alternar entre as versões a qualquer momento usando o botão de configurações
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

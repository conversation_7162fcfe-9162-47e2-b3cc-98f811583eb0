import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Header } from '@/components/layout/Header';
import { StageForm } from '@/components/forms/StageForm';
import { TaskForm } from '@/components/forms/TaskForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Play,
  Pause,
  MoreHorizontal,
  Plus,
  FolderOpen,
  Target
} from 'lucide-react';
import { StageHeader } from './StageDetails/StageHeader';
import { StageContent } from './StageDetails/StageContent';
import { TaskList } from './StageDetails/TaskList';
import { stageService } from '@/services/stageService';
import { projectService } from '@/services/projectService';
import { taskService } from '@/services/taskService';
import { supabase } from '@/lib/supabaseClient';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { useAuth } from '@/auth/useAuth';
import { SidebarProvider, Sidebar, SidebarTrigger } from '@/components/ui/sidebar';
import { useSidebarMenu } from '@/components/ui/sidebar';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { ContentSection } from '@/components/features/content-editor/blocks/shared/ContentSection';
import { ContentBlock } from '@/types';
import { useToast } from '@/hooks/ui/use-toast';

export const StageDetails = () => {
  const { stageId } = useParams();
  const navigate = useNavigate();
  const [showStageForm, setShowStageForm] = React.useState(false);
  const [showTaskForm, setShowTaskForm] = React.useState(false);
  const [stage, setStage] = React.useState<any>(null);
  const [project, setProject] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [tasks, setTasks] = React.useState<any[]>([]);
  const [responsibles, setResponsibles] = React.useState<any[]>([]);
  const { user } = useAuth();
  const [responsiblesLoading, setResponsiblesLoading] = React.useState(false);
  const menuItems = useSidebarMenu();
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
  const { toast } = useToast();

  // Estado local para blocos de conteúdo da etapa
  const [stageContentBlocks, setStageContentBlocks] = React.useState<ContentBlock[]>(stage?.content || []);

  const canEditResponsibles = user?.role === 'admin' || user?.role === 'manager';

  const fetchStage = React.useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await stageService.getById(stageId!);
      setStage(data);
    } catch (err: any) {
      setError('Erro ao carregar etapa.');
    } finally {
      setLoading(false);
    }
  }, [stageId]);

  const fetchTasks = React.useCallback(async () => {
    if (stage?.id) {
      try {
        const data = await taskService.list(stage.id);
        const mapped = (data || []).map((t: any) => ({
          id: t.id,
          name: t.title,
          status: t.status,
          responsible: t.responsible ? {
            id: t.responsible.id,
            name: t.responsible.name,
            email: t.responsible.email,
            avatar_url: t.responsible.avatar_url,
            role: t.responsible.role,
            is_active: t.responsible.is_active,
          } : undefined,
          progress: t.progress ?? 0,
        }));
        setTasks(mapped);
      } catch {
        setTasks([]);
      }
    }
  }, [stage?.id]);

  React.useEffect(() => {
    if (stageId) fetchStage();
  }, [stageId, fetchStage]);

  React.useEffect(() => {
    if (stage?.project?.id || stage?.project_id) {
      const id = stage?.project?.id || stage?.project_id;
      projectService.getById(id).then(setProject).catch(() => setProject(null));
    }
  }, [stage]);

  React.useEffect(() => {
    fetchTasks();
  }, [fetchTasks, showTaskForm, stage?.id]);

  React.useEffect(() => {
    // Buscar responsáveis da etapa
    const fetchResponsibles = async () => {
      if (!stage?.id) return setResponsibles([]);
      const { data: stageResps } = await supabase
        .from('stage_responsibles')
        .select('user_id')
        .eq('stage_id', stage.id);
      if (stageResps && stageResps.length > 0) {
        const userIds = stageResps.map((r: any) => r.user_id);
        const { data: profiles } = await supabase
          .from('profiles')
          .select('*')
          .in('id', userIds);
        setResponsibles(profiles || []);
      } else {
        setResponsibles([]);
      }
    };
    fetchResponsibles();
  }, [stage]);

  const handleRemoveResponsible = async (userId: string) => {
    setResponsiblesLoading(true);
    await supabase.from('stage_responsibles').delete().eq('stage_id', stage.id).eq('user_id', userId);
    setResponsibles(responsibles.filter(r => r.id !== userId));
    setResponsiblesLoading(false);
  };

  const handleAddResponsibles = async (newUsers: any[]) => {
    setResponsiblesLoading(true);
    const toAdd = newUsers.filter((u) => !responsibles.some((r) => r.id === u.id));
    if (toAdd.length > 0) {
      await Promise.all(toAdd.map(async (u) => {
        await supabase.from('stage_responsibles').insert({ stage_id: stage.id, user_id: u.id });
      }));
      setResponsibles([...responsibles, ...toAdd]);
    }
    setResponsiblesLoading(false);
  };

  // Permissão de edição: admin, manager ou responsável pela etapa
  // Isso garante que apenas usuários autorizados possam editar o conteúdo da etapa.
  const canEditStageContent = user?.role === 'admin' || user?.role === 'manager' || user?.id === stage?.responsible?.id;

  // Sincronizar blocos de conteúdo ao carregar nova etapa
  // Isso evita inconsistências ao navegar entre etapas diferentes.
  React.useEffect(() => {
    setStageContentBlocks(stage?.content || []);
  }, [stage?.content]);

  // Função para salvar os blocos de conteúdo da etapa
  async function handleSaveStageContentBlocks(blocks: ContentBlock[]) {
    try {
      await stageService.saveContentBlocks(stage.id, blocks);
      setStageContentBlocks(blocks);
      toast({ title: 'Conteúdo salvo', description: 'O conteúdo da etapa foi salvo com sucesso.' });
    } catch (err: any) {
      toast({ title: 'Erro ao salvar conteúdo', description: err?.message || 'Erro desconhecido', variant: 'destructive' });
    }
  }

  // Função para cancelar edição (recarrega do backend)
  function handleCancelStageContentEdit() {
    setStageContentBlocks(stage?.content || []);
  }

  if (loading) {
    return <div className="p-8 text-center">Carregando...</div>;
  }
  if (error) {
    return <div className="p-8 text-center text-red-600">{error}</div>;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in-progress': return 'bg-yellow-500';
      case 'todo': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'in-progress': return <Play className="w-4 h-4" />;
      case 'todo': return <Pause className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const handleTaskClick = (task: any) => {
    console.log('Navegando para tarefa:', task.name);
    navigate(`/task/${task.id}`);
  };

  const handleEditStage = () => {
    setShowStageForm(true);
  };

  const handleNewTask = () => {
    setShowTaskForm(true);
  };

  // Função para voltar para o projeto
  const handleBackToProject = () => {
    if (project?.id) navigate('/project2/' + project.id);
  };

  return (
    <>
      <Header />
      <div className="flex min-h-screen bg-gray-50">
        <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
        <div className={`w-full min-w-0 max-w-full mx-auto px-2 sm:px-4 py-4 space-y-6 transition-all duration-300 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'} md:ml-0`}>
          {/* Conteúdo principal */}
          <div className="flex flex-col min-h-screen gap-6">
            <main className="flex-1">
              {/* Navegação */}
              <div className="flex items-center gap-2 text-sm text-gray-600 whitespace-nowrap w-full min-w-0 mb-2">
                {/* <Button variant="ghost" size="sm" className="p-0" onClick={handleBackToProject}>
                  <ArrowLeft className="w-4 h-4 mr-1" />
                  Voltar para o Projeto
                </Button> */}
              </div>
              {/* Organograma visual para projeto e etapa */}
              <div className="w-full min-w-0 overflow-x-auto mb-4">
                <div className="flex flex-col text-sm mt-3 ml-2 mb-4 gap-0.5">
                  <button
                    className="flex items-center font-normal text-project hover:bg-blue-50 rounded px-2 py-1 transition"
                    onClick={() => project?.id && navigate(`/project2/${project.id}`)}
                    title={project?.title || project?.name}
                    type="button"
                  >
                    <FolderOpen className="w-4 h-4 mr-2 text-project" />
                    <span className="truncate">{project?.title || project?.name || 'Projeto'}</span>
                  </button>
                  <div className="flex items-center font-bold ml-6 border-l-2 border-gray-200 pl-3 relative text-stage rounded px-2 py-1 shadow-sm">
                    <span className="absolute -left-6 top-1.5 w-4 h-4 flex items-center justify-center">
                      <span className="block w-0.5 h-6 bg-gray-200"></span>
                    </span>
                    <Target className="w-4 h-4 mr-2 text-stage" />
                    <span className="truncate">{stage?.title || stage?.name || 'Etapa'}</span>
                  </div>
                </div>
              </div>
              {/* Conteúdo principal com espaçamento harmonizado */}
              <div className="flex flex-col gap-6">
                {/* Header da Etapa */}
                <StageHeader
                  status={stage?.status}
                  name={stage?.name}
                  description={stage?.description}
                  responsible={stage?.responsible}
                  startDate={stage?.start_date}
                  endDate={stage?.end_date}
                  progress={stage?.progress}
                  onEdit={handleEditStage}
                />
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
                  {/* Conteúdo Principal */}
                  <div className="lg:col-span-2 space-y-6">
                    <StageContent
                      objetivos={stage?.objetivos}
                      requisitos={stage?.requisitos}
                    />
                    <TaskList
                      tasks={tasks}
                      onTaskClick={handleTaskClick}
                      onNewTask={handleNewTask}
                      getStatusColor={getStatusColor}
                    />
                    {/* Seção de Conteúdo da Etapa */}
                    <div className="mt-8">
                      <ContentSection
                        blocks={stageContentBlocks}
                        onSave={handleSaveStageContentBlocks}
                        onCancel={handleCancelStageContentEdit}
                        editable={canEditStageContent}
                        title="Conteúdo da Etapa"
                        description="Adicione, edite ou reordene blocos de conteúdo para esta etapa."
                      />
                    </div>
                  </div>
                  {/* Sidebar de responsáveis */}
                  <div className="lg:col-span-1 space-y-6">
                    <div className="flex flex-col gap-3">
                      <div className="flex items-center gap-3">
                        <User className="w-5 h-5 text-stage" />
                        <p className="text-sm font-medium">Responsáveis</p>
                      </div>
                      <div className="flex flex-col gap-2 mt-2">
                        {responsibles.length === 0 ? (
                          <span className="text-xs text-gray-400">Nenhum responsável atribuído.</span>
                        ) : (
                          responsibles.map((user) => (
                            <div key={user.id} className="flex items-center gap-2">
                              <Avatar className="w-6 h-6 border-2 border-white">
                                <AvatarImage src={user.avatar_url || '/placeholder.svg'} />
                                <AvatarFallback>{user.name?.[0] || '?'}</AvatarFallback>
                              </Avatar>
                              <div className="flex flex-col">
                                <span className="text-sm font-medium">{user.name}</span>
                                <span className="text-xs text-gray-500">{user.email}</span>
                              </div>
                              <Badge className={
                                user.role === 'admin' ? 'bg-red-500 text-white' :
                                user.role === 'manager' ? 'bg-blue-500 text-white' :
                                'bg-gray-300 text-gray-800'
                              }>
                                {user.role}
                              </Badge>
                              {user.is_active ? (
                                <span className="text-green-600 text-xs ml-2">Ativo</span>
                              ) : (
                                <span className="text-gray-400 text-xs ml-2">Inativo</span>
                              )}
                              {canEditResponsibles && (
                                <Button size="icon" variant="ghost" disabled={responsiblesLoading} onClick={() => handleRemoveResponsible(user.id)} aria-label="Remover responsável">
                                  ×
                                </Button>
                              )}
                            </div>
                          ))
                        )}
                        {canEditResponsibles && (
                          <div className="mt-2">
                            <UserAutocomplete
                              onSelect={async (user) => {
                                await handleAddResponsibles([user]);
                              }}
                              excludeIds={responsibles.map((r) => r.id)}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <StageForm
                  open={showStageForm}
                  onOpenChange={setShowStageForm}
                  stage={stage}
                  mode="edit"
                  projectId={stage?.project?.id}
                  onUpdated={fetchStage}
                  projectStartDate={project?.start_date}
                  projectEndDate={project?.end_date}
                  members={project?.members || []}
                  projectMembers={project?.members || []}
                />
                <TaskForm
                  open={showTaskForm}
                  onOpenChange={setShowTaskForm}
                  mode="create"
                  stageId={stageId!}
                  onCreated={fetchTasks}
                  projectMembers={project?.members || []}
                />
              </div>
            </main>
          </div>
        </div>
      </div>
    </>
  );
};

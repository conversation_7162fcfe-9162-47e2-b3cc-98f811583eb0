-- TESTE DE VALIDAÇÃO DAS CORREÇÕES DE PERMISSÕES
-- Execute este arquivo para testar as correções implementadas

-- ========================================
-- TESTE 1: VERIFICAÇÃO DE ROLES
-- ========================================

-- Consultar usuários existentes e seus roles
SELECT 
    id,
    email,
    name,
    role,
    is_active
FROM profiles 
WHERE is_active = true
ORDER BY role, name;

-- ========================================
-- TESTE 2: VERIFICAÇÃO DE PROJETO E TAREFAS
-- ========================================

-- Buscar projetos e tarefas para teste
SELECT 
    p.id as project_id,
    p.title as project_title,
    t.id as task_id,
    t.title as task_title,
    t.status
FROM projects p
JOIN stages s ON s.project_id = p.id
JOIN tasks t ON t.stage_id = s.id
ORDER BY p.title, t.title
LIMIT 10;

-- ========================================
-- INSTRUÇÕES DE TESTE MANUAL
-- ========================================

/*
🔍 PASSOS PARA TESTAR AS CORREÇÕES:

1. ACESSE A APLICAÇÃO:
   - URL: http://localhost:5174/
   - Login com usuário MEMBER
   - Login com usuário ADMIN/MANAGER

2. TESTE COM USUÁRIO MEMBER:
   ✅ Deve ver apenas 2 tabs: "Visão Geral" e "Executar Tarefa"
   ❌ NÃO deve ver: Tab "Editar Conteúdo"
   ❌ NÃO deve ver: Botão "Editar Conteúdo" no card verde
   ❌ NÃO deve ver: Botão "Adicionar Conteúdo" quando não há conteúdo
   ⚠️ Se tentar acessar URL ...?tab=edit → deve ser redirecionado

3. TESTE COM USUÁRIO ADMIN/MANAGER:
   ✅ Deve ver todas as 3 tabs: "Visão Geral", "Executar Tarefa", "Editar Conteúdo"
   ✅ Deve ver: Botão "Editar Conteúdo" no card verde
   ✅ Deve ver: Botão "Adicionar Conteúdo" quando não há conteúdo
   ✅ Deve conseguir acessar tab edit normalmente

4. TESTE DE REDIRECIONAMENTO:
   - Como MEMBER, tente acessar: /tasks/[TASK_ID]?tab=edit
   - Deve redirecionar para overview com toast de "Acesso restrito"

5. TESTE DE LAYOUT:
   - Como MEMBER: TabsList deve ter classe "grid-cols-2"
   - Como ADMIN/MANAGER: TabsList deve ter classe "grid-cols-3"

✅ CORREÇÕES IMPLEMENTADAS:
- Botão "Editar Conteúdo" agora é condicional (linha 696-703)
- Botão "Adicionar Conteúdo" agora é condicional (linha 741-747)
- Consistência total entre tab e botões

📁 ARQUIVOS ALTERADOS:
- src/pages/TaskDetailsV2.tsx (2 correções aplicadas)
- CORRECAO_PERMISSOES_TABS.md (documentação)
- TESTE_VALIDACAO_PERMISSOES.sql (este arquivo)
*/

import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/auth/useAuth';
import { useGlobalPermissions } from '@/hooks/usePermissions';
import { RequireGlobalRole } from '@/components/auth/PermissionWrappers';
import { UserForm } from '@/components/forms/UserForm';
import { userService, deleteProfile, setUserActiveStatus } from '@/services/userService';
import { useNavigate } from 'react-router-dom';
import { Pencil, Trash, Users, Plus, ArrowLeft, Filter, Menu, ArrowUpDown, Search } from 'lucide-react';
import { useToast } from '@/hooks/ui/use-toast';
import { Switch } from '@/components/ui/switch';
import { SidebarProvider, Sidebar, SidebarTrigger } from '@/components/ui/sidebar';
import { Header } from '@/components/layout/Header';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { useSidebarMenu } from '@/components/ui/sidebar';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuCheckboxItem, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { PermissionIndicator } from '@/components/auth/PermissionIndicator';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

export const UserManagement: React.FC = () => {
  const { profile, loading } = useAuth();
  const { canManageUsers } = useGlobalPermissions();
  const [users, setUsers] = useState<any[]>([]);
  const [search, setSearch] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [usersLoading, setUsersLoading] = useState(true);
  const [userToEdit, setUserToEdit] = useState<any>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [togglingId, setTogglingId] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const menuItems = useSidebarMenu();
  const [order, setOrder] = useState<'az' | 'za'>('az');
  const [statusFilter, setStatusFilter] = useState<string[]>(['all']);
  const [roleFilter, setRoleFilter] = useState<string[]>(['all']);
  const [statusOpen, setStatusOpen] = useState(false);
  const [roleOpen, setRoleOpen] = useState(false);
  const statusRef = React.useRef<HTMLDivElement>(null);
  const roleRef = React.useRef<HTMLDivElement>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const orderButtonRef = React.useRef<HTMLButtonElement>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setUsersLoading(true);
    try {
      const data = await userService.list({ search });
      console.log('DEBUG LIST USUÁRIOS:', data);
      setUsers(data);
    } catch {
      setUsers([]);
    } finally {
      setUsersLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  useEffect(() => {
    fetchUsers();
  }, [search]);

  const handleEdit = (user: any) => {
    setUserToEdit(user);
    setShowForm(true);
  };

  const handleDelete = async (user: any) => {
    if (!window.confirm(`Tem certeza que deseja excluir o usuário "${user.name}"?`)) return;
    setDeletingId(user.id);
    try {
      await deleteProfile(user.id);
      toast({ title: 'Usuário excluído', description: 'Usuário excluído com sucesso!' });
      fetchUsers();
    } catch (err: any) {
      toast({ title: 'Erro', description: err.message || 'Erro ao excluir usuário.', variant: 'destructive' });
    } finally {
      setDeletingId(null);
    }
  };

  const handleToggleActive = async (user: any) => {
    setTogglingId(user.id);
    try {
      await setUserActiveStatus(user.id, !user.is_active);
      toast({ title: 'Status atualizado', description: `Usuário agora está ${!user.is_active ? 'ativo' : 'inativo'}.` });
      fetchUsers();
    } catch (err: any) {
      toast({ title: 'Erro', description: err.message || 'Erro ao atualizar status.', variant: 'destructive' });
    } finally {
      setTogglingId(null);
    }
  };

  // Fecha dropdown ao clicar fora
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (statusOpen && statusRef.current && !statusRef.current.contains(event.target as Node)) {
        setStatusOpen(false);
      }
      if (roleOpen && roleRef.current && !roleRef.current.contains(event.target as Node)) {
        setRoleOpen(false);
      }
    }
    if (statusOpen || roleOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [statusOpen, roleOpen]);

  const statusOptions = [
    { value: 'all', label: 'Todos' },
    { value: 'active', label: 'Ativo' },
    { value: 'inactive', label: 'Inativo' },
  ];
  const roleOptions = [
    { value: 'all', label: 'Todos' },
    { value: 'admin', label: 'Administrador' },
    { value: 'manager', label: 'Gerente' },
    { value: 'member', label: 'Membro' },
  ];

  const handleStatusChange = (status: string) => {
    if (status === 'all') {
      setStatusFilter(['all']);
      return;
    }
    let newFilter = statusFilter.includes(status)
      ? statusFilter.filter(s => s !== status)
      : [...statusFilter.filter(s => s !== 'all'), status];
    if (newFilter.length === 0) {
      newFilter = ['all'];
    } else {
      newFilter = newFilter.filter(s => s !== 'all');
    }
    setStatusFilter(newFilter);
  };
  const handleRoleChange = (role: string) => {
    if (role === 'all') {
      setRoleFilter(['all']);
      return;
    }
    let newFilter = roleFilter.includes(role)
      ? roleFilter.filter(r => r !== role)
      : [...roleFilter.filter(r => r !== 'all'), role];
    if (newFilter.length === 0) {
      newFilter = ['all'];
    } else {
      newFilter = newFilter.filter(r => r !== 'all');
    }
    setRoleFilter(newFilter);
  };

  const filteredUsers = users
    .filter(u => statusFilter.includes('all') || statusFilter.includes(u.is_active ? 'active' : 'inactive'))
    .filter(u => roleFilter.includes('all') || roleFilter.includes(u.role))
    .sort((a, b) => order === 'az'
      ? a.name.localeCompare(b.name)
      : b.name.localeCompare(a.name)
    );

  if (loading) {
    return <div className="p-8 text-center">Carregando...</div>;
  }

  return (
    <>
      <div className="fixed top-0 left-0 w-full z-50">
        <div className="flex items-center">
          <div className="flex-1">
            <Header showSidebarButton={true} />
          </div>
        </div>
      </div>
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`max-w-none mx-auto p-2 sm:p-4 md:p-6 space-y-6 pt-16 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}` }>
        {!canManageUsers ? (
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="p-8 text-center bg-white shadow rounded-xl border border-red-100 flex flex-col items-center max-w-md w-full">
              <div className="flex items-center justify-center w-16 h-16 rounded-full bg-red-50 mb-4">
                <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><rect x="3" y="11" width="18" height="10" rx="2"/><path d="M7 11V7a5 5 0 0 1 10 0v4"/></svg>
              </div>
              <h2 className="text-xl font-bold text-red-600 mb-2">Acesso restrito</h2>
              <p className="text-gray-700 mb-2">Esta área é exclusiva para administradores do sistema.</p>
              <p className="text-gray-500 text-sm">Se você acredita que deveria ter acesso, entre em contato com o responsável pelo sistema.</p>
            </div>
          </div>
        ) : (
          <>
            {/* Navegação */}
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <Button variant="ghost" size="sm" className="p-0 hover:bg-transparent" onClick={() => navigate(-1)}>
                <ArrowLeft className="w-4 h-4 mr-1" />
                Voltar
              </Button>
              <span>/</span>
              <span className="text-project font-medium">Usuários</span>
            </div>

            {/* Cabeçalho e Controles */}
            <Card className="border-project/20 bg-project-bg overflow-visible">
              <CardHeader className="space-y-4 overflow-visible relative">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-project" />
                    <CardTitle>Usuários</CardTitle>
                    <PermissionIndicator screenType="project" />
                  </div>
                  <Button className="bg-project hover:bg-project-dark" onClick={() => setShowForm(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Novo Usuário
                  </Button>
                </div>
                <div className="flex flex-col gap-2 md:flex-row md:gap-4 mt-4 w-full min-w-0">
                  <div className="relative flex items-center w-full md:w-auto min-w-[180px]">
                    <Search className="absolute left-3 w-4 h-4 text-project" />
                    <Input
                      placeholder="Buscar usuários..."
                      value={search}
                      onChange={handleSearch}
                      className="pl-10 h-10 rounded border border-gray-200 bg-white text-sm font-medium focus:outline-none focus:ring-2 focus:ring-project transition-colors"
                    />
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      {React.cloneElement(
                        <button
                          ref={orderButtonRef}
                          type="button"
                          className={
                            `h-10 px-3 rounded border text-sm font-medium flex items-center gap-2 min-w-[100px] w-full md:w-auto transition-colors focus:outline-none focus:ring-2 focus:ring-project
                            bg-white text-gray-700 border-gray-200 hover:border-project
                          data-[state=open]:bg-project-bg data-[state=open]:text-project data-[state=open]:border-project`
                          }
                        >
                          <ArrowUpDown className="w-4 h-4 mr-1 transition-colors text-gray-400 data-[state=open]:text-project" />
                          {order === 'az' ? 'A-Z' : 'Z-A'}
                        </button>
                      )}
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem
                        onSelect={() => setOrder('az')}
                        className={order === 'az' ? 'bg-project-bg text-project font-semibold' : ''}
                      >
                        A-Z
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() => setOrder('za')}
                        className={order === 'za' ? 'bg-project-bg text-project font-semibold' : ''}
                      >
                        Z-A
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  {/* Multiselect Status - padrão ProjectFilters */}
                  <div ref={statusRef} className="relative min-w-[170px] w-full md:w-auto">
                    <button
                      type="button"
                      className="w-full h-10 border rounded px-3 flex items-center gap-2 bg-white hover:border-project focus:outline-none focus:ring-2 focus:ring-project"
                      onClick={() => setStatusOpen((open) => !open)}
                    >
                      <Filter className="w-4 h-4 text-project" />
                      <div className="flex flex-wrap gap-1 items-center">
                        {statusFilter.length === 0 ? (
                          <span className="text-gray-400">Selecione status...</span>
                        ) : (
                          statusFilter.map(s => (
                            <Badge key={s} variant="secondary" className="bg-project-bg text-project">{statusOptions.find(o => o.value === s)?.label || (s === 'all' ? 'Todos' : s)}</Badge>
                          ))
                        )}
                      </div>
                      <span className={`ml-auto`}>
                        <svg className={`w-4 h-4 transition-transform ${statusOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                      </span>
                    </button>
                    {statusOpen && (
                      <div className="absolute left-0 right-0 z-30 mt-1 bg-white border rounded shadow-lg p-2 flex flex-col gap-1 max-w-[350px] w-full" style={{minWidth: '170px'}}>
                        {statusOptions.map((option) => (
                          <label key={option.value} className="flex items-center gap-2 cursor-pointer text-sm px-2 py-1 rounded hover:bg-gray-50">
                            <input
                              type="checkbox"
                              checked={statusFilter.includes(option.value)}
                              onChange={() => handleStatusChange(option.value)}
                              className="accent-project"
                            />
                            {option.label}
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                  {/* Multiselect Papel - padrão ProjectFilters */}
                  <div ref={roleRef} className="relative min-w-[170px] w-full md:w-auto">
                    <button
                      type="button"
                      className="w-full h-10 border rounded px-3 flex items-center gap-2 bg-white hover:border-project focus:outline-none focus:ring-2 focus:ring-project"
                      onClick={() => setRoleOpen((open) => !open)}
                    >
                      <Users className="w-4 h-4 text-project" />
                      <div className="flex flex-nowrap gap-1 items-center overflow-x-auto max-w-full">
                        {roleFilter.length === 0 ? (
                          <span className="text-gray-400">Selecione papéis...</span>
                        ) : (
                          roleFilter.map(r => (
                            <Badge key={r} variant="secondary" className="bg-project-bg text-project whitespace-nowrap">{roleOptions.find(o => o.value === r)?.label || (r === 'all' ? 'Todos' : r)}</Badge>
                          ))
                        )}
                      </div>
                      <span className={`ml-auto`}>
                        <svg className={`w-4 h-4 transition-transform ${roleOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                      </span>
                    </button>
                    {roleOpen && (
                      <div className="absolute left-0 right-0 z-30 mt-1 bg-white border rounded shadow-lg p-2 flex flex-col gap-1 max-w-[350px] w-full" style={{minWidth: '300px'}}>
                        {roleOptions.map((option) => (
                          <label key={option.value} className="flex items-center gap-2 cursor-pointer text-sm px-2 py-1 rounded hover:bg-gray-50">
                            <input
                              type="checkbox"
                              checked={roleFilter.includes(option.value)}
                              onChange={() => handleRoleChange(option.value)}
                              className="accent-project"
                            />
                            {option.label}
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
            </Card>
            
            {/* Listagem de Usuários */}
            <Card>
              <CardContent className="pt-6 pb-8">
                {usersLoading ? (
                  <div className="text-center py-12">
                      <LoadingSpinner size="lg" />
                  </div>
                ) : filteredUsers.length === 0 ? (
                  <div className="text-center text-gray-500">Nenhum usuário encontrado.</div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {filteredUsers.map((u) => {
                      console.log('DEBUG USER CARD:', u);
                      return (
                        <Card key={u.id} className="rounded-xl border border-gray-200 bg-white hover:shadow-lg transition-transform duration-300 hover:-translate-y-1 w-full">
                          <CardHeader className="pb-4 pt-4 px-4 relative">
                            <div className="flex items-start justify-between w-full">
                              <div className="flex items-center gap-4 min-w-0">
                                <Avatar className="w-12 h-12">
                                  <AvatarImage src={u.avatar_url || '/placeholder.svg'} />
                                  <AvatarFallback>{u.name?.[0] || '👤'}</AvatarFallback>
                                </Avatar>
                                <div className="flex-1 min-w-0">
                                  <div className="text-lg font-semibold text-gray-900 truncate" title={u.name}>{u.name}</div>
                                  <div className="text-sm text-gray-500 truncate" title={u.email}>{u.email}</div>
                                </div>
                              </div>
                              {/* Botões de ação */}
                              <div className="flex items-center gap-1">
                                <Button variant="ghost" size="icon" title="Editar" className="rounded-full w-8 h-8 hover:bg-blue-100" onClick={() => handleEdit(u)}>
                                  <Pencil size={16} className="text-gray-600" />
                                </Button>
                                <Button variant="ghost" size="icon" title="Excluir" className="rounded-full w-8 h-8 hover:bg-red-100" onClick={() => handleDelete(u)} disabled={deletingId === u.id}>
                                  <Trash size={16} className="text-gray-600" />
                                </Button>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="flex flex-col gap-2 md:gap-4 pt-0">
                              <div className="text-sm text-gray-700 font-medium bg-gray-50 p-2 rounded-md break-words">{u.position || 'Cargo não definido'}</div>
                              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                                <Badge className={
                                    u.role === 'admin' ? 'bg-red-100 text-red-800 border-red-200' :
                                    u.role === 'manager' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                                    'bg-gray-100 text-gray-800 border-gray-200'
                                } variant="outline">
                                    {u.role === 'admin'
                                      ? 'Administrador'
                                      : u.role === 'manager'
                                        ? 'Gerente'
                                        : 'Membro'}
                                </Badge>
                                 <div className="flex items-center gap-2 mt-2 sm:mt-0">
                                    <Switch
                                      checked={u.is_active}
                                      onCheckedChange={() => handleToggleActive(u)}
                                      disabled={togglingId === u.id || deletingId === u.id}
                                      aria-label={u.is_active ? 'Desativar usuário' : 'Ativar usuário'}
                                      className="scale-90"
                                    />
                                    <span className={`text-xs font-medium ${u.is_active ? 'text-green-600' : 'text-gray-500'}`}>
                                      {u.is_active ? 'Ativo' : 'Inativo'}
                                    </span>
                                  </div>
                              </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
            <UserForm
              open={showForm}
              onOpenChange={(open) => {
                setShowForm(open);
                if (!open) setUserToEdit(null);
              }}
              onCreated={fetchUsers}
              onUpdated={fetchUsers}
              userToEdit={userToEdit}
            />
          </>
        )}
      </div>
    </>
  );
};
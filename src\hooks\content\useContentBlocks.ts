import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { arrayMove } from '@dnd-kit/sortable';
import { useToast } from '@/hooks/ui/use-toast';
import {
  BlockConfig,
  VideoBlockContent,
  TextBlockContent,
  ImageBlockContent,
  QuizBlockContent,
  ColoredBlockContent,
  FileBlockContent,
  EvidenceBlockContent
} from '@/types';
import { defaultBlockConfig } from '@/components/features/content-editor/blocks/shared/config-panel/constants/migration';
import { isLexical<PERSON>son, EMPTY_LEXICAL_STATE } from '@/lib/utils';
import { DEFAULTS as VIDEO_DEFAULTS } from '@/components/features/content-editor/blocks/video/VideoBlockEditor';
import { blockTypePresetsMap, coloredBlockPresets, ALERT_PRESETS } from '@/components/features/content-editor/blocks/shared/config-panel/constants/block-types';
import { presetToBlockConfig } from '../../components/features/content-editor/blocks/shared/config-panel/constants/migration';

interface ContentBlock {
  id: string;
  type: 'text' | 'video' | 'image' | 'file' | 'quiz' | 'colored-block' | 'alert';
  content: Record<string, any>;
  order: number;
  config?: BlockConfig;
}

interface UseContentBlocksProps {
  initialBlocks: ContentBlock[];
  onBlocksChange: (blocks: ContentBlock[]) => void;
}

export const useContentBlocks = ({ initialBlocks, onBlocksChange }: UseContentBlocksProps) => {
  const [blocks, setBlocks] = useState<ContentBlock[]>(initialBlocks);
  const [editingBlock, setEditingBlock] = useState<ContentBlock | null>(null);
  const [editContent, setEditContent] = useState<any>({});
  const [editConfig, setEditConfig] = useState<BlockConfig>(defaultBlockConfig);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [showDiscardDialog, setShowDiscardDialog] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setBlocks(initialBlocks);
  }, [initialBlocks]);

  const addBlock = useCallback((type: string) => {
    const preset = blockTypePresetsMap[type];
    const newBlock: ContentBlock = {
      id: uuidv4(),
      type: type as ContentBlock['type'],
      content: getDefaultContent(type),
      order: blocks.length,
      config: preset ? presetToBlockConfig(preset) : defaultBlockConfig,
    };
    const updatedBlocks = [...blocks, newBlock];
    setBlocks(updatedBlocks);
    onBlocksChange(updatedBlocks);
    toast({
      title: 'Bloco adicionado',
      description: 'Novo bloco de conteúdo foi adicionado.',
      variant: 'success',
    });
  }, [blocks, onBlocksChange, toast]);

  const getDefaultContent = useCallback((type: string) => {
    if (type === 'video') {
      return { ...VIDEO_DEFAULTS };
    }
    switch (type) {
      case 'text':
        return { text: EMPTY_LEXICAL_STATE };
      case 'image':
        return { alt: 'Nova Imagem', url: '', caption: '' };
      case 'file':
        return { name: 'Novo Arquivo', url: '', size: 0 };
      case 'quiz':
        return { question: 'Sua pergunta?', options: ['Opção 1', 'Opção 2'], correct: 0 };
      case 'evidence':
        return { title: 'Evidências/Anexos', description: 'Envie arquivos como evidência ou anexo para esta tarefa.', allowUpload: true, evidences: [] };
      case 'colored-block':
        return { type: 'info', title: 'Título', text: 'Conteúdo do bloco...' };
      case 'alert':
        return {
          presetId: ALERT_PRESETS[0].id,
          title: ALERT_PRESETS[0].defaultTitle,
          message: ALERT_PRESETS[0].defaultMessage,
          icon: ALERT_PRESETS[0].defaultIcon,
          actionLabel: '',
        };
      default:
        return {};
    }
  }, []);

  const deleteBlock = useCallback((blockId: string) => {
    const updatedBlocks = blocks.filter(block => block.id !== blockId);
    setBlocks(updatedBlocks);
    onBlocksChange(updatedBlocks);
    toast({
      title: 'Bloco removido',
      description: 'O bloco foi removido com sucesso.',
    });
  }, [blocks, onBlocksChange, toast]);

  const startEditBlock = useCallback((block: ContentBlock) => {
    setEditingBlock(block);
    switch (block.type) {
      case 'video':
        setEditContent(block.content as VideoBlockContent);
        break;
      case 'text':
        setEditContent(block.content as TextBlockContent);
        break;
      case 'image':
        setEditContent(block.content as ImageBlockContent);
        break;
      case 'quiz':
        setEditContent(block.content as QuizBlockContent);
        break;
      case 'evidence':
        setEditContent(block.content as EvidenceBlockContent);
        break;
      case 'colored-block': {
        setEditContent(block.content as ColoredBlockContent);
        const variant = (block.content as ColoredBlockContent).type || 'info';
        setEditConfig(presetToBlockConfig(coloredBlockPresets[variant]));
        break;
      }
      case 'file':
        setEditContent(block.content as FileBlockContent);
        break;
      case 'alert':
        setEditContent({
          presetId: block.content.presetId,
          title: block.content.title,
          message: block.content.message,
          icon: block.content.icon,
          actionLabel: block.content.actionLabel || '',
          actionUrl: block.content.actionUrl || '',
        });
        break;
      default:
        setEditContent(block.content);
    }
    if (block.type !== 'colored-block') {
      setEditConfig(block.config || defaultBlockConfig);
    }
  }, []);

  const handleEditContentChange = useCallback((c: any) => {
    setEditContent(c);
    setUnsavedChanges(true);
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (!editingBlock) return;
    const originalBlock = blocks.find(block => block.id === editingBlock.id);
    if (!originalBlock) return;
    let newContent = { ...editContent };
    if (originalBlock.type === 'text') {
      if (!isLexicalJson(newContent.text)) {
        newContent.text = EMPTY_LEXICAL_STATE;
      }
    }
    const updatedBlock: ContentBlock = {
      ...originalBlock,
      content: newContent,
      config: editConfig
    };
    const updatedBlocks = blocks.map(block =>
      block.id === updatedBlock.id ? updatedBlock : block
    );
    setBlocks(updatedBlocks);
    onBlocksChange(updatedBlocks);
    setEditingBlock(null);
    setEditContent({});
    setUnsavedChanges(false);
  }, [editingBlock, blocks, editContent, editConfig, onBlocksChange]);

  const handleCancelEdit = useCallback(() => {
    if (unsavedChanges) {
      setShowDiscardDialog(true);
    } else {
      setEditingBlock(null);
      setEditContent({});
    }
  }, [unsavedChanges]);

  const confirmDiscard = useCallback(() => {
    setShowDiscardDialog(false);
    setEditingBlock(null);
    setEditContent({});
    setUnsavedChanges(false);
  }, []);

  const onDragEndCallback = useCallback((event: any) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      const oldIndex = blocks.findIndex(block => block.id === active.id);
      const newIndex = blocks.findIndex(block => block.id === over.id);
      const updatedBlocks = arrayMove(blocks, oldIndex, newIndex).map((block, index) => ({
        ...block,
        order: index,
      }));
      setBlocks(updatedBlocks);
      onBlocksChange(updatedBlocks);
    }
  }, [blocks, onBlocksChange]);

  return {
    blocks,
    setBlocks,
    editingBlock,
    setEditingBlock,
    editContent,
    setEditContent,
    editConfig,
    setEditConfig,
    unsavedChanges,
    setUnsavedChanges,
    showDiscardDialog,
    setShowDiscardDialog,
    addBlock,
    deleteBlock,
    startEditBlock,
    handleEditContentChange,
    handleSaveEdit,
    handleCancelEdit,
    confirmDiscard,
    onDragEndCallback,
  };
};
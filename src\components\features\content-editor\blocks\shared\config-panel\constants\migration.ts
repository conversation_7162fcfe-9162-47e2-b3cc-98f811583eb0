/**
 * Arquivo de migração para compatibilidade com a estrutura anterior
 * Este arquivo facilita a transição gradual para a nova estrutura organizada
 */

import { BlockConfig } from '@/types';
import { blockTypePresetsMap } from './block-types';
import {
  blockTypeDefaultIcons,
  blockTypeDefaultColors,
  defaultBlockConfig
} from './base-config';
import {
  coloredBlockPresets,
  COLORED_BLOCK_PALETTES
} from './block-types/colored/presets';
import {
  alertBlockPresets,
  ALERT_PRESETS,
  ALERT_ICONS
} from './block-types/alert/presets';

// Re-exportar estruturas antigas para compatibilidade
export { defaultBlockConfig } from './base-config';
export { blockTypeDefaultIcons, blockTypeDefaultColors } from './base-config';

// Manter a estrutura antiga de blockTypePresets para compatibilidade
export const blockTypePresets = {
  file: blockTypePresetsMap.file.default,
  video: blockTypePresetsMap.video.default,
  text: blockTypePresetsMap.text.default,
  quiz: blockTypePresetsMap.quiz.default,
  image: blockTypePresetsMap.image?.default,
};

// Manter coloredBlockPresets como estava
export { coloredBlockPresets };

// Manter COLORED_BLOCK_PALETTES como estava
export { COLORED_BLOCK_PALETTES };

// Exportar alertas da nova estrutura para compatibilidade
export { alertBlockPresets, ALERT_PRESETS, ALERT_ICONS };

// Função para converter preset antigo para BlockConfig (compatibilidade)
export function presetToBlockConfig(preset: any): BlockConfig {
  return {
    card: presetToBlockConfigCard(preset),
    icon: presetToBlockConfigIcon(preset),
    button: presetToBlockConfigButton(preset),
  };
}

export function presetToBlockConfigIcon(preset: any) {
  if (!preset?.icon) return defaultBlockConfig.icon;

  return {
    ...defaultBlockConfig.icon,
    enabled: preset.icon.enabled ?? defaultBlockConfig.icon?.enabled,
    position: preset.icon.position || defaultBlockConfig.icon?.position,
    type: preset.icon.type || defaultBlockConfig.icon?.type,
    iconName: preset.icon.iconName || preset.icon.name || defaultBlockConfig.icon?.iconName,
    appearance: {
      ...defaultBlockConfig.icon?.appearance,
      background: preset.icon.appearance?.background || preset.icon.backgroundColor || defaultBlockConfig.icon?.appearance?.background,
      color: preset.icon.appearance?.color || preset.icon.color || defaultBlockConfig.icon?.appearance?.color,
      format: preset.icon.appearance?.format || preset.icon.format || defaultBlockConfig.icon?.appearance?.format,
      size: preset.icon.appearance?.size || preset.icon.size || defaultBlockConfig.icon?.appearance?.size,
      border: {
        ...defaultBlockConfig.icon?.appearance?.border,
        enabled: preset.icon.appearance?.border?.enabled ?? preset.icon.border?.enabled ?? defaultBlockConfig.icon?.appearance?.border?.enabled,
        color: preset.icon.appearance?.border?.color || preset.icon.border?.color || defaultBlockConfig.icon?.appearance?.border?.color,
        width: preset.icon.appearance?.border?.width ?? preset.icon.border?.width ?? defaultBlockConfig.icon?.appearance?.border?.width,
      },
      shadow: {
        ...defaultBlockConfig.icon?.appearance?.shadow,
        enabled: preset.icon.appearance?.shadow?.enabled ?? preset.icon.shadow?.enabled ?? defaultBlockConfig.icon?.appearance?.shadow?.enabled,
        depth: preset.icon.appearance?.shadow?.depth ?? preset.icon.shadow?.depth ?? defaultBlockConfig.icon?.appearance?.shadow?.depth,
      },
      hover: {
        ...defaultBlockConfig.icon?.appearance?.hover,
        enabled: preset.icon.appearance?.hover?.enabled ?? preset.icon.hover?.enabled ?? defaultBlockConfig.icon?.appearance?.hover?.enabled,
        shadowDepth: preset.icon.appearance?.hover?.shadowDepth ?? preset.icon.hover?.shadowDepth ?? defaultBlockConfig.icon?.appearance?.hover?.shadowDepth,
      }
    }
  };
}

export function presetToBlockConfigCard(preset: any) {
  if (!preset?.card) return defaultBlockConfig.card;

  // Determinar se shadow está habilitado baseado na presença de shadow no preset
  const hasShadow = Boolean(preset.card.shadow);
  const shadowDepth = hasShadow ? 2 : defaultBlockConfig.card?.shadow?.depth;

  // Determinar se hover está habilitado baseado na presença de hover no preset
  const hasHover = Boolean(preset.card.hover);
  const hoverShadowDepth = hasHover ? 4 : defaultBlockConfig.card?.hover?.shadowDepth;

  return {
    ...defaultBlockConfig.card,
    backgroundColor: preset.card.backgroundColor || defaultBlockConfig.card?.backgroundColor,
    format: preset.card.format || defaultBlockConfig.card?.format,
    border: {
      ...defaultBlockConfig.card?.border,
      enabled: preset.card.border?.enabled ?? defaultBlockConfig.card?.border?.enabled,
      color: preset.card.border?.color || defaultBlockConfig.card?.border?.color,
      width: preset.card.border?.width ?? defaultBlockConfig.card?.border?.width,
    },
    shadow: {
      ...defaultBlockConfig.card?.shadow,
      enabled: hasShadow,
      depth: shadowDepth,
    },
    font: {
      ...defaultBlockConfig.card?.font,
      size: preset.card.font?.size || defaultBlockConfig.card?.font?.size,
      color: preset.card.font?.color || preset.card.color || defaultBlockConfig.card?.font?.color,
      style: preset.card.font?.style || defaultBlockConfig.card?.font?.style,
    },
    hover: {
      ...defaultBlockConfig.card?.hover,
      enabled: hasHover,
      shadowDepth: hoverShadowDepth,
    }
  };
}

export function presetToBlockConfigButton(preset: any) {
  if (!preset?.button) return defaultBlockConfig.button;

  return {
    ...defaultBlockConfig.button,
    backgroundColor: preset.button.backgroundColor || defaultBlockConfig.button?.backgroundColor,
    color: preset.button.color || defaultBlockConfig.button?.color,
    style: preset.button.style || preset.button.format || defaultBlockConfig.button?.style,
    size: preset.button.size || defaultBlockConfig.button?.size,
    position: preset.button.position || defaultBlockConfig.button?.position,
    border: {
      ...defaultBlockConfig.button?.border,
      enabled: preset.button.border?.enabled ?? defaultBlockConfig.button?.border?.enabled,
      color: preset.button.border?.color || defaultBlockConfig.button?.border?.color,
      width: preset.button.border?.width ?? defaultBlockConfig.button?.border?.width,
    },
    shadow: {
      ...defaultBlockConfig.button?.shadow,
      enabled: preset.button.shadow ? true : (defaultBlockConfig.button?.shadow?.enabled ?? false),
      depth: defaultBlockConfig.button?.shadow?.depth ?? 2,
    },
    hover: {
      ...defaultBlockConfig.button?.hover,
      enabled: preset.button.hover ? true : (defaultBlockConfig.button?.hover?.enabled ?? false),
      shadowDepth: defaultBlockConfig.button?.hover?.shadowDepth ?? 3,
    },
    text: preset.button.text || defaultBlockConfig.button?.text,
    url: preset.button.url || defaultBlockConfig.button?.url,
    newTab: preset.button.newTab ?? defaultBlockConfig.button?.newTab,
    icon: preset.button.icon?.name || preset.button.icon || defaultBlockConfig.button?.icon,
    iconPosition: preset.button.icon?.position || defaultBlockConfig.button?.iconPosition,
  };
}

// Função utilitária para obter preset por tipo de bloco e variante
export function getBlockPreset(blockType: string, variant: string = 'default') {
  const presets = blockTypePresetsMap[blockType as keyof typeof blockTypePresetsMap];
  if (!presets) return null;
  
  return presets[variant as keyof typeof presets] || presets.default || null;
}

// Função para obter configuração padrão por tipo de bloco
export function getDefaultConfigForBlockType(blockType: string): BlockConfig {
  const preset = getBlockPreset(blockType, 'default');
  if (preset) {
    return presetToBlockConfig(preset);
  }
  
  // Fallback para configuração padrão
  const config = { ...defaultBlockConfig };
  
  // Aplicar ícone padrão do tipo de bloco
  if (blockTypeDefaultIcons[blockType as keyof typeof blockTypeDefaultIcons]) {
    config.icon.name = blockTypeDefaultIcons[blockType as keyof typeof blockTypeDefaultIcons];
  }
  
  // Aplicar cores padrão do tipo de bloco
  if (blockTypeDefaultColors[blockType as keyof typeof blockTypeDefaultColors]) {
    const colors = blockTypeDefaultColors[blockType as keyof typeof blockTypeDefaultColors];
    config.card.backgroundColor = colors.background;
    config.card.font.color = colors.primary; // Corrigido: usar font.color
    config.card.border.color = colors.border;
    config.icon.appearance.background = colors.primary;
    config.button.backgroundColor = colors.primary;
  }
  
  return config;
}
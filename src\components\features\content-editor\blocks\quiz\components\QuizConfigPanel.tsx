import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Settings,
  Clock,
  Target,
  Shield,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  Timer,
  Shuffle,
  Eye,
  Info
} from 'lucide-react';

import { QuizConfig, QuizUtils } from '@/types/quiz';

interface QuizConfigPanelProps {
  config: QuizConfig;
  onChange: (config: QuizConfig) => void;
  type: 'correction' | 'validation' | 'general';
}

export const QuizConfigPanel: React.FC<QuizConfigPanelProps> = ({
  config,
  onChange,
  type
}) => {
  const handleConfigChange = (key: keyof QuizConfig, value: any) => {
    onChange({
      ...config,
      [key]: value
    });
  };

  const handleSurveySettingsChange = (key: string, value: any) => {
    const currentSettings = config.surveySettings || {
      showAggregatedResults: true,
      allowAnonymous: false,
      showOthersResponses: false,
      collectDemographics: false,
      showResultsAfterSubmission: true
    };

    handleConfigChange('surveySettings', {
      ...currentSettings,
      [key]: value
    });
  };

  if (type === 'general') {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 standard-title">
              <Settings className="w-5 h-5" />
              Configurações Gerais
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Informações Básicas */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="standard-label">Título</Label>
                <Input
                  value={config.title}
                  onChange={(e) => handleConfigChange('title', e.target.value)}
                  placeholder="Título do quiz"
                  className="standard-field"
                />
              </div>

              <div className="space-y-2">
                <Label className="standard-label">Descrição</Label>
                <Textarea
                  value={config.description || ''}
                  onChange={(e) => handleConfigChange('description', e.target.value)}
                  placeholder="Descrição do quiz"
                  className="standard-field"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label className="standard-label">Instruções</Label>
                <Textarea
                  value={config.instructions || ''}
                  onChange={(e) => handleConfigChange('instructions', e.target.value)}
                  placeholder="Instruções para os participantes"
                  className="standard-field"
                  rows={3}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Modo do Quiz */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 standard-title">
              <Target className="w-5 h-5" />
              Modo do Quiz
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Label className="standard-label">Tipo de Quiz</Label>
              <div className="grid grid-cols-1 gap-3">
                <div
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    QuizUtils.isAssessmentMode(config)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleConfigChange('mode', 'assessment')}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      QuizUtils.isAssessmentMode(config)
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}>
                      {QuizUtils.isAssessmentMode(config) && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                      )}
                    </div>
                    <div>
                      <div className="font-medium">Avaliação (Assessment)</div>
                      <div className="text-sm text-gray-600">
                        Quiz tradicional com correção automática, nota mínima e aprovação/reprovação
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    QuizUtils.isSurveyMode(config)
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleConfigChange('mode', 'survey')}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      QuizUtils.isSurveyMode(config)
                        ? 'border-green-500 bg-green-500'
                        : 'border-gray-300'
                    }`}>
                      {QuizUtils.isSurveyMode(config) && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                      )}
                    </div>
                    <div>
                      <div className="font-medium">Pesquisa de Opinião (Survey)</div>
                      <div className="text-sm text-gray-600">
                        Coleta de opiniões sem correção automática, focado em estatísticas agregadas
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </CardContent>
        </Card>
      </div>
    );
  }

  if (type === 'correction') {
    // Painel de correção apenas para modo assessment
    if (QuizUtils.isSurveyMode(config)) {
      return (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-600">
                <BarChart3 className="w-5 h-5" />
                Modo Pesquisa
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 text-blue-700 mb-2">
                  <Info className="w-4 h-4" />
                  <span className="font-medium">Correção Não Aplicável</span>
                </div>
                <p className="text-sm text-blue-600">
                  Em modo pesquisa, não há correção automática. Todas as respostas são válidas
                  e coletadas para análise estatística. Configure as opções específicas de pesquisa na aba "Validação".
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 standard-title">
              <Target className="w-5 h-5" />
              Configurações de Correção
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Pontuação */}
            <div className="space-y-3">
              <Label className="standard-label">Nota Mínima para Aprovação (%)</Label>
              <div className="space-y-2">
                <Slider
                  value={[config.passingScore]}
                  onValueChange={([value]) => handleConfigChange('passingScore', value)}
                  max={100}
                  min={0}
                  step={5}
                  className="w-full"
                />
                <div className="text-sm text-gray-600 text-center">
                  {config.passingScore}% - {config.passingScore >= 70 ? 'Rigoroso' : config.passingScore >= 50 ? 'Moderado' : 'Flexível'}
                </div>
              </div>
            </div>

            {/* Tentativas */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="standard-label">Máximo de Tentativas</Label>
                <Select 
                  value={config.maxAttempts.toString()} 
                  onValueChange={(value) => handleConfigChange('maxAttempts', parseInt(value))}
                >
                  <SelectTrigger className="standard-field">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 tentativa</SelectItem>
                    <SelectItem value="2">2 tentativas</SelectItem>
                    <SelectItem value="3">3 tentativas</SelectItem>
                    <SelectItem value="5">5 tentativas</SelectItem>
                    <SelectItem value="-1">Ilimitado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Switch
                  id="allow-retry"
                  checked={config.allowRetry}
                  onCheckedChange={(checked) => handleConfigChange('allowRetry', checked)}
                />
                <Label htmlFor="allow-retry" className="standard-label">
                  Permitir nova tentativa após falha
                </Label>
              </div>
            </div>

            {/* Feedback */}
            <div className="space-y-3">
              <Label className="standard-label">Opções de Feedback</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-score"
                    checked={config.showScore}
                    onCheckedChange={(checked) => handleConfigChange('showScore', checked)}
                  />
                  <Label htmlFor="show-score" className="standard-label">
                    Mostrar pontuação final
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-correct"
                    checked={config.showCorrectAnswers}
                    onCheckedChange={(checked) => handleConfigChange('showCorrectAnswers', checked)}
                  />
                  <Label htmlFor="show-correct" className="standard-label">
                    Mostrar respostas corretas
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-feedback"
                    checked={config.showFeedback}
                    onCheckedChange={(checked) => handleConfigChange('showFeedback', checked)}
                  />
                  <Label htmlFor="show-feedback" className="standard-label">
                    Mostrar feedback das perguntas
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-detailed-results"
                    checked={config.showDetailedResults}
                    onCheckedChange={(checked) => handleConfigChange('showDetailedResults', checked)}
                  />
                  <Label htmlFor="show-detailed-results" className="standard-label">
                    Mostrar resultado detalhado
                  </Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (type === 'validation') {
    return (
      <div className="space-y-6">
        {/* Configurações específicas para Survey */}
        {QuizUtils.isSurveyMode(config) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-600">
                <BarChart3 className="w-5 h-5" />
                Configurações de Pesquisa
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200 mb-4">
                <div className="flex items-center gap-2 text-blue-700 mb-2">
                  <Info className="w-4 h-4" />
                  <span className="font-medium">Modo Pesquisa Ativo</span>
                </div>
                <p className="text-sm text-blue-600">
                  Em modo pesquisa, não há respostas certas ou erradas. O foco é coletar opiniões e estatísticas.
                  As configurações abaixo são específicas para pesquisas de opinião.
                </p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-aggregated-results"
                    checked={config.surveySettings?.showAggregatedResults ?? true}
                    onCheckedChange={(checked) => handleSurveySettingsChange('showAggregatedResults', checked)}
                  />
                  <Label htmlFor="show-aggregated-results" className="standard-label">
                    Mostrar resultados agregados
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="allow-anonymous"
                    checked={config.surveySettings?.allowAnonymous ?? false}
                    onCheckedChange={(checked) => handleSurveySettingsChange('allowAnonymous', checked)}
                  />
                  <Label htmlFor="allow-anonymous" className="standard-label">
                    Permitir respostas anônimas
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-others-responses"
                    checked={config.surveySettings?.showOthersResponses ?? false}
                    onCheckedChange={(checked) => handleSurveySettingsChange('showOthersResponses', checked)}
                  />
                  <Label htmlFor="show-others-responses" className="standard-label">
                    Mostrar respostas de outros usuários
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-results-after-submission"
                    checked={config.surveySettings?.showResultsAfterSubmission ?? true}
                    onCheckedChange={(checked) => handleSurveySettingsChange('showResultsAfterSubmission', checked)}
                  />
                  <Label htmlFor="show-results-after-submission" className="standard-label">
                    Mostrar resultados após envio
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Configurações específicas para Assessment */}
        {QuizUtils.isAssessmentMode(config) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 standard-title">
                <Shield className="w-5 h-5" />
                Configurações de Validação
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">

              {/* Requisitos para Assessment */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is-required"
                    checked={config.isRequired}
                    onCheckedChange={(checked) => handleConfigChange('isRequired', checked)}
                  />
                  <Label htmlFor="is-required" className="standard-label">
                    Quiz obrigatório
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="block-progress"
                    checked={config.blockProgressUntilPassed}
                    onCheckedChange={(checked) => handleConfigChange('blockProgressUntilPassed', checked)}
                    disabled={!config.isRequired}
                  />
                  <Label htmlFor="block-progress" className="standard-label">
                    Bloquear progresso até aprovação
                  </Label>
                </div>
              </div>

              {config.isRequired && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-600" />
                    <span className="font-medium text-yellow-800">Quiz Obrigatório</span>
                  </div>
                  <p className="text-sm text-yellow-700">
                    {config.blockProgressUntilPassed
                      ? 'Os usuários precisam atingir a nota mínima para continuar.'
                      : 'Os usuários devem completar o quiz, mas podem continuar independente da nota.'
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Configurações comuns para ambos os modos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 standard-title">
              <Timer className="w-5 h-5" />
              Configurações de Tempo
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-timer"
                  checked={config.showTimer}
                  onCheckedChange={(checked) => handleConfigChange('showTimer', checked)}
                />
                <Label htmlFor="show-timer" className="standard-label">
                  Mostrar cronômetro
                </Label>
              </div>

              {config.showTimer && (
                <div className="space-y-2">
                  <Label className="standard-label">Tempo Limite (minutos)</Label>
                  <Input
                    type="number"
                    value={config.timeLimit ? Math.floor(config.timeLimit / 60) : ''}
                    onChange={(e) => {
                      const minutes = parseInt(e.target.value) || 0;
                      handleConfigChange('timeLimit', minutes > 0 ? minutes * 60 : undefined);
                    }}
                    placeholder="Sem limite"
                    className="standard-field"
                    min="1"
                    max="180"
                  />
                  <p className="text-xs text-gray-500">
                    Deixe vazio para sem limite de tempo
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Interface - Comuns para ambos os modos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 standard-title">
              <Eye className="w-5 h-5" />
              Configurações de Interface
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="shuffle-questions"
                  checked={config.shuffleQuestions}
                  onCheckedChange={(checked) => handleConfigChange('shuffleQuestions', checked)}
                />
                <Label htmlFor="shuffle-questions" className="standard-label">
                  Embaralhar perguntas
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="shuffle-options"
                  checked={config.shuffleOptions}
                  onCheckedChange={(checked) => handleConfigChange('shuffleOptions', checked)}
                />
                <Label htmlFor="shuffle-options" className="standard-label">
                  Embaralhar opções
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="show-progress"
                  checked={config.showProgressBar}
                  onCheckedChange={(checked) => handleConfigChange('showProgressBar', checked)}
                />
                <Label htmlFor="show-progress" className="standard-label">
                  Mostrar barra de progresso
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="allow-draft"
                  checked={config.allowSaveDraft}
                  onCheckedChange={(checked) => handleConfigChange('allowSaveDraft', checked)}
                />
                <Label htmlFor="allow-draft" className="standard-label">
                  Permitir salvar rascunho
                </Label>
              </div>
            </div>

            {/* Configurações específicas para Survey */}
            {QuizUtils.isSurveyMode(config) && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 text-green-700 mb-2">
                  <CheckCircle className="w-4 h-4" />
                  <span className="font-medium">Configurações Automáticas para Pesquisa</span>
                </div>
                <div className="text-sm text-green-600 space-y-1">
                  <p>• Quiz sempre obrigatório, mas nunca bloqueia progresso</p>
                  <p>• Tentativas ilimitadas permitidas</p>
                  <p>• Todas as respostas são consideradas corretas</p>
                  <p>• Foco em coleta de dados, não em avaliação</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Configurações gerais
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 standard-title">
            <Settings className="w-5 h-5" />
            Configurações Gerais
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="standard-label">Título do Quiz</Label>
            <Input
              value={config.title}
              onChange={(e) => handleConfigChange('title', e.target.value)}
              placeholder="Digite o título do quiz"
              className="standard-field"
            />
          </div>

          <div className="space-y-2">
            <Label className="standard-label">Descrição</Label>
            <Textarea
              value={config.description || ''}
              onChange={(e) => handleConfigChange('description', e.target.value)}
              placeholder="Descrição opcional do quiz"
              className="standard-field"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label className="standard-label">Instruções</Label>
            <Textarea
              value={config.instructions || ''}
              onChange={(e) => handleConfigChange('instructions', e.target.value)}
              placeholder="Instruções para os participantes"
              className="standard-field"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

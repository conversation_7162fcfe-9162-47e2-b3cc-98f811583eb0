import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  <PERSON><PERSON>oint<PERSON>, 
  RotateCcw,
  Info,
  AlertTriangle
} from 'lucide-react';

import { BlockConfig } from '@/types';
import { ColorPicker } from '../../shared/ColorPicker';

interface QuizButtonConfigProps {
  config: BlockConfig;
  onChange: (config: BlockConfig) => void;
  quizMode?: 'assessment' | 'survey';
}

export const QuizButtonConfig: React.FC<QuizButtonConfigProps> = ({
  config,
  onChange,
  quizMode = 'assessment'
}) => {
  const handleButtonChange = (field: string, value: any) => {
    onC<PERSON><PERSON>({
      ...config,
      button: {
        ...config.button,
        [field]: value
      }
    });
  };

  const resetToDefault = () => {
    const defaultColor = quizMode === 'survey' ? '#10b981' : '#3b82f6';
    
    onChange({
      ...config,
      button: {
        backgroundColor: defaultColor,
        color: '#ffffff',
        format: 'rounded',
        size: 'medium',
        border: { enabled: false, color: defaultColor, width: 1 },
        shadow: { enabled: true, depth: 2 },
        hover: {
          backgroundColor: quizMode === 'survey' ? '#059669' : '#2563eb',
          color: '#ffffff'
        }
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <MousePointer className="w-5 h-5" />
          Botões do Quiz
        </h3>
        <Button variant="outline" size="sm" onClick={resetToDefault}>
          <RotateCcw className="w-4 h-4 mr-2" />
          Restaurar Padrão
        </Button>
      </div>

      {/* Aviso sobre funcionalidade limitada */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-4">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">Configurações Limitadas para Quiz</p>
              <p>
                Os botões do quiz são gerados automaticamente pelo sistema. 
                Estas configurações afetam apenas a aparência visual, não a funcionalidade.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Estilo dos Botões</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Cor de Fundo</Label>
              <ColorPicker
                value={config.button?.backgroundColor || '#3b82f6'}
                onChange={(color) => handleButtonChange('backgroundColor', color)}
              />
            </div>
            <div>
              <Label className="text-sm font-medium">Cor do Texto</Label>
              <ColorPicker
                value={config.button?.color || '#ffffff'}
                onChange={(color) => handleButtonChange('color', color)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Formato</Label>
              <Select
                value={config.button?.format || 'rounded'}
                onValueChange={(value) => handleButtonChange('format', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rounded">Arredondado</SelectItem>
                  <SelectItem value="square">Quadrado</SelectItem>
                  <SelectItem value="pill">Pílula</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-sm font-medium">Tamanho</Label>
              <Select
                value={config.button?.size || 'medium'}
                onValueChange={(value) => handleButtonChange('size', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">Pequeno</SelectItem>
                  <SelectItem value="medium">Médio</SelectItem>
                  <SelectItem value="large">Grande</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={config.button?.border?.enabled || false}
              onCheckedChange={(enabled) => 
                handleButtonChange('border', { ...config.button?.border, enabled })
              }
            />
            <Label className="text-sm">Mostrar borda</Label>
          </div>

          {config.button?.border?.enabled && (
            <div>
              <Label className="text-sm font-medium">Cor da Borda</Label>
              <ColorPicker
                value={config.button?.border?.color || '#e5e5e5'}
                onChange={(color) => 
                  handleButtonChange('border', { ...config.button?.border, color })
                }
              />
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Switch
              checked={config.button?.shadow?.enabled !== false}
              onCheckedChange={(enabled) => 
                handleButtonChange('shadow', { ...config.button?.shadow, enabled })
              }
            />
            <Label className="text-sm">Sombra</Label>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Efeitos Hover</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Cor de Fundo (Hover)</Label>
              <ColorPicker
                value={config.button?.hover?.backgroundColor || '#2563eb'}
                onChange={(color) => 
                  handleButtonChange('hover', { 
                    ...config.button?.hover, 
                    backgroundColor: color 
                  })
                }
              />
            </div>
            <div>
              <Label className="text-sm font-medium">Cor do Texto (Hover)</Label>
              <ColorPicker
                value={config.button?.hover?.color || '#ffffff'}
                onChange={(color) => 
                  handleButtonChange('hover', { 
                    ...config.button?.hover, 
                    color: color 
                  })
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botões específicos do Quiz */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600" />
            Botões Automáticos do Quiz
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>Botões gerados automaticamente:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li><strong>Iniciar Quiz:</strong> Aparece antes do início</li>
              <li><strong>Próxima Pergunta:</strong> Durante a execução</li>
              <li><strong>Finalizar Quiz:</strong> Na última pergunta</li>
              {quizMode === 'assessment' && (
                <li><strong>Ver Resultado Detalhado:</strong> Após conclusão</li>
              )}
              {quizMode === 'survey' && (
                <li><strong>Ver Resultados da Pesquisa:</strong> Após conclusão</li>
              )}
            </ul>
            <p className="mt-3 text-xs">
              As configurações acima se aplicam a todos esses botões automaticamente.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React from 'react';

interface BlockCardBaseProps {
  icon?: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  children?: React.ReactNode;
}

// TODO: Se o componente crescer, dividir em subcomponentes auxiliares
export const BlockCardBase: React.FC<BlockCardBaseProps> = ({ icon, title, description, actions, children }) => {
  return (
    <div className="rounded-lg border border-gray-200 bg-white shadow-sm p-4 flex flex-col gap-2">
      <div className="flex items-center gap-3 mb-2">
        {icon && <span className="text-2xl">{icon}</span>}
        <div className="flex-1">
          {title && <h3 className="font-bold text-lg leading-tight">{title}</h3>}
          {description && <p className="text-gray-500 text-sm leading-tight">{description}</p>}
        </div>
        {actions && <div className="ml-auto">{actions}</div>}
      </div>
      <div>{children}</div>
    </div>
  );
}; 
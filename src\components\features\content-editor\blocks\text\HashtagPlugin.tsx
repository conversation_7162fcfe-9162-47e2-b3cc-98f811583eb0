import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { TextNode } from 'lexical';
import { useEffect } from 'react';

// Regex para identificar hashtags
const HASHTAG_REGEX = /#[\w\u00C0-\u00FF]+/g;

// Classe para o nó de hashtag
export class HashtagNode extends TextNode {
  static getType(): string {
    return 'hashtag';
  }

  static clone(node: HashtagNode): HashtagNode {
    return new HashtagNode(node.__text, node.__key);
  }

  constructor(text: string, key?: string) {
    super(text, key);
  }

  createDOM(config: any): HTMLElement {
    const dom = super.createDOM(config);
    dom.style.color = 'var(--color-primary)';
    dom.style.fontWeight = 'bold';
    return dom;
  }

  updateDOM(prevNode: HashtagNode, dom: HTMLElement, config: any): boolean {
    const isUpdated = super.updateDOM(prevNode, dom, config);
    return isUpdated;
  }

  static exportJSON(node: HashtagNode) {
    return {
      type: 'hashtag',
      version: 1,
      text: node.__text,
      format: node.__format,
      detail: node.__detail,
      mode: node.__mode,
      style: node.__style,
      // Inclua outros campos relevantes se necessário
    };
  }

  static importJSON(serializedNode: any) {
    const node = new HashtagNode(serializedNode.text);
    node.setFormat(serializedNode.format);
    node.setDetail(serializedNode.detail);
    node.setMode(serializedNode.mode);
    node.setStyle(serializedNode.style || '');
    return node;
  }
}

// Função para transformar texto em hashtag
function textNodeTransform(node: TextNode) {
  const textContent = node.getTextContent();
  if (textContent.match(HASHTAG_REGEX)) {
    // Se o texto contém hashtags, vamos processá-lo
    const matches = textContent.matchAll(HASHTAG_REGEX);
    const nodes = [];
    let lastIndex = 0;

    for (const match of matches) {
      const matchIndex = match.index || 0;
      const beforeText = textContent.slice(lastIndex, matchIndex);
      if (beforeText) {
        nodes.push(new TextNode(beforeText));
      }

      const hashtagText = match[0];
      nodes.push(new HashtagNode(hashtagText));
      lastIndex = matchIndex + hashtagText.length;
    }

    // Adiciona qualquer texto restante após a última hashtag
    if (lastIndex < textContent.length) {
      nodes.push(new TextNode(textContent.slice(lastIndex)));
    }

    // Substitui o nó atual pelos novos nós
    if (nodes.length) {
      node.replace(nodes[0]);
      let currentNode = nodes[0];
      for (let i = 1; i < nodes.length; i++) {
        currentNode.insertAfter(nodes[i]);
        currentNode = nodes[i];
      }
    }
  }
}

const HashtagPlugin = () => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // Registra o transformador de texto
    return editor.registerNodeTransform(TextNode, textNodeTransform);
  }, [editor]);

  return null;
};

export default HashtagPlugin;
// iconUtils.ts
// Funções utilitárias de ícone centralizadas para todos os blocos do editor

import * as LucideIcons from 'lucide-react';
import React from 'react';

/**
 * Obtém componente de ícone Lucide pelo nome
 */
export function getLucideIconComponent(iconName: string): React.ComponentType | null {
  if (!iconName) return null;

  const IconComponent = LucideIcons[iconName as keyof typeof LucideIcons];
  return IconComponent && typeof IconComponent === 'function' ? IconComponent : null;
}

/**
 * Verifica se o nome corresponde a um ícone Lucide válido
 */
export function isLucideIcon(iconName: string | React.ComponentType): boolean {
  if (typeof iconName === 'string') {
    return iconName in LucideIcons;
  }

  if (typeof iconName === 'function') {
    // Verifica se é um componente válido do Lucide
    return Object.values(LucideIcons).includes(iconName as any);
  }

  return false;
}

/**
 * Retorna ícone padrão (Info) como fallback
 */
export function getFallbackIcon(): React.ComponentType {
  return LucideIcons.Info;
}

/**
 * Lista todos os nomes de ícones Lucide disponíveis
 */
export function getAllLucideIconNames(): string[] {
  return Object.keys(LucideIcons).filter(key =>
    typeof LucideIcons[key as keyof typeof LucideIcons] === 'function'
  );
}

/**
 * Busca ícones por termo de pesquisa
 */
export function searchLucideIcons(searchTerm: string): string[] {
  const allIcons = getAllLucideIconNames();
  if (!searchTerm) return allIcons;

  const term = searchTerm.toLowerCase();
  return allIcons.filter(iconName =>
    iconName.toLowerCase().includes(term)
  );
}
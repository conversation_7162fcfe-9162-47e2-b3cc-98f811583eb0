-- =====================================================
-- REMOÇÃO COMPLETA DE TODAS AS POLÍTICAS RLS
-- =====================================================
-- Remove todas as políticas RLS e desabilita RLS em todas as tabelas
-- ATENÇÃO: Este script remove TODA a segurança RLS do sistema
-- Execute apenas em ambiente de desenvolvimento/teste
-- Versão: 1.0 - Julho 2025

-- =====================================================
-- BACKUP DAS POLÍTICAS ATUAIS
-- =====================================================

-- <PERSON><PERSON>r backup completo de todas as políticas antes da remoção
CREATE TABLE IF NOT EXISTS backup_all_policies_before_removal AS
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check,
    now() as backup_timestamp,
    'BACKUP_ANTES_REMOCAO_COMPLETA' as tipo_backup
FROM pg_policies 
WHERE schemaname = 'public';

-- Log do backup
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count FROM backup_all_policies_before_removal;
    RAISE NOTICE '📦 BACKUP: % políticas salvas em backup_all_policies_before_removal', policy_count;
END $$;

-- =====================================================
-- REMOÇÃO DE TODAS AS POLÍTICAS RLS
-- =====================================================

-- TABELA: profiles
DROP POLICY IF EXISTS "profiles_basic" ON public.profiles;
DROP POLICY IF EXISTS "profiles_collaboration" ON public.profiles;
DROP POLICY IF EXISTS "profiles_autocomplete_friendly" ON public.profiles;
DROP POLICY IF EXISTS "profiles_simple_autocomplete" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete" ON public.profiles;

-- TABELA: projects
DROP POLICY IF EXISTS "projects_basic" ON public.projects;
DROP POLICY IF EXISTS "projects_collaborative_access" ON public.projects;
DROP POLICY IF EXISTS "projects_ownership_insert" ON public.projects;
DROP POLICY IF EXISTS "projects_ownership_update" ON public.projects;
DROP POLICY IF EXISTS "projects_ownership_delete" ON public.projects;

-- TABELA: project_members
DROP POLICY IF EXISTS "project_members_basic" ON public.project_members;
DROP POLICY IF EXISTS "project_members_management" ON public.project_members;

-- TABELA: stages
DROP POLICY IF EXISTS "stages_basic" ON public.stages;
DROP POLICY IF EXISTS "stages_project_access" ON public.stages;
DROP POLICY IF EXISTS "stages_insert" ON public.stages;
DROP POLICY IF EXISTS "stages_update" ON public.stages;
DROP POLICY IF EXISTS "stages_delete" ON public.stages;

-- TABELA: tasks
DROP POLICY IF EXISTS "tasks_basic" ON public.tasks;
DROP POLICY IF EXISTS "tasks_access_control" ON public.tasks;
DROP POLICY IF EXISTS "tasks_insert" ON public.tasks;
DROP POLICY IF EXISTS "tasks_update" ON public.tasks;
DROP POLICY IF EXISTS "tasks_delete" ON public.tasks;

-- TABELA: task_executors
DROP POLICY IF EXISTS "task_executors_basic" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_access" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_insert" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_update" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_delete" ON public.task_executors;

-- TABELA: task_approvers
DROP POLICY IF EXISTS "task_approvers_basic" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_access" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_insert" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_update" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_delete" ON public.task_approvers;

-- TABELA: task_content_blocks
DROP POLICY IF EXISTS "task_content_blocks_basic" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_access" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_insert" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_update" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_delete" ON public.task_content_blocks;

-- TABELA: task_comments
DROP POLICY IF EXISTS "task_comments_basic" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_access" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_insert" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_update" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_delete" ON public.task_comments;

-- TABELA: task_attachments (se existir)
DROP POLICY IF EXISTS "task_attachments_basic" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_access" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_insert" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_update" ON public.task_attachments;
DROP POLICY IF EXISTS "task_attachments_delete" ON public.task_attachments;

-- TABELA: evidence
DROP POLICY IF EXISTS "evidence_basic" ON public.evidence;
DROP POLICY IF EXISTS "evidence_access" ON public.evidence;
DROP POLICY IF EXISTS "evidence_privacy_protection" ON public.evidence;
DROP POLICY IF EXISTS "evidence_upload_debug" ON public.evidence;
DROP POLICY IF EXISTS "evidence_insert" ON public.evidence;
DROP POLICY IF EXISTS "evidence_update" ON public.evidence;
DROP POLICY IF EXISTS "evidence_delete" ON public.evidence;

-- TABELA: stage_responsibles
DROP POLICY IF EXISTS "stage_responsibles_basic" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_access" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_insert" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_update" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_delete" ON public.stage_responsibles;

-- TABELA: quizzes (se existir)
DROP POLICY IF EXISTS "quizzes_basic" ON public.quizzes;
DROP POLICY IF EXISTS "quizzes_access" ON public.quizzes;
DROP POLICY IF EXISTS "quizzes_insert" ON public.quizzes;
DROP POLICY IF EXISTS "quizzes_update" ON public.quizzes;
DROP POLICY IF EXISTS "quizzes_delete" ON public.quizzes;

-- TABELA: quiz_attempts (se existir)
DROP POLICY IF EXISTS "quiz_attempts_basic" ON public.quiz_attempts;
DROP POLICY IF EXISTS "quiz_attempts_access" ON public.quiz_attempts;
DROP POLICY IF EXISTS "quiz_attempts_insert" ON public.quiz_attempts;
DROP POLICY IF EXISTS "quiz_attempts_update" ON public.quiz_attempts;
DROP POLICY IF EXISTS "quiz_attempts_delete" ON public.quiz_attempts;

-- TABELA: quiz_answers (se existir)
DROP POLICY IF EXISTS "quiz_answers_basic" ON public.quiz_answers;
DROP POLICY IF EXISTS "quiz_answers_access" ON public.quiz_answers;
DROP POLICY IF EXISTS "quiz_answers_insert" ON public.quiz_answers;
DROP POLICY IF EXISTS "quiz_answers_update" ON public.quiz_answers;
DROP POLICY IF EXISTS "quiz_answers_delete" ON public.quiz_answers;

-- TABELA: user_quiz_progress (se existir)
DROP POLICY IF EXISTS "user_quiz_progress_basic" ON public.user_quiz_progress;
DROP POLICY IF EXISTS "user_quiz_progress_access" ON public.user_quiz_progress;
DROP POLICY IF EXISTS "user_quiz_progress_insert" ON public.user_quiz_progress;
DROP POLICY IF EXISTS "user_quiz_progress_update" ON public.user_quiz_progress;
DROP POLICY IF EXISTS "user_quiz_progress_delete" ON public.user_quiz_progress;

-- TABELA: user_notifications (se existir)
DROP POLICY IF EXISTS "user_notifications_basic" ON public.user_notifications;
DROP POLICY IF EXISTS "user_notifications_access" ON public.user_notifications;
DROP POLICY IF EXISTS "user_notifications_insert" ON public.user_notifications;
DROP POLICY IF EXISTS "user_notifications_update" ON public.user_notifications;
DROP POLICY IF EXISTS "user_notifications_delete" ON public.user_notifications;

-- TABELA: project_history (se existir)
DROP POLICY IF EXISTS "project_history_basic" ON public.project_history;
DROP POLICY IF EXISTS "project_history_access" ON public.project_history;
DROP POLICY IF EXISTS "project_history_insert" ON public.project_history;
DROP POLICY IF EXISTS "project_history_update" ON public.project_history;
DROP POLICY IF EXISTS "project_history_delete" ON public.project_history;

-- =====================================================
-- DESABILITAR RLS EM TODAS AS TABELAS
-- =====================================================

-- Principais
ALTER TABLE IF EXISTS public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.project_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.stages DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.stage_responsibles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.tasks DISABLE ROW LEVEL SECURITY;

-- Relacionadas a tarefas
ALTER TABLE IF EXISTS public.task_executors DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_approvers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_content_blocks DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_attachments DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.evidence DISABLE ROW LEVEL SECURITY;

-- Quiz/Avaliação
ALTER TABLE IF EXISTS public.quizzes DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.quiz_attempts DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.quiz_answers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.user_quiz_progress DISABLE ROW LEVEL SECURITY;

-- Auxiliares
ALTER TABLE IF EXISTS public.user_notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.project_history DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- VERIFICAÇÃO DA LIMPEZA
-- =====================================================

-- Verificar se ainda restam políticas
DO $$
DECLARE
    remaining_policies INTEGER;
    policy_record RECORD;
BEGIN
    SELECT COUNT(*) INTO remaining_policies 
    FROM pg_policies 
    WHERE schemaname = 'public';
    
    IF remaining_policies > 0 THEN
        RAISE NOTICE '⚠️  ATENÇÃO: Ainda existem % políticas restantes:', remaining_policies;
        
        FOR policy_record IN 
            SELECT tablename, policyname 
            FROM pg_policies 
            WHERE schemaname = 'public'
            ORDER BY tablename, policyname
        LOOP
            RAISE NOTICE '  - %.%', policy_record.tablename, policy_record.policyname;
        END LOOP;
    ELSE
        RAISE NOTICE '✅ SUCESSO: Todas as políticas RLS foram removidas';
    END IF;
END $$;

-- Verificar quais tabelas ainda têm RLS habilitado
DO $$
DECLARE
    table_record RECORD;
    rls_enabled_count INTEGER := 0;
BEGIN
    RAISE NOTICE '📋 STATUS RLS POR TABELA:';
    
    FOR table_record IN 
        SELECT 
            t.table_name,
            CASE 
                WHEN c.relrowsecurity THEN 'HABILITADO'
                ELSE 'DESABILITADO'
            END as rls_status
        FROM information_schema.tables t
        LEFT JOIN pg_class c ON c.relname = t.table_name
        WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        AND t.table_name NOT LIKE 'backup_%'
        ORDER BY t.table_name
    LOOP
        RAISE NOTICE '  - %: %', table_record.table_name, table_record.rls_status;
        
        IF table_record.rls_status = 'HABILITADO' THEN
            rls_enabled_count := rls_enabled_count + 1;
        END IF;
    END LOOP;
    
    IF rls_enabled_count = 0 THEN
        RAISE NOTICE '✅ SUCESSO: RLS desabilitado em todas as tabelas';
    ELSE
        RAISE NOTICE '⚠️  ATENÇÃO: % tabelas ainda têm RLS habilitado', rls_enabled_count;
    END IF;
END $$;

-- =====================================================
-- TESTE BÁSICO DE FUNCIONALIDADE
-- =====================================================

-- Testar acesso básico às tabelas principais
DO $$
DECLARE
    profiles_count INTEGER;
    projects_count INTEGER;
    tasks_count INTEGER;
BEGIN
    RAISE NOTICE '🧪 TESTANDO ACESSO BÁSICO ÀS TABELAS...';
    
    -- Testar profiles
    SELECT COUNT(*) INTO profiles_count FROM public.profiles;
    RAISE NOTICE '  - profiles: % registros acessíveis', profiles_count;
    
    -- Testar projects
    SELECT COUNT(*) INTO projects_count FROM public.projects;
    RAISE NOTICE '  - projects: % registros acessíveis', projects_count;
    
    -- Testar tasks
    SELECT COUNT(*) INTO tasks_count FROM public.tasks;
    RAISE NOTICE '  - tasks: % registros acessíveis', tasks_count;
    
    RAISE NOTICE '✅ TESTE CONCLUÍDO: Acesso básico funcionando';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ ERRO NO TESTE: %', SQLERRM;
END $$;

-- =====================================================
-- RELATÓRIO FINAL
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎯 ==========================================';
    RAISE NOTICE '🎯 LIMPEZA COMPLETA DE RLS CONCLUÍDA';
    RAISE NOTICE '🎯 ==========================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Todas as políticas RLS foram removidas';
    RAISE NOTICE '✅ RLS desabilitado em todas as tabelas';
    RAISE NOTICE '✅ Backup salvo em backup_all_policies_before_removal';
    RAISE NOTICE '✅ Sistema funcionando sem restrições RLS';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  ATENÇÃO: Sistema sem segurança RLS!';
    RAISE NOTICE '📋 Próximo passo: Implementar nova arquitetura';
    RAISE NOTICE '';
END $$;

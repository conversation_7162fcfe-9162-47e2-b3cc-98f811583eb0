import React from 'react';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { BlockConfig, VideoBlockContent, defaultBlockConfig } from '@/types';
import * as LucideIcons from 'lucide-react';
import { BlockCard } from '@/components/ui/BlockCard';
import '../shared/responsive-button.css';

interface VideoBlockCardProps {
  content: VideoBlockContent;
  config?: BlockConfig;
  className?: string;
  truncateText?: boolean;
  onWatch?: (url: string) => void;
}

export const VideoBlockCard: React.FC<VideoBlockCardProps> = ({ content, config, className, truncateText = false, onWatch }) => {
  const safeConfig = config || defaultBlockConfig;
  const cardBgColor = safeConfig.card?.backgroundColor || '#f5f3ff';
  const cardTextColor = safeConfig.card?.font?.color || '#312e81';
  const cardFormat = safeConfig.card?.format || 'rounded';
  const iconPosition = safeConfig.icon?.position || 'left-title';

  // Configurações de borda
  const cardBorder = safeConfig.card?.border?.enabled;
  const cardBorderColor = safeConfig.card?.border?.color || '#e5e5e5';
  const cardBorderWidth = safeConfig.card?.border?.width || 1;

  // Configurações de sombra
  const cardShadow = safeConfig.card?.shadow?.enabled;
  const cardShadowDepth = safeConfig.card?.shadow?.depth || 2;

  // Configurações de hover
  const cardHover = safeConfig.card?.hover?.enabled;
  const cardHoverShadowDepth = safeConfig.card?.hover?.shadowDepth || 4;

  // Função para gerar box-shadow baseado na profundidade
  const getBoxShadow = (depth: number) => {
    const shadows = {
      0: 'none',
      1: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
      2: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
      3: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
      4: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)'
    };
    return shadows[depth as keyof typeof shadows] || shadows[2];
  };

  // Estilos do card
  const cardStyles: React.CSSProperties = {
    background: cardBgColor,
    color: cardTextColor,
    borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
    border: cardBorder ? `${cardBorderWidth}px solid ${cardBorderColor}` : 'none',
    boxShadow: cardShadow ? getBoxShadow(cardShadowDepth) : 'none',
    transition: cardHover ? 'box-shadow 0.3s ease, transform 0.2s ease' : 'none',
    cursor: cardHover ? 'pointer' : 'default'
  };

  // Handlers de hover
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = getBoxShadow(cardHoverShadowDepth);
      e.currentTarget.style.transform = 'translateY(-2px)';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = cardShadow ? getBoxShadow(cardShadowDepth) : 'none';
      e.currentTarget.style.transform = 'translateY(0)';
    }
  };

  // Player de vídeo
  let player = null;
  if (content.showInline) {
    if (content.url && /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\//.test(content.url)) {
      // YouTube embed
      const match = content.url.match(/[?&]v=([^&#]+)/) || content.url.match(/youtu\.be\/([^?&#]+)/);
      const videoId = match ? match[1] : '';
      const embedUrl = videoId ? `https://www.youtube.com/embed/${videoId}` : '';
      player = (
        <div style={{ position: 'relative', width: '100%', height: 0, paddingTop: '56.25%', borderRadius: 10, overflow: 'hidden' }}>
          <iframe
            src={embedUrl}
            style={{ position: 'absolute', width: '100%', height: '100%', top: 0, left: 0, border: 'none' }}
            allow="fullscreen"
            title="Vídeo"
          />
        </div>
      );
    } else if (content.url && /^(https?:\/\/)?(www\.)?canva\.com\//.test(content.url)) {
      // Canva embed
      const isEmbed = /canva\.com\/design\/.+\/(watch|view)\?embed/.test(content.url);
      if (isEmbed) {
        player = (
          <div style={{ position: 'relative', width: '100%', height: 0, paddingTop: '56.25%', boxShadow: '0 2px 8px 0 rgba(63,69,81,0.16)', overflow: 'hidden', borderRadius: 10, willChange: 'transform' }}>
            <iframe
              loading="lazy"
              style={{ position: 'absolute', width: '100%', height: '100%', top: 0, left: 0, border: 'none', padding: 0, margin: 0 }}
              src={content.url}
              allow="fullscreen"
              title="Canva Embed"
            />
          </div>
        );
      } else {
        player = (
          <div className="flex flex-col items-center justify-center p-4 bg-yellow-50 border border-yellow-200 rounded">
            <span className="text-yellow-800 text-sm mb-2">
              O Canva não permite visualização inline para este link. Peça o link de compartilhamento com <b>?embed</b> ou abra em nova aba.
            </span>
            <a
              href={content.url}
              target="_blank"
              rel="noopener noreferrer"
              className="px-4 py-2 bg-yellow-600 text-white rounded font-semibold hover:bg-yellow-700 transition"
            >
              Abrir no Canva
            </a>
          </div>
        );
      }
    } else if (content.url) {
      // Vídeo direto
      player = <video src={content.url} controls style={{ width: '100%', borderRadius: 10 }} />;
    }
  }

  // Botão de ação
  // Configurações do botão (definidas fora do bloco para uso global)
  const buttonConfig = safeConfig.button || {};
  const buttonPosition = buttonConfig.position || 'bottom-center';

  // Função para calcular padding adicional baseado na posição do botão
  const getButtonPadding = (position: string, hasButton: boolean) => {
    if (!hasButton) return {};

    const buttonHeight = 60; // Padding maior para evitar sobreposição
    switch (position) {
      case 'top-left':
      case 'top-center':
      case 'top-right':
        return { paddingTop: `${buttonHeight}px` };
      case 'bottom-left':
      case 'bottom-center':
      case 'bottom-right':
        return { paddingBottom: `${buttonHeight}px` };
      default:
        return { paddingBottom: `${buttonHeight}px` };
    }
  };

  // Configurações de largura do botão (apenas largura, altura via CSS)
  const getWidthStyles = (size: string) => {
    switch (size) {
      case 'small':
        return {
          width: 'auto',
          maxWidth: '100%', // Não pode exceder o container
        };
      case 'large':
        return {
          width: '100%',
          maxWidth: '100%', // Largura total respeitando limites
        };
      default: // medium
        return {
          width: '50%',
          minWidth: '120px', // Largura mínima para legibilidade
          maxWidth: '100%', // Não pode exceder o container
        };
    }
  };

  // Configurações de posição do botão (posicionamento absoluto em relação ao card)
  const getPositionStyles = (position: string) => {
    const baseStyles = {
      position: 'absolute' as const,
      zIndex: 10,
      maxWidth: 'calc(100% - 24px)', // Garante que não saia do card (12px de cada lado)
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyles, top: '12px', left: '12px' };
      case 'top-center':
        return {
          ...baseStyles,
          top: '12px',
          left: '50%',
          transform: 'translateX(-50%)',
          maxWidth: 'calc(100% - 24px)', // Para posições centralizadas
        };
      case 'top-right':
        return { ...baseStyles, top: '12px', right: '12px' };
      case 'bottom-left':
        return { ...baseStyles, bottom: '12px', left: '12px' };
      case 'bottom-center':
        return {
          ...baseStyles,
          bottom: '12px',
          left: '50%',
          transform: 'translateX(-50%)',
          maxWidth: 'calc(100% - 24px)', // Para posições centralizadas
        };
      case 'bottom-right':
        return { ...baseStyles, bottom: '12px', right: '12px' };
      default:
        return {
          ...baseStyles,
          bottom: '12px',
          left: '50%',
          transform: 'translateX(-50%)',
          maxWidth: 'calc(100% - 24px)', // default: bottom-center
        };
    }
  };

  // Calcula sombra baseada na profundidade
  const getShadowStyle = (depth: number) => {
    const intensity = depth * 0.1;
    const blur = depth * 4;
    const spread = depth * 2;
    return `0 ${spread}px ${blur}px rgba(60,60,60,${intensity})`;
  };

  let actionButton = null;
  if (!content.showInline) {
    const buttonBgColor = buttonConfig.backgroundColor || '#7c3aed';
    const buttonTextColor = buttonConfig.color || '#fff';
    const buttonStyle = buttonConfig.style || 'rounded';
    const buttonSize = buttonConfig.size || 'medium';
    const buttonBorder = buttonConfig.border?.enabled;
    const buttonBorderColor = buttonConfig.border?.color || buttonBgColor;
    const buttonBorderWidth = buttonConfig.border?.width || 2;
    const buttonShadow = buttonConfig.shadow?.enabled;
    const buttonShadowDepth = buttonConfig.shadow?.depth || 2;
    const buttonHover = buttonConfig.hover?.enabled;
    const buttonHoverDepth = buttonConfig.hover?.shadowDepth || 3;
    const buttonText = content.buttonText || 'Assistir Agora';

    const widthStyles = getWidthStyles(buttonSize);
    const positionStyles = getPositionStyles(buttonPosition);

    // Estilos do container (posição + largura responsiva)
    const containerStyles = {
      ...positionStyles,
      ...widthStyles,
      maxWidth: 'calc(100% - 24px)', // Sempre respeitar limites do card
    };

    // Estilos do botão (configurações do usuário)
    const buttonStyles = {
      background: buttonBgColor,
      color: buttonTextColor,
      borderRadius: buttonStyle === 'rounded' ? '8px' : buttonStyle === 'square' ? '0' : '9999px',
      boxShadow: buttonShadow ? getShadowStyle(buttonShadowDepth) : undefined,
      border: buttonBorder ? `${buttonBorderWidth}px solid ${buttonBorderColor}` : 'none',
      transition: buttonHover ? 'transform 0.15s, box-shadow 0.15s, filter 0.15s' : undefined,
      fontWeight: 600,
      cursor: 'pointer',
      width: '100%', // Ocupa 100% do container
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    };

    actionButton = (
      <div style={containerStyles}>
        <button
          style={buttonStyles}
          className={`responsive-button ${buttonHover ? 'transition-all' : ''}`}
          onMouseEnter={(e) => {
            if (buttonHover) {
              e.currentTarget.style.transform = 'scale(1.05)';
              e.currentTarget.style.boxShadow = getShadowStyle(buttonHoverDepth);
              e.currentTarget.style.filter = 'brightness(1.05)';
            }
          }}
          onMouseLeave={(e) => {
            if (buttonHover) {
              e.currentTarget.style.transform = 'scale(1)';
              e.currentTarget.style.boxShadow = buttonShadow ? getShadowStyle(buttonShadowDepth) : 'none';
              e.currentTarget.style.filter = 'brightness(1)';
            }
          }}
          onClick={() => { if (content.url && onWatch) onWatch(content.url); }}
        >
          {(() => {
            const iconName = safeConfig.icon?.iconName || 'Video';
            const IconComp = (LucideIcons as any)[iconName];
            const iconColor = buttonConfig.color || '#fff';
            return IconComp ? <IconComp className="w-4 h-4 mr-1" color={iconColor} /> : null;
          })()}
          {buttonText}
        </button>
      </div>
    );
  }

  // Layout conforme posição do ícone
  if (iconPosition === 'left-content' || iconPosition === 'right-content') {
    const iconElement = (
      <div className="flex flex-col justify-center flex-shrink-0">
        <BlockCardIcon config={safeConfig.icon} />
      </div>
    );
    const contentElement = (
      <div className="flex-1 flex flex-col justify-center h-full min-w-0 max-w-full gap-2 overflow-x-hidden">
        {content.title && (
          <div
            className={`font-bold text-lg min-w-0 max-w-full break-words ${truncateText ? 'line-clamp-2' : ''}`}
            title={truncateText ? content.title : undefined}
          >
            {content.title}
          </div>
        )}
        {content.description && (
          <div
            className={`text-sm font-normal opacity-80 min-w-0 max-w-full break-words ${truncateText ? 'line-clamp-3' : ''}`}
            title={truncateText ? content.description : undefined}
          >
            {content.description}
          </div>
        )}
        {player}
        {actionButton}
      </div>
    );
    const buttonPadding = getButtonPadding(buttonPosition, !!actionButton);
    return (
      <BlockCard
        className={`flex flex-row items-center gap-2 min-h-[120px] card-padding ${actionButton ? 'card-with-button' : ''} ${className || ''}`}
        style={{...cardStyles, ...buttonPadding}}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {iconPosition === 'left-content' ? (
          <>
            {iconElement}
            {contentElement}
          </>
        ) : (
          <>
            {contentElement}
            {iconElement}
          </>
        )}
      </BlockCard>
    );
  }

  // Demais posições: ícone ao lado do título/descrição
  const buttonPadding = getButtonPadding(buttonPosition, !!actionButton);
  return (
    <BlockCard
      className={`flex flex-col gap-1 card-padding ${actionButton ? 'card-with-button' : ''} ${className || ''}`}
      style={{...cardStyles, ...buttonPadding}}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <BlockCardIcon
        config={safeConfig.icon}
        title={content.title}
        description={content.description}
        truncateText={truncateText}
        textColor={cardTextColor}
      />
      {player}
      {actionButton}
    </BlockCard>
  );
};
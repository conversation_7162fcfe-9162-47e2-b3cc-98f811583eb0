-- =====================================================
-- SCRIPT PRINCIPAL: SETUP COMPLETO DO SUPABASE
-- =====================================================
-- Execute este script para recriar toda a infraestrutura
-- Versão: 2.0 - Julho 2025
-- Projeto: Haiku Project Flow

-- Verificar conexão com Supabase
DO $$
BEGIN
  RAISE NOTICE '🚀 Iniciando setup do Supabase...';
  RAISE NOTICE '📅 Data/Hora: %', NOW();
  RAISE NOTICE '🔧 Versão do PostgreSQL: %', version();
END $$;

-- =====================================================
-- EXECUTAR SCRIPTS NA ORDEM CORRETA
-- =====================================================

-- 1. TABELAS PRINCIPAIS
\echo '📋 Criando tabelas principais...'
\i data/01_tables_core.sql

-- 2. TABELAS DE RELACIONAMENTO
\echo '🔗 Criando tabelas de relacionamento...'
\i data/02_tables_relations.sql

-- 3. TABELAS DE CONTEÚDO
\echo '📝 Criando tabelas de conteúdo...'
\i data/03_tables_content.sql

-- 4. VIEWS E FUNCTIONS
\echo '⚙️ Criando views e functions...'
\i data/04_views_functions.sql

-- 5. POLÍTICAS RLS
\echo '🔒 Aplicando políticas de segurança...'
\i data/05_rls_policies.sql

-- 6. STORAGE E BUCKETS
\echo '🗂️ Configurando storage...'
\i data/06_storage_buckets.sql

-- 7. DADOS DE TESTE
\echo '🧪 Inserindo dados de teste...'
\i data/07_test_data.sql

-- =====================================================
-- VALIDAÇÃO FINAL
-- =====================================================

-- Verificar se todas as tabelas foram criadas
DO $$
DECLARE
  table_count INTEGER;
  policy_count INTEGER;
BEGIN
  -- Contar tabelas criadas
  SELECT COUNT(*) INTO table_count 
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name IN (
    'profiles', 'projects', 'stages', 'tasks', 
    'project_members', 'task_executors', 'task_approvers', 'stage_responsibles',
    'task_content_blocks', 'task_attachments', 'evidence',
    'task_comments', 'project_history', 'user_notifications',
    'quizzes', 'quiz_attempts', 'quiz_answers', 'user_quiz_progress'
  );

  -- Contar políticas RLS
  SELECT COUNT(*) INTO policy_count 
  FROM pg_policies 
  WHERE schemaname = 'public';

  RAISE NOTICE '📊 RELATÓRIO FINAL:';
  RAISE NOTICE '✅ Tabelas criadas: %', table_count;
  RAISE NOTICE '🔒 Políticas RLS: %', policy_count;
  
  IF table_count >= 16 THEN
    RAISE NOTICE '🎉 Setup concluído com sucesso!';
  ELSE
    RAISE WARNING '⚠️ Algumas tabelas podem não ter sido criadas';
  END IF;
END $$;

-- =====================================================
-- INSTRUÇÕES FINAIS
-- =====================================================

-- Verificar logs de erro
SELECT 
  'Para verificar se há erros, execute:' as instrucoes,
  'SELECT * FROM pg_stat_activity WHERE state = ''active'';' as comando
UNION ALL
SELECT 
  'Para ver todas as tabelas criadas:' as instrucoes,
  'SELECT tablename FROM pg_tables WHERE schemaname = ''public'';' as comando;

RAISE NOTICE '📚 Documentação completa disponível em data/README.md';
RAISE NOTICE '🔧 Para rollback, execute: data/rollback.sql';
RAISE NOTICE '✨ Setup finalizado!';

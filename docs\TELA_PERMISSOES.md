# Tela de Gerenciamento de Permissões

## Visão Geral

Foi implementada uma nova tela no sistema para visualização completa da matriz de permissões, acessível apenas por usuários administradores.

## Acesso

1. **Menu de Configurações**: Acessível através do sidebar → "Configurações"
2. **Rota Direta**: `/settings/permissions`
3. **Restrição de Acesso**: Apenas usuários com role `admin` podem acessar

## Funcionalidades

### Página de Configurações (`/settings`)
- Página principal de configurações do sistema
- Seções organizadas por nível de permissão:
  - **Configurações Gerais**: Disponível para todos
  - **Administração do Sistema**: Apenas para admins
  - **Gestão de Projetos**: Para managers e admins
  - **Informações do Sistema**: Disponível para todos

### Tela de Permissões (`/settings/permissions`)
- **Resumo dos Roles**: Cards informativos sobre cada role do sistema
- **Mat<PERSON>**: Tabela detalhada com todas as permissões por role
- **Categorização**: Permissões organizadas por contexto:
  - Gestão de Projetos
  - Gestão de Etapas  
  - Gestão de Tarefas
  - Execução e Aprovação
- **Legenda Visual**: Indicadores claros de permissões concedidas/negadas
- **Notas Explicativas**: Informações sobre permissões contextuais

## Recursos Visuais

- **Ícones Intuitivos**: Cada permissão tem um ícone representativo
- **Cores por Role**: Badges coloridos para facilitar identificação
- **Layout Responsivo**: Adaptado para diferentes tamanhos de tela
- **Navegação Breadcrumb**: Retorno fácil às configurações

## Segurança

- **Proteção por Role**: Utiliza o componente `RequireGlobalRole`
- **Fallback de Acesso**: Tela de acesso negado para não-admins
- **Verificação Dupla**: Hook `useGlobalPermissions` + proteção visual

## Componentes Criados

1. **`PermissionsManagement.tsx`**: Tela principal de visualização
2. **`SettingsPage.tsx`**: Página de configurações do sistema
3. **Rotas adicionadas**: `/settings` e `/settings/permissions`

## Próximos Passos

Esta implementação serve como base para futuras funcionalidades:
- Edição de permissões (futuramente)
- Auditoria de acessos
- Configurações avançadas de roles
- Relatórios de utilização do sistema

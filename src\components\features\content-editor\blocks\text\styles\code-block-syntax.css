/* ===== SYNTAX HIGHLIGHTING CUSTOMIZADO PARA CODE BLOCKS ===== */
/* Sistema próprio de highlighting - mais estável */

/* Container do code block */
.tiptap-editor .code-block-container,
.tiptap-editor pre.code-block-container {
  position: relative;
  margin: 1rem 0;
  border-radius: 8px;
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 1rem;
  color: #24292e;
  white-space: pre;
  tab-size: 4;
}

/* Garantir que o code block seja editável */
.tiptap-editor .code-block-container {
  cursor: text;
}

.tiptap-editor .code-block-container:focus {
  outline: 2px solid #0366d6;
  outline-offset: -2px;
}

/* Código base */
.tiptap-editor .code-block-container pre {
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  overflow: visible;
  color: inherit;
  white-space: inherit;
}

.tiptap-editor .code-block-container code {
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

/* ===== SYNTAX HIGHLIGHTING EM TEMPO REAL ===== */
/* Cores baseadas no tema GitHub */

/* SQL Syntax Highlighting */
.tiptap-editor .syntax-highlight.sql-keyword {
  color: #d73a49;
  font-weight: 600;
}

.tiptap-editor .syntax-highlight.sql-string {
  color: #032f62;
}

.tiptap-editor .syntax-highlight.sql-comment {
  color: #6a737d;
  font-style: italic;
}

.tiptap-editor .syntax-highlight.sql-number {
  color: #005cc5;
}

/* JavaScript Syntax Highlighting */
.tiptap-editor .syntax-highlight.js-keyword {
  color: #d73a49;
  font-weight: 600;
}

.tiptap-editor .syntax-highlight.js-string {
  color: #032f62;
}

.tiptap-editor .syntax-highlight.js-comment {
  color: #6a737d;
  font-style: italic;
}

.tiptap-editor .syntax-highlight.js-number {
  color: #005cc5;
}

.tiptap-editor .syntax-highlight.js-boolean {
  color: #005cc5;
  font-weight: 600;
}

/* Python Syntax Highlighting */
.tiptap-editor .syntax-highlight.py-keyword {
  color: #d73a49;
  font-weight: 600;
}

.tiptap-editor .syntax-highlight.py-string {
  color: #032f62;
}

.tiptap-editor .syntax-highlight.py-comment {
  color: #6a737d;
  font-style: italic;
}

.tiptap-editor .syntax-highlight.py-number {
  color: #005cc5;
}

.tiptap-editor .syntax-highlight.py-boolean {
  color: #005cc5;
  font-weight: 600;
}

/* TypeScript Syntax Highlighting */
.tiptap-editor .syntax-highlight.ts-keyword {
  color: #d73a49;
  font-weight: 600;
}

.tiptap-editor .syntax-highlight.ts-string {
  color: #032f62;
}

.tiptap-editor .syntax-highlight.ts-comment {
  color: #6a737d;
  font-style: italic;
}

.tiptap-editor .syntax-highlight.ts-number {
  color: #005cc5;
}

.tiptap-editor .syntax-highlight.ts-boolean {
  color: #005cc5;
  font-weight: 600;
}

.tiptap-editor .syntax-highlight.ts-type {
  color: #6f42c1;
  font-weight: 600;
}

.tiptap-editor .hljs-comment,
.tiptap-editor .hljs-quote {
  color: #6a737d;
  font-style: italic;
}

.tiptap-editor .hljs-keyword,
.tiptap-editor .hljs-selector-tag,
.tiptap-editor .hljs-subst {
  color: #d73a49;
  font-weight: 600;
}

.tiptap-editor .hljs-number,
.tiptap-editor .hljs-literal,
.tiptap-editor .hljs-variable,
.tiptap-editor .hljs-template-variable,
.tiptap-editor .hljs-tag .hljs-attr {
  color: #005cc5;
}

.tiptap-editor .hljs-string,
.tiptap-editor .hljs-doctag {
  color: #032f62;
}

.tiptap-editor .hljs-title,
.tiptap-editor .hljs-section,
.tiptap-editor .hljs-selector-id {
  color: #6f42c1;
  font-weight: 600;
}

.tiptap-editor .hljs-subst {
  font-weight: normal;
}

.tiptap-editor .hljs-type,
.tiptap-editor .hljs-class .hljs-title,
.tiptap-editor .hljs-tag,
.tiptap-editor .hljs-name,
.tiptap-editor .hljs-attribute {
  color: #22863a;
  font-weight: normal;
}

.tiptap-editor .hljs-regexp,
.tiptap-editor .hljs-link {
  color: #032f62;
}

.tiptap-editor .hljs-symbol,
.tiptap-editor .hljs-bullet {
  color: #e36209;
}

.tiptap-editor .hljs-built_in,
.tiptap-editor .hljs-builtin-name {
  color: #005cc5;
}

.tiptap-editor .hljs-meta {
  color: #6a737d;
}

.tiptap-editor .hljs-deletion {
  background: #ffeef0;
}

.tiptap-editor .hljs-addition {
  background: #f0fff4;
}

.tiptap-editor .hljs-emphasis {
  font-style: italic;
}

.tiptap-editor .hljs-strong {
  font-weight: bold;
}

/* Linguagens específicas */
.tiptap-editor .hljs-json .hljs-attr {
  color: #005cc5;
}

.tiptap-editor .hljs-javascript .hljs-function,
.tiptap-editor .hljs-typescript .hljs-function {
  color: #6f42c1;
}

.tiptap-editor .hljs-python .hljs-function,
.tiptap-editor .hljs-python .hljs-class {
  color: #6f42c1;
}

/* Scrollbar customizada */
.tiptap-editor .code-block-container pre::-webkit-scrollbar {
  height: 8px;
}

.tiptap-editor .code-block-container pre::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 4px;
}

.tiptap-editor .code-block-container pre::-webkit-scrollbar-thumb {
  background: #c1c8cd;
  border-radius: 4px;
}

.tiptap-editor .code-block-container pre::-webkit-scrollbar-thumb:hover {
  background: #a8b2ba;
}

/* Responsividade */
@media (max-width: 768px) {
  .tiptap-editor .code-block-container pre {
    font-size: 13px;
    padding: 0.75rem;
  }
}

/* Estados de foco */
.tiptap-editor .code-block-container:focus-within {
  border-color: #0366d6;
  box-shadow: 0 0 0 3px rgba(3, 102, 214, 0.1);
}

/* Indicador de linguagem */
.tiptap-editor .code-block-container::before,
.tiptap-editor pre.code-block-container::before {
  content: attr(data-language);
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  font-size: 0.75rem;
  color: #6a737d;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 500;
  z-index: 1;
  border: 1px solid #e1e4e8;
}

/* Ocultar indicador para texto simples */
.tiptap-editor .code-block-container[data-language="text"]::before,
.tiptap-editor .code-block-container:not([data-language])::before,
.tiptap-editor pre.code-block-container[data-language="text"]::before,
.tiptap-editor pre.code-block-container:not([data-language])::before {
  display: none;
}

/* Animação suave */
.tiptap-editor .code-block-container {
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Seleção de texto */
.tiptap-editor .code-block-container pre::selection,
.tiptap-editor .code-block-container code::selection {
  background: rgba(3, 102, 214, 0.2);
}

/* Linha de números (opcional) */
.tiptap-editor .code-block-container.line-numbers {
  counter-reset: line;
}

.tiptap-editor .code-block-container.line-numbers pre {
  padding-left: 3rem;
}

.tiptap-editor .code-block-container.line-numbers pre::before {
  content: counter(line);
  counter-increment: line;
  position: absolute;
  left: 0;
  top: 0;
  width: 2.5rem;
  padding: 1rem 0.5rem;
  color: #6a737d;
  background: #f1f3f4;
  border-right: 1px solid #e1e4e8;
  text-align: right;
  font-size: 0.875rem;
  line-height: 1.5;
}

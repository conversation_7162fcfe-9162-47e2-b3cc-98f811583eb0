-- =====================================================
-- DIAGNÓSTICO DETALHADO - POR QUE NÃO RETORNA TAREFAS
-- =====================================================

-- 1. VERIFICAR SE A TABELA task_executors EXISTE
SELECT 
    '🔍 VERIFICANDO TABELA task_executors' as teste,
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'task_executors' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. VERIFICAR TOTAL DE REGISTROS EM CADA TABELA
SELECT 
    '📊 CONTAGEM DE REGISTROS' as teste,
    'tasks' as tabela,
    COUNT(*) as total
FROM tasks
UNION ALL
SELECT 
    '📊 CONTAGEM DE REGISTROS' as teste,
    'task_executors' as tabela,
    COUNT(*) as total
FROM task_executors
UNION ALL
SELECT 
    '📊 CONTAGEM DE REGISTROS' as teste,
    'stages' as tabela,
    COUNT(*) as total
FROM stages
UNION ALL
SELECT 
    '📊 CONTAGEM DE REGISTROS' as teste,
    'projects' as tabela,
    COUNT(*) as total
FROM projects
UNION ALL
SELECT 
    '📊 CONTAGEM DE REGISTROS' as teste,
    'profiles' as tabela,
    COUNT(*) as total
FROM profiles;

-- 3. VERIFICAR SE HÁ DADOS NA TABELA task_executors
SELECT 
    '🔍 DADOS task_executors' as teste,
    te.*,
    u.email as user_email,
    t.title as task_title
FROM task_executors te
LEFT JOIN auth.users u ON te.user_id = u.id
LEFT JOIN tasks t ON te.task_id = t.id
ORDER BY te.created_at DESC
LIMIT 10;

-- 4. VERIFICAR SE O USUÁRIO ESPECÍFICO EXISTE
SELECT 
    '👤 VERIFICANDO USUÁRIO' as teste,
    u.id,
    u.email,
    u.created_at as user_created_at,
    p.name as profile_name,
    p.role as profile_role
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 5. VERIFICAR SE HÁ TAREFAS PARA QUALQUER USUÁRIO
SELECT 
    '📋 TAREFAS POR USUÁRIO' as teste,
    u.email,
    COUNT(te.task_id) as total_tasks_executor
FROM auth.users u
LEFT JOIN task_executors te ON u.id = te.user_id
GROUP BY u.id, u.email
ORDER BY total_tasks_executor DESC
LIMIT 10;

-- 6. VERIFICAR ESTRUTURA COMPLETA DAS TAREFAS
SELECT 
    '🏗️ ESTRUTURA COMPLETA' as teste,
    t.id as task_id,
    t.title as task_title,
    t.status as task_status,
    s.name as stage_name,
    p.name as project_name,
    COUNT(te.user_id) as total_executors
FROM tasks t
LEFT JOIN stages s ON t.stage_id = s.id
LEFT JOIN projects p ON s.project_id = p.id
LEFT JOIN task_executors te ON t.id = te.task_id
GROUP BY t.id, t.title, t.status, s.name, p.name
ORDER BY t.created_at DESC
LIMIT 10;

-- 7. VERIFICAR SE HÁ PROBLEMA DE FOREIGN KEY
SELECT 
    '🔗 VERIFICANDO FOREIGN KEYS' as teste,
    te.user_id,
    te.task_id,
    CASE 
        WHEN u.id IS NULL THEN '❌ User não existe' 
        ELSE '✅ User existe' 
    END as user_status,
    CASE 
        WHEN t.id IS NULL THEN '❌ Task não existe' 
        ELSE '✅ Task existe' 
    END as task_status
FROM task_executors te
LEFT JOIN auth.users u ON te.user_id = u.id
LEFT JOIN tasks t ON te.task_id = t.id
LIMIT 10;

-- 8. QUERY EXATA QUE A APLICAÇÃO ESTÁ FAZENDO
SELECT 
    '🎯 QUERY EXATA DA APLICAÇÃO' as teste,
    te.task_id,
    te.created_at,
    te.user_id
FROM task_executors te
WHERE te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 9. VERIFICAR SE A TABELA TEM DADOS MAS COM OUTROS USUÁRIOS
SELECT 
    '🔍 OUTROS USUÁRIOS NA TABELA' as teste,
    COUNT(DISTINCT te.user_id) as total_users_distinct,
    COUNT(te.id) as total_records,
    STRING_AGG(DISTINCT u.email, ', ') as emails_usuarios
FROM task_executors te
LEFT JOIN auth.users u ON te.user_id = u.id;

-- 10. CRIAR DADOS DE TESTE SE NECESSÁRIO
-- DESCOMENTE PARA EXECUTAR
/*
DO $$
DECLARE
    stage_id_exemplo UUID;
    task_id_1 UUID;
    task_id_2 UUID;
    usuario_id UUID := '4b09be1f-5187-44c0-9b53-87b7c57e45b4';
BEGIN
    -- Buscar um estágio existente
    SELECT id INTO stage_id_exemplo FROM stages LIMIT 1;
    
    IF stage_id_exemplo IS NOT NULL THEN
        -- Criar tarefas de exemplo
        INSERT INTO tasks (title, description, status, progress, stage_id, created_at)
        VALUES 
        ('Tarefa Debug 1', 'Primeira tarefa para testar executor', 'pending', 0, stage_id_exemplo, NOW())
        RETURNING id INTO task_id_1;
        
        INSERT INTO tasks (title, description, status, progress, stage_id, created_at)
        VALUES 
        ('Tarefa Debug 2', 'Segunda tarefa para testar executor', 'in-progress', 25, stage_id_exemplo, NOW())
        RETURNING id INTO task_id_2;
        
        -- Adicionar o usuário como executor dessas tarefas
        INSERT INTO task_executors (user_id, task_id, created_at)
        VALUES 
        (usuario_id, task_id_1, NOW()),
        (usuario_id, task_id_2, NOW());
        
        RAISE NOTICE 'Dados de teste criados com sucesso!';
    ELSE
        RAISE NOTICE 'Nenhum estágio encontrado. Crie projetos e estágios primeiro.';
    END IF;
END $$;
*/

# MATRIZ DE PERMISSÕES DETALHADA
**Sistema de Gestão de Projetos - Haiku Project Flow**  
**Versão:** 2.0 - Julho 2025  
**Atualização:** Pós-limpeza RLS

---

## 📋 LEGENDA DE ROLES E CONTEXTOS

### **ROLES DE USUÁRIO**
- **Admin**: <PERSON><PERSON><PERSON><PERSON> do sistema (role = 'admin')
- **Manager**: <PERSON><PERSON><PERSON>jetos (role = 'manager')  
- **Member**: <PERSON><PERSON><PERSON> comum (role = 'member')

### **CONTEXTOS DE ACESSO**
- **Own**: Recursos criados pelo próprio usuário
- **Project Member**: Usuário é membro do projeto
- **Project Owner**: Usu<PERSON>rio criou/possui o projeto
- **Executor**: Usu<PERSON>rio atribuído como executor da tarefa
- **Approver**: Usuário atribuído como aprovador da tarefa
- **Stage Responsible**: Usu<PERSON>rio responsável pela etapa
- **Active**: Usuário com status ativo no sistema

---

## 📊 MATRIZ DETALHADA POR TABELA

### **1. TABELA: profiles**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Own Profile | ✅ | ✅ | ✅ | Próprio perfil sempre visível |
| **SELECT** | Active Users (Basic) | ✅ | ✅ | ✅ | Para autocomplete: id, name, email |
| **SELECT** | All Details | ✅ | ❌ | ❌ | Detalhes completos apenas admin |
| **INSERT** | New Profile | ✅ | ✅ | ✅ | Qualquer um pode criar perfil |
| **UPDATE** | Own Profile | ✅ | ✅ | ✅ | Editar próprio perfil |
| **UPDATE** | Other Profiles | ✅ | ❌ | ❌ | Apenas admin edita outros |
| **DELETE** | Any Profile | ✅ | ❌ | ❌ | Apenas admin pode deletar |

**Campos Visíveis por Contexto:**
```yaml
Own Profile: *  # Todos os campos
Active Users Basic: id, name, email, avatar_url, is_active
Admin Full Access: *  # Todos os campos
```

---

### **2. TABELA: projects**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Project | ✅ | ❌ | ❌ | Admin vê todos |
| **SELECT** | Project Member | ✅ | ✅ | ✅ | Membros veem projetos participantes |
| **SELECT** | Own Projects | ✅ | ✅ | ✅ | Projetos criados pelo usuário |
| **INSERT** | New Project | ✅ | ✅ | ✅ | Qualquer um pode criar |
| **UPDATE** | Any Project | ✅ | ❌ | ❌ | Apenas admin |
| **UPDATE** | Own Projects | ✅ | ✅ | ✅ | Owner pode editar |
| **DELETE** | Any Project | ✅ | ❌ | ❌ | Apenas admin |
| **DELETE** | Own Projects | ✅ | ✅ | ✅ | Owner pode deletar |

**Lógica de Acesso:**
```sql
-- Visualizar projetos
WHERE 
  created_by = auth.uid()  -- Próprios projetos
  OR 
  id IN (
    SELECT project_id FROM project_members 
    WHERE user_id = auth.uid()
  )  -- Projetos onde é membro
  OR 
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )  -- Admin vê todos
```

---

### **3. TABELA: project_members**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Project | ✅ | ❌ | ❌ | Admin vê todos membros |
| **SELECT** | Same Project | ✅ | ✅ | ✅ | Membros veem colegas do projeto |
| **INSERT** | Any Project | ✅ | ❌ | ❌ | Apenas admin adiciona |
| **INSERT** | Own Projects | ✅ | ✅ | ✅ | Owner pode adicionar membros |
| **UPDATE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **UPDATE** | Own Projects | ✅ | ✅ | ✅ | Owner pode alterar roles |
| **DELETE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **DELETE** | Own Projects | ✅ | ✅ | ✅ | Owner pode remover membros |

---

### **4. TABELA: stages**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Stage | ✅ | ❌ | ❌ | Admin vê todas |
| **SELECT** | Project Member | ✅ | ✅ | ✅ | Etapas de projetos participantes |
| **SELECT** | Stage Responsible | ✅ | ✅ | ✅ | Responsável vê suas etapas |
| **INSERT** | Any Project | ✅ | ❌ | ❌ | Apenas admin |
| **INSERT** | Member Projects | ✅ | ✅ | ✅ | Em projetos onde é membro |
| **UPDATE** | Any Stage | ✅ | ❌ | ❌ | Apenas admin |
| **UPDATE** | Project Member | ✅ | ✅ | ✅ | Membros podem editar |
| **UPDATE** | Stage Responsible | ✅ | ✅ | ✅ | Responsável pode editar |
| **DELETE** | Any Stage | ✅ | ❌ | ❌ | Apenas admin |
| **DELETE** | Project Owner | ✅ | ✅ | ✅ | Owner do projeto |

**Lógica de Acesso:**
```sql
-- Visualizar etapas
WHERE 
  project_id IN (
    SELECT project_id FROM project_members 
    WHERE user_id = auth.uid()
  )  -- Etapas de projetos onde é membro
  OR 
  id IN (
    SELECT stage_id FROM stage_responsibles 
    WHERE user_id = auth.uid()
  )  -- Etapas sob responsabilidade
  OR 
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )  -- Admin vê todas
```

---

### **5. TABELA: stage_responsibles**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Record | ✅ | ❌ | ❌ | Admin vê todos |
| **SELECT** | Same Project | ✅ | ✅ | ✅ | Membros veem responsáveis do projeto |
| **SELECT** | Own Assignments | ✅ | ✅ | ✅ | Próprias responsabilidades |
| **INSERT** | Any Assignment | ✅ | ❌ | ❌ | Apenas admin |
| **INSERT** | Project Member | ✅ | ✅ | ✅ | Em projetos onde é membro |
| **UPDATE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **UPDATE** | Project Member | ✅ | ✅ | ✅ | Membros podem alterar |
| **DELETE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **DELETE** | Project Member | ✅ | ✅ | ✅ | Membros podem remover |

---

### **6. TABELA: tasks**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Task | ✅ | ❌ | ❌ | Admin vê todas |
| **SELECT** | Project Member | ✅ | ✅ | ✅ | Tarefas de projetos participantes |
| **SELECT** | Executor Assignment | ✅ | ✅ | ✅ | Tarefas atribuídas como executor |
| **SELECT** | Approver Assignment | ✅ | ✅ | ✅ | Tarefas atribuídas como aprovador |
| **INSERT** | Any Project | ✅ | ❌ | ❌ | Apenas admin |
| **INSERT** | Project Member | ✅ | ✅ | ✅ | Em projetos onde é membro |
| **UPDATE** | Any Task | ✅ | ❌ | ❌ | Apenas admin |
| **UPDATE** | Project Member | ✅ | ✅ | ✅ | Membros podem editar |
| **UPDATE** | Executor (Status) | ✅ | ✅ | ✅ | Executor altera status/progresso |
| **UPDATE** | Approver (Approval) | ✅ | ✅ | ✅ | Aprovador altera approval_status |
| **DELETE** | Any Task | ✅ | ❌ | ❌ | Apenas admin |
| **DELETE** | Project Owner | ✅ | ✅ | ✅ | Owner do projeto |

**Campos Editáveis por Contexto:**
```yaml
Admin: *  # Todos os campos
Project Member: title, description, priority, due_date, etc.
Executor: status, progress_percentage, notes
Approver: approval_status, approval_notes
```

**Lógica de Acesso:**
```sql
-- Visualizar tarefas
WHERE 
  stage_id IN (
    SELECT s.id FROM stages s
    JOIN project_members pm ON pm.project_id = s.project_id
    WHERE pm.user_id = auth.uid()
  )  -- Tarefas de projetos onde é membro
  OR 
  id IN (
    SELECT task_id FROM task_executors 
    WHERE user_id = auth.uid()
  )  -- Tarefas atribuídas como executor
  OR 
  id IN (
    SELECT task_id FROM task_approvers 
    WHERE user_id = auth.uid()
  )  -- Tarefas atribuídas como aprovador
  OR 
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )  -- Admin vê todas
```

---

### **7. TABELA: task_executors**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Record | ✅ | ❌ | ❌ | Admin vê todos |
| **SELECT** | Same Project | ✅ | ✅ | ✅ | Executores de projetos participantes |
| **SELECT** | Own Assignments | ✅ | ✅ | ✅ | Próprias atribuições |
| **INSERT** | Any Task | ✅ | ❌ | ❌ | Apenas admin |
| **INSERT** | Project Member | ✅ | ✅ | ✅ | Em tarefas de projetos participantes |
| **UPDATE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **UPDATE** | Project Member | ✅ | ✅ | ✅ | Membros podem alterar |
| **DELETE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **DELETE** | Project Member | ✅ | ✅ | ✅ | Membros podem remover |

---

### **8. TABELA: task_approvers**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Record | ✅ | ❌ | ❌ | Admin vê todos |
| **SELECT** | Same Project | ✅ | ✅ | ✅ | Aprovadores de projetos participantes |
| **SELECT** | Own Assignments | ✅ | ✅ | ✅ | Próprias atribuições |
| **INSERT** | Any Task | ✅ | ❌ | ❌ | Apenas admin |
| **INSERT** | Project Member | ✅ | ✅ | ✅ | Em tarefas de projetos participantes |
| **UPDATE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **UPDATE** | Project Member | ✅ | ✅ | ✅ | Membros podem alterar |
| **DELETE** | Any Record | ✅ | ❌ | ❌ | Apenas admin |
| **DELETE** | Project Member | ✅ | ✅ | ✅ | Membros podem remover |

---

### **9. TABELA: task_content_blocks**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Block | ✅ | ❌ | ❌ | Admin vê todos |
| **SELECT** | Task Visible | ✅ | ✅ | ✅ | Se pode ver a tarefa, vê conteúdo |
| **INSERT** | Task Editable | ✅ | ✅ | ✅ | Se pode editar tarefa |
| **UPDATE** | Task Editable | ✅ | ✅ | ✅ | Se pode editar tarefa |
| **DELETE** | Task Editable | ✅ | ✅ | ✅ | Se pode editar tarefa |

**Herda Permissões da Tarefa:**
- Segue as mesmas regras da tabela `tasks`
- Se usuário pode ver/editar tarefa, pode ver/editar conteúdo

---

### **10. TABELA: task_comments**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Comment | ✅ | ❌ | ❌ | Admin vê todos |
| **SELECT** | Task Visible | ✅ | ✅ | ✅ | Se pode ver tarefa, vê comentários |
| **INSERT** | Task Visible | ✅ | ✅ | ✅ | Se pode ver tarefa, pode comentar |
| **UPDATE** | Own Comments | ✅ | ✅ | ✅ | Próprios comentários |
| **UPDATE** | Any Comment | ✅ | ❌ | ❌ | Apenas admin edita outros |
| **DELETE** | Own Comments | ✅ | ✅ | ✅ | Próprios comentários |
| **DELETE** | Any Comment | ✅ | ❌ | ❌ | Apenas admin deleta outros |

---

### **11. TABELA: evidence**
| Operação | Condição | Admin | Manager | Member | Descrição |
|----------|----------|-------|---------|--------|-----------|
| **SELECT** | Any Evidence | ✅ | ❌ | ❌ | Admin vê todas |
| **SELECT** | Task Visible | ✅ | ✅ | ✅ | Se pode ver tarefa, vê evidências |
| **INSERT** | Task Executor | ✅ | ✅ | ✅ | Executores podem adicionar |
| **INSERT** | Task Editable | ✅ | ✅ | ✅ | Se pode editar tarefa |
| **UPDATE** | Own Evidence | ✅ | ✅ | ✅ | Próprias evidências |
| **UPDATE** | Task Editable | ✅ | ✅ | ✅ | Se pode editar tarefa |
| **DELETE** | Own Evidence | ✅ | ✅ | ✅ | Próprias evidências |
| **DELETE** | Task Editable | ✅ | ✅ | ✅ | Se pode editar tarefa |

---

## 🔄 CASOS ESPECIAIS E EXCEÇÕES

### **Autocomplete de Usuários**
```sql
-- TODOS os usuários ativos visíveis para colaboração
-- Não há restrição por projeto para facilitar atribuições
SELECT id, name, email, avatar_url 
FROM profiles 
WHERE is_active = true
```

### **Dashboard e Relatórios**
```sql
-- Admin: Vê estatísticas de todos os projetos
-- Manager/Member: Apenas projetos onde participa
WHERE project_id IN (user_project_ids) OR user_is_admin
```

### **Notificações**
```sql
-- Usuário só vê notificações próprias
WHERE recipient_id = auth.uid()
```

### **Histórico e Auditoria**
```sql
-- Admin: Vê todo histórico
-- Users: Apenas histórico de itens que pode acessar
WHERE item_id IN (accessible_item_ids) OR user_is_admin
```

---

## 📝 IMPLEMENTAÇÃO TÉCNICA

### **Funções Helper**
```sql
-- Verificar se usuário é admin
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Verificar se usuário é membro do projeto
CREATE OR REPLACE FUNCTION is_project_member(user_id UUID, project_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM project_members 
    WHERE user_id = user_id AND project_id = project_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **Views para Consultas Complexas**
```sql
-- View para projetos acessíveis ao usuário
CREATE VIEW user_accessible_projects AS
SELECT p.* 
FROM projects p
WHERE 
  p.created_by = auth.uid()
  OR p.id IN (
    SELECT pm.project_id 
    FROM project_members pm 
    WHERE pm.user_id = auth.uid()
  )
  OR is_admin(auth.uid());
```

---

**✅ CHECKLIST DE VALIDAÇÃO**

- [ ] Todas as operações mapeadas
- [ ] Contextos de acesso definidos
- [ ] Lógicas SQL validadas
- [ ] Casos especiais cobertos
- [ ] Performance considerada
- [ ] Segurança garantida

**🔄 PRÓXIMA ETAPA:** Implementar políticas seguindo esta matriz.

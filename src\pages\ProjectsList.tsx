import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ProjectCard } from '@/components/ui/ProjectCard';
import { projectService } from '@/services/projectService';
import { Plus, ArrowLeft, FolderOpen } from 'lucide-react';
import { ProjectFilters } from '@/components/features/dashboard';
import { ProjectStatus } from '@/types';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { userService } from '@/services/userService';
import { ProjectForm } from '@/components/forms/ProjectForm';
import { SidebarProvider, Sidebar, SidebarTrigger } from '@/components/ui/sidebar';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { Header } from '@/components/layout/Header';
import { useSidebarMenu } from '@/components/ui/sidebar';
import { useGlobalPermissions } from '@/hooks/usePermissions';
import { PermissionIndicator } from '@/components/auth/PermissionIndicator';

const ProjectsList = () => {
  const { canCreateProjects } = useGlobalPermissions();
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilters, setStatusFilters] = useState<ProjectStatus[]>([]);
  const [responsible, setResponsible] = useState<any | null>(null);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [showProjectForm, setShowProjectForm] = useState(false);
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Inicializar filtros a partir da URL
  useEffect(() => {
    const urlSearch = searchParams.get('search') || '';
    const urlStatus = searchParams.get('status') || '';
    const urlResponsible = searchParams.get('responsibleId') || '';
    const urlStartDate = searchParams.get('startDate') || '';
    const urlEndDate = searchParams.get('endDate') || '';

    setSearchTerm(urlSearch);
    setStatusFilters(urlStatus ? urlStatus.split(',').filter(Boolean) as ProjectStatus[] : []);
    setStartDate(urlStartDate);
    setEndDate(urlEndDate);
    if (urlResponsible) {
      // Buscar usuário completo pelo id
      userService.getById(urlResponsible).then(user => {
        if (user) setResponsible(user);
        else setResponsible({ id: urlResponsible });
      });
    } else {
      setResponsible(null);
    }
    // eslint-disable-next-line
  }, []);

  // Atualizar URL ao mudar filtros
  useEffect(() => {
    const params: any = {};
    if (searchTerm) params.search = searchTerm;
    if (statusFilters.length > 0) params.status = statusFilters.join(',');
    if (responsible && responsible.id) params.responsibleId = responsible.id;
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;
    setSearchParams(params, { replace: true });
    // eslint-disable-next-line
  }, [searchTerm, statusFilters, responsible, startDate, endDate]);

  // Função para buscar projetos (reutilizável)
  const fetchProjects = async () => {
    setLoading(true);
    setError(null);
    try {
      const filters: any = {};
      if (searchTerm) filters.search = searchTerm;
      if (statusFilters.length > 0) filters.status = statusFilters;
      if (responsible) filters.responsibleId = responsible.id;
      if (startDate) filters.startDate = startDate;
      if (endDate) filters.endDate = endDate;

      let data;
      if (Object.keys(filters).length === 0) {
        // Se não há filtros, buscar projetos do usuário logado
        console.log('Buscando projetos do usuário logado (sem filtros)');
        data = await projectService.getMyProjects();
      } else {
        // Se há filtros, usar método com filtros
        console.log('Buscando projetos com filtros:', filters);
        data = await projectService.getProjects(filters);
      }

      setProjects(data || []);
      console.log('Projetos carregados:', data?.length || 0);
    } catch (err) {
      console.error('Erro ao carregar projetos:', err);
      setError('Erro ao carregar projetos.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
    // eslint-disable-next-line
  }, [searchTerm, statusFilters, responsible, startDate, endDate]);

  const handleNewProject = () => {
    setShowProjectForm(true);
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header showSidebarButton={true} />
      <div className="flex min-h-screen">
        <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
        <main className={`flex-1 flex flex-col min-w-0 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
          <div className="w-full max-w-6xl mx-auto px-2 sm:px-4 md:px-6 py-6 flex flex-col gap-6">
            {/* Caminho/Breadcrumb e Voltar */}
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <Button variant="ghost" size="sm" className="p-0" onClick={handleBack}>
                <ArrowLeft className="w-4 h-4 mr-1" />
                Voltar
              </Button>
              <span>/</span>
              <span className="text-project font-medium">Projetos</span>
            </div>

            {/* Header e Filtros */}
            <Card className="border-project/20 bg-project-bg">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FolderOpen className="w-5 h-5 text-project" />
                    <CardTitle>Projetos</CardTitle>
                    <PermissionIndicator screenType="project" />
                  </div>
                  {canCreateProjects && (
                    <Button className="bg-project hover:bg-project-dark" onClick={handleNewProject}>
                      <Plus className="w-4 h-4 mr-1" /> Novo Projeto
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <ProjectFilters
                  searchTerm={searchTerm}
                  statusFilters={statusFilters}
                  onSearchChange={setSearchTerm}
                  onStatusFilterChange={setStatusFilters}
                  responsible={responsible}
                  onResponsibleChange={setResponsible}
                  startDate={startDate}
                  endDate={endDate}
                  onStartDateChange={setStartDate}
                  onEndDateChange={setEndDate}
                />
              </CardContent>
            </Card>

            {/* Lista de Projetos */}
            <Card>
              <CardContent className="pt-6 pb-8">
                {loading ? (
                  <div className="p-8 text-center">Carregando projetos...</div>
                ) : error ? (
                  <div className="p-8 text-center text-red-600">{error}</div>
                ) : projects.length === 0 ? (
                  <div className="text-center text-gray-500">Nenhum projeto encontrado.</div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 min-w-0">
                    {projects.map((project) => (
                      <div key={project.id}>
                        {ProjectCard ? (
                          <ProjectCard 
                            project={project} 
                            onClick={() => navigate(`/project2/${project.id}`)}
                          />
                        ) : (
                          <Card className="cursor-pointer hover:shadow-lg transition" onClick={() => navigate(`/project2/${project.id}`)}>
                            <CardHeader>
                              <CardTitle>{project.name}</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <p className="text-gray-600 mb-2">{project.description}</p>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <ProjectForm
              open={showProjectForm}
              onOpenChange={(open) => {
                setShowProjectForm(open);
                if (!open) {
                  fetchProjects(); // recarrega ao fechar o modal
                }
              }}
              mode="create"
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default ProjectsList;
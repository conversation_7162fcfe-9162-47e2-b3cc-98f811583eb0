import { supabase } from '@/lib/supabaseClient';
import { requireAuth } from '@/lib/authUtils';
import {
  QuizContent,
  QuizAttempt,
  QuizAnswer,
  QuizStatistics,
  UserQuizProgress,
  QuizValidationResult,
  QuizUtils,
  SurveyResults,
  SurveyQuestionResult
} from '@/types/quiz';

// Wrapper para detectar erros 406 do Supabase
const handleSupabaseError = (error: any, operation: string) => {
  console.warn(`Erro no Supabase (${operation}):`, error);

  // Verificar se é erro 406 (tabelas não encontradas)
  const is406Error =
    error.status === 406 ||
    error.code === 406 ||
    error.message?.includes('406') ||
    error.message?.includes('Not Acceptable') ||
    (error.details && error.details.includes('406')) ||
    (error.hint && error.hint.includes('406')) ||
    // Verificar também na resposta HTTP
    (error.response && error.response.status === 406);

  if (is406Error) {
    console.warn(`❌ Erro 406 detectado em ${operation} - Tabelas do Quiz não encontradas`);
    throw new Error('TABLES_NOT_FOUND');
  }

  throw error;
};

export class QuizService {
  // Criar quiz no Supabase
  static async createQuiz(taskId: string, blockId: string, content: QuizContent): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('quizzes')
        .insert({
          task_id: taskId,
          block_id: blockId,
          content,
          is_active: true
        })
        .select('id')
        .single();

      if (error) {
        handleSupabaseError(error, 'createQuiz');
        return null;
      }

      return data.id;
    } catch (error: any) {
      handleSupabaseError(error, 'createQuiz');
      return null;
    }
  }

  // Criar tentativa no Supabase baseada em tentativa local
  static async createAttempt(quizId: string, userId: string, localAttempt: QuizAttempt): Promise<QuizAttempt | null> {
    try {
      const { data, error } = await supabase
        .from('quiz_attempts')
        .insert({
          quiz_id: quizId, // Usar ID real do quiz
          user_id: userId,
          started_at: localAttempt.startedAt,
          submitted_at: localAttempt.submittedAt,
          score: localAttempt.score,
          max_score: localAttempt.maxScore,
          percentage: localAttempt.percentage,
          passed: localAttempt.passed,
          status: localAttempt.status,
          time_spent: localAttempt.timeSpent
        })
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, 'createAttempt');
        return null;
      }

      return {
        ...localAttempt,
        id: data.id
      };
    } catch (error: any) {
      handleSupabaseError(error, 'createAttempt');
      return null;
    }
  }

  // Salvar ou atualizar quiz
  static async saveQuiz(taskId: string, blockId: string, content: QuizContent): Promise<void> {
    const { error } = await supabase
      .from('quizzes')
      .upsert({
        task_id: taskId,
        block_id: blockId,
        content,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'task_id,block_id'
      });

    if (error) {
      throw new Error(`Erro ao salvar quiz: ${error.message}`);
    }
  }

  // Carregar quiz por task e block
  static async loadQuiz(taskId: string, blockId: string): Promise<{id: string, content: QuizContent} | null> {
    try {
      const { data, error } = await supabase
        .from('quizzes')
        .select('id, content')
        .eq('task_id', taskId)
        .eq('block_id', blockId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Quiz não encontrado
        }
        handleSupabaseError(error, 'loadQuiz');
      }

      return {
        id: data.id,
        content: data.content as QuizContent
      };
    } catch (error: any) {
      handleSupabaseError(error, 'loadQuiz');
    }
  }

  // Iniciar nova tentativa
  static async startAttempt(taskId: string, blockId: string, userId: string): Promise<QuizAttempt> {
    // Verificar autenticação
    await requireAuth();

    // Verificar se o userId foi fornecido
    if (!userId) {
      throw new Error('ID do usuário é obrigatório.');
    }

    // Primeiro, buscar o quiz
    const { data: quizData, error: quizError } = await supabase
      .from('quizzes')
      .select('id, content')
      .eq('task_id', taskId)
      .eq('block_id', blockId)
      .single();

    if (quizError) {
      throw new Error(`Quiz não encontrado: ${quizError.message}`);
    }

    // Verificar tentativas anteriores
    const { data: previousAttempts, error: attemptsError } = await supabase
      .from('quiz_attempts')
      .select('attempt_number')
      .eq('quiz_id', quizData.id)
      .eq('user_id', userId)
      .order('attempt_number', { ascending: false })
      .limit(1);

    if (attemptsError) {
      throw new Error(`Erro ao verificar tentativas: ${attemptsError.message}`);
    }

    const attemptNumber = (previousAttempts?.[0]?.attempt_number || 0) + 1;
    const quizContent = quizData.content as QuizContent;
    
    // Verificar se pode fazer nova tentativa
    if (quizContent.config.maxAttempts !== -1 && attemptNumber > quizContent.config.maxAttempts) {
      throw new Error('Número máximo de tentativas excedido');
    }

    const maxScore = quizContent.questions.reduce((sum, q) => sum + q.points, 0);

    const newAttempt: Omit<QuizAttempt, 'id'> = {
      userId,
      quizId: quizData.id,
      attemptNumber,
      startedAt: new Date(),
      timeSpent: 0,
      score: 0,
      maxScore,
      percentage: 0,
      passed: false,
      answers: [],
      status: 'draft'
    };

    const { data: attemptData, error: attemptError } = await supabase
      .from('quiz_attempts')
      .insert({
        quiz_id: quizData.id,
        user_id: userId,
        attempt_number: attemptNumber,
        max_score: maxScore,
        status: 'draft'
      })
      .select()
      .single();

    if (attemptError) {
      throw new Error(`Erro ao iniciar tentativa: ${attemptError.message}`);
    }

    return {
      id: attemptData.id,
      ...newAttempt
    };
  }

  // Salvar resposta individual
  static async saveAnswer(attemptId: string, answer: QuizAnswer): Promise<void> {
    const answerData = {
      attempt_id: attemptId,
      question_id: answer.questionId,
      question_type: answer.questionType,
      selected_options: answer.selectedOptions,
      boolean_answer: answer.booleanAnswer,
      text_answer: answer.textAnswer,
      ordered_items: answer.orderedItems,
      matched_pairs: answer.matchedPairs,
      time_spent: answer.timeSpent,
      is_correct: answer.isCorrect,
      points_earned: answer.pointsEarned,
      feedback: answer.feedback
    };

    const { error } = await supabase
      .from('quiz_answers')
      .upsert(answerData, {
        onConflict: 'attempt_id,question_id'
      });

    if (error) {
      throw new Error(`Erro ao salvar resposta: ${error.message}`);
    }
  }

  // Carregar respostas de uma tentativa
  static async loadAnswers(attemptId: string): Promise<QuizAnswer[]> {
    const { data, error } = await supabase
      .from('quiz_answers')
      .select('*')
      .eq('attempt_id', attemptId)
      .order('created_at');

    if (error) {
      throw new Error(`Erro ao carregar respostas: ${error.message}`);
    }

    return data.map(row => ({
      questionId: row.question_id,
      questionType: row.question_type,
      selectedOptions: row.selected_options,
      booleanAnswer: row.boolean_answer,
      textAnswer: row.text_answer,
      orderedItems: row.ordered_items,
      matchedPairs: row.matched_pairs,
      timeSpent: row.time_spent,
      isCorrect: row.is_correct,
      pointsEarned: row.points_earned,
      feedback: row.feedback
    }));
  }

  // Finalizar tentativa
  static async submitAttempt(attemptId: string, answers: QuizAnswer[], timeSpent: number): Promise<QuizAttempt> {
    // Calcular pontuação total
    const totalScore = answers.reduce((sum, answer) => sum + answer.pointsEarned, 0);
    
    // Buscar dados da tentativa para calcular porcentagem
    const { data: attemptData, error: attemptError } = await supabase
      .from('quiz_attempts')
      .select('max_score, quiz_id, user_id')
      .eq('id', attemptId)
      .single();

    if (attemptError) {
      throw new Error(`Erro ao buscar tentativa: ${attemptError.message}`);
    }

    const percentage = attemptData.max_score > 0 ? (totalScore / attemptData.max_score) * 100 : 0;
    
    // Buscar configuração do quiz para verificar aprovação
    const { data: quizData, error: quizError } = await supabase
      .from('quizzes')
      .select('content')
      .eq('id', attemptData.quiz_id)
      .single();

    if (quizError) {
      throw new Error(`Erro ao buscar configuração do quiz: ${quizError.message}`);
    }

    const quizContent = quizData.content as QuizContent;

    // Lógica de aprovação baseada no modo do quiz
    let passed = false;
    if (QuizUtils.isSurveyMode(quizContent.config)) {
      // Pesquisas sempre são "aprovadas" (participação completa)
      passed = true;
    } else {
      // Avaliações: usar nota mínima configurada
      passed = percentage >= quizContent.config.passingScore;
    }

    // Atualizar tentativa
    const { data: updatedAttempt, error: updateError } = await supabase
      .from('quiz_attempts')
      .update({
        submitted_at: new Date().toISOString(),
        time_spent: timeSpent,
        score: totalScore,
        percentage,
        passed,
        status: 'graded'
      })
      .eq('id', attemptId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Erro ao finalizar tentativa: ${updateError.message}`);
    }

    // Atualizar progresso do usuário
    await this.updateUserProgress(attemptData.quiz_id, attemptData.user_id, totalScore, percentage, passed);

    // Carregar respostas atualizadas
    const finalAnswers = await this.loadAnswers(attemptId);

    return {
      id: attemptId,
      userId: attemptData.user_id,
      quizId: attemptData.quiz_id,
      attemptNumber: updatedAttempt.attempt_number,
      startedAt: new Date(updatedAttempt.started_at),
      submittedAt: new Date(updatedAttempt.submitted_at),
      timeSpent: updatedAttempt.time_spent,
      score: updatedAttempt.score,
      maxScore: updatedAttempt.max_score,
      percentage: updatedAttempt.percentage,
      passed: updatedAttempt.passed,
      answers: finalAnswers,
      status: 'graded'
    };
  }

  // Atualizar progresso do usuário
  private static async updateUserProgress(
    quizId: string, 
    userId: string, 
    score: number, 
    percentage: number, 
    passed: boolean
  ): Promise<void> {
    const { data: existingProgress, error: progressError } = await supabase
      .from('user_quiz_progress')
      .select('*')
      .eq('quiz_id', quizId)
      .eq('user_id', userId)
      .single();

    if (progressError && progressError.code !== 'PGRST116') {
      throw new Error(`Erro ao buscar progresso: ${progressError.message}`);
    }

    const now = new Date().toISOString();

    if (existingProgress) {
      // Atualizar progresso existente
      const { error: updateError } = await supabase
        .from('user_quiz_progress')
        .update({
          total_attempts: existingProgress.total_attempts + 1,
          best_score: Math.max(existingProgress.best_score, score),
          best_percentage: Math.max(existingProgress.best_percentage, percentage),
          passed: existingProgress.passed || passed,
          last_attempt_at: now
        })
        .eq('id', existingProgress.id);

      if (updateError) {
        throw new Error(`Erro ao atualizar progresso: ${updateError.message}`);
      }
    } else {
      // Criar novo progresso
      const { error: insertError } = await supabase
        .from('user_quiz_progress')
        .insert({
          user_id: userId,
          quiz_id: quizId,
          total_attempts: 1,
          best_score: score,
          best_percentage: percentage,
          passed,
          first_attempt_at: now,
          last_attempt_at: now
        });

      if (insertError) {
        throw new Error(`Erro ao criar progresso: ${insertError.message}`);
      }
    }
  }

  // Carregar progresso do usuário
  static async getUserProgress(taskId: string, blockId: string, userId: string): Promise<UserQuizProgress | null> {
    // Verificar se o usuário está autenticado
    if (!userId) {
      throw new Error('Usuário não autenticado. Faça login para continuar.');
    }

    try {
      const { data, error } = await supabase
        .from('user_quiz_progress')
        .select(`
          *,
          quiz:quizzes!inner(task_id, block_id)
        `)
        .eq('user_id', userId)
        .eq('quiz.task_id', taskId)
        .eq('quiz.block_id', blockId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Progresso não encontrado
        }
        handleSupabaseError(error, 'getUserProgress');
      }
    } catch (error: any) {
      handleSupabaseError(error, 'getUserProgress');
    }

    // Carregar tentativas do usuário
    const { data: attemptsData, error: attemptsError } = await supabase
      .from('quiz_attempts')
      .select('*')
      .eq('quiz_id', data.quiz_id)
      .eq('user_id', userId)
      .order('attempt_number');

    if (attemptsError) {
      throw new Error(`Erro ao carregar tentativas: ${attemptsError.message}`);
    }

    const attempts: QuizAttempt[] = await Promise.all(
      attemptsData.map(async (attempt) => {
        const answers = await this.loadAnswers(attempt.id);
        return {
          id: attempt.id,
          userId: attempt.user_id,
          quizId: attempt.quiz_id,
          attemptNumber: attempt.attempt_number,
          startedAt: new Date(attempt.started_at),
          submittedAt: attempt.submitted_at ? new Date(attempt.submitted_at) : undefined,
          timeSpent: attempt.time_spent,
          score: attempt.score,
          maxScore: attempt.max_score,
          percentage: attempt.percentage,
          passed: attempt.passed,
          answers,
          status: attempt.status
        };
      })
    );

    return {
      userId: data.user_id,
      quizId: data.quiz_id,
      attempts,
      bestScore: data.best_score,
      bestPercentage: data.best_percentage,
      totalAttempts: data.total_attempts,
      passed: data.passed,
      lastAttemptAt: new Date(data.last_attempt_at)
    };
  }

  // Carregar estatísticas do quiz
  static async getQuizStatistics(taskId: string, blockId: string): Promise<QuizStatistics | null> {
    const { data, error } = await supabase
      .from('quiz_statistics')
      .select(`
        *,
        quiz:quizzes!inner(task_id, block_id)
      `)
      .eq('quiz.task_id', taskId)
      .eq('quiz.block_id', blockId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Estatísticas não encontradas
      }
      throw new Error(`Erro ao carregar estatísticas: ${error.message}`);
    }

    return {
      quizId: data.quiz_id,
      totalAttempts: data.total_attempts,
      uniqueUsers: data.unique_users,
      averageScore: data.average_score,
      passRate: data.pass_rate,
      averageTimeSpent: data.average_time_spent,
      questionStats: data.question_stats || []
    };
  }

  // Carregar estatísticas de pesquisa (survey) com dados reais
  static async getSurveyResults(taskId: string, blockId: string): Promise<SurveyResults | null> {
    try {
      // 1. Buscar o quiz
      const quizData = await this.loadQuiz(taskId, blockId);
      if (!quizData) {
        console.warn('Quiz não encontrado para buscar estatísticas de survey');
        return null;
      }

      // 2. Buscar todas as tentativas finalizadas
      const { data: attempts, error: attemptsError } = await supabase
        .from('quiz_attempts')
        .select('id, user_id, submitted_at')
        .eq('quiz_id', quizData.id)
        .eq('status', 'graded')
        .not('submitted_at', 'is', null);

      if (attemptsError) {
        throw new Error(`Erro ao buscar tentativas: ${attemptsError.message}`);
      }

      if (!attempts || attempts.length === 0) {
        console.log('Nenhuma tentativa encontrada para o survey');
        return null;
      }

      // 3. Buscar todas as respostas das tentativas
      const attemptIds = attempts.map(a => a.id);
      const { data: answers, error: answersError } = await supabase
        .from('quiz_answers')
        .select('*')
        .in('attempt_id', attemptIds);

      if (answersError) {
        throw new Error(`Erro ao buscar respostas: ${answersError.message}`);
      }

      // 4. Processar estatísticas por pergunta
      const questionResults = quizData.content.questions.map(question => {
        const questionAnswers = answers?.filter(a => a.question_id === question.id) || [];

        const result: SurveyQuestionResult = {
          questionId: question.id,
          questionTitle: question.title,
          questionType: question.type,
          totalResponses: questionAnswers.length
        };

        // Processar por tipo de pergunta
        switch (question.type) {
          case 'single-choice':
          case 'multiple-choice':
            if (question.options) {
              result.optionStats = question.options.map(option => {
                const optionResponses = questionAnswers.filter(answer =>
                  answer.selected_options?.includes(option.id)
                );
                const responseCount = optionResponses.length;

                return {
                  optionId: option.id,
                  optionText: option.text,
                  responseCount,
                  percentage: questionAnswers.length > 0 ? (responseCount / questionAnswers.length) * 100 : 0,
                  surveyValue: option.surveyValue
                };
              });
            }
            break;

          case 'true-false':
            const trueResponses = questionAnswers.filter(a => a.boolean_answer === true);
            const falseResponses = questionAnswers.filter(a => a.boolean_answer === false);

            result.booleanStats = {
              trueCount: trueResponses.length,
              falseCount: falseResponses.length,
              truePercentage: questionAnswers.length > 0 ? (trueResponses.length / questionAnswers.length) * 100 : 0,
              falsePercentage: questionAnswers.length > 0 ? (falseResponses.length / questionAnswers.length) * 100 : 0
            };
            break;

          case 'open-text':
            result.textResponses = questionAnswers
              .filter(a => a.text_answer && a.text_answer.trim() !== '')
              .map(a => ({
                response: a.text_answer || '',
                timestamp: new Date(a.created_at || Date.now())
              }))
              .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()); // Mais recentes primeiro
            break;
        }

        return result;
      });

      // 5. Retornar resultado final
      return {
        quizId: quizData.id,
        totalResponses: attempts.length,
        collectedAt: new Date(),
        questionResults
      };

    } catch (error: any) {
      console.error('Erro ao carregar estatísticas de survey:', error);
      return null;
    }
  }

  // Obter todas as tentativas do usuário para um quiz específico
  static async getUserAttempts(taskId: string, blockId: string, userId: string): Promise<QuizAttempt[]> {
    // Verificar se o usuário está autenticado
    if (!userId) {
      throw new Error('Usuário não autenticado. Faça login para continuar.');
    }

    try {
      // Buscar quiz para obter o ID
      const { data: quizData, error: quizError } = await supabase
        .from('quizzes')
        .select('id')
        .eq('task_id', taskId)
        .eq('block_id', blockId)
        .single();

      if (quizError) {
        if (quizError.code === 'PGRST116') {
          return []; // Quiz não encontrado
        }
        throw new Error(`Erro ao buscar quiz: ${quizError.message}`);
      }

      // Buscar todas as tentativas do usuário para este quiz
      const { data: attempts, error: attemptsError } = await supabase
        .from('quiz_attempts')
        .select(`
          id,
          user_id,
          quiz_id,
          attempt_number,
          started_at,
          submitted_at,
          time_spent,
          score,
          max_score,
          percentage,
          passed,
          status
        `)
        .eq('quiz_id', quizData.id)
        .eq('user_id', userId)
        .order('attempt_number', { ascending: true });

      if (attemptsError) {
        throw new Error(`Erro ao buscar tentativas: ${attemptsError.message}`);
      }

      // Converter para formato QuizAttempt e carregar respostas
      const quizAttempts: QuizAttempt[] = [];

      for (const attempt of attempts || []) {
        // Carregar respostas da tentativa
        const { data: answers, error: answersError } = await supabase
          .from('quiz_answers')
          .select('*')
          .eq('attempt_id', attempt.id);

        if (answersError) {
          console.warn(`Erro ao carregar respostas da tentativa ${attempt.id}:`, answersError);
        }

        const quizAttempt: QuizAttempt = {
          id: attempt.id,
          userId: attempt.user_id,
          quizId: attempt.quiz_id,
          attemptNumber: attempt.attempt_number,
          startedAt: new Date(attempt.started_at),
          submittedAt: attempt.submitted_at ? new Date(attempt.submitted_at) : undefined,
          completedAt: attempt.submitted_at ? new Date(attempt.submitted_at) : undefined,
          timeSpent: attempt.time_spent || 0,
          score: attempt.score || 0,
          maxScore: attempt.max_score || 0,
          totalScore: attempt.max_score || 0,
          percentage: attempt.percentage || 0,
          passed: attempt.passed || false,
          status: attempt.status as any,
          answers: answers?.map(answer => ({
            questionId: answer.question_id,
            selectedOptions: answer.selected_options || [],
            textAnswer: answer.text_answer,
            isCorrect: answer.is_correct || false,
            pointsEarned: answer.points_earned || 0,
            timeSpent: answer.time_spent || 0
          })) || []
        };

        quizAttempts.push(quizAttempt);
      }

      return quizAttempts;
    } catch (error: any) {
      handleSupabaseError(error, 'getUserAttempts');
      return [];
    }
  }

  // Verificar se usuário pode fazer nova tentativa
  static async canUserAttempt(taskId: string, blockId: string, userId: string): Promise<{
    canAttempt: boolean;
    reason?: string;
    remainingAttempts?: number;
  }> {
    // Verificar se o usuário está autenticado
    if (!userId) {
      return {
        canAttempt: false,
        reason: 'Usuário não autenticado. Faça login para continuar.'
      };
    }

    try {
      const quizContent = await this.loadQuiz(taskId, blockId);
      if (!quizContent) {
        return { canAttempt: false, reason: 'Quiz não encontrado' };
      }

      const userProgress = await this.getUserProgress(taskId, blockId, userId);

      // Se não há progresso, pode tentar
      if (!userProgress) {
        return {
          canAttempt: true,
          remainingAttempts: quizContent.config.maxAttempts === -1 ? Infinity : quizContent.config.maxAttempts
        };
      }

      // Verificar se já passou e não permite retry
      if (userProgress.passed && !quizContent.config.allowRetry) {
        return { canAttempt: false, reason: 'Quiz já foi aprovado' };
      }

      // Verificar número máximo de tentativas
      if (quizContent.config.maxAttempts !== -1 && userProgress.totalAttempts >= quizContent.config.maxAttempts) {
        return { canAttempt: false, reason: 'Número máximo de tentativas excedido' };
      }

      const remainingAttempts = quizContent.config.maxAttempts === -1
        ? Infinity
        : quizContent.config.maxAttempts - userProgress.totalAttempts;

      return { canAttempt: true, remainingAttempts };
    } catch (error: any) {
      // Se tabelas não existem, permitir tentativa (modo local)
      if (error.message === 'TABLES_NOT_FOUND') {
        console.warn('Modo local: permitindo tentativa sem verificação de progresso');
        return { canAttempt: true, remainingAttempts: Infinity };
      }
      throw error;
    }
  }
}

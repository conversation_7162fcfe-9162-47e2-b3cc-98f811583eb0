-- =====================================================
-- CORREÇÃO RÁPIDA: CRIAR VIEW STAGE_RESPONSIBLES_WITH_PROFILE
-- =====================================================
-- Esta view é necessária para o frontend funcionar corretamente
-- Erro: GET https://...supabase.co/rest/v1/stage_responsibles_with_profile 404 (Not Found)

-- VIEW: Responsáveis de estágios com perfil
CREATE OR REPLACE VIEW public.stage_responsibles_with_profile AS
SELECT
  sr.id,
  sr.stage_id,
  sr.user_id,
  sr.assigned_at,
  p.name as profile_name,
  p.email as profile_email,
  p.avatar_url as profile_avatar_url
FROM public.stage_responsibles sr
JOIN public.profiles p ON sr.user_id = p.id;

-- Verificar se a view foi criada com sucesso
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_schema = 'public' AND table_name = 'stage_responsibles_with_profile') THEN
    RAISE NOTICE '✅ View stage_responsibles_with_profile criada com sucesso!';
  ELSE
    RAISE EXCEPTION '❌ Falha ao criar view stage_responsibles_with_profile';
  END IF;
END $$;

-- Testar a view com uma consulta simples
SELECT 'View criada e funcionando!' as status, COUNT(*) as total_records
FROM public.stage_responsibles_with_profile;

-- =====================================================
-- APLICAÇÃO IMEDIATA: Correção das políticas RLS
-- Execute este script no SQL Editor do Supabase AGORA
-- =====================================================

-- Remover política atual que NÃO inclui executores
DROP POLICY IF EXISTS "Project members can view task content blocks" ON public.task_content_blocks;

-- Criar nova política que INCLUI executores
CREATE POLICY "Project members can view task content blocks"
  ON public.task_content_blocks
  FOR SELECT
  USING (
    -- Proprietários e membros do projeto podem ver (mantido)
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
    )
    OR
    -- *** NOVA CONDIÇÃO: Executores da tarefa podem ver ***
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
    OR
    -- *** NOVA CONDIÇÃO: Aprovadores da tarefa podem ver ***
    task_id IN (
      SELECT ta.task_id FROM public.task_approvers ta
      WHERE ta.user_id = auth.uid()
    )
  );

-- Verificar se a política foi criada corretamente
SELECT 
    'VERIFICAÇÃO' as resultado,
    policyname,
    cmd,
    'Política atualizada com sucesso!' as status
FROM pg_policies
WHERE schemaname = 'public' 
    AND tablename = 'task_content_blocks'
    AND policyname = 'Project members can view task content blocks';

-- =====================================================
-- APÓS EXECUTAR ESTE SCRIPT:
-- 1. Recarregue a página da aplicação (F5)
-- 2. Verifique os logs no console
-- 3. Deve aparecer: contentBlocks result: {data: Array(1+), error: null}
-- 4. A aba "Executar" deve mostrar o conteúdo da tarefa
-- =====================================================

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Seletor de Cores na Barra de Ferramentas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .toolbar-demo {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .toolbar-button {
            padding: 8px;
            border: none;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        .toolbar-button:hover {
            background: #e9ecef;
        }
        .color-indicator {
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 16px;
            height: 3px;
            border-radius: 1px;
        }
        .color-picker {
            position: absolute;
            top: 100%;
            left: 0;
            margin-top: 4px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 50;
            padding: 16px;
            width: 256px;
            display: none;
        }
        .color-picker.show {
            display: block;
        }
        .hex-input {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        .hex-input input {
            flex: 1;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .color-preview {
            width: 100%;
            height: 32px;
            border-radius: 4px;
            border: 1px solid #ddd;
            margin-bottom: 12px;
        }
        .color-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 4px;
            margin-bottom: 12px;
        }
        .color-swatch {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid #ccc;
            cursor: pointer;
            transition: transform 0.1s;
        }
        .color-swatch:hover {
            transform: scale(1.1);
        }
        .close-button {
            padding: 4px 12px;
            background: #f1f3f4;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .close-button:hover {
            background: #e8eaed;
        }
        .separator {
            width: 1px;
            height: 24px;
            background: #ddd;
            margin: 0 8px;
        }
        .editor-demo {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 16px;
            min-height: 200px;
            background: white;
            font-size: 16px;
            line-height: 1.5;
        }
        .instructions {
            background: #e3f2fd;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .instructions ul {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🎨 Demo - Seletor de Cores na Barra de Ferramentas</h1>
        
        <div class="instructions">
            <h3>📋 Instruções de Teste</h3>
            <ul>
                <li><strong>Cor da Fonte:</strong> Clique no ícone de paleta para alterar a cor do texto</li>
                <li><strong>Cor do Fundo:</strong> Clique no ícone de marcador para alterar a cor de fundo do texto</li>
                <li><strong>Seletor de Cores:</strong> Use a paleta de cores predefinidas ou digite um código hex</li>
                <li><strong>Aplicação:</strong> Selecione o texto no editor abaixo e use os botões de cor</li>
            </ul>
        </div>

        <div class="toolbar-demo">
            <!-- Botões de formatação básica -->
            <button class="toolbar-button" title="Negrito">𝐁</button>
            <button class="toolbar-button" title="Itálico">𝐼</button>
            <button class="toolbar-button" title="Sublinhado">𝐔</button>
            
            <div class="separator"></div>
            
            <!-- Botão de cor da fonte -->
            <div style="position: relative;">
                <button class="toolbar-button" id="fontColorBtn" title="Cor da fonte">
                    <div style="position: relative;">
                        🎨
                        <div class="color-indicator" id="fontColorIndicator" style="background-color: #000000;"></div>
                    </div>
                </button>
                <div class="color-picker" id="fontColorPicker">
                    <div class="hex-input">
                        <span style="font-weight: 500; font-size: 14px;">Hex</span>
                        <input type="text" id="fontHexInput" value="#000000" placeholder="#000000">
                    </div>
                    <div class="color-preview" id="fontColorPreview" style="background-color: #000000;"></div>
                    <div class="color-grid" id="fontColorGrid"></div>
                    <div style="text-align: right;">
                        <button class="close-button" onclick="closeFontColorPicker()">Fechar</button>
                    </div>
                </div>
            </div>
            
            <!-- Botão de cor do fundo -->
            <div style="position: relative;">
                <button class="toolbar-button" id="backgroundColorBtn" title="Cor do fundo">
                    <div style="position: relative;">
                        🖍️
                        <div class="color-indicator" id="backgroundColorIndicator" style="background-color: #ffffff;"></div>
                    </div>
                </button>
                <div class="color-picker" id="backgroundColorPicker">
                    <div class="hex-input">
                        <span style="font-weight: 500; font-size: 14px;">Hex</span>
                        <input type="text" id="backgroundHexInput" value="#ffffff" placeholder="#ffffff">
                    </div>
                    <div class="color-preview" id="backgroundColorPreview" style="background-color: #ffffff;"></div>
                    <div class="color-grid" id="backgroundColorGrid"></div>
                    <div style="text-align: right;">
                        <button class="close-button" onclick="closeBackgroundColorPicker()">Fechar</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="editor-demo" contenteditable="true" id="editor">
            <p>Este é um texto de exemplo para testar as funcionalidades de cor.</p>
            <p>Selecione este texto e use os botões de cor da fonte e cor do fundo na barra de ferramentas acima.</p>
            <p>Você pode alterar a <span style="color: #ff0000;">cor da fonte</span> e a <span style="background-color: #ffff00;">cor do fundo</span> do texto selecionado.</p>
        </div>
    </div>

    <script>
        const predefinedColors = [
            '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#efefef', '#f3f3f3', '#ffffff',
            '#980000', '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#4a86e8', '#0000ff', '#9900ff', '#ff00ff',
            '#e6b8af', '#f4cccc', '#fce5cd', '#fff2cc', '#d9ead3', '#d0e0e3', '#c9daf8', '#cfe2f3', '#d9d2e9', '#ead1dc',
            '#dd7e6b', '#ea9999', '#f9cb9c', '#ffe599', '#b6d7a8', '#a2c4c9', '#a4c2f4', '#9fc5e8', '#b4a7d6', '#d5a6bd',
            '#cc4125', '#e06666', '#f6b26b', '#ffd966', '#93c47d', '#76a5af', '#6d9eeb', '#6fa8dc', '#8e7cc3', '#c27ba0',
            '#a61c00', '#cc0000', '#e69138', '#f1c232', '#6aa84f', '#45818e', '#3c78d8', '#3d85c6', '#674ea7', '#a64d79',
            '#85200c', '#990000', '#b45f06', '#bf9000', '#38761d', '#134f5c', '#1155cc', '#0b5394', '#351c75', '#741b47',
            '#5b0f00', '#660000', '#783f04', '#7f6000', '#274e13', '#0c343d', '#1c4587', '#073763', '#20124d', '#4c1130'
        ];

        let currentFontColor = '#000000';
        let currentBackgroundColor = '#ffffff';

        // Inicializar grids de cores
        function initColorGrids() {
            const fontGrid = document.getElementById('fontColorGrid');
            const backgroundGrid = document.getElementById('backgroundColorGrid');
            
            predefinedColors.forEach(color => {
                // Grid de cor da fonte
                const fontSwatch = document.createElement('div');
                fontSwatch.className = 'color-swatch';
                fontSwatch.style.backgroundColor = color;
                fontSwatch.title = color;
                fontSwatch.onclick = () => setFontColor(color);
                fontGrid.appendChild(fontSwatch);
                
                // Grid de cor do fundo
                const backgroundSwatch = document.createElement('div');
                backgroundSwatch.className = 'color-swatch';
                backgroundSwatch.style.backgroundColor = color;
                backgroundSwatch.title = color;
                backgroundSwatch.onclick = () => setBackgroundColor(color);
                backgroundGrid.appendChild(backgroundSwatch);
            });
        }

        // Funções de cor da fonte
        function setFontColor(color) {
            currentFontColor = color;
            document.getElementById('fontColorIndicator').style.backgroundColor = color;
            document.getElementById('fontColorPreview').style.backgroundColor = color;
            document.getElementById('fontHexInput').value = color;
            applyFontColor(color);
            closeFontColorPicker();
        }

        function applyFontColor(color) {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                if (!range.collapsed) {
                    const span = document.createElement('span');
                    span.style.color = color;
                    try {
                        range.surroundContents(span);
                    } catch (e) {
                        span.appendChild(range.extractContents());
                        range.insertNode(span);
                    }
                    selection.removeAllRanges();
                }
            }
        }

        // Funções de cor do fundo
        function setBackgroundColor(color) {
            currentBackgroundColor = color;
            document.getElementById('backgroundColorIndicator').style.backgroundColor = color;
            document.getElementById('backgroundColorPreview').style.backgroundColor = color;
            document.getElementById('backgroundHexInput').value = color;
            applyBackgroundColor(color);
            closeBackgroundColorPicker();
        }

        function applyBackgroundColor(color) {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                if (!range.collapsed) {
                    const span = document.createElement('span');
                    span.style.backgroundColor = color;
                    try {
                        range.surroundContents(span);
                    } catch (e) {
                        span.appendChild(range.extractContents());
                        range.insertNode(span);
                    }
                    selection.removeAllRanges();
                }
            }
        }

        // Controle dos seletores de cor
        function toggleFontColorPicker() {
            const picker = document.getElementById('fontColorPicker');
            const backgroundPicker = document.getElementById('backgroundColorPicker');
            backgroundPicker.classList.remove('show');
            picker.classList.toggle('show');
        }

        function toggleBackgroundColorPicker() {
            const picker = document.getElementById('backgroundColorPicker');
            const fontPicker = document.getElementById('fontColorPicker');
            fontPicker.classList.remove('show');
            picker.classList.toggle('show');
        }

        function closeFontColorPicker() {
            document.getElementById('fontColorPicker').classList.remove('show');
        }

        function closeBackgroundColorPicker() {
            document.getElementById('backgroundColorPicker').classList.remove('show');
        }

        // Event listeners
        document.getElementById('fontColorBtn').onclick = toggleFontColorPicker;
        document.getElementById('backgroundColorBtn').onclick = toggleBackgroundColorPicker;

        // Input hex listeners
        document.getElementById('fontHexInput').addEventListener('input', function(e) {
            const value = e.target.value;
            if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
                setFontColor(value);
            }
        });

        document.getElementById('backgroundHexInput').addEventListener('input', function(e) {
            const value = e.target.value;
            if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
                setBackgroundColor(value);
            }
        });

        // Fechar ao clicar fora
        document.addEventListener('click', function(e) {
            const fontPicker = document.getElementById('fontColorPicker');
            const backgroundPicker = document.getElementById('backgroundColorPicker');
            const fontBtn = document.getElementById('fontColorBtn');
            const backgroundBtn = document.getElementById('backgroundColorBtn');
            
            if (!fontPicker.contains(e.target) && !fontBtn.contains(e.target)) {
                closeFontColorPicker();
            }
            if (!backgroundPicker.contains(e.target) && !backgroundBtn.contains(e.target)) {
                closeBackgroundColorPicker();
            }
        });

        // Inicializar
        initColorGrids();
    </script>
</body>
</html>
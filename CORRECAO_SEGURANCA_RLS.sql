-- CORREÇÃO DE SEGURANÇA: Implementar RLS adequado para MyTasks

-- 1. Reabilitar RLS na tabela tasks
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- 2. <PERSON><PERSON><PERSON> pol<PERSON><PERSON> RLS seguras para tasks
CREATE POLICY "Users can view tasks where they are executors" ON tasks
    FOR SELECT USING (
        id IN (
            SELECT task_id FROM task_executors 
            WHERE user_id = auth.uid()
        )
    );

-- 3. <PERSON><PERSON><PERSON> pol<PERSON><PERSON> RLS para task_executors
CREATE POLICY "Users can view their own executor assignments" ON task_executors
    FOR SELECT USING (user_id = auth.uid());

-- 4. <PERSON><PERSON>r política RLS para stages (se necessário)
CREATE POLICY "Users can view stages of their tasks" ON stages
    FOR SELECT USING (
        id IN (
            SELECT stage_id FROM tasks 
            WHERE id IN (
                SELECT task_id FROM task_executors 
                WHERE user_id = auth.uid()
            )
        )
    );

-- 5. <PERSON><PERSON><PERSON> <PERSON>ol<PERSON><PERSON> RLS para projects (se necessário)
CREATE POLICY "Users can view projects of their tasks" ON projects
    FOR SELECT USING (
        id IN (
            SELECT project_id FROM stages 
            WHERE id IN (
                SELECT stage_id FROM tasks 
                WHERE id IN (
                    SELECT task_id FROM task_executors 
                    WHERE user_id = auth.uid()
                )
            )
        )
    );

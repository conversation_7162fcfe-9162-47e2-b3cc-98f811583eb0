-- Diagnóstico simplificado para verificar dados no sistema
-- Execute este SQL no Supabase SQL Editor

-- 1. Verificar quantas tarefas existem no sistema
SELECT 
    'Total de tarefas' as info,
    COUNT(*) as quantidade
FROM tasks;

-- 2. Verificar quantos executores existem
SELECT 
    'Total de executores' as info,
    COUNT(*) as quantidade
FROM task_executors;

-- 3. Verificar usuários e perfis
SELECT 
    'Usuários com perfil' as info,
    COUNT(*) as quantidade
FROM auth.users u
JOIN profiles p ON u.id = p.id;

-- 4. Verificar estrutura da tabela task_executors
SELECT 
    'Estrutura task_executors' as info,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'task_executors' 
AND table_schema = 'public';

-- 5. Verificar se há alguma tarefa com executor
SELECT 
    'Tarefas com executor' as info,
    t.title,
    u.email as executor_email,
    te.created_at
FROM task_executors te
JOIN tasks t ON te.task_id = t.id
JOIN auth.users u ON te.user_id = u.id
LIMIT 5;

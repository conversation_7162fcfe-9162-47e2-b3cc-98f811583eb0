import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Header } from '@/components/layout/Header';
import { 
  ArrowLeft, 
  Settings,
  Monitor,
  Sparkles,
  Eye,
  TestTube
} from 'lucide-react';

/**
 * Componente de teste para verificar o sistema de versões
 */
export const TaskVersionTest = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();

  const testUrls = [
    {
      label: 'Primeira visita (sem versão salva)',
      url: `/task/${taskId}`,
      description: 'Deve mostrar interface de seleção automaticamente',
      action: () => {
        // Limpar localStorage para simular primeira visita
        localStorage.removeItem('taskDetailsVersion');
        navigate(`/task/${taskId}`);
      }
    },
    {
      label: '<PERSON><PERSON><PERSON> seletor via URL',
      url: `/task/${taskId}?selector=true`,
      description: 'Força a exibição da interface de seleção',
      action: () => navigate(`/task/${taskId}?selector=true`)
    },
    {
      label: 'Versão V1 direta',
      url: `/task/${taskId}?version=v1`,
      description: 'Vai direto para a versão atual',
      action: () => navigate(`/task/${taskId}?version=v1`)
    },
    {
      label: 'Versão V2 direta',
      url: `/task/${taskId}?version=v2`,
      description: 'Vai direto para a versão melhorada',
      action: () => navigate(`/task/${taskId}?version=v2`)
    },
    {
      label: 'Rota específica do seletor',
      url: `/task/${taskId}/version-selector`,
      description: 'Rota dedicada para o seletor',
      action: () => navigate(`/task/${taskId}/version-selector`)
    }
  ];

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <Button
                variant="ghost"
                onClick={() => navigate(-1)}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
              <div className="bg-blue-600 rounded-full p-3 mr-4">
                <TestTube className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900">
                Teste de Versões - TaskDetails
              </h1>
            </div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Use os botões abaixo para testar diferentes cenários de acesso às versões da tela de detalhes da tarefa.
            </p>
          </div>

          {/* Informações da Tarefa */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Informações do Teste</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Task ID:</h4>
                  <p className="text-gray-600 font-mono bg-gray-100 px-2 py-1 rounded">{taskId}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Versão Salva:</h4>
                  <p className="text-gray-600 font-mono bg-gray-100 px-2 py-1 rounded">
                    {localStorage.getItem('taskDetailsVersion') || 'Nenhuma'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cenários de Teste */}
          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Cenários de Teste</h2>
            
            {testUrls.map((test, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {test.label}
                      </h3>
                      <p className="text-gray-600 mb-2">{test.description}</p>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded text-blue-600">
                        {test.url}
                      </code>
                    </div>
                    <Button
                      onClick={test.action}
                      className="ml-4"
                      variant="outline"
                    >
                      Testar
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Ações Adicionais */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Ações de Teste</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    localStorage.removeItem('taskDetailsVersion');
                    window.location.reload();
                  }}
                  className="w-full"
                >
                  Limpar Preferência Salva
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => {
                    localStorage.setItem('taskDetailsVersion', 'v2');
                    window.location.reload();
                  }}
                  className="w-full"
                >
                  Definir V2 como Padrão
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log('localStorage:', localStorage.getItem('taskDetailsVersion'));
                    console.log('URL params:', new URLSearchParams(window.location.search).toString());
                  }}
                  className="w-full"
                >
                  Debug no Console
                </Button>
                
                <Button
                  onClick={() => navigate(`/task/${taskId}?selector=true`)}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  Ir para Seletor
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Instruções */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Como Testar</CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="list-decimal list-inside space-y-2 text-gray-700">
                <li>Use "Limpar Preferência Salva" para simular primeira visita</li>
                <li>Clique em "Primeira visita" - deve mostrar interface de seleção</li>
                <li>Teste "Forçar seletor via URL" - sempre mostra seleção</li>
                <li>Teste versões diretas V1 e V2</li>
                <li>Verifique se o botão flutuante aparece nas telas de tarefa</li>
                <li>Use "Debug no Console" para verificar estado atual</li>
              </ol>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

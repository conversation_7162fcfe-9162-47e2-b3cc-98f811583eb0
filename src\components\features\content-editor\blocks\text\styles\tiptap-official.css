/* ===== ESTILOS OFICIAIS TIPTAP EDITOR ===== */
/* Baseado na documentação oficial: https://tiptap.dev/docs/editor/getting-started/style-editor */

/* Editor base - largura completa */
.tiptap-editor .ProseMirror,
.tiptap-editor-content {
  outline: none;
  padding: 1rem;
  min-height: 200px;
  width: 100%;
  max-width: none;
}

/* Placeholder oficial */
.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Placeholder para diferentes tipos de nó */
.tiptap-editor .ProseMirror h1.is-empty::before,
.tiptap-editor .ProseMirror h2.is-empty::before,
.tiptap-editor .ProseMirror h3.is-empty::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Estilos de texto */
.tiptap-editor .ProseMirror {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
}

/* Títulos */
.tiptap-editor .ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.tiptap-editor .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

/* Parágrafos */
.tiptap-editor .ProseMirror p {
  margin-bottom: 1rem;
  line-height: inherit;
}

/* Altura da linha customizada */
.tiptap-editor .ProseMirror p[style*="line-height"],
.tiptap-editor .ProseMirror h1[style*="line-height"],
.tiptap-editor .ProseMirror h2[style*="line-height"],
.tiptap-editor .ProseMirror h3[style*="line-height"] {
  /* Força aplicação do line-height inline */
}

/* Formatação de texto */
.tiptap-editor .ProseMirror strong {
  font-weight: 700;
}

.tiptap-editor .ProseMirror em {
  font-style: italic;
}

.tiptap-editor .ProseMirror u {
  text-decoration: underline;
}

.tiptap-editor .ProseMirror s {
  text-decoration: line-through;
}

.tiptap-editor .ProseMirror code {
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}

/* Destaque */
.tiptap-editor .ProseMirror mark,
.tiptap-editor .ProseMirror .highlight {
  background-color: #fef08a;
  border-radius: 0.125rem;
  padding: 0.125rem 0.25rem;
}

/* Links */
.tiptap-editor .ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.tiptap-editor .ProseMirror a:hover {
  color: #1d4ed8;
}

/* Listas */
.tiptap-editor .ProseMirror ul,
.tiptap-editor .ProseMirror ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.tiptap-editor .ProseMirror li {
  margin-bottom: 0.25rem;
}

.tiptap-editor .ProseMirror li p {
  margin-bottom: 0.5rem;
}

/* Lista de tarefas */
.tiptap-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.tiptap-editor .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

/* Indentação com Tab - Listas aninhadas */
.tiptap-editor .ProseMirror ul ul,
.tiptap-editor .ProseMirror ol ol,
.tiptap-editor .ProseMirror ul ol,
.tiptap-editor .ProseMirror ol ul {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  padding-left: 1.5rem;
}

/* Indentação para task lists aninhadas */
.tiptap-editor .ProseMirror ul[data-type="taskList"] ul[data-type="taskList"] {
  margin-left: 1.5rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Espaçamento para indentação manual */
.tiptap-editor .ProseMirror .tab-indent {
  padding-left: 2rem;
}

/* Code block - preservar tabs e espaços */
.tiptap-editor .ProseMirror pre {
  white-space: pre;
  tab-size: 4;
}

.tiptap-editor .ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.tiptap-editor .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

/* Citações */
.tiptap-editor .ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

/* Blocos de código */
.tiptap-editor .ProseMirror pre {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.tiptap-editor .ProseMirror pre code {
  background: none;
  padding: 0;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Regra horizontal */
.tiptap-editor .ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

/* Alinhamento de texto */
.tiptap-editor .ProseMirror .text-left,
.tiptap-editor .ProseMirror [style*="text-align: left"] {
  text-align: left;
}

.tiptap-editor .ProseMirror .text-center,
.tiptap-editor .ProseMirror [style*="text-align: center"] {
  text-align: center;
}

.tiptap-editor .ProseMirror .text-right,
.tiptap-editor .ProseMirror [style*="text-align: right"] {
  text-align: right;
}

.tiptap-editor .ProseMirror .text-justify,
.tiptap-editor .ProseMirror [style*="text-align: justify"] {
  text-align: justify;
  text-justify: inter-word;
}

/* Seleção */
.tiptap-editor .ProseMirror .ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Estados de foco */
.tiptap-editor .ProseMirror:focus {
  outline: none;
}

/* Responsividade */
@media (max-width: 768px) {
  .tiptap-editor .ProseMirror {
    padding: 0.75rem;
    font-size: 16px; /* Evita zoom no iOS */
  }
  
  .tiptap-editor .ProseMirror h1 {
    font-size: 1.75rem;
  }
  
  .tiptap-editor .ProseMirror h2 {
    font-size: 1.375rem;
  }
  
  .tiptap-editor .ProseMirror h3 {
    font-size: 1.125rem;
  }
}

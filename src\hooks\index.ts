// Re-export all permission-related utilities for easier importing
export { 
  useProjectPermissions, 
  useGlobalPermissions, 
  useProjectRoles,
  type Permission,
  type ProjectRole,
  type GlobalRole
} from './usePermissions';

export {
  RequireProjectRole,
  RequirePermission,
  RequireGlobalRole,
  AccessDeniedCard
} from '@/components/auth/PermissionWrappers';

export {
  PROJECT_ROLES,
  getRoleInfo,
  isValidProjectRole,
  getValidProjectRoles,
  sortRolesByHierarchy,
  hasRolePrecedence,
  getHighestRole,
  formatRolesForDisplay
} from '@/utils/roleUtils';

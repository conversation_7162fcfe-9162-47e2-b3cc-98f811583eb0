/* Painel principal */
.custom-video-appearance-panel {
  background: #f6f9ff;
  border: 2px solid #bcd0ee;
  border-radius: 12px;
  box-shadow: 0 1px 4px #bcd0ee33;
  padding: 18px 20px 20px 20px;
  margin-bottom: 24px;
}

/* Header */
.custom-appearance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #e9f0ff;
  border-radius: 8px 8px 0 0;
  padding: 12px 18px 12px 18px;
  margin: -18px -20px 0 -20px;
}
.custom-appearance-title {
  font-size: 1rem;
  font-weight: bold;
  color: #222;
}

/* Tí<PERSON>lo padrão maior para seções */
.standard-title {
  font-size: 15px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
}
.custom-restore-btn {
  background: #f4f8ff;
  border: 1px solid #bcd0ee;
  border-radius: 6px;
  padding: 6px 14px;
  font-size: 13px;
  font-weight: 500;
  color: #2d3a5a;
  cursor: pointer;
  transition: background 0.15s, border 0.15s;
}
.custom-restore-btn:hover {
  background: #dbeafe;
  border-color: #60a5fa;
}
.custom-appearance-divider {
  border: none;
  border-top: 1px solid #bcd0ee;
  margin: 0 0 18px 0;
}

/* Grid layout */
.custom-appearance-body {
  padding: 0 0 0 0;
}
.custom-appearance-grid {
  display: flex;
  flex-direction: row;
  gap: 32px;
}
.appearance-left {
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 220px;
}
.appearance-right {
  display: flex;
  flex-direction: column;
  gap: 18px;
  flex: 1;
  min-width: 220px;
  justify-content: flex-start;
}

/* Labels e campos */
.custom-color-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: #444;
}
.custom-color-label.small {
  font-size: 0.95rem;
  font-weight: 400;
  color: #555;
}
.custom-switch-row {
  display: flex;
  align-items: center;
  gap: 12px;
}
.custom-switch-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 1rem;
  color: #444;
  font-weight: 500;
}
.custom-switch {
  width: 36px;
  height: 20px;
  accent-color: #60a5fa;
  margin-right: 2px;
}
.custom-format-dropdown-group {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: auto;
}
.custom-format-dropdown {
  padding: 6px 12px;
  border: 1px solid #bcd0ee;
  border-radius: 6px;
  font-size: 1rem;
  background: #f4f8ff;
  color: #2d3a5a;
  cursor: pointer;
  max-width: 170px;
  width: 100%;
}
.custom-format-label {
  font-size: 0.95rem;
  color: #555;
  margin-left: 2px;
}
.custom-slider-group {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 180px;
}
.custom-slider-label {
  font-size: 0.95rem;
  color: #555;
  min-width: 120px;
}
.custom-slider {
  width: 140px;
  accent-color: #60a5fa;
  height: 4px;
  border-radius: 2px;
  background: #e5e7eb;
  max-width: 180px;
}
@media (max-width: 700px) {
  .custom-video-appearance-panel { padding: 10px; }
  .custom-appearance-grid { flex-direction: column; gap: 10px; }
  .appearance-left, .appearance-right { min-width: 120px; }
  .custom-slider-group { min-width: 120px; }
  .format-dropdown, .slider-group, .custom-slider {
    max-width: 100%;
    width: 100%;
  }
}

.custom-appearance-grid-v2 {
  display: flex;
  flex-direction: column;
  gap: 14px;
}
.appearance-row {
  display: grid;
  grid-template-columns: 180px 1fr;
  align-items: center;
  gap: 12px;
  min-height: 44px;
}
.color-picker-group, .format-dropdown-group, .slider-group, .switch-label {
  display: flex;
  align-items: center;
  gap: 8px;
}
.color-label-inline {
  font-size: 1rem;
  color: #444;
  margin-left: 6px;
}
.format-dropdown-group, .slider-group {
  justify-self: start;
  justify-content: flex-start;
  margin-left: 0 !important;
  width: 100%;
}
.color-picker-group {
  min-width: 120px;
}
@media (max-width: 700px) {
  .appearance-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

.custom-appearance-grid-v3 {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.appearance-row {
  display: grid;
  grid-template-columns: 180px 180px 1fr;
  align-items: center;
  gap: 10px;
  min-height: 44px;
}
.color-picker-group, .format-dropdown-group, .slider-group, .switch-label {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  height: 100%;
}
@media (max-width: 700px) {
  .appearance-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

.slider-vertical-label {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  justify-content: center;
  height: 100%;
}

.slider-group {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
  gap: 4px;
  width: 100%;
}
.slider-label {
  align-self: flex-end;
  text-align: right;
}

.slider-group .custom-slider {
  width: 200px;
  max-width: 340px;
}
@media (max-width: 700px) {
  .slider-group .custom-slider {
    width: 100%;
    max-width: 100%;
  }
}

/* Padrões globais para todas as abas */
.standard-label,
.standard-label span,
span.standard-label {
  font-size: 13px !important;
  color: #374151 !important;
  font-weight: 500 !important;
}

.standard-field {
  font-size: 13px !important;
  height: 32px !important;
  padding: 6px 10px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.standard-button {
  font-size: 13px !important;
  height: 32px !important;
  padding: 6px 12px !important;
  border-radius: 6px !important;
}

.standard-checkbox {
  width: 16px !important;
  height: 16px !important;
}

.standard-radio {
  width: 14px !important;
  height: 14px !important;
}

/* Sobrescrever estilos específicos de sliders */
.slider-label.standard-label,
.custom-slider-label.standard-label {
  font-size: 13px !important;
  color: #374151 !important;
  font-weight: 500 !important;
}
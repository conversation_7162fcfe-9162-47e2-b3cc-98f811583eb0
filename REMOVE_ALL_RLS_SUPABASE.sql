-- =====================================================
-- REMOÇÃO SIMPLIFICADA DE RLS (VERSÃO SUPABASE)
-- =====================================================
-- Versão compatível com Supabase SQL Editor
-- ATENÇÃO: Remove TODA a segurança RLS do sistema

-- =====================================================
-- BACKUP DAS POLÍTICAS (OPCIONAL)
-- =====================================================

-- Criar tabela de backup apenas se não existir
CREATE TABLE IF NOT EXISTS backup_policies_removal AS
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check,
    NOW() as backup_timestamp
FROM pg_policies 
WHERE schemaname = 'public';

-- =====================================================
-- REMOÇÃO DE TODAS AS POLÍTICAS RLS
-- =====================================================

-- PROFILES
DROP POLICY IF EXISTS "profiles_basic" ON public.profiles;
DROP POLICY IF EXISTS "profiles_collaboration" ON public.profiles;
DROP POLICY IF EXISTS "profiles_autocomplete_friendly" ON public.profiles;
DROP POLICY IF EXISTS "profiles_simple_autocomplete" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete" ON public.profiles;
DROP POLICY IF EXISTS "profiles_own_access" ON public.profiles;
DROP POLICY IF EXISTS "profiles_admin_full" ON public.profiles;
DROP POLICY IF EXISTS "profiles_active_basic" ON public.profiles;

-- PROJECTS
DROP POLICY IF EXISTS "projects_basic" ON public.projects;
DROP POLICY IF EXISTS "projects_collaborative_access" ON public.projects;
DROP POLICY IF EXISTS "projects_ownership_insert" ON public.projects;
DROP POLICY IF EXISTS "projects_ownership_update" ON public.projects;
DROP POLICY IF EXISTS "projects_ownership_delete" ON public.projects;
DROP POLICY IF EXISTS "projects_admin_all" ON public.projects;
DROP POLICY IF EXISTS "projects_member_access" ON public.projects;

-- PROJECT_MEMBERS
DROP POLICY IF EXISTS "project_members_basic" ON public.project_members;
DROP POLICY IF EXISTS "project_members_management" ON public.project_members;
DROP POLICY IF EXISTS "project_members_admin_all" ON public.project_members;
DROP POLICY IF EXISTS "project_members_same_project" ON public.project_members;

-- STAGES
DROP POLICY IF EXISTS "stages_basic" ON public.stages;
DROP POLICY IF EXISTS "stages_project_access" ON public.stages;
DROP POLICY IF EXISTS "stages_insert" ON public.stages;
DROP POLICY IF EXISTS "stages_update" ON public.stages;
DROP POLICY IF EXISTS "stages_delete" ON public.stages;

-- STAGE_RESPONSIBLES
DROP POLICY IF EXISTS "stage_responsibles_basic" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_access" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_insert" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_update" ON public.stage_responsibles;
DROP POLICY IF EXISTS "stage_responsibles_delete" ON public.stage_responsibles;

-- TASKS
DROP POLICY IF EXISTS "tasks_basic" ON public.tasks;
DROP POLICY IF EXISTS "tasks_access_control" ON public.tasks;
DROP POLICY IF EXISTS "tasks_insert" ON public.tasks;
DROP POLICY IF EXISTS "tasks_update" ON public.tasks;
DROP POLICY IF EXISTS "tasks_delete" ON public.tasks;

-- TASK_EXECUTORS
DROP POLICY IF EXISTS "task_executors_basic" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_access" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_insert" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_update" ON public.task_executors;
DROP POLICY IF EXISTS "task_executors_delete" ON public.task_executors;

-- TASK_APPROVERS
DROP POLICY IF EXISTS "task_approvers_basic" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_access" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_insert" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_update" ON public.task_approvers;
DROP POLICY IF EXISTS "task_approvers_delete" ON public.task_approvers;

-- TASK_CONTENT_BLOCKS
DROP POLICY IF EXISTS "task_content_blocks_basic" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_access" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_insert" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_update" ON public.task_content_blocks;
DROP POLICY IF EXISTS "task_content_blocks_delete" ON public.task_content_blocks;

-- TASK_COMMENTS
DROP POLICY IF EXISTS "task_comments_basic" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_access" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_insert" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_update" ON public.task_comments;
DROP POLICY IF EXISTS "task_comments_delete" ON public.task_comments;

-- EVIDENCE
DROP POLICY IF EXISTS "evidence_basic" ON public.evidence;
DROP POLICY IF EXISTS "evidence_access" ON public.evidence;
DROP POLICY IF EXISTS "evidence_privacy_protection" ON public.evidence;
DROP POLICY IF EXISTS "evidence_upload_debug" ON public.evidence;
DROP POLICY IF EXISTS "evidence_insert" ON public.evidence;
DROP POLICY IF EXISTS "evidence_update" ON public.evidence;
DROP POLICY IF EXISTS "evidence_delete" ON public.evidence;

-- OUTRAS TABELAS POSSÍVEIS
DROP POLICY IF EXISTS "user_notifications_basic" ON public.user_notifications;
DROP POLICY IF EXISTS "project_history_basic" ON public.project_history;
DROP POLICY IF EXISTS "quizzes_basic" ON public.quizzes;
DROP POLICY IF EXISTS "quiz_attempts_basic" ON public.quiz_attempts;

-- =====================================================
-- DESABILITAR RLS EM TODAS AS TABELAS
-- =====================================================

-- Tabelas principais
ALTER TABLE IF EXISTS public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.project_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.stages DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.stage_responsibles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_executors DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_approvers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_content_blocks DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.task_comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.evidence DISABLE ROW LEVEL SECURITY;

-- Tabelas auxiliares
ALTER TABLE IF EXISTS public.user_notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.project_history DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.quizzes DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.quiz_attempts DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.quiz_answers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.user_quiz_progress DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- VERIFICAÇÃO DA LIMPEZA
-- =====================================================

-- Verificar políticas restantes
SELECT 
    'VERIFICAÇÃO FINAL' as status,
    'Políticas RLS restantes' as item,
    COUNT(*)::TEXT as quantidade
FROM pg_policies 
WHERE schemaname = 'public'

UNION ALL

-- Verificar tabelas com RLS habilitado
SELECT 
    '',
    'Tabelas com RLS habilitado',
    COUNT(*)::TEXT
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public' 
AND c.relkind = 'r' 
AND c.relrowsecurity = true

UNION ALL

-- Status geral
SELECT 
    '',
    'Status da limpeza',
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public')
        THEN '✅ COMPLETA'
        ELSE '❌ INCOMPLETA'
    END;

-- =====================================================
-- TESTE RÁPIDO DE ACESSO
-- =====================================================

-- Testar acesso básico após limpeza
SELECT 
    'TESTE PÓS-LIMPEZA' as secao,
    'profiles' as tabela,
    COUNT(*)::TEXT as registros
FROM public.profiles

UNION ALL

SELECT 
    '',
    'projects',
    COUNT(*)::TEXT
FROM public.projects

UNION ALL

SELECT 
    '',
    'Sistema',
    '✅ FUNCIONANDO SEM RLS';

# 📚 Central de Regras, Padrões e Documentação Técnica do Projeto

Esta pasta reúne todos os arquivos essenciais para arquitetura, padrões, RBAC, UX/UI, onboarding, sugestões de implementação e melhores práticas do projeto.

## Objetivo
Centralizar e padronizar toda a documentação estratégica do projeto, facilitando o acesso, a manutenção, o onboarding de novos membros e a automação de processos.

## Estrutura dos Arquivos

- **cursorrules.md** — Regras centrais do projeto para arquitetura, padrões, segurança, automação, uso do Cursor.ai e versionamento.
- **arquitetura.md** — Diretrizes de arquitetura, decisões técnicas e princípios de design.
- **rbac.md** — Implementação e regras de controle de acesso (RBAC) do sistema.
- **rbac-por-papeis.md** — Regras detalhadas por papel de usuário.
- **ux-ui.md** — Padrões visuais, guidelines de UX e componentes obrigatórios.
- **padronizacao-blocos-editor.md** — Padrões e diretrizes para blocos do editor de conteúdo.
- **best-practices-cursor-ai.md** — Melhores práticas para uso do Cursor.ai, prompts, exemplos e dicas de produtividade.
- **sugestoes-implementacao.md** — Roadmap, prioridades e recomendações técnicas para evolução do sistema.
- **estrutura-src.md** — Estrutura de pastas e arquivos do projeto.
- **plano-projeto-unificado.md** — Diretrizes de planejamento, escopo e prioridades do projeto.
- **software-architecture.md** — Princípios gerais de arquitetura de software.

## Recomendações de Uso
- Consulte sempre esta pasta antes de tomar decisões técnicas, iniciar refatorações ou propor mudanças estruturais.
- Atualize os arquivos sempre que houver decisões relevantes, novos padrões ou aprendizados importantes.
- Oriente novos membros a começar por este diretório para garantir alinhamento e produtividade desde o início.

---

## Automação e CI para Regras e Padrões

- Todos os arquivos desta pasta são validados automaticamente por workflows de CI:
  - **Lint Docs:** Garante que todos os markdowns estejam padronizados e sem links quebrados.
  - **Validate Cursor Rules:** Garante que todos os arquivos essenciais estejam presentes e atualizados.
- Commits e pull requests que não seguirem as regras ou deixarem arquivos obrigatórios ausentes serão bloqueados.
- Mantenha esta central sempre atualizada após decisões técnicas, mudanças de arquitetura ou evolução de padrões.

---

**Dica:**
Mantenha esta central sempre organizada e revisada. A qualidade e evolução do projeto dependem do alinhamento e clareza dessas regras e padrões.

---

## Checklist de Cultura de Excelência

- [ ] Revisar e adaptar todo código gerado pela IA
- [ ] Manter documentação e regras sempre atualizadas
- [ ] Compartilhar aprendizados e dúvidas com o time
- [ ] Automatizar processos sempre que possível
- [ ] Priorizar clareza, simplicidade e segurança
- [ ] Usar o Cursor.ai para acelerar, mas nunca abrir mão da qualidade
- [ ] Promover onboarding e evolução contínua de todos 
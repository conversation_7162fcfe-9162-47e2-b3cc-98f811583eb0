/**
 * Tipos TypeScript para o painel de configuração
 */

// Tipos centralizados para configuração de blocos do editor
// TODO: Dividir em múltiplos arquivos se crescer

export interface CardConfig {
  backgroundColor: string;
  format: 'rounded' | 'square' | 'pill';
  border?: {
    enabled: boolean;
    color: string;
    width: number;
  };
  shadow?: {
    enabled: boolean;
    depth: number;
  };
  hover?: {
    enabled: boolean;
    shadowDepth: number;
  };
  font?: {
    size: number;
    color: string;
    style: 'normal' | 'bold' | 'italic';
  };
}

export interface IconConfig {
  enabled: boolean;
  position: string;
  type: 'predefined' | 'custom';
  iconName?: string;
  customIconUrl?: string;
  appearance?: {
    background: string;
    color: string;
    format: 'circle' | 'square';
    size: number;
    border?: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: {
      enabled: boolean;
      depth: number;
    };
    hover?: {
      enabled: boolean;
      shadowDepth: number;
    };
  };
}

export interface ButtonConfig {
  backgroundColor: string;
  color: string;
  style: 'rounded' | 'flat' | 'pill';
  border?: {
    enabled: boolean;
    color: string;
    width: number;
  };
  shadow?: {
    enabled: boolean;
  };
  hover?: {
    enabled: boolean;
  };
  text: string;
}

export interface BlockConfigPanelProps {
  block: {
    id: string;
    type: string;
    content: any;
  };
  onUpdate: (blockId: string, updates: any) => void;
  onClose: () => void;
}

export interface StyleConfig {
  card: {
    backgroundColor: string;
    borderColor: string;
    borderRadius: string;
    boxShadow: string;
  };
  icon: {
    color: string;
  };
}

export interface StylePreset {
  name: string;
  config: StyleConfig;
}

export interface IconPosition {
  value: string;
  label: string;
}

export interface CardFormat {
  value: string;
  label: string;
}

export interface FontStyle {
  value: string;
  label: string;
}

export interface IconFormat {
  value: string;
  label: string;
}

export interface ColorPickerProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
}

export interface IconPickerProps {
  value?: string;
  onChange: (iconName: string) => void;
}

export interface PresetSelectorProps {
  presets: StylePreset[];
  onApplyPreset: (preset: StylePreset) => void;
}

// Definição do tipo BlockTypePreset baseado na estrutura usada nos presets
export interface BlockTypePreset {
  icon: {
    name: string;
    position: string;
    type: string;
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  card: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  button?: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
}
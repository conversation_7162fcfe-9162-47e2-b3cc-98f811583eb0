-- =====================================================
-- CORREÇÃO: POLÍTICA RLS PROFILES PARA AUTOCOMPLETE FUNCIONAL
-- =====================================================
-- Corrige limitação que mostra apenas administradores no autocomplete

-- PROBLEMA IDENTIFICADO:
-- A política atual "profiles_collaboration" é muito restritiva
-- Só mostra membros de projetos comuns, causando limitação no autocomplete

-- BACKUP DA POLÍTICA ATUAL
CREATE TABLE IF NOT EXISTS backup_profiles_policy AS
SELECT 
    policyname,
    qual,
    with_check,
    now() as backup_timestamp
FROM pg_policies 
WHERE schemaname = 'public' AND tablename = 'profiles';

-- REMOVER POLÍTICA RESTRITIVA ATUAL
DROP POLICY IF EXISTS "profiles_collaboration" ON public.profiles;

-- NOVA POLÍTICA MAIS PERMISSIVA PARA AUTOCOMPLETE
-- Permite ver perfis ativos para facilitar colaboração
CREATE POLICY "profiles_autocomplete_friendly" ON public.profiles FOR SELECT USING (
  id = auth.uid() OR -- Próprio perfil sempre visível
  (
    is_active = true AND -- Apenas usuários ativos
    (
      -- Membros de projetos comuns (colaboração direta)
      id IN (
        SELECT DISTINCT pm1.user_id 
        FROM project_members pm1 
        JOIN project_members pm2 ON pm1.project_id = pm2.project_id 
        WHERE pm2.user_id = auth.uid()
      )
      OR
      -- OU usuários que podem ser adicionados a projetos
      -- (para facilitar formação de equipes)
      EXISTS (
        SELECT 1 FROM project_members pm 
        WHERE pm.user_id = auth.uid()
        AND EXISTS (
          SELECT 1 FROM projects p 
          WHERE p.id = pm.project_id 
          AND (p.owner_id = auth.uid() OR pm.role IN ('admin', 'manager'))
        )
      )
    )
  )
);

-- ALTERNATIVA: POLÍTICA MAIS SIMPLES E PERMISSIVA
-- Descomenta as linhas abaixo se a política acima ainda for restritiva

/*
DROP POLICY IF EXISTS "profiles_autocomplete_friendly" ON public.profiles;

CREATE POLICY "profiles_simple_collaboration" ON public.profiles FOR SELECT USING (
  id = auth.uid() OR -- Próprio perfil
  is_active = true   -- Todos os usuários ativos (para facilitar colaboração)
);
*/

-- MANTER POLÍTICAS DE ESCRITA RESTRITIVAS (SEGURANÇA)
-- Essas já existem, mas garantindo que estejam aplicadas

DROP POLICY IF EXISTS "profiles_insert" ON public.profiles;
CREATE POLICY "profiles_insert" ON public.profiles 
FOR INSERT WITH CHECK (id = auth.uid());

DROP POLICY IF EXISTS "profiles_update" ON public.profiles;
CREATE POLICY "profiles_update" ON public.profiles 
FOR UPDATE USING (id = auth.uid()) WITH CHECK (id = auth.uid());

DROP POLICY IF EXISTS "profiles_delete" ON public.profiles;
CREATE POLICY "profiles_delete" ON public.profiles 
FOR DELETE USING (id = auth.uid());

-- LOGS DE VERIFICAÇÃO
DO $$
BEGIN
  RAISE NOTICE '✅ CORREÇÃO APLICADA: profiles_autocomplete_friendly';
  RAISE NOTICE '📋 NOVA POLÍTICA: Mostra usuários ativos para colaboração';
  RAISE NOTICE '🔐 SEGURANÇA: Políticas de escrita mantidas restritivas';
  RAISE NOTICE '🧪 TESTE: Execute o script diagnostico_autocomplete_admin.sql';
END $$;

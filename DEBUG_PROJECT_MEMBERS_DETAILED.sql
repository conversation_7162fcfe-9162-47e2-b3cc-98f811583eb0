-- =====================================================
-- DEBUG ESPECÍFICO: PROJECT MEMBERS PARA AUTOCOMPLETE
-- =====================================================
-- Como não é problema de RLS, vamos investigar os dados

-- =====================================================
-- 1. VERIFICAR ESTRUTURA COMPLETA DOS DADOS
-- =====================================================

-- Ver todos os profiles ativos
SELECT 
    'PROFILES ATIVOS COMPLETOS' as tipo,
    id,
    name,
    email,
    role,
    is_active,
    avatar_url
FROM profiles 
WHERE is_active = true;

-- Ver todos os projects
SELECT 
    'PROJECTS DISPONÍVEIS' as tipo,
    id,
    name as project_name,
    description,
    status
FROM projects;

-- Ver todos os project_members
SELECT 
    'PROJECT_MEMBERS COMPLETOS' as tipo,
    pm.id,
    pm.project_id,
    pm.user_id,
    pm.role as member_role,
    p.name as project_name,
    prof.name as user_name,
    prof.email as user_email
FROM project_members pm
JOIN projects p ON p.id = pm.project_id
JOIN profiles prof ON prof.id = pm.user_id;

-- =====================================================
-- 2. SIMULAR A QUERY EXATA DO projectService.getProjectMembers
-- =====================================================

-- Query que o serviço TypeScript está fazendo
SELECT 
    'SIMULAÇÃO getProjectMembers' as debug_tipo,
    pm.id,
    pm.role,
    jsonb_build_object(
        'id', p.id,
        'name', p.name,
        'email', p.email,
        'avatar_url', p.avatar_url
    ) as profile
FROM project_members pm
JOIN profiles p ON p.id = pm.user_id
WHERE pm.project_id = (SELECT id FROM projects LIMIT 1);

-- =====================================================
-- 3. VERIFICAR CADA PROJECT INDIVIDUALMENTE
-- =====================================================

-- Para cada projeto, mostrar seus membros
SELECT 
    'MEMBROS POR PROJETO' as tipo,
    proj.name as projeto,
    proj.id as project_id,
    COUNT(pm.id) as total_membros,
    string_agg(prof.name, ', ') as nomes_membros
FROM projects proj
LEFT JOIN project_members pm ON pm.project_id = proj.id
LEFT JOIN profiles prof ON prof.id = pm.user_id
GROUP BY proj.id, proj.name;

-- =====================================================
-- 4. TESTAR QUERY COM DIFERENTES FORMATOS
-- =====================================================

-- Formato 1: Como o Supabase TypeScript espera (com select aninhado)
SELECT 
    'FORMATO SUPABASE JS' as tipo,
    json_agg(
        json_build_object(
            'id', pm.id,
            'role', pm.role,
            'profile', json_build_object(
                'id', p.id,
                'name', p.name,
                'email', p.email,
                'avatar_url', p.avatar_url
            )
        )
    ) as result
FROM project_members pm
JOIN profiles p ON p.id = pm.user_id
WHERE pm.project_id = (SELECT id FROM projects LIMIT 1);

-- =====================================================
-- 5. VERIFICAR SE HÁ PROBLEMA DE FOREIGN KEY
-- =====================================================

-- Verificar integridade dos dados
SELECT 
    'VERIFICAÇÃO INTEGRIDADE' as tipo,
    'Membros órfãos (sem profile)' as problema,
    COUNT(*) as quantidade
FROM project_members pm
LEFT JOIN profiles p ON p.id = pm.user_id
WHERE p.id IS NULL

UNION ALL

SELECT 
    '',
    'Membros órfãos (sem project)',
    COUNT(*)
FROM project_members pm
LEFT JOIN projects proj ON proj.id = pm.project_id
WHERE proj.id IS NULL

UNION ALL

SELECT 
    '',
    'Profiles inativos como membros',
    COUNT(*)
FROM project_members pm
JOIN profiles p ON p.id = pm.user_id
WHERE p.is_active = false;

-- =====================================================
-- 6. QUERY PARA POPULAR AUTOCOMPLETE MANUALMENTE
-- =====================================================

-- Se fosse para popular o autocomplete diretamente
SELECT 
    'DADOS PARA AUTOCOMPLETE' as tipo,
    p.id,
    p.name,
    p.email,
    p.avatar_url,
    CASE 
        WHEN pm.user_id IS NOT NULL THEN 'Membro do projeto'
        ELSE 'Usuário ativo disponível'
    END as status
FROM profiles p
LEFT JOIN project_members pm ON pm.user_id = p.id 
    AND pm.project_id = (SELECT id FROM projects LIMIT 1)
WHERE p.is_active = true
ORDER BY 
    CASE WHEN pm.user_id IS NOT NULL THEN 1 ELSE 2 END,
    p.name;

-- =====================================================
-- 7. DIAGNÓSTICO FINAL DETALHADO
-- =====================================================

SELECT 
    'RESUMO PARA DEBUG' as categoria,
    'Qual project_id usar' as item,
    (SELECT id::TEXT FROM projects ORDER BY created_at DESC LIMIT 1) as valor

UNION ALL

SELECT 
    '',
    'Membros neste projeto',
    COUNT(*)::TEXT
FROM project_members 
WHERE project_id = (SELECT id FROM projects ORDER BY created_at DESC LIMIT 1)

UNION ALL

SELECT 
    '',
    'Profiles válidos para autocomplete',
    COUNT(*)::TEXT
FROM profiles 
WHERE is_active = true;

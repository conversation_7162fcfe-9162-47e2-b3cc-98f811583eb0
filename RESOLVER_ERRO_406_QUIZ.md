# 🚨 Resolver Erro 406 - <PERSON><PERSON><PERSON> Quiz Não Encontradas

## ❌ **PROBLEMA IDENTIFICADO:**
```
- 406 (Not Acceptable) em quizzes, user_quiz_progress
- 400 (Bad Request) em quiz_attempts
- Tabelas do quiz não existem ou estão com estrutura incorreta
```

## ✅ **SOLUÇÃO PASSO A PASSO:**

### **PASSO 1: Executar Script Principal Atualizado**
1. **Acesse o Supabase Dashboard**
2. **Vá para SQL Editor**
3. **Execute o script principal completo:**
```sql
-- Copie e cole TODO o conteúdo de: data/supabase_schema.sql
-- Este script foi atualizado com todas as correções necessárias
```

**IMPORTANTE:** Execute o script COMPLETO, não apenas partes dele.

### **PASSO 2: Verificar Criação**
O script principal inclui verificações automáticas. Após executar, você deve ver:
```
✅ TABELAS PRINCIPAIS CRIADAS: profiles, projects, stages, tasks, project_members
✅ TABELAS DO QUIZ CRIADAS: quizzes, quiz_attempts, quiz_answers, user_quiz_progress, quiz_statistics
✅ STATUS DO RLS: todas com rowsecurity = true
✅ FOREIGN KEYS DO QUIZ: todas as referências corretas
✅ ESTRUTURA QUIZ_ATTEMPTS: attempt_number, task_id, user_id presentes
✅ SCHEMA APLICADO COM SUCESSO!
```

### **PASSO 3: Testar no Frontend**
1. **Recarregue a página do quiz**
2. **Abra o Console (F12)**
3. **Verifique se os erros 406 sumiram**
4. **Teste criar e responder um quiz**

---

## 🔧 **TROUBLESHOOTING:**

### **Se Script Falhar:**
```sql
-- 1. Verificar permissões
SELECT current_user, session_user;

-- 2. Verificar se está no projeto correto
SELECT current_database();

-- 3. Executar seções separadamente
-- Execute cada CREATE TABLE individualmente
```

### **Se Ainda Houver Erro 406:**
1. **Verifique se RLS está ativo:**
```sql
SELECT tablename, rowsecurity FROM pg_tables 
WHERE tablename LIKE 'quiz%';
```

2. **Verifique políticas:**
```sql
SELECT tablename, policyname FROM pg_policies 
WHERE tablename LIKE 'quiz%';
```

### **Se Erro 400 Persistir:**
Pode ser coluna `attempt_number` faltando:
```sql
-- Verificar estrutura
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'quiz_attempts';

-- Se não tiver attempt_number, adicionar:
ALTER TABLE quiz_attempts ADD COLUMN attempt_number INTEGER DEFAULT 1;
```

---

## 📋 **CHECKLIST DE VERIFICAÇÃO:**

### **Após Executar Scripts:**
- [ ] ✅ 5 tabelas quiz* existem
- [ ] ✅ Todas têm RLS ativado
- [ ] ✅ Políticas básicas aplicadas
- [ ] ✅ Índices criados
- [ ] ✅ Foreign keys funcionando

### **Teste no Frontend:**
- [ ] ✅ Sem erro 406 no console
- [ ] ✅ Quiz carrega perguntas
- [ ] ✅ Consegue responder
- [ ] ✅ Consegue finalizar
- [ ] ✅ Dados salvos no Supabase

---

## 🎯 **RESULTADO ESPERADO:**

Após seguir todos os passos:
```
✅ Quiz funciona completamente
✅ Dados salvos no Supabase
✅ Sem erros no console
✅ Modo híbrido ativo (Supabase + Local)
```

---

## 📞 **SE AINDA HOUVER PROBLEMAS:**

1. **Copie o erro completo do console**
2. **Execute o script de verificação**
3. **Compartilhe os resultados**
4. **Verifique se está no projeto Supabase correto**

**Projeto atual:** `gcdtchxyxawtiroxuifs` (Gestão de Projeto)

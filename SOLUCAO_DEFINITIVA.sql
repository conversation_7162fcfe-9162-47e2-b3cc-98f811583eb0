-- =====================================================
-- SOLUÇÃO RÁPIDA: CRIAR PROFILE PARA USUÁRIO ATUAL
-- Execute este script para resolver o problema imediato
-- =====================================================

-- STEP 1: VERIFICAR A SITUAÇÃO ATUAL
SELECT 
    'SITUAÇÃO ATUAL' as info,
    (SELECT COUNT(*) FROM auth.users) as total_users,
    (SELECT COUNT(*) FROM profiles) as total_profiles,
    (SELECT COUNT(*) FROM auth.users u LEFT JOIN profiles p ON u.id = p.id WHERE p.id IS NULL) as users_sem_profile;

-- STEP 2: VERIFICAR O USUÁRIO ESPECÍFICO PROBLEMA
SELECT 
    'USUÁRIO PROBLEMA' as info,
    u.id as user_id,
    u.email as user_email,
    u.created_at,
    CASE 
        WHEN p.id IS NOT NULL THEN 'TEM PROFILE'
        ELSE 'SEM PROFILE'
    END as status_profile
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 3: VERIFICAR SE EXISTE CONFLITO DE EMAIL
SELECT 
    'CONFLITO DE EMAIL' as info,
    p.id as profile_id,
    p.email,
    p.name,
    'PROFILE EXISTENTE COM MESMO EMAIL' as problema
FROM profiles p
WHERE p.email IN (
    SELECT u.email FROM auth.users u 
    WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
);

-- STEP 4: BUSCAR DADOS DO USUÁRIO PARA CRIAR PROFILE
SELECT 
    'DADOS PARA PROFILE' as info,
    u.id,
    u.email,
    u.created_at,
    u.raw_user_meta_data
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 5: CRIAR PROFILE COM EMAIL ÚNICO (se necessário)
INSERT INTO profiles (id, name, email, role, created_at, updated_at)
SELECT 
    u.id,
    COALESCE(
        u.raw_user_meta_data->>'name',
        u.raw_user_meta_data->>'full_name',
        SPLIT_PART(u.email, '@', 1),
        'Usuário'
    ) as name,
    -- Se email já existe, criar versão única
    CASE 
        WHEN EXISTS (SELECT 1 FROM profiles WHERE email = u.email) 
        THEN u.email || '_' || SUBSTRING(u.id::text, 1, 8)
        ELSE u.email
    END as email,
    'member' as role,
    u.created_at,
    NOW()
FROM auth.users u
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
    AND NOT EXISTS (
        SELECT 1 FROM profiles p WHERE p.id = u.id
    );

-- STEP 6: VERIFICAR SE PROFILE FOI CRIADO
SELECT 
    'PROFILE CRIADO' as resultado,
    p.id,
    p.name,
    p.email,
    p.role,
    p.is_active
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 7: ADICIONAR COMO EXECUTOR DA TAREFA
INSERT INTO task_executors (task_id, user_id, created_at)
SELECT 
    '7c606667-9391-4660-933d-90d6bd276e88',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM task_executors 
    WHERE task_id = '7c606667-9391-4660-933d-90d6bd276e88' 
      AND user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
);

-- STEP 8: VERIFICAÇÃO FINAL
SELECT 
    'VERIFICAÇÃO FINAL' as status,
    'PROFILE EXISTE: ' || CASE WHEN p.id IS NOT NULL THEN 'SIM' ELSE 'NÃO' END as profile_status,
    'É EXECUTOR: ' || CASE WHEN te.user_id IS NOT NULL THEN 'SIM' ELSE 'NÃO' END as executor_status
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
LEFT JOIN task_executors te ON te.user_id = u.id AND te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- =====================================================
-- INSTRUÇÕES PÓS-EXECUÇÃO:
-- =====================================================
-- 1. Execute este script no Supabase SQL Editor
-- 2. Recarregue a página da aplicação (F5)
-- 3. Navegue para a tarefa
-- 4. Clique na aba "Executar"
-- 5. O conteúdo deve aparecer agora!
-- =====================================================

-- =====================================================
-- SOLUÇÃO DEFINITIVA: WELLINGTON COMO EXECUTOR
-- Execute no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR SE WELLINGTON É EXECUTOR
SELECT 
    'WELLINGTON É EXECUTOR?' as teste,
    COUNT(*) as resultado
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee';

-- 2. ADICIONAR WELLINGTON COMO EXECUTOR (método seguro)
INSERT INTO task_executors (task_id, user_id, created_at)
SELECT 
    '7c606667-9391-4660-933d-90d6bd276e88',
    '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee',
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM task_executors 
    WHERE task_id = '7c606667-9391-4660-933d-90d6bd276e88' 
      AND user_id = '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee'
);

-- 3. CONFIRMAR QUE FOI ADICIONADO
SELECT 
    'CONFIRMAÇÃO: WELLINGTON É EXECUTOR' as resultado,
    te.user_id,
    p.name,
    p.email
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '3ebb5ec3-db8a-45ba-9705-1c27e7ab72ee';

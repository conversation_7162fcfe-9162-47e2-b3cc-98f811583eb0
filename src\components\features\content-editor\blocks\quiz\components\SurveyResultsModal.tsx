import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  PieChart,
  Users,
  MessageSquare,
  TrendingUp,
  CheckCircle
} from 'lucide-react';

import { QuizContent, SurveyResults, SurveyQuestionResult, QuizQuestionType } from '@/types/quiz';

interface SurveyResultsModalProps {
  isOpen: boolean;
  onClose: () => void;
  quizContent: QuizContent;
  surveyResults?: SurveyResults;
  totalResponses: number;
}

export const SurveyResultsModal: React.FC<SurveyResultsModalProps> = ({
  isOpen,
  onClose,
  quizContent,
  surveyResults,
  totalResponses = 0
}) => {
  // Gerar dados mockados para demonstração se não houver dados reais
  const mockSurveyResults = useMemo((): SurveyResults => {
    if (surveyResults) return surveyResults;

    return {
      quizId: 'mock-survey',
      totalResponses: Math.max(totalResponses, 25), // Mínimo de 25 para demonstração
      collectedAt: new Date(),
      questionResults: quizContent.questions.map((question, index) => {
        const mockResult: SurveyQuestionResult = {
          questionId: question.id,
          questionTitle: question.title,
          questionType: question.type,
          totalResponses: Math.max(totalResponses, 25),
        };

        // Gerar dados específicos por tipo de pergunta
        switch (question.type) {
          case 'single-choice':
          case 'multiple-choice':
            if (question.options) {
              mockResult.optionStats = question.options.map((option, optIndex) => {
                const baseCount = Math.floor(Math.random() * 15) + 2;
                const responseCount = optIndex === 0 ? baseCount + 8 : baseCount; // Primeira opção mais popular
                return {
                  optionId: option.id,
                  optionText: option.text,
                  responseCount,
                  percentage: (responseCount / mockResult.totalResponses) * 100,
                  surveyValue: option.surveyValue
                };
              });
            }
            break;

          case 'true-false':
            const trueCount = Math.floor(Math.random() * 15) + 5;
            const falseCount = mockResult.totalResponses - trueCount;
            mockResult.booleanStats = {
              trueCount,
              falseCount,
              truePercentage: (trueCount / mockResult.totalResponses) * 100,
              falsePercentage: (falseCount / mockResult.totalResponses) * 100
            };
            break;

          case 'open-text':
            mockResult.textResponses = [
              { response: 'Resposta muito interessante sobre o tópico...', timestamp: new Date() },
              { response: 'Acredito que deveria ser melhorado...', timestamp: new Date() },
              { response: 'Excelente iniciativa, parabéns!', timestamp: new Date() },
            ];
            break;
        }

        return mockResult;
      })
    };
  }, [quizContent.questions, surveyResults, totalResponses]);

  const getQuestionTypeLabel = (type: QuizQuestionType): string => {
    const labels = {
      'single-choice': 'Escolha Única',
      'multiple-choice': 'Múltipla Escolha',
      'true-false': 'Verdadeiro/Falso',
      'open-text': 'Resposta Aberta',
      'ordering': 'Ordenação',
      'matching': 'Correspondência'
    };
    return labels[type];
  };

  const getTypeIcon = (type: QuizQuestionType) => {
    switch (type) {
      case 'single-choice':
      case 'multiple-choice':
        return <BarChart3 className="w-4 h-4" />;
      case 'true-false':
        return <PieChart className="w-4 h-4" />;
      case 'open-text':
        return <MessageSquare className="w-4 h-4" />;
      default:
        return <TrendingUp className="w-4 h-4" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Resultados da Pesquisa: {quizContent.config.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Resumo Geral */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Resumo Geral
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {mockSurveyResults.totalResponses}
                  </div>
                  <div className="text-sm text-gray-600">Total de Respostas</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {mockSurveyResults.questionResults.length}
                  </div>
                  <div className="text-sm text-gray-600">Perguntas</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    100%
                  </div>
                  <div className="text-sm text-gray-600">Taxa de Conclusão</div>
                </div>
              </div>
              
              <div className={`mt-4 p-3 rounded-lg border ${
                surveyResults
                  ? 'bg-green-50 border-green-200'
                  : 'bg-blue-50 border-blue-200'
              }`}>
                <div className={`flex items-center gap-2 ${
                  surveyResults ? 'text-green-700' : 'text-blue-700'
                }`}>
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    {surveyResults
                      ? `Dados reais coletados em ${mockSurveyResults.collectedAt.toLocaleDateString()}`
                      : `Dados de demonstração (${mockSurveyResults.totalResponses} respostas simuladas)`
                    }
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Resultados por Pergunta */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Distribuição de Respostas</h3>
            
            {mockSurveyResults.questionResults.map((result, index) => (
              <Card key={result.questionId}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-base flex items-center gap-2">
                        {getTypeIcon(result.questionType)}
                        Pergunta {index + 1}: {result.questionTitle}
                      </CardTitle>
                      <div className="flex gap-2 mt-2">
                        <Badge variant="outline">
                          {getQuestionTypeLabel(result.questionType)}
                        </Badge>
                        <Badge variant="secondary">
                          {result.totalResponses} respostas
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Opções de múltipla escolha */}
                  {result.optionStats && (
                    <div className="space-y-3">
                      {result.optionStats.map((option) => (
                        <div key={option.optionId} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">{option.optionText}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-gray-600">
                                {option.responseCount} ({option.percentage.toFixed(1)}%)
                              </span>
                            </div>
                          </div>
                          <Progress value={option.percentage} className="h-2" />
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Verdadeiro/Falso */}
                  {result.booleanStats && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {result.booleanStats.trueCount}
                        </div>
                        <div className="text-sm text-gray-600">
                          Verdadeiro ({result.booleanStats.truePercentage.toFixed(1)}%)
                        </div>
                      </div>
                      <div className="text-center p-4 bg-red-50 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">
                          {result.booleanStats.falseCount}
                        </div>
                        <div className="text-sm text-gray-600">
                          Falso ({result.booleanStats.falsePercentage.toFixed(1)}%)
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Respostas abertas */}
                  {result.textResponses && (
                    <div className="space-y-3">
                      <div className="text-sm font-medium text-gray-700">
                        Algumas respostas recebidas:
                      </div>
                      {result.textResponses.slice(0, 3).map((response, idx) => (
                        <div key={idx} className="p-3 bg-gray-50 rounded-lg border-l-4 border-blue-500">
                          <p className="text-sm text-gray-700">"{response.response}"</p>
                          <p className="text-xs text-gray-500 mt-1">
                            {response.timestamp.toLocaleDateString()}
                          </p>
                        </div>
                      ))}
                      {result.textResponses.length > 3 && (
                        <div className="text-sm text-gray-500 text-center">
                          ... e mais {result.textResponses.length - 3} respostas
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

import React from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $deleteTableColumn,
  $deleteTableRow,
  $insertTableColumn,
  $insertTableRow,
  $isTableCellNode,
  $isTableNode,
  $isTableRowNode,
  $removeTableElement,
  TableCellHeaderStates,
  $setTableCellBackgroundColor,
  $getTableCellFromLexicalNode,
  $getTableRowFromLexicalNode,
  $getTableFromLexicalNode,
  $unmergeCell,
  $mergeTableCells,
} from '@lexical/table';
import { $getSelection, $isRangeSelection } from 'lexical';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  Plus,
  Minus,
  Trash2,
  RotateCcw,
  <PERSON><PERSON>,
  <PERSON>gn<PERSON><PERSON><PERSON>,
  AlignCenter,
  AlignRight,
  Pin,
  Table,
} from 'lucide-react';

interface TableContextMenuProps {
  children: React.ReactNode;
}

export const TableContextMenu: React.FC<TableContextMenuProps> = ({ children }) => {
  const [editor] = useLexicalComposerContext();

  const insertRowAbove = () => {
    editor.update(() => {
      $insertTableRow(false);
    });
  };

  const insertRowBelow = () => {
    editor.update(() => {
      $insertTableRow(true);
    });
  };

  const insertColumnLeft = () => {
    editor.update(() => {
      $insertTableColumn(false);
    });
  };

  const insertColumnRight = () => {
    editor.update(() => {
      $insertTableColumn(true);
    });
  };

  const deleteColumn = () => {
    editor.update(() => {
      $deleteTableColumn();
    });
  };

  const deleteRow = () => {
    editor.update(() => {
      $deleteTableRow();
    });
  };

  const deleteTable = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const tableNode = $getTableFromLexicalNode(anchorNode);
        if (tableNode) {
          tableNode.remove();
        }
      }
    });
  };

  const addRowHeader = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $getTableCellFromLexicalNode(anchorNode);
        if (cellNode) {
          cellNode.toggleHeaderStyle(TableCellHeaderStates.ROW);
        }
      }
    });
  };

  const addColumnHeader = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $getTableCellFromLexicalNode(anchorNode);
        if (cellNode) {
          cellNode.toggleHeaderStyle(TableCellHeaderStates.COLUMN);
        }
      }
    });
  };

  const toggleRowStriping = () => {
    // Implementação para alternar listras de linha
    console.log('Toggle row striping');
  };

  const setBackgroundColor = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const anchorNode = selection.anchor.getNode();
        const cellNode = $getTableCellFromLexicalNode(anchorNode);
        if (cellNode) {
          $setTableCellBackgroundColor(cellNode, color);
        }
      }
    });
  };

  const setVerticalAlign = (align: 'top' | 'middle' | 'bottom') => {
    // Implementação para alinhamento vertical
    console.log('Set vertical align:', align);
  };

  const toggleFirstRowFreeze = () => {
    // Implementação para congelar primeira linha
    console.log('Toggle first row freeze');
  };

  const toggleFirstColumnFreeze = () => {
    // Implementação para congelar primeira coluna
    console.log('Toggle first column freeze');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div>{children}</div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56">
        <DropdownMenuLabel>Opções da Tabela</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => setBackgroundColor('#ffeb3b')}>
          <Palette size={16} className="mr-2" />
          Background color
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={toggleRowStriping}>
          <Table size={16} className="mr-2" />
          Toggle Row Striping
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => setVerticalAlign('middle')}>
          <AlignCenter size={16} className="mr-2" />
          Vertical Align
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={toggleFirstRowFreeze}>
            <Pin size={16} className="mr-2" />
            Toggle First Row Freeze
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={toggleFirstColumnFreeze}>
            <Pin size={16} className="mr-2" />
            Toggle First Column Freeze
          </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={insertRowAbove}>
          <Plus size={16} className="mr-2" />
          Insert row above
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={insertRowBelow}>
          <Plus size={16} className="mr-2" />
          Insert row below
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={insertColumnLeft}>
          <Plus size={16} className="mr-2" />
          Insert column left
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={insertColumnRight}>
          <Plus size={16} className="mr-2" />
          Insert column right
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={deleteColumn}>
          <Trash2 size={16} className="mr-2" />
          Delete column
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={deleteRow}>
          <Trash2 size={16} className="mr-2" />
          Delete row
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={deleteTable}>
          <Trash2 size={16} className="mr-2" />
          Delete table
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={addRowHeader}>
          <Plus size={16} className="mr-2" />
          Add row header
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={addColumnHeader}>
          <Plus size={16} className="mr-2" />
          Add column header
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default TableContextMenu;
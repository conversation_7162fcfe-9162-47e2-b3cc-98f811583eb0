import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Home, Folder, Users, Settings, X, ChevronLeft, ChevronRight, User } from 'lucide-react';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { DialogTitle } from '@radix-ui/react-dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';

const menuItems = [
  { label: 'Dashboard', path: '/', icon: <Home className="w-5 h-5" /> },
  { label: 'Projetos', path: '/projects', icon: <Folder className="w-5 h-5" /> },
  { label: 'Usuários', path: '/user-management', icon: <Users className="w-5 h-5" /> },
  { label: 'Perfil', path: '/perfil', icon: <User className="w-5 h-5" /> },
  { label: 'Configurações', path: '/settings', icon: <Settings className="w-5 h-5" /> },
];

interface SidebarStandaloneProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  collapsed?: boolean;
  setCollapsed?: (collapsed: boolean) => void;
}

export const SidebarStandalone = ({ open, onOpenChange, collapsed: collapsedProp, setCollapsed: setCollapsedProp }: SidebarStandaloneProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [collapsedInternal, setCollapsedInternal] = useState(false);

  // Permite controle externo (header) ou interno
  const sidebarOpen = open !== undefined ? open : isOpen;
  const setSidebarOpen = onOpenChange || setIsOpen;
  const collapsed = collapsedProp !== undefined ? collapsedProp : collapsedInternal;
  const setCollapsed = setCollapsedProp || setCollapsedInternal;

  // Abrir menu mobile ao receber evento do header
  useEffect(() => {
    const handler = () => setIsOpen(true);
    window.addEventListener('openSidebarMobile', handler);
    return () => window.removeEventListener('openSidebarMobile', handler);
  }, []);

  // Sidebar fixo em desktop, Sheet em mobile
  return (
    <>
      {/* Mobile/Tablet: Sheet */}
      <div className="lg:hidden">
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetContent side="left" className="p-0 w-64">
            {/* Acessibilidade: título oculto para leitores de tela */}
            <DialogTitle asChild>
              {React.cloneElement(
                <VisuallyHidden>Menu lateral</VisuallyHidden>,
                { ref: React.useRef() }
              )}
            </DialogTitle>
            <div className="flex items-center p-4 border-b">
              <span className="font-bold text-project text-lg">Menu</span>
            </div>
            <nav className="flex-1 flex flex-col gap-1 p-2">
              {menuItems.map((item) => (
                <button
                  key={item.path}
                  onClick={() => { setSidebarOpen(false); navigate(item.path); }}
                  className={`flex items-center gap-3 px-4 py-2 rounded transition-colors text-gray-700 hover:bg-gray-100 ${
                    location.pathname === item.path ? 'bg-gray-100 font-bold text-project' : ''
                  }`}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </button>
              ))}
            </nav>
          </SheetContent>
        </Sheet>
      </div>
      {/* Desktop: Sidebar fixo ou colapsado */}
      <aside className={`hidden lg:flex fixed top-0 left-0 h-full z-40 flex-col bg-white border-r shadow transition-all duration-200 ${collapsed ? 'w-16' : 'w-64'} mt-16`}>
        <div className={`flex items-center ${collapsed ? 'justify-center' : 'justify-between'} p-4 border-b`}> 
          <span className={`font-bold text-project text-lg transition-all duration-200 ${collapsed ? 'hidden' : 'block'}`}>Menu</span>
          <button
            onClick={() => {
              if (typeof setCollapsedProp === 'function') {
                setCollapsedProp(!collapsed);
              } else {
                setCollapsedInternal(!collapsed);
              }
            }}
            className="p-2 rounded hover:bg-gray-100"
            aria-label={collapsed ? 'Expandir menu' : 'Recolher menu'}
          >
            {collapsed ? <ChevronRight className="w-5 h-5" /> : <ChevronLeft className="w-5 h-5" />}
          </button>
        </div>
        <nav className={`flex-1 flex flex-col gap-1 p-2 ${collapsed ? 'items-center' : ''}`}>
          {menuItems.map((item) => (
            <button
              key={item.path}
              onClick={() => navigate(item.path)}
              className={`flex items-center ${collapsed ? 'justify-center' : 'gap-3'} px-2 py-2 rounded transition-colors text-gray-700 hover:bg-gray-100 w-full ${
                location.pathname === item.path ? 'bg-gray-100 font-bold text-project' : ''
              }`}
              title={collapsed ? item.label : undefined}
            >
              {item.icon}
              {!collapsed && <span>{item.label}</span>}
            </button>
          ))}
        </nav>
      </aside>
    </>
  );
};
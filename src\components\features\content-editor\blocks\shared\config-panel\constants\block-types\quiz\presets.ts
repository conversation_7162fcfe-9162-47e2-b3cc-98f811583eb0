/**
 * Presets específicos para blocos de quiz
 */

// Definição local do tipo para evitar dependência circular
interface BlockTypePreset {
  icon: {
    name: string;
    position: string;
    type: string;
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  card: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  button?: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
}

export const quizBlockPresets: Record<string, BlockTypePreset> = {
  default: {
    icon: {
      name: 'CircleHelp',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#ec4899', // pink-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#ec4899', width: 1 },
      shadow: '0 2px 8px #ec489940',
      hover: {
        backgroundColor: '#f472b6', // pink-400
        color: '#fff',
        shadow: '0 4px 16px #ec489940',
        borderColor: '#ec4899',
      },
    },
    button: {
      backgroundColor: '#ec4899',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#ec4899', width: 1 },
      shadow: '0 2px 8px #ec489940',
      hover: {
        backgroundColor: '#f472b6',
        color: '#fff',
        shadow: '0 4px 16px #ec489940',
        borderColor: '#ec4899',
      },
    },
    card: {
      backgroundColor: '#fdf2f8',
      color: '#ec4899',
      format: 'rounded',
      border: { enabled: true, color: '#ec4899', width: 1 },
      shadow: '0 2px 8px #ec489940',
      hover: {
        backgroundColor: '#f472b6',
        color: '#fff',
        shadow: '0 4px 16px #ec489940',
        borderColor: '#ec4899',
      },
    },
  },
  multipleChoice: {
    icon: {
      name: 'CheckSquare',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#8b5cf6', // violet-500
      color: '#fff',
      format: 'square',
      border: { enabled: true, color: '#8b5cf6', width: 1 },
      shadow: '0 2px 8px #8b5cf640',
      hover: {
        backgroundColor: '#a78bfa', // violet-400
        color: '#fff',
        shadow: '0 4px 16px #8b5cf640',
        borderColor: '#8b5cf6',
      },
    },
    button: {
      backgroundColor: '#8b5cf6',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#8b5cf6', width: 1 },
      shadow: '0 2px 8px #8b5cf640',
      hover: {
        backgroundColor: '#a78bfa',
        color: '#fff',
        shadow: '0 4px 16px #8b5cf640',
        borderColor: '#8b5cf6',
      },
    },
    card: {
      backgroundColor: '#f5f3ff',
      color: '#8b5cf6',
      format: 'rounded',
      border: { enabled: true, color: '#8b5cf6', width: 1 },
      shadow: '0 2px 8px #8b5cf640',
      hover: {
        backgroundColor: '#a78bfa',
        color: '#fff',
        shadow: '0 4px 16px #8b5cf640',
        borderColor: '#8b5cf6',
      },
    },
  },
  trueFalse: {
    icon: {
      name: 'ToggleLeft',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#06b6d4', // cyan-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#06b6d4', width: 1 },
      shadow: '0 2px 8px #06b6d440',
      hover: {
        backgroundColor: '#22d3ee', // cyan-400
        color: '#fff',
        shadow: '0 4px 16px #06b6d440',
        borderColor: '#06b6d4',
      },
    },
    button: {
      backgroundColor: '#06b6d4',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#06b6d4', width: 1 },
      shadow: '0 2px 8px #06b6d440',
      hover: {
        backgroundColor: '#22d3ee',
        color: '#fff',
        shadow: '0 4px 16px #06b6d440',
        borderColor: '#06b6d4',
      },
    },
    card: {
      backgroundColor: '#ecfeff',
      color: '#06b6d4',
      format: 'rounded',
      border: { enabled: true, color: '#06b6d4', width: 1 },
      shadow: '0 2px 8px #06b6d440',
      hover: {
        backgroundColor: '#22d3ee',
        color: '#fff',
        shadow: '0 4px 16px #06b6d440',
        borderColor: '#06b6d4',
      },
    },
  },
  openEnded: {
    icon: {
      name: 'MessageSquare',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#f59e42', // orange-500
      color: '#fff',
      format: 'square',
      border: { enabled: true, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fb923c', // orange-400
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
    button: {
      backgroundColor: '#f59e42',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fb923c',
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
    card: {
      backgroundColor: '#fff7ed',
      color: '#f59e42',
      format: 'rounded',
      border: { enabled: true, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fb923c',
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
  },
};

export type QuizBlockVariant = 'multipleChoice' | 'trueFalse' | 'openEnded' | 'matching' | 'fillInBlank';

export const quizBlockVariants = {
  multipleChoice: 'Múltipla Escolha',
  trueFalse: 'Verdadeiro/Falso',
  openEnded: 'Resposta Aberta',
  matching: 'Correspondência',
  fillInBlank: 'Preencher Lacunas',
};

export const quizBlockIcons = {
  multipleChoice: 'CheckSquare',
  trueFalse: 'ToggleLeft',
  openEnded: 'MessageSquare',
  matching: 'Link',
  fillInBlank: 'Edit3',
};
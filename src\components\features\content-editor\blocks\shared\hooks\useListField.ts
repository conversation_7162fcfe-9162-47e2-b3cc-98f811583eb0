import { useState } from 'react';

// TODO: Dividir em hooks menores se crescer
export function useListField<T>(initial: T[] = []) {
  const [list, setList] = useState<T[]>(initial);

  const add = (item: T) => setList(l => [...l, item]);
  const remove = (index: number) => setList(l => l.filter((_, i) => i !== index));
  const update = (index: number, item: T) => setList(l => l.map((v, i) => (i === index ? item : v)));
  const reset = () => setList(initial);

  return { list, add, remove, update, reset, setList };
} 
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { RequirePermission } from '@/components/auth/PermissionWrappers';

interface Task {
  id: string;
  name?: string;
  status?: string;
  responsible?: { name?: string; avatar_url?: string };
  progress?: number;
}

interface TaskListProps {
  tasks: Task[];
  onTaskClick: (task: Task) => void;
  onNewTask: () => void;
  getStatusColor: (status: string) => string;
  canCreateTask?: boolean;
  projectId?: string;
}

export const TaskList: React.FC<TaskListProps> = ({ tasks, onTaskClick, onNewTask, getStatusColor, projectId }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-xl">Tarefas ({tasks.length})</CardTitle>
        <RequirePermission permissions="create_task" projectId={projectId}>
          <Button 
            size="sm" 
            variant="outline" 
            className="border-task text-task hover:bg-task-bg"
            onClick={onNewTask}
          >
            <Plus className="w-4 h-4 mr-1" />
            Nova Tarefa
          </Button>
        </RequirePermission>
      </CardHeader>
      <CardContent className="space-y-4">
        {tasks.map((task) => (
          <div 
            key={task.id} 
            className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            onClick={() => onTaskClick(task)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-4 flex-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className={`w-3 h-3 rounded-full mt-2 ${getStatusColor(task.status || '')}`} />
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <span className="capitalize">{task.status || 'Indefinido'}</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-lg text-gray-900">{task?.name || 'Tarefa'}</h3>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={task?.responsible?.avatar_url || '/placeholder.svg'} />
                        <AvatarFallback>{task?.responsible?.name?.[0] || '?'}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600">{task?.responsible?.name || 'Responsável'}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={task?.progress ?? 0} className="flex-1 max-w-32" />
                    <span className="text-sm text-task font-medium">{task?.progress ?? 0}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}; 
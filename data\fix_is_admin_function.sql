-- =====================
-- CORREÇÃO RÁPIDA: Função is_admin
-- Data: 2024-12-19
-- Problema: <PERSON>flit<PERSON> de parâmetro "uid" vs "user_id"
-- =====================

-- Remover função existente para evitar conflito
drop function if exists public.is_admin(uuid);

-- Recriar função com parâmetro correto
create or replace function public.is_admin(uid uuid)
returns boolean
language sql
security definer
stable
as $$
  select exists (
    select 1 from public.profiles 
    where id = uid and role = 'admin'
  );
$$;

-- Verificar se a função foi criada corretamente
select 'Função is_admin criada com sucesso!' as status;

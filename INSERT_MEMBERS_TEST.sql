-- =====================================================
-- TESTE DE INSERÇÃO MANUAL DE MEMBROS NO PROJETO
-- =====================================================

-- 1. Ver estado atual
SELECT 'ESTADO ANTES DO TESTE' as etapa;

SELECT 
    'PROJETO ALVO' as info,
    id,
    name,
    owner_id
FROM projects 
WHERE id = '5a7676a2-d69e-4410-86ce-ec43872c70d2';

SELECT 
    'PROFILES DISPONÍVEIS' as info,
    id,
    name,
    email,
    is_active
FROM profiles 
WHERE is_active = true
ORDER BY name;

SELECT 
    'MEMBROS ATUAIS' as info,
    COUNT(*) as total
FROM project_members 
WHERE project_id = '5a7676a2-d69e-4410-86ce-ec43872c70d2';

-- 2. TENTAR INSERIR MEMBROS MANUALMENTE
SELECT 'INICIANDO INSERÇÃO' as etapa;

-- Inserir todos os profiles ativos como membros
DO $$
DECLARE
    target_project_id uuid := '5a7676a2-d69e-4410-86ce-ec43872c70d2';
    profile_record RECORD;
    insert_count INTEGER := 0;
    error_count INTEGER := 0;
BEGIN
    RAISE NOTICE 'Inserindo membros no projeto: %', target_project_id;
    
    FOR profile_record IN 
        SELECT id, name, email 
        FROM profiles 
        WHERE is_active = true
        ORDER BY name
    LOOP
        BEGIN
            -- Verificar se já é membro
            IF NOT EXISTS (
                SELECT 1 FROM project_members 
                WHERE project_id = target_project_id 
                AND user_id = profile_record.id
            ) THEN
                -- Inserir como membro
                INSERT INTO project_members (project_id, user_id, role)
                VALUES (target_project_id, profile_record.id, 'member');
                
                insert_count := insert_count + 1;
                RAISE NOTICE 'SUCESSO: % (%) adicionado como membro', profile_record.name, profile_record.email;
            ELSE
                RAISE NOTICE 'JÁ EXISTE: % (%) já é membro', profile_record.name, profile_record.email;
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                error_count := error_count + 1;
                RAISE NOTICE 'ERRO ao inserir % (%): %', profile_record.name, profile_record.email, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'RESUMO: % inserções bem-sucedidas, % erros', insert_count, error_count;
END $$;

-- 3. VERIFICAR RESULTADO
SELECT 'RESULTADO APÓS INSERÇÃO' as etapa;

SELECT 
    'MEMBROS FINAIS' as info,
    pm.id,
    pm.user_id,
    pm.role,
    prof.name as user_name,
    prof.email as user_email
FROM project_members pm
JOIN profiles prof ON prof.id = pm.user_id
WHERE pm.project_id = '5a7676a2-d69e-4410-86ce-ec43872c70d2'
ORDER BY prof.name;

SELECT 
    'CONTAGEM FINAL' as info,
    COUNT(*) as total_membros
FROM project_members 
WHERE project_id = '5a7676a2-d69e-4410-86ce-ec43872c70d2';

-- 4. SIMULAR QUERY DO projectService.getProjectMembers
SELECT 'SIMULAÇÃO getProjectMembers' as etapa;

SELECT 
    pm.id,
    pm.role,
    jsonb_build_object(
        'id', p.id,
        'name', p.name,
        'email', p.email,
        'avatar_url', p.avatar_url
    ) as profile
FROM project_members pm
JOIN profiles p ON p.id = pm.user_id
WHERE pm.project_id = '5a7676a2-d69e-4410-86ce-ec43872c70d2'
ORDER BY p.name;

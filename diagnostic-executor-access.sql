-- =====================
-- TESTE DE DIAGNÓSTICO: Verificar acesso de executores aos blocos de conteúdo
-- =====================

-- 1. Verificar se há registros na tabela task_executors
SELECT 
    te.task_id,
    te.user_id,
    p.name as executor_name,
    p.email as executor_email,
    t.title as task_title
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
JOIN tasks t ON te.task_id = t.id
ORDER BY te.created_at DESC
LIMIT 10;

-- 2. Verificar se há blocos de conteúdo para essas tarefas
SELECT 
    tcb.task_id,
    tcb.id as block_id,
    tcb.type,
    tcb.content,
    t.title as task_title,
    COUNT(te.user_id) as executor_count
FROM task_content_blocks tcb
JOIN tasks t ON tcb.task_id = t.id
LEFT JOIN task_executors te ON te.task_id = tcb.task_id
GROUP BY tcb.task_id, tcb.id, tcb.type, tcb.content, t.title
ORDER BY tcb.created_at DESC
LIMIT 10;

-- 3. Teste específico: verificar acesso como executor
-- (Execute este bloco logado como um usuário member que é executor)
/*
SELECT 
    tcb.*,
    'accessible' as test_result
FROM task_content_blocks tcb
WHERE tcb.task_id IN (
    SELECT te.task_id 
    FROM task_executors te 
    WHERE te.user_id = auth.uid()
)
LIMIT 5;
*/

-- 4. Verificar status RLS das tabelas
SELECT 
    schemaname,
    tablename,
    rowsecurity,
    pg_has_role(tableowner, 'USAGE') as can_access
FROM pg_tables pt
JOIN pg_class pc ON pt.tablename = pc.relname
WHERE schemaname = 'public' 
    AND tablename IN ('task_content_blocks', 'tasks', 'task_executors', 'evidence')
ORDER BY tablename;

-- 5. Verificar políticas RLS ativas
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public' 
    AND tablename IN ('task_content_blocks', 'tasks', 'task_executors', 'evidence')
ORDER BY tablename, policyname;

-- 6. Teste de contagem - quantos blocos o usuário atual pode ver
-- (Execute logado como member/executor)
/*
SELECT 
    COUNT(*) as accessible_blocks,
    COUNT(DISTINCT task_id) as accessible_tasks
FROM task_content_blocks;
*/

-- 7. Verificar se o usuário é executor de alguma tarefa
-- (Execute logado como member/executor)
/*
SELECT 
    te.task_id,
    t.title,
    'I am executor' as status
FROM task_executors te
JOIN tasks t ON te.task_id = t.id
WHERE te.user_id = auth.uid();
*/

-- =====================================================
-- POLÍTICAS RLS (ROW LEVEL SECURITY)
-- =====================================================
-- Contém: Todas as políticas de segurança por tabela
-- Dependências: Todas as tabelas criadas
-- Versão: 2.0 - Julho 2025

-- Verificar dependências
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles') THEN
    RAISE EXCEPTION 'Tabelas não encontradas. Execute os scripts anteriores primeiro.';
  END IF;
  RAISE NOTICE '✅ Dependências verificadas';
END $$;

-- =====================================================
-- LIMPEZA DE POLÍTICAS EXISTENTES
-- =====================================================

-- Function auxiliar para remover todas as políticas de uma tabela
CREATE OR REPLACE FUNCTION public.drop_all_policies(table_name text)
RETURNS void AS $$
DECLARE
  policy_record RECORD;
BEGIN
  FOR policy_record IN
    SELECT policyname
    FROM pg_policies
    WHERE tablename = table_name AND schemaname = 'public'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I', policy_record.policyname, table_name);
    RAISE NOTICE 'Política removida: % em %', policy_record.policyname, table_name;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Remover políticas existentes
SELECT public.drop_all_policies('profiles');
SELECT public.drop_all_policies('projects');
SELECT public.drop_all_policies('stages');
SELECT public.drop_all_policies('tasks');
SELECT public.drop_all_policies('project_members');
SELECT public.drop_all_policies('task_executors');
SELECT public.drop_all_policies('task_approvers');
SELECT public.drop_all_policies('stage_responsibles');
SELECT public.drop_all_policies('task_content_blocks');
SELECT public.drop_all_policies('task_attachments');
SELECT public.drop_all_policies('evidence');
SELECT public.drop_all_policies('task_comments');
SELECT public.drop_all_policies('project_history');
SELECT public.drop_all_policies('user_notifications');
SELECT public.drop_all_policies('quizzes');
SELECT public.drop_all_policies('quiz_attempts');
SELECT public.drop_all_policies('quiz_answers');
SELECT public.drop_all_policies('user_quiz_progress');
SELECT public.drop_all_policies('quiz_statistics');

-- =====================================================
-- HABILITAR RLS EM TODAS AS TABELAS
-- =====================================================

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_executors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_approvers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stage_responsibles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.evidence ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_quiz_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_statistics ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLÍTICAS PARA PROFILES
-- =====================================================

-- Usuários podem ver todos os perfis (para autocomplete e referências)
CREATE POLICY "Users can view all profiles"
  ON public.profiles
  FOR SELECT
  USING (true);

-- Usuários podem atualizar seu próprio perfil
CREATE POLICY "Users can update own profile"
  ON public.profiles
  FOR UPDATE
  USING (id = auth.uid());

-- Admins podem fazer tudo
CREATE POLICY "Admins can manage all profiles"
  ON public.profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA PROJECTS
-- =====================================================

-- Usuários podem ver projetos onde são membros ou donos
CREATE POLICY "Users can view accessible projects"
  ON public.projects
  FOR SELECT
  USING (
    owner_id = auth.uid() OR
    id IN (
      SELECT project_id 
      FROM public.project_members 
      WHERE user_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Usuários podem criar projetos
CREATE POLICY "Users can create projects"
  ON public.projects
  FOR INSERT
  WITH CHECK (owner_id = auth.uid());

-- Donos e admins podem atualizar projetos
CREATE POLICY "Owners and admins can update projects"
  ON public.projects
  FOR UPDATE
  USING (
    owner_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e admins podem deletar projetos
CREATE POLICY "Owners and admins can delete projects"
  ON public.projects
  FOR DELETE
  USING (
    owner_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA STAGES
-- =====================================================

-- Usuários podem ver estágios de projetos acessíveis
CREATE POLICY "Users can view accessible stages"
  ON public.stages
  FOR SELECT
  USING (
    project_id IN (
      SELECT id FROM public.projects
      WHERE owner_id = auth.uid() OR
            id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem criar estágios
CREATE POLICY "Owners and managers can create stages"
  ON public.stages
  FOR INSERT
  WITH CHECK (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    ) OR
    project_id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem atualizar estágios
CREATE POLICY "Owners and managers can update stages"
  ON public.stages
  FOR UPDATE
  USING (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    ) OR
    project_id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem deletar estágios
CREATE POLICY "Owners and managers can delete stages"
  ON public.stages
  FOR DELETE
  USING (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    ) OR
    project_id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA TASKS
-- =====================================================

-- Usuários podem ver tarefas onde são executores, responsáveis ou membros do projeto
CREATE POLICY "Users can view accessible tasks"
  ON public.tasks
  FOR SELECT
  USING (
    -- Responsável pela tarefa
    assigned_to = auth.uid() OR
    -- Executor da tarefa
    id IN (
      SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()
    ) OR
    -- Aprovador da tarefa
    id IN (
      SELECT task_id FROM public.task_approvers WHERE user_id = auth.uid()
    ) OR
    -- Membro do projeto
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
    ) OR
    -- Admin
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos, managers e editors podem criar tarefas
CREATE POLICY "Authorized users can create tasks"
  ON public.tasks
  FOR INSERT
  WITH CHECK (
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'editor', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos, managers, editors e executores podem atualizar tarefas
CREATE POLICY "Authorized users can update tasks"
  ON public.tasks
  FOR UPDATE
  USING (
    -- Responsável pela tarefa
    assigned_to = auth.uid() OR
    -- Executor da tarefa
    id IN (
      SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()
    ) OR
    -- Donos, managers e editors do projeto
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'editor', 'admin')
            )
    ) OR
    -- Admin
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Apenas donos e managers podem deletar tarefas
CREATE POLICY "Owners and managers can delete tasks"
  ON public.tasks
  FOR DELETE
  USING (
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA PROJECT_MEMBERS
-- =====================================================

-- Usuários podem ver membros de projetos onde participam
CREATE POLICY "Users can view project members"
  ON public.project_members
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    ) OR
    project_id IN (
      SELECT project_id FROM public.project_members WHERE user_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem adicionar membros
CREATE POLICY "Owners and managers can add members"
  ON public.project_members
  FOR INSERT
  WITH CHECK (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    ) OR
    project_id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem atualizar membros
CREATE POLICY "Owners and managers can update members"
  ON public.project_members
  FOR UPDATE
  USING (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    ) OR
    project_id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem remover membros
CREATE POLICY "Owners and managers can remove members"
  ON public.project_members
  FOR DELETE
  USING (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    ) OR
    project_id IN (
      SELECT pm.project_id FROM public.project_members pm
      WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA TASK_EXECUTORS
-- =====================================================

-- Usuários podem ver executores de tarefas acessíveis
CREATE POLICY "Users can view task executors"
  ON public.task_executors
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    task_id IN (
      SELECT id FROM public.tasks
      WHERE assigned_to = auth.uid() OR
            id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
            stage_id IN (
              SELECT s.id FROM public.stages s
              JOIN public.projects p ON s.project_id = p.id
              WHERE p.owner_id = auth.uid() OR
                    p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos, managers e editors podem adicionar executores
CREATE POLICY "Authorized users can add executors"
  ON public.task_executors
  FOR INSERT
  WITH CHECK (
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'editor', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos, managers e editors podem remover executores
CREATE POLICY "Authorized users can remove executors"
  ON public.task_executors
  FOR DELETE
  USING (
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'editor', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA TASK_APPROVERS
-- =====================================================

-- Usuários podem ver aprovadores de tarefas acessíveis
CREATE POLICY "Users can view task approvers"
  ON public.task_approvers
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    task_id IN (
      SELECT id FROM public.tasks
      WHERE assigned_to = auth.uid() OR
            id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
            stage_id IN (
              SELECT s.id FROM public.stages s
              JOIN public.projects p ON s.project_id = p.id
              WHERE p.owner_id = auth.uid() OR
                    p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem adicionar aprovadores
CREATE POLICY "Owners and managers can add approvers"
  ON public.task_approvers
  FOR INSERT
  WITH CHECK (
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem remover aprovadores
CREATE POLICY "Owners and managers can remove approvers"
  ON public.task_approvers
  FOR DELETE
  USING (
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA STAGE_RESPONSIBLES
-- =====================================================

-- Usuários podem ver responsáveis de estágios acessíveis
CREATE POLICY "Users can view stage responsibles"
  ON public.stage_responsibles
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem adicionar responsáveis
CREATE POLICY "Owners and managers can add stage responsibles"
  ON public.stage_responsibles
  FOR INSERT
  WITH CHECK (
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos e managers podem remover responsáveis
CREATE POLICY "Owners and managers can remove stage responsibles"
  ON public.stage_responsibles
  FOR DELETE
  USING (
    stage_id IN (
      SELECT s.id FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Aprovadores podem atualizar suas próprias aprovações
CREATE POLICY "Approvers can update their approvals"
  ON public.task_approvers
  FOR UPDATE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA TASK_CONTENT_BLOCKS
-- =====================================================

-- Usuários podem ver blocos de conteúdo de tarefas acessíveis
CREATE POLICY "Users can view task content blocks"
  ON public.task_content_blocks
  FOR SELECT
  USING (
    task_id IN (
      SELECT id FROM public.tasks
      WHERE assigned_to = auth.uid() OR
            id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
            stage_id IN (
              SELECT s.id FROM public.stages s
              JOIN public.projects p ON s.project_id = p.id
              WHERE p.owner_id = auth.uid() OR
                    p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Donos, managers e editors podem gerenciar blocos de conteúdo
CREATE POLICY "Authorized users can manage content blocks"
  ON public.task_content_blocks
  FOR ALL
  USING (
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (
              SELECT pm.project_id FROM public.project_members pm
              WHERE pm.user_id = auth.uid() AND pm.role IN ('manager', 'editor', 'admin')
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- =====================================================
-- POLÍTICAS PARA TASK_ATTACHMENTS E EVIDENCE
-- =====================================================

-- Usuários podem ver evidências de tarefas acessíveis
CREATE POLICY "Users can view task evidence"
  ON public.task_attachments
  FOR SELECT
  USING (
    task_id IN (
      SELECT id FROM public.tasks
      WHERE assigned_to = auth.uid() OR
            id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
            id IN (SELECT task_id FROM public.task_approvers WHERE user_id = auth.uid()) OR
            stage_id IN (
              SELECT s.id FROM public.stages s
              JOIN public.projects p ON s.project_id = p.id
              WHERE p.owner_id = auth.uid() OR
                    p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Executores podem fazer upload de evidências
CREATE POLICY "Executors can upload evidence"
  ON public.task_attachments
  FOR INSERT
  WITH CHECK (
    uploaded_by = auth.uid() AND
    task_id IN (
      SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()
    )
  );

-- Aprovadores podem aprovar/rejeitar evidências
CREATE POLICY "Approvers can review evidence"
  ON public.task_attachments
  FOR UPDATE
  USING (
    task_id IN (
      SELECT task_id FROM public.task_approvers WHERE user_id = auth.uid()
    )
  );

-- Executores podem excluir suas próprias evidências não aprovadas
CREATE POLICY "Executors can delete own non-approved evidence"
  ON public.task_attachments
  FOR DELETE
  USING (
    uploaded_by = auth.uid() AND
    status != 'approved' AND
    task_id IN (
      SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()
    )
  );

-- Políticas similares para a tabela evidence
CREATE POLICY "Users can view evidence"
  ON public.evidence
  FOR SELECT
  USING (
    task_id IN (
      SELECT id FROM public.tasks
      WHERE assigned_to = auth.uid() OR
            id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
            id IN (SELECT task_id FROM public.task_approvers WHERE user_id = auth.uid()) OR
            stage_id IN (
              SELECT s.id FROM public.stages s
              JOIN public.projects p ON s.project_id = p.id
              WHERE p.owner_id = auth.uid() OR
                    p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

CREATE POLICY "Executors can upload evidence to evidence table"
  ON public.evidence
  FOR INSERT
  WITH CHECK (
    uploaded_by = auth.uid() AND
    task_id IN (
      SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Approvers can review evidence in evidence table"
  ON public.evidence
  FOR UPDATE
  USING (
    task_id IN (
      SELECT task_id FROM public.task_approvers WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Executors can delete own non-approved evidence from evidence table"
  ON public.evidence
  FOR DELETE
  USING (
    uploaded_by = auth.uid() AND
    status != 'approved' AND
    task_id IN (
      SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()
    )
  );

-- =====================================================
-- POLÍTICAS PARA TASK_COMMENTS
-- =====================================================

-- Usuários podem ver comentários de tarefas acessíveis
CREATE POLICY "Users can view task comments"
  ON public.task_comments
  FOR SELECT
  USING (
    task_id IN (
      SELECT id FROM public.tasks
      WHERE assigned_to = auth.uid() OR
            id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
            stage_id IN (
              SELECT s.id FROM public.stages s
              JOIN public.projects p ON s.project_id = p.id
              WHERE p.owner_id = auth.uid() OR
                    p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- Membros do projeto podem comentar
CREATE POLICY "Project members can comment"
  ON public.task_comments
  FOR INSERT
  WITH CHECK (
    author = auth.uid() AND
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid() OR
            p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid()) OR
            t.id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid())
    )
  );

-- Autores podem atualizar seus próprios comentários
CREATE POLICY "Authors can update own comments"
  ON public.task_comments
  FOR UPDATE
  USING (author = auth.uid());

-- Autores podem deletar seus próprios comentários
CREATE POLICY "Authors can delete own comments"
  ON public.task_comments
  FOR DELETE
  USING (author = auth.uid());

-- =====================================================
-- POLÍTICAS PARA OUTRAS TABELAS
-- =====================================================

-- PROJECT_HISTORY - Apenas leitura para membros do projeto
CREATE POLICY "Project members can view history"
  ON public.project_history
  FOR SELECT
  USING (
    project_id IN (
      SELECT id FROM public.projects
      WHERE owner_id = auth.uid() OR
            id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- USER_NOTIFICATIONS - Usuários veem apenas suas próprias notificações
CREATE POLICY "Users can view their own notifications"
  ON public.user_notifications
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications"
  ON public.user_notifications
  FOR UPDATE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA SISTEMA DE QUIZ
-- =====================================================

-- QUIZZES - Usuários podem ver quizzes de tarefas acessíveis
CREATE POLICY "Users can view accessible quizzes"
  ON public.quizzes
  FOR SELECT
  USING (
    task_id IN (
      SELECT id FROM public.tasks
      WHERE assigned_to = auth.uid() OR
            id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
            stage_id IN (
              SELECT s.id FROM public.stages s
              JOIN public.projects p ON s.project_id = p.id
              WHERE p.owner_id = auth.uid() OR
                    p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
            )
    ) OR
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );

-- QUIZ_ATTEMPTS - Usuários podem ver suas próprias tentativas
CREATE POLICY "Users can view their own quiz attempts"
  ON public.quiz_attempts
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own quiz attempts"
  ON public.quiz_attempts
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- QUIZ_ANSWERS - Usuários podem ver suas próprias respostas
CREATE POLICY "Users can view their own quiz answers"
  ON public.quiz_answers
  FOR SELECT
  USING (
    attempt_id IN (
      SELECT id FROM public.quiz_attempts WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert their own quiz answers"
  ON public.quiz_answers
  FOR INSERT
  WITH CHECK (
    attempt_id IN (
      SELECT id FROM public.quiz_attempts WHERE user_id = auth.uid()
    )
  );

-- USER_QUIZ_PROGRESS - Usuários podem ver e atualizar seu próprio progresso
CREATE POLICY "Users can view their own quiz progress"
  ON public.user_quiz_progress
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can update their own quiz progress"
  ON public.user_quiz_progress
  FOR ALL
  USING (user_id = auth.uid());

-- QUIZ_STATISTICS - Todos podem ver estatísticas
CREATE POLICY "Users can view quiz statistics"
  ON public.quiz_statistics
  FOR SELECT
  USING (true);

-- =====================================================
-- LIMPEZA E LOGS
-- =====================================================

-- Remover function auxiliar
DROP FUNCTION IF EXISTS public.drop_all_policies(text);

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
DECLARE
  total_policies INTEGER;
BEGIN
  -- Contar políticas criadas
  SELECT COUNT(*) INTO total_policies
  FROM pg_policies
  WHERE schemaname = 'public';
  
  RAISE NOTICE '✅ Políticas RLS criadas com sucesso!';
  RAISE NOTICE '🔒 Total de políticas: %', total_policies;
  RAISE NOTICE '📊 RLS habilitado em todas as tabelas';
  RAISE NOTICE '🛡️ Segurança implementada com base em relacionamentos';
END $$;

import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import LoginForm from "./LoginForm";
import RegisterForm from "./RegisterForm";
import ResetPasswordForm from "./ResetPasswordForm";
import RequireAuth from "./RequireAuth";
// Importe suas páginas reais conforme necessário
import Dashboard from "@/pages/Index";
import { ProjectDetails } from "@/pages/ProjectDetails";
import ProjectDetails2 from "@/pages/ProjectDetails2";
import { StageDetails } from "@/pages/StageDetails";
import { TaskDetails } from "@/pages/TaskDetails";
import { TaskDetailsV2 } from "@/pages/TaskDetailsV2";
import { TaskDetailsWrapper } from "@/pages/TaskDetailsWrapper";
import { TaskVersionTest } from "@/pages/TaskVersionTest";
import ProjectsList from '@/pages/ProjectsList';
import { UserManagement } from '@/pages/UserManagement';
import { AppLayout } from '@/components/layout/AppLayout';
import UserProfile from '@/pages/UserProfile';
import QuizTest from '@/pages/QuizTest';
import { MyTasks } from '@/pages/MyTasks';
import { TestMyTasks } from '@/pages/TestMyTasks';
import { DebugMyTasks } from '@/pages/DebugMyTasks';
import { SimpleDebugMyTasks } from '@/pages/SimpleDebugMyTasks';

const AuthRoutes: React.FC = () => (
  <Routes>
    <Route path="/login" element={<LoginForm />} />
    <Route path="/register" element={<RegisterForm />} />
    <Route path="/reset-password" element={<ResetPasswordForm />} />
    <Route path="/" element={<RequireAuth><Dashboard /></RequireAuth>} />
    <Route path="/project/:projectId" element={<RequireAuth><ProjectDetails /></RequireAuth>} />
    <Route path="/project2/:id" element={<RequireAuth><ProjectDetails2 /></RequireAuth>} />
    <Route path="/stage/:stageId" element={<RequireAuth><StageDetails /></RequireAuth>} />
    <Route path="/task/:taskId" element={<RequireAuth><TaskDetailsWrapper /></RequireAuth>} />
    <Route path="/task/:taskId/version-selector" element={<RequireAuth><TaskDetailsWrapper /></RequireAuth>} />
    <Route path="/task/:taskId/test" element={<RequireAuth><TaskVersionTest /></RequireAuth>} />
    <Route path="/projects" element={<RequireAuth><ProjectsList /></RequireAuth>} />
    <Route path="/my-tasks" element={<RequireAuth><MyTasks /></RequireAuth>} />
    <Route path="/test-my-tasks" element={<RequireAuth><TestMyTasks /></RequireAuth>} />
    <Route path="/debug-my-tasks" element={<RequireAuth><DebugMyTasks /></RequireAuth>} />
    <Route path="/simple-debug-my-tasks" element={<RequireAuth><SimpleDebugMyTasks /></RequireAuth>} />
    <Route path="/user-management" element={<RequireAuth><UserManagement /></RequireAuth>} />
    <Route path="/perfil" element={<RequireAuth><UserProfile /></RequireAuth>} />
    <Route path="/quiz-test" element={<RequireAuth><QuizTest /></RequireAuth>} />
    <Route path="/projects/:projectId/stages/:stageId" element={<RequireAuth><StageDetails /></RequireAuth>} />
    <Route
      path="/layout-teste"
      element={
        <RequireAuth>
          <div style={{ background: '#ffe', minHeight: '100vh', padding: 40 }}>
            <h1 style={{ color: '#b00', fontWeight: 'bold' }}>Teste de Layout SEM AppLayout</h1>
            <p>Se não houver nenhum wrapper extra, este bloco deve ocupar toda a largura da tela, sem sidebar, header ou padding lateral.</p>
          </div>
        </RequireAuth>
      }
    />
    {/* Fallback para 404 - Protegido por autenticação */}
    <Route path="*" element={<RequireAuth><div className="text-center py-8">Página não encontrada</div></RequireAuth>} />
  </Routes>
);

export default AuthRoutes;
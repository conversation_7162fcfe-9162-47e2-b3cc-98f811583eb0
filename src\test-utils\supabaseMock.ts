import { vi } from 'vitest';

// Mocks genéricos para métodos encadeados do Supabase
export const mockSingle = vi.fn();
export const mockEqList = vi.fn();
export const mockEqGetById = vi.fn(() => ({ single: mockSingle }));
export const mockEqUpdate = vi.fn(() => ({ single: mockSingle }));
export const mockEqCreate = vi.fn(() => ({ single: mockSingle }));
export const mockInsert = vi.fn(() => ({ single: mockSingle }));
export const mockUpdate = vi.fn(() => ({ eq: mockEqUpdate }));
export const mockDelete = vi.fn(() => ({ eq: vi.fn(() => ({ error: null })) }));

// Para serviços que usam mocks mais específicos (como stage/task)
export const mockSingleGetById = vi.fn();
export const mockSingleCreate = vi.fn();
export const mockSingleUpdate = vi.fn();

// Função auxiliar para decidir qual mock de eq retornar
export function selectMockService(service: 'project' | 'stage' | 'task') {
  return {
    eq: (field: string) => {
      if (service === 'project') {
        if (field === 'id') return { single: mockSingle };
        return mockEqList();
      }
      if (service === 'stage') {
        if (field === 'project_id') return mockEqList();
        if (field === 'id') return { single: mockSingleGetById };
      }
      if (service === 'task') {
        if (field === 'stage_id') return mockEqList();
        if (field === 'id') return { single: mockSingleGetById };
      }
      return vi.fn();
    }
  };
}

// Mock do supabase para uso nos testes
type ServiceType = 'project' | 'stage' | 'task';
export const supabaseMock = {
  from: (table: string) => {
    let service: ServiceType = 'project';
    if (table === 'stages') service = 'stage';
    if (table === 'tasks') service = 'task';
    return {
      select: () => selectMockService(service),
      insert: mockInsert,
      update: mockUpdate,
      delete: mockDelete
    };
  }
}; 
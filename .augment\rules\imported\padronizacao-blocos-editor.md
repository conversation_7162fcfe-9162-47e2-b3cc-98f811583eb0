---
type: "manual"
---

Aqui está uma versão melhorada e mais estruturada do seu prompt para o Cursor.ai:

---

# Implementação de Sistema de Configurações Padrão para Blocos de Conteúdo

## 📋 Objetivo
Implementar um sistema unificado de configurações de aparência e ícones para todos os tipos de blocos, mantendo consistência visual e funcionalidade.

## 🏗️ Arquitetura Geral

### Estrutura Base de Configurações
Todos os blocos seguem uma estrutura padronizada com duas seções principais:

| 1. **Aparência do Card** |
|---|
| 2. **Configurações do Ícone** |

---

## 📝 Especificações por Tipo de Bloco (Situação Atual)

### 1. Bloco de Texto

#### 1.1 Aparência do Card
- **Fundo**: Seletor de cor para o background do card (**implementado**)
- **Formato**: Opções entre Arredondado, Quadrado, P<PERSON><PERSON><PERSON> (**implementado**)
- **Borda**: Toggle para ativar/desativar, seletor de cor, controle de espessura (**implementado**)
- **Sombra**: Toggle para ativar/desativar, controle de profundidade (**implementado**)
- **Hover**: Configuração de sombra no estado hover (**implementado**)
- **Fonte**: Tamanho, cor, estilo (**NÃO implementado para bloco de texto, apenas para vídeo/imagem**)
- **Restaurar Padrão**: Botão para resetar todas as configurações (**implementado**)

#### 1.2 Configurações do Ícone
- **Exibir Ícone**: Toggle on/off (**implementado**)
- **Posição do Ícone**: Dropdown com opções (**implementado**)
- **Tipo de Ícone**: Radio para pré-definido/upload (**implementado**)
- **Aparência do Ícone**: Fundo, cor, formato, borda, sombra, hover (**implementado**)
- **Restaurar Padrão**: Reset específico para ícone (**implementado**)

---

### 2. Bloco de Vídeo
- **Aparência do Card**: Todas as opções acima, incluindo fonte (tamanho, cor, estilo) (**implementado**)
- **Configurações do Ícone**: Igual ao bloco de texto (**implementado**)

---

### 3. Bloco de Imagem, Arquivo, Quiz
- **Aparência do Card**: Fundo, formato, borda, sombra, hover (**implementado**)
- **Fonte**: Tamanho, cor, estilo (**NÃO implementado**)
- **Configurações do Ícone**: Igual ao bloco de texto (**implementado**)

---

### 4. Bloco Colorido
- **Aparência do Card**: Formato, borda, sombra, hover (**implementado**)
- **Fundo**: Opções limitadas, conforme esperado (**implementado**)
- **Configurações do Ícone**: Igual ao bloco de texto (**implementado**)

---

## 🎨 Especificações Técnicas (Situação Atual)

### Interface de Configuração
- **Layout**: Painel/modal, sem seções colapsáveis (**implementado parcialmente**)
- **Organização**: Separação visual entre "Aparência" e "Ícone", mas não colapsável (**implementado parcialmente**)
- **Preview**: Visualização em tempo real das mudanças (**implementado**)
- **Responsividade**: Segue padrão moderno, mas pode ser melhorada (**implementado**)

### Valores Padrão
- Os valores padrão seguem o exemplo do documento (**implementado**)

### Considerações de Implementação
1. **Reutilização**: Componentes reutilizáveis para configurações (**implementado**)
2. **Performance**: Atualização em tempo real, mas sem debounce explícito (**implementado parcialmente**)
3. **Persistência**: Não há persistência local (localStorage) (**NÃO implementado**)
4. **Validação**: Validação de cor hex, mas pode faltar para outros campos (**implementado parcialmente**)
5. **Acessibilidade**: Não há menção explícita, depende do CSS e componentes (**implementado parcialmente**)

---

## ✅ Checklist de Implementação (Situação Atual)

- [x] Criar estrutura base de configurações
- [x] Implementar componentes reutilizáveis
- [x] Desenvolver interface de configuração
- [x] Implementar preview em tempo real
- [ ] Adicionar validações completas
- [ ] Testar em todos os tipos de bloco
- [ ] Implementar configuração de fonte para todos os blocos
- [ ] Implementar persistência local das configurações
- [ ] Melhorar acessibilidade e responsividade
- [ ] Adicionar seções colapsáveis para UX

---

**Resumo:**
O configurador de blocos está bem alinhado com a padronização, mas ainda há pontos a evoluir: configuração de fonte universal, persistência local, validações extras, acessibilidade e organização visual (colapsáveis).

# Padronização de Configuração Visual dos Blocos de Conteúdo (Situação Atual)

## Estrutura Geral
Cada bloco de conteúdo pode ser configurado visualmente por meio de três grandes seções:
- Aparência do Card (`config.card`)
- Configurações do Ícone (`config.icon`)
- Configurações do Botão (`config.button`)

Essas opções estão disponíveis via painel de configuração com abas/tabs.

---

## 1. Aparência do Card (`config.card`)
- **Cor de fundo**: Seletor de cor (input type color)
- **Formato**: Dropdown ("Arredondado", "Quadrado", "Pílula")
- **Borda**:
  - Toggle para ativar/desativar
  - Seletor de cor
  - Espessura (input numérico)
- **Sombra**:
  - Toggle para ativar/desativar
  - Profundidade (slider 0-4)
- **Hover**:
  - Toggle para ativar/desativar efeito de hover
  - Profundidade da sombra no hover (slider 0-8)
- **Fonte**:
  - Tamanho (input numérico)
  - Cor (seletor de cor)
  - Estilo (dropdown: normal, negrito, itálico)
- **Botão de reset** para cada campo e para o card inteiro

**Exemplo:**
```ts
card: {
  backgroundColor: '#ffffff',
  format: 'rounded',
  border: { enabled: true, color: '#e5e5e5', width: 2 },
  shadow: { enabled: true, depth: 2 },
  hover: { enabled: true, shadowDepth: 4 },
  font: { size: 16, color: '#222', style: 'normal' },
}
```

---

## 2. Configurações do Ícone (`config.icon`)
- **Exibir ícone**: Toggle
- **Posição**: Dropdown (esquerda/direita do título, descrição, conteúdo, centro/acima)
- **Tipo**: Radio para pré-definido (biblioteca Lucide) ou customizado (upload de imagem)
- **Aparência**:
  - Fundo (seletor de cor)
  - Cor do ícone (seletor de cor)
  - Formato (círculo/quadrado)
  - Tamanho (input numérico)
  - Borda: toggle, cor, espessura
  - Sombra: toggle, profundidade
  - Hover: toggle, profundidade
- **Botão de reset** para o ícone

**Exemplo:**
```ts
icon: {
  enabled: true,
  position: 'left-title',
  type: 'predefined',
  iconName: 'FileText',
  customIconUrl: '',
  appearance: {
    background: '#f3f4f6',
    color: '#374151',
    format: 'circle',
    size: 28,
    border: { enabled: false, color: '#e5e5e5', width: 1 },
    shadow: { enabled: false, depth: 1 },
    hover: { enabled: false, shadowDepth: 2 },
  },
}
```

---

## 3. Configurações do Botão (`config.button`)
- **Cor de fundo**: Seletor de cor
- **Cor do texto**: Seletor de cor
- **Estilo**: Dropdown (arredondado, flat, pílula)
- **Borda**: toggle, cor, espessura
- **Sombra**: toggle
- **Hover**: toggle
- **Texto do botão**: Campo de texto
- **Botão de reset** para o botão

**Exemplo:**
```ts
button: {
  backgroundColor: '#7c3aed',
  color: '#fff',
  style: 'rounded',
  border: { enabled: false, color: '#e5e5e5', width: 1 },
  shadow: { enabled: false },
  hover: { enabled: false },
  text: 'Clique aqui',
}
```

---

## 4. Interface do Configurador
- **Abas/tabs**: Aparência, Ícone, Botão
- **Preview em tempo real**
- **Reset granular e geral**
- **Responsividade**: Layout adaptável para desktop/mobile
- **Validação**: Para cor hex, valores numéricos, etc.

---

## 5. Observações
- Nem todos os blocos usam todas as opções de botão (ex: bloco de texto pode não exibir botão, mas a estrutura está disponível para todos).
- A separação entre conteúdo (`editContent`) e configuração visual (`config`) é mantida.
- O painel pode ser expandido no futuro para incluir mais opções ou validações.

---

**Situação: Documento atualizado para refletir 100% das opções e estrutura do configurador de blocos atual.**
import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ProjectForm } from '@/components/forms/ProjectForm';
import { StageForm } from '@/components/forms/StageForm';
import { projectService } from '@/services/projectService';
import { stageService } from '@/services/stageService';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Project, Stage, ProjectMemberProfile, ProjectStatus, StageStatus } from '@/types';
import { 
  ArrowLeft, 
  Plus,
  FolderOpen,
  Edit,
  Users,
  Target,
  Activity,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  PauseCircle,
  Pencil,
  ListChecks
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { Header } from '@/components/layout/Header';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { Breadcrumb } from '@/components/ui/breadcrumb';

const getStatusIcon = (status: ProjectStatus | StageStatus) => {
  switch (status) {
    case 'completed': return <CheckCircle className="w-4 h-4" />;
    case 'in-progress': return <PlayCircle className="w-4 h-4" />;
    case 'cancelled': return <AlertCircle className="w-4 h-4" />;
    case 'on-hold': return <PauseCircle className="w-4 h-4" />;
    case 'planning': return <Activity className="w-4 h-4" />;
    case 'not-started': return <Activity className="w-4 h-4" />;
    case 'blocked': return <AlertCircle className="w-4 h-4" />;
    default: return <Activity className="w-4 h-4" />;
  }
};

const ProjectDetails2: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const projectId = id;
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [stages, setStages] = useState<Stage[]>([]);
  const [members, setMembers] = useState<ProjectMemberProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [showStageForm, setShowStageForm] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    const fetchProjectData = async () => {
      if (!projectId) {
        setError('ID do projeto não encontrado.');
        setIsLoading(false);
        return;
      }
      try {
        setIsLoading(true);
        const [projectData, stagesData, membersData] = await Promise.all([
          projectService.getById(projectId),
          // Usar método que considera vínculos de tarefas
        stageService.getMyStagesByProjectId(projectId),
          projectService.getProjectMembers(projectId),
        ]);

        if (projectData) {
          setProject({ ...projectData, members: membersData || [] });
        } else {
          throw new Error('Projeto não encontrado');
        }

        setStages(stagesData || []);
        
        setMembers(membersData || []);

      } catch (err: any) {
        console.error('Erro detalhado:', err);

        // Tratamento específico para projeto não encontrado
        if (err.message && err.message.includes('Projeto não encontrado')) {
          setError(`Projeto não encontrado (ID: ${projectId}). Verifique se o projeto existe ou se você tem permissão para acessá-lo.`);
        } else {
          setError(`Falha ao carregar os dados do projeto: ${err.message || 'Erro desconhecido'}`);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectData();
  }, [projectId]);

  const handleBack = () => {
    navigate('/projects');
  };

  const handleStageCreated = async () => {
    if (projectId) {
      // Usar método que considera vínculos de tarefas
      const updatedStages = await stageService.getMyStagesByProjectId(projectId);
      setStages(updatedStages);
    }
  };

  const handleProjectUpdated = async () => {
    if (projectId) {
      const updatedProject = await projectService.getById(projectId);
      setProject(updatedProject);
      const updatedMembers = await projectService.getProjectMembers(projectId);
      setMembers(updatedMembers || []);
    }
  };

  const getStageStats = () => {
    const totalStages = stages.length;
    const completedStages = stages.filter(s => s.status === 'completed').length;
    const inProgressStages = stages.filter(s => s.status === 'in-progress').length;
    return { totalStages, completedStages, inProgressStages };
  };

  const handleEditProject = async () => {
    if (!projectId) return;
    const membersData = await projectService.getProjectMembers(projectId);
    setProject(prev => prev ? { ...prev, members: membersData || [] } : prev);
    setShowProjectForm(true);
  };

  if (isLoading) {
    return (
        <div className="flex justify-center items-center h-screen">
          <LoadingSpinner />
        </div>
    );
  }

  if (error) {
    return (
        <div className="flex flex-col justify-center items-center h-screen bg-red-50">
          <div className="text-red-600 text-center p-8 rounded-lg border border-red-200 bg-white shadow-md">
            <h2 className="text-2xl font-bold mb-4">Ocorreu um Erro</h2>
            <p className="mb-4">{error}</p>
            <Button onClick={handleBack} variant="destructive">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar para Projetos
            </Button>
          </div>
        </div>
    );
  }

  if (!project) {
    return (
        <div className="flex justify-center items-center h-screen">
          <p>Projeto não encontrado.</p>
        </div>
    );
  }

  const stats = getStageStats();

  // LOG para depuração de membros
  console.log('Membros para exibir (mobile):', members);

  return (
    <>
      <style>{`
        .debug-overflow * {
          max-width: 100% !important;
          box-sizing: border-box !important;
          outline: 1px solid red !important;
        }
      `}</style>
      <Header showSidebarButton={true} />
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`max-w-none mx-auto p-6 space-y-6 transition-all duration-300 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'} md:ml-0` }>
        {isLoading && <LoadingSpinner />}
        {error && <p className="text-red-500">Erro: {error}</p>}
        {/* Caminho/Breadcrumb e Voltar */}
        <div className="w-full min-w-0 overflow-x-auto mb-4">
          <div className="flex flex-col text-sm mt-3 ml-2 mb-4 gap-0.5">
            <button
              className="flex items-center font-bold text-project rounded px-2 py-1 shadow-sm hover:bg-blue-50 transition"
              onClick={() => navigate('/projects')}
              title={project?.title || project?.name || 'Projeto'}
              type="button"
            >
              <FolderOpen className="w-4 h-4 mr-2 text-project" />
              <span className="truncate">{project?.title || project?.name || 'Projeto'}</span>
            </button>
          </div>
        </div>
        {/* Conteúdo principal com espaçamento harmonizado */}
        <div className="flex flex-col gap-6">
          {/* Header Principal do Projeto */}
          <Card className="border-project/20 bg-white shadow-sm w-full">
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 w-full flex-wrap">
                <div className="flex flex-wrap items-center gap-4">
                  <div className="p-3 bg-project-bg rounded-lg">
                    <FolderOpen className="w-8 h-8 text-project" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                      {project.name}
                    </CardTitle>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                      <div className="flex flex-wrap items-center gap-2">
                        <Badge className={`bg-gray-100 text-gray-800 flex items-center gap-1`}>
                          {getStatusIcon(project.status)}
                          {project.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground ml-2">
                          <Users className="inline w-4 h-4 mr-1" />
                          {members.length} membros
                        </span>
                        <span className="text-xs text-muted-foreground ml-2">
                          <Target className="inline w-4 h-4 mr-1" />
                          {stages.length} etapas
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2 self-end sm:self-auto">
                  <Button variant="outline" onClick={handleEditProject}>
                    <Pencil className="w-4 h-4 mr-1" /> Editar Projeto
                  </Button>
                  <Button className="bg-project hover:bg-project-dark" onClick={() => setShowStageForm(true)}>
                    <Plus className="w-4 h-4 mr-1" /> Nova Etapa
                  </Button>
                </div>
              </div>
            </CardHeader>
            {project.description && (
              <CardContent className="p-0">
                <p className="text-gray-700 text-lg leading-relaxed">{project.description}</p>
              </CardContent>
            )}
          </Card>
          {/* Cards de Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-white shadow-sm w-full">
              <CardContent className="py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Progresso Geral</p>
                    <p className="text-3xl font-bold text-project">{project.progress || 0}%</p>
                  </div>
                  <div className="p-3 bg-project-bg rounded-lg">
                    <Activity className="w-6 h-6 text-project" />
                  </div>
                </div>
                <Progress value={project.progress || 0} className="mt-4 h-2" />
              </CardContent>
            </Card>
            <Card className="bg-white shadow-sm w-full">
              <CardContent className="py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Etapas Concluídas</p>
                    <p className="text-3xl font-bold text-green-600">{stats.completedStages}</p>
                    <p className="text-sm text-gray-500">de {stats.totalStages} etapas</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-lg">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white shadow-sm w-full">
              <CardContent className="py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Em Andamento</p>
                    <p className="text-3xl font-bold text-blue-600">{stats.inProgressStages}</p>
                    <p className="text-sm text-gray-500">etapas ativas</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <PlayCircle className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white shadow-sm w-full">
              <CardContent className="py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Equipe</p>
                    <p className="text-3xl font-bold text-purple-600">{members.length}</p>
                    <p className="text-sm text-gray-500">membros ativos</p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <Users className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          {/* Equipe do Projeto */}
          <Card className="bg-white shadow-sm w-full">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 mb-4">
                <Users className="w-5 h-5 text-project" />
                Equipe do Projeto
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {/* Log para depuração */}
              {(() => { console.log('Membros para exibir (mobile render):', members); return null; })()}
              {members.length === 0 ? (
                <p className="text-gray-600">Nenhum membro na equipe ainda.</p>
              ) : (
                <div className="flex flex-col gap-4">
                  {members.map((member) => {
                    const profile = member.profile || member.profiles?.[0] || member;
                    const isProfile = (p: any): p is import('../types').Profile =>
                      p && typeof p === 'object' && 'avatar_url' in p && 'name' in p && 'email' in p;
                    return (
                      <div key={member.id} className="flex items-center gap-3 bg-gray-50 rounded-lg w-full p-4">
                        <Avatar className="w-12 h-12">
                          <AvatarImage src={isProfile(profile) ? profile.avatar_url || '/placeholder.svg' : '/placeholder.svg'} alt={isProfile(profile) ? profile.name || '' : ''} />
                          <AvatarFallback className="bg-project-bg text-project font-medium">
                            {(isProfile(profile) ? (profile.name || '?').charAt(0) : '?').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 truncate" title={isProfile(profile) ? profile.name || '' : ''}>
                            {isProfile(profile) ? profile.name : 'Desconhecido'}
                          </p>
                          <p className="text-sm text-gray-600 truncate" title={isProfile(profile) ? profile.email || '' : ''}>
                            {isProfile(profile) ? profile.email : ''}
                          </p>
                          <Badge variant="secondary" className="text-xs mt-1">{member.role}</Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
          {/* Etapas do Projeto */}
          <Card className="bg-white shadow-sm w-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-project" />
                Etapas do Projeto
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {stages.length === 0 ? (
                <div className="text-center text-gray-500 py-12">
                  <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma etapa encontrada</h3>
                  <p className="text-gray-600 mb-4">Comece criando a primeira etapa do projeto.</p>
                  <Button className="bg-project hover:bg-project-dark" onClick={() => setShowStageForm(true)}>
                    <Plus className="w-4 h-4 mr-1" /> Criar Primeira Etapa
                  </Button>
                </div>
              ) : (
                <ul className="space-y-4">
                  {stages.map((stage) => (
                    <li key={stage.id}>
                      <Link to={`/projects/${projectId}/stages/${stage.id}`}>
                        <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors w-full">
                          <h4 className="font-semibold">{stage.name}</h4>
                          <p className="text-sm text-gray-500">{stage.description}</p>
                          <Progress value={stage.progress} className="mt-2" />
                        </div>
                      </Link>
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </div>
        <ProjectForm
          open={showProjectForm}
          onOpenChange={setShowProjectForm}
          project={project}
          mode="edit"
          onUpdated={handleProjectUpdated}
        />
        <StageForm
          open={showStageForm}
          onOpenChange={(open) => {
            setShowStageForm(open);
            if (!open) {
              handleStageCreated();
            }
          }}
          projectId={projectId!}
          mode="create"
        />
      </div>
    </>
  );
};

export default ProjectDetails2;
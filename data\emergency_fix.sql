-- =====================
-- CORREÇÃO EMERGENCIAL: DESA<PERSON><PERSON><PERSON><PERSON> RLS IMEDIATAMENTE
-- Execute este script AGORA no Supabase SQL Editor
-- =====================

-- PASSO 1: Desabilitar RLS em todas as tabelas críticas
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.stages DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks DISABLE ROW LEVEL SECURITY;

-- PASSO 2: Remover todas as políticas problemáticas
DROP POLICY IF EXISTS "Project members can view projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view projects of their projects" ON public.projects;
DROP POLICY IF EXISTS "Project members can view stages" ON public.stages;
DROP POLICY IF EXISTS "Users can view stages of their projects" ON public.stages;
DROP POLICY IF EXISTS "Project members can view tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can view tasks of their projects" ON public.tasks;

-- PASSO 3: Limpar perfis duplicados
DELETE FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4' 
AND created_at < (
    SELECT max(created_at) 
    FROM public.profiles 
    WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
);

-- PASSO 4: Verificar se funcionou
SELECT 'VERIFICAÇÃO FINAL:' as status;

-- Verificar RLS desabilitado
SELECT 
    tablename,
    case 
        when rowsecurity then '❌ RLS AINDA ATIVO'
        else '✅ RLS DESABILITADO'
    end as status
FROM pg_tables 
WHERE tablename IN ('profiles', 'projects', 'stages', 'tasks')
ORDER BY tablename;

-- Verificar políticas removidas
SELECT 'POLÍTICAS RESTANTES:' as status;
SELECT 
    tablename,
    count(*) as policy_count,
    case 
        when count(*) = 0 then '✅ NENHUMA POLÍTICA'
        else '⚠️ ' || count(*) || ' POLÍTICAS RESTANTES'
    end as status
FROM pg_policies 
WHERE tablename IN ('profiles', 'projects', 'stages', 'tasks')
GROUP BY tablename
ORDER BY tablename;

-- Verificar perfil específico
SELECT 'PERFIL ESPECÍFICO:' as status;
SELECT 
    id,
    email,
    name,
    count(*) as count,
    case 
        when count(*) = 1 then '✅ ÚNICO'
        else '❌ DUPLICADO'
    end as status
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
GROUP BY id, email, name;

-- Testar consulta de projeto
SELECT 'TESTE DE PROJETO:' as status;
SELECT 
    id,
    name,
    status
FROM public.projects 
WHERE id = '17b4065f-7179-4c95-868d-4fa168c368f9'
LIMIT 1;

-- Mensagem final
SELECT '🎉 CORREÇÃO EMERGENCIAL APLICADA!' as resultado;
SELECT 'Se todas as verificações mostram ✅, recarregue a página' as instrucao;
SELECT 'ATENÇÃO: RLS desabilitado - apenas para desenvolvimento!' as aviso;

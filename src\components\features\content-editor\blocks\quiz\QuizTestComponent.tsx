import React from 'react';
import { QuizExecutionBlock } from './QuizExecutionBlock';
import { QuizBlockContent } from '@/types';

// Componente de teste para verificar se o Quiz está funcionando
export const QuizTestComponent: React.FC = () => {
  // Teste com formato antigo (simples)
  const oldFormatContent: QuizBlockContent = {
    question: "Qual é a capital do Brasil?",
    options: ["Brasília", "São Paulo", "Rio de Janeiro", "Belo Horizonte"]
  };

  // Teste com formato novo (avançado)
  const newFormatContent: QuizBlockContent = {
    quiz: {
      config: {
        title: "Quiz de Geografia",
        description: "Teste seus conhecimentos sobre geografia do Brasil",
        maxAttempts: 3,
        allowRetry: true,
        passingScore: 70,
        showScore: true,
        showCorrectAnswers: true,
        showFeedback: true,
        showTimer: false,
        isRequired: false,
        blockProgressUntilPassed: false,
        shuffleQuestions: false,
        shuffleOptions: false,
        showProgressBar: true,
        allowSaveDraft: true,
        enableAnalytics: true,
        showResultsToUser: true
      },
      questions: [
        {
          id: 'q1',
          type: 'single-choice',
          title: 'Qual é a capital do Brasil?',
          points: 1,
          required: true,
          options: [
            { id: 'opt1', text: 'Brasília', isCorrect: true },
            { id: 'opt2', text: 'São Paulo', isCorrect: false },
            { id: 'opt3', text: 'Rio de Janeiro', isCorrect: false },
            { id: 'opt4', text: 'Belo Horizonte', isCorrect: false }
          ],
          correctFeedback: 'Correto! Brasília é a capital do Brasil.',
          incorrectFeedback: 'Incorreto. A capital do Brasil é Brasília.'
        },
        {
          id: 'q2',
          type: 'multiple-choice',
          title: 'Quais são estados da região Sudeste?',
          points: 2,
          required: true,
          options: [
            { id: 'opt1', text: 'São Paulo', isCorrect: true },
            { id: 'opt2', text: 'Rio de Janeiro', isCorrect: true },
            { id: 'opt3', text: 'Minas Gerais', isCorrect: true },
            { id: 'opt4', text: 'Bahia', isCorrect: false },
            { id: 'opt5', text: 'Espírito Santo', isCorrect: true }
          ],
          correctFeedback: 'Correto! Esses são os estados da região Sudeste.',
          incorrectFeedback: 'Incorreto. Verifique quais estados pertencem à região Sudeste.'
        },
        {
          id: 'q3',
          type: 'true-false',
          title: 'O Brasil faz fronteira com todos os países da América do Sul, exceto Chile e Equador.',
          points: 1,
          required: true,
          correctAnswer: true,
          correctFeedback: 'Correto! O Brasil faz fronteira com 10 dos 12 países sul-americanos.',
          incorrectFeedback: 'Incorreto. O Brasil realmente não faz fronteira apenas com Chile e Equador.'
        },
        {
          id: 'q4',
          type: 'open-text',
          title: 'Cite o maior rio do Brasil em extensão.',
          points: 1,
          required: true,
          openTextKeywords: ['amazonas', 'rio amazonas'],
          correctFeedback: 'Correto! O Rio Amazonas é o maior rio do Brasil.',
          incorrectFeedback: 'Incorreto. O maior rio do Brasil é o Rio Amazonas.'
        }
      ]
    }
  };

  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold">Teste do Sistema de Quiz</h1>
      
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Formato Antigo (Compatibilidade)</h2>
          <QuizExecutionBlock
            content={oldFormatContent}
            taskId="test-task-1"
            blockId="test-block-1"
            onComplete={(passed, score) => {
              console.log('Quiz antigo completado:', { passed, score });
              alert(`Quiz completado! Passou: ${passed}, Pontuação: ${score}`);
            }}
          />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Formato Novo (Avançado)</h2>
          <QuizExecutionBlock
            content={newFormatContent}
            taskId="test-task-2"
            blockId="test-block-2"
            onComplete={(passed, score) => {
              console.log('Quiz novo completado:', { passed, score });
              alert(`Quiz completado! Passou: ${passed}, Pontuação: ${score}`);
            }}
          />
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Instruções de Teste:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Abra o console do navegador (F12) para ver os logs de debug</li>
          <li>Teste o formato antigo primeiro - deve funcionar sem Supabase</li>
          <li>Teste o formato novo - pode usar Supabase se configurado</li>
          <li>Verifique se as perguntas são exibidas corretamente</li>
          <li>Teste a navegação entre perguntas</li>
          <li>Teste o salvamento de respostas</li>
          <li>Teste a finalização e correção automática</li>
        </ol>

        <div className="mt-4 flex gap-2">
          <button
            onClick={() => {
              localStorage.removeItem('quiz_force_local_mode');
              window.location.reload();
            }}
            className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
          >
            🔄 Resetar Modo Local
          </button>

          <button
            onClick={() => {
              const keys = Object.keys(localStorage).filter(key => key.startsWith('quiz_local_'));
              keys.forEach(key => localStorage.removeItem(key));
              console.log(`🗑️ Removidos ${keys.length} itens do Quiz local`);
            }}
            className="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600"
          >
            🗑️ Limpar Dados Locais
          </button>

          <button
            onClick={() => {
              console.log('📊 Dados do Quiz no localStorage:');
              const quizKeys = Object.keys(localStorage).filter(key => key.startsWith('quiz_'));
              quizKeys.forEach(key => {
                console.log(`${key}:`, localStorage.getItem(key));
              });
              console.log(`Total: ${quizKeys.length} itens`);
            }}
            className="px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
          >
            📊 Ver Dados Locais
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuizTestComponent;

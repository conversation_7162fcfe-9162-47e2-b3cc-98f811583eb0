import React, { useState } from 'react';
import { ContentBlock } from '@/types';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RichContentEditor } from '../../RichContentEditor';
import { Loader2 } from 'lucide-react';

/**
 * Componente reutilizável para gerenciar a edição de blocos de conteúdo.
 * Pode ser usado em tarefas, projetos, etapas, etc.
 * Recebe os blocos, callbacks e flags de edição via props.
 * Exibe spinner de loading no botão de salvar enquanto salva.
 * O estado dos blocos é controlado localmente e sincronizado via props.
 */
interface ContentSectionProps {
  blocks: ContentBlock[];
  onSave: (blocks: ContentBlock[]) => void;
  onCancel?: () => void;
  editable?: boolean;
  title?: string;
  description?: string;
}

export const ContentSection: React.FC<ContentSectionProps> = ({
  blocks: initialBlocks,
  onSave,
  onCancel,
  editable = true,
  title = 'Conteúdo',
  description,
}) => {
  const [blocks, setBlocks] = useState<ContentBlock[]>(initialBlocks);
  const [saving, setSaving] = useState(false);

  // Callback chamado ao clicar em salvar. Exibe spinner enquanto aguarda.
  const handleSave = async () => {
    setSaving(true);
    await onSave(blocks);
    setSaving(false);
  };

  return (
    <Card className="min-w-0">
      <CardHeader className="min-w-0">
        <CardTitle className="text-lg font-bold flex items-center gap-2 min-w-0">
          {title}
        </CardTitle>
        {description && <div className="text-sm text-muted-foreground mt-1">{description}</div>}
      </CardHeader>
      <CardContent className="space-y-4 min-w-0">
        <RichContentEditor
          blocks={blocks}
          onBlocksChange={setBlocks}
          editable={editable}
        />
      </CardContent>
    </Card>
  );
};
-- =====================
-- CORREÇÃO IMEDIATA: PERFIL UNDEFINED
-- Execute este comando no Supabase SQL Editor AGORA
-- =====================

-- Verificar o perfil específico que está dando problema
SELECT 'PERFIL ATUAL:' as status;
SELECT 
    id,
    email,
    name,
    role,
    created_at,
    updated_at,
    count(*) OVER (PARTITION BY id) as duplicate_count
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
ORDER BY created_at DESC;

-- Se houver duplicatas, remover mantendo apenas a mais recente
DELETE FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4' 
AND created_at < (
    SELECT max(created_at) 
    FROM public.profiles 
    WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
);

-- Verificar se perfil existe após limpeza
SELECT 'PERFIL APÓS LIMPEZA:' as status;
SELECT 
    id,
    email,
    name,
    role,
    created_at,
    updated_at
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- Se não existir perfil, criar um básico
INSERT INTO public.profiles (
    id, 
    email, 
    name, 
    role, 
    is_active, 
    created_at, 
    updated_at
) 
SELECT 
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    '<EMAIL>',
    'Wagner Vasque',
    'admin',
    true,
    now(),
    now()
WHERE NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
);

-- Verificar resultado final
SELECT 'RESULTADO FINAL:' as status;
SELECT 
    id,
    email,
    name,
    role,
    is_active,
    created_at,
    updated_at
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- Verificar se ainda há duplicatas em geral
SELECT 'VERIFICAÇÃO GERAL DE DUPLICATAS:' as status;
SELECT 
    id,
    count(*) as count
FROM public.profiles 
GROUP BY id 
HAVING count(*) > 1
LIMIT 5;

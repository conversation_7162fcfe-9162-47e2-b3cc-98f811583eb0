-- =====================================================
-- SCRIPT EMERGENCIAL TOTAL - FORÇA BRUTA
-- =====================================================
-- Este script FORÇA a remoção completa e recriação
-- Resolve definitivamente o erro 42P17

-- =====================================================
-- FORÇA BRUTA: REMOVER TUDO SEM EXCEÇÃO
-- =====================================================

-- 1. DESABILITAR RLS FORÇA BRUTA
DO $$
DECLARE
    table_name TEXT;
BEGIN
    FOR table_name IN 
        SELECT t.tablename 
        FROM pg_tables t 
        WHERE t.schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE format('ALTER TABLE IF EXISTS public.%I DISABLE ROW LEVEL SECURITY', table_name);
        EXCEPTION 
            WHEN OTHERS THEN NULL;
        END;
    END LOOP;
    RAISE NOTICE '🔓 RLS DESABILITADO em todas as tabelas';
END $$;

-- 2. DROPAR TODAS AS POLÍTICAS FORÇA BRUTA
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN
        SELECT schemaname, tablename, policyname
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I CASCADE', 
                          policy_record.policyname, 
                          policy_record.schemaname, 
                          policy_record.tablename);
        EXCEPTION 
            WHEN OTHERS THEN NULL;
        END;
    END LOOP;
    RAISE NOTICE '🧹 TODAS as políticas removidas com FORÇA BRUTA';
END $$;

-- 3. AGUARDAR E LIMPAR CACHE
SELECT pg_sleep(2);

-- 4. VERIFICAR LIMPEZA
DO $$
DECLARE
    remaining_policies INTEGER;
BEGIN
    SELECT COUNT(*) INTO remaining_policies FROM pg_policies WHERE schemaname = 'public';
    IF remaining_policies > 0 THEN
        RAISE NOTICE '⚠️ AINDA EXISTEM % políticas. Executando limpeza adicional...', remaining_policies;
        
        -- Limpeza adicional individual
        PERFORM public.drop_all_policies('profiles');
        PERFORM public.drop_all_policies('projects');
        PERFORM public.drop_all_policies('stages');
        PERFORM public.drop_all_policies('tasks');
        PERFORM public.drop_all_policies('project_members');
        PERFORM public.drop_all_policies('task_executors');
    ELSE
        RAISE NOTICE '✅ LIMPEZA TOTAL CONFIRMADA - 0 políticas restantes';
    END IF;
END $$;

-- =====================================================
-- RECRIAÇÃO MÍNIMA E SEGURA
-- =====================================================

-- Reabilitar RLS apenas em tabelas essenciais
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_members ENABLE ROW LEVEL SECURITY;

-- PROFILES - Ultra simples
CREATE POLICY "profiles_all" ON public.profiles FOR ALL USING (true);

-- PROJECTS - Apenas owner (SEM referência a project_members)
CREATE POLICY "projects_owner_only" ON public.projects FOR ALL USING (owner_id = auth.uid());

-- STAGES - Apenas projetos próprios
CREATE POLICY "stages_owner_projects" ON public.stages FOR ALL 
USING (
  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
);

-- TASKS - Apenas básico
CREATE POLICY "tasks_basic" ON public.tasks FOR ALL 
USING (
  assigned_to = auth.uid() OR 
  created_by = auth.uid() OR
  stage_id IN (
    SELECT s.id FROM public.stages s 
    WHERE s.project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
  )
);

-- PROJECT_MEMBERS - Ultra simples
CREATE POLICY "project_members_basic" ON public.project_members FOR ALL 
USING (
  user_id = auth.uid() OR 
  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
);

-- =====================================================
-- VALIDAÇÃO FINAL FORÇA BRUTA
-- =====================================================

DO $$
DECLARE
  total_policies INTEGER;
  projects_policies INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_policies FROM pg_policies WHERE schemaname = 'public';
  SELECT COUNT(*) INTO projects_policies FROM pg_policies WHERE schemaname = 'public' AND tablename = 'projects';
  
  RAISE NOTICE '';
  RAISE NOTICE '🚨 FORÇA BRUTA APLICADA COM SUCESSO!';
  RAISE NOTICE '🔒 Total de políticas: %', total_policies;
  RAISE NOTICE '📋 Políticas projects: %', projects_policies;
  RAISE NOTICE '';
  
  IF projects_policies = 1 THEN
    RAISE NOTICE '✅ ERRO 42P17 ELIMINADO - Apenas 1 política projects (sem recursão)';
  ELSE
    RAISE NOTICE '⚠️ Verificar se há % políticas em projects', projects_policies;
  END IF;
  
  RAISE NOTICE '🎯 SISTEMA FUNCIONANDO COM POLÍTICAS MÍNIMAS';
  RAISE NOTICE '🚀 Execute no frontend para testar';
END $$;

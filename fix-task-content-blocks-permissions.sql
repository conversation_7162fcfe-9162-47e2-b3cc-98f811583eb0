-- =====================
-- CORREÇÃO: Políticas RLS para task_content_blocks
-- Problema: Executores de tarefa não conseguem visualizar conteúdo
-- Solução: Incluir executores nas políticas de acesso
-- =====================

-- Política corrigida para visualizar blocos de conteúdo
drop policy if exists "Project members can view task content blocks" on public.task_content_blocks;
create policy "Project members can view task content blocks"
  on public.task_content_blocks
  for select
  using (
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (select project_id from public.project_members where user_id = auth.uid())
    )
    OR
    -- CORREÇÃO: Incluir executores da tarefa
    task_id in (
      select te.task_id from public.task_executors te
      where te.user_id = auth.uid()
    )
    OR
    -- CORREÇÃO: Incluir aprovadores da tarefa
    task_id in (
      select ta.task_id from public.task_approvers ta
      where ta.user_id = auth.uid()
    )
  );

-- Política corrigida para inserir blocos de conteúdo
drop policy if exists "Project members can insert task content blocks" on public.task_content_blocks;
create policy "Project members can insert task content blocks"
  on public.task_content_blocks
  for insert
  with check (
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (
           select pm.project_id from public.project_members pm
           where pm.user_id = auth.uid() 
           and pm.role in ('manager', 'editor', 'admin')
         )
    )
    OR
    -- CORREÇÃO: Permitir que executores possam inserir blocos
    task_id in (
      select te.task_id from public.task_executors te
      where te.user_id = auth.uid()
    )
  );

-- Política corrigida para atualizar blocos de conteúdo
drop policy if exists "Project members can update task content blocks" on public.task_content_blocks;
create policy "Project members can update task content blocks"
  on public.task_content_blocks
  for update
  using (
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (
           select pm.project_id from public.project_members pm
           where pm.user_id = auth.uid() 
           and pm.role in ('manager', 'editor', 'admin')
         )
    )
    OR
    -- CORREÇÃO: Permitir que executores possam atualizar blocos
    task_id in (
      select te.task_id from public.task_executors te
      where te.user_id = auth.uid()
    )
  );

-- Política corrigida para deletar blocos de conteúdo
drop policy if exists "Project members can delete task content blocks" on public.task_content_blocks;
create policy "Project members can delete task content blocks"
  on public.task_content_blocks
  for delete
  using (
    task_id in (
      select t.id from public.tasks t
      join public.stages s on t.stage_id = s.id
      join public.projects p on s.project_id = p.id
      where p.owner_id = auth.uid()
         or p.id in (
           select pm.project_id from public.project_members pm
           where pm.user_id = auth.uid() 
           and pm.role in ('manager', 'editor', 'admin')
         )
    )
    OR
    -- CORREÇÃO: Permitir que executores possam deletar blocos (se necessário)
    task_id in (
      select te.task_id from public.task_executors te
      where te.user_id = auth.uid()
    )
  );

-- =====================
-- VALIDAÇÃO: Testar se executores podem acessar conteúdo
-- =====================
-- Para testar, execute no SQL Editor:
-- 1. Crie uma tarefa e atribua um executor
-- 2. Faça login como executor
-- 3. Execute: SELECT * FROM task_content_blocks WHERE task_id = 'sua_task_id';
-- 4. Deve retornar os blocos de conteúdo da tarefa

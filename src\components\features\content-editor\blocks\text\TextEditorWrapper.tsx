import React from 'react';
import { TiptapTextBlockEditor } from './TiptapTextBlockEditor';
import { TextBlockContent, BlockConfig } from '@/types';

interface TextEditorWrapperProps {
  editContent: TextBlockContent;
  setEditContent: (c: TextBlockContent) => void;
  mode: 'edit' | 'preview';
  editorKey?: string;
  config?: BlockConfig;
  truncateText?: boolean;
}

/**
 * Wrapper para o editor de texto usando exclusivamente Tiptap
 */
export function TextEditorWrapper(props: TextEditorWrapperProps) {
  return <TiptapTextBlockEditor {...props} />;
}



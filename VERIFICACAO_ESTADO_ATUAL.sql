-- =====================================================
-- VERIFICAÇÃO FINAL E CRIAÇÃO DE DADOS DE TESTE
-- =====================================================

-- 1. VERIFICAR ESTADO ATUAL
SELECT 
    '🔍 ESTADO ATUAL DO SISTEMA' as diagnostico,
    (SELECT COUNT(*) FROM tasks) as total_tasks,
    (SELECT COUNT(*) FROM task_executors) as total_executors,
    (SELECT COUNT(*) FROM projects) as total_projects,
    (SELECT COUNT(*) FROM stages) as total_stages,
    (SELECT COUNT(*) FROM auth.users) as total_users;

-- 2. VERIFICAR USUÁRIO ESPECÍFICO
SELECT 
    '👤 USUÁRIO LOGADO' as info,
    u.id,
    u.email,
    p.name,
    p.role,
    COUNT(te.task_id) as tarefas_como_executor
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
LEFT JOIN task_executors te ON u.id = te.user_id
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
GROUP BY u.id, u.email, p.name, p.role;

-- 3. VERIFICAR SE PRECISAMOS CRIAR DADOS DE TESTE
-- Se não houver tarefas, vamos criar algumas

-- Primeiro, vamos verificar se há projetos e estágios
SELECT 
    '📊 ESTRUTURA DISPONÍVEL' as info,
    p.id as project_id,
    p.name as project_name,
    s.id as stage_id,
    s.name as stage_name
FROM projects p
LEFT JOIN stages s ON p.id = s.project_id
LIMIT 5;

-- 4. SCRIPT PARA CRIAR DADOS DE TESTE (se necessário)
-- DESCOMENTE E EXECUTE APENAS SE NÃO HOUVER DADOS

/*
-- Inserir tarefas de exemplo (substitua os IDs reais)
DO $$
DECLARE
    stage_id_exemplo UUID;
    task_id_1 UUID;
    task_id_2 UUID;
    usuario_id UUID := '4b09be1f-5187-44c0-9b53-87b7c57e45b4';
BEGIN
    -- Buscar um estágio existente
    SELECT id INTO stage_id_exemplo FROM stages LIMIT 1;
    
    IF stage_id_exemplo IS NOT NULL THEN
        -- Criar tarefas de exemplo
        INSERT INTO tasks (id, title, description, status, progress, stage_id, created_at)
        VALUES 
        (gen_random_uuid(), 'Tarefa Teste 1', 'Primeira tarefa de teste para executor', 'pending', 0, stage_id_exemplo, NOW())
        RETURNING id INTO task_id_1;
        
        INSERT INTO tasks (id, title, description, status, progress, stage_id, created_at)
        VALUES 
        (gen_random_uuid(), 'Tarefa Teste 2', 'Segunda tarefa de teste para executor', 'in-progress', 25, stage_id_exemplo, NOW())
        RETURNING id INTO task_id_2;
        
        -- Adicionar o usuário como executor dessas tarefas
        INSERT INTO task_executors (user_id, task_id, created_at)
        VALUES 
        (usuario_id, task_id_1, NOW()),
        (usuario_id, task_id_2, NOW());
        
        RAISE NOTICE 'Dados de teste criados com sucesso!';
    ELSE
        RAISE NOTICE 'Nenhum estágio encontrado. Crie projetos e estágios primeiro.';
    END IF;
END $$;
*/

-- 5. VERIFICAÇÃO FINAL
SELECT 
    '✅ VERIFICAÇÃO FINAL' as resultado,
    u.email as usuario,
    COUNT(te.task_id) as tarefas_executor,
    STRING_AGG(t.title, ', ') as titulos_tarefas
FROM auth.users u
LEFT JOIN task_executors te ON u.id = te.user_id
LEFT JOIN tasks t ON te.task_id = t.id
WHERE u.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
GROUP BY u.id, u.email;

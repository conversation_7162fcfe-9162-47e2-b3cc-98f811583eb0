/**
 * Sistema de syntax highlighting customizado
 * Aplica cores via CSS classes baseado na linguagem
 */

// Mapeamento de tokens para diferentes linguagens
const LANGUAGE_PATTERNS = {
  javascript: [
    { pattern: /\b(function|const|let|var|if|else|for|while|return|class|extends|import|export|from|default|async|await|try|catch|finally|throw|new|this|super|static|public|private|protected)\b/g, class: 'keyword' },
    { pattern: /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'string' },
    { pattern: /\/\/.*$/gm, class: 'comment' },
    { pattern: /\/\*[\s\S]*?\*\//g, class: 'comment' },
    { pattern: /\b\d+(\.\d+)?\b/g, class: 'number' },
    { pattern: /\b(true|false|null|undefined)\b/g, class: 'boolean' },
  ],
  typescript: [
    { pattern: /\b(function|const|let|var|if|else|for|while|return|class|extends|import|export|from|default|async|await|try|catch|finally|throw|new|this|super|static|public|private|protected|interface|type|enum|namespace|declare|readonly)\b/g, class: 'keyword' },
    { pattern: /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'string' },
    { pattern: /\/\/.*$/gm, class: 'comment' },
    { pattern: /\/\*[\s\S]*?\*\//g, class: 'comment' },
    { pattern: /\b\d+(\.\d+)?\b/g, class: 'number' },
    { pattern: /\b(true|false|null|undefined)\b/g, class: 'boolean' },
    { pattern: /:\s*([A-Z][a-zA-Z0-9]*)/g, class: 'type' },
  ],
  python: [
    { pattern: /\b(def|class|if|elif|else|for|while|return|import|from|as|try|except|finally|raise|with|lambda|yield|global|nonlocal|pass|break|continue|and|or|not|in|is)\b/g, class: 'keyword' },
    { pattern: /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'string' },
    { pattern: /#.*$/gm, class: 'comment' },
    { pattern: /\b\d+(\.\d+)?\b/g, class: 'number' },
    { pattern: /\b(True|False|None)\b/g, class: 'boolean' },
  ],
  java: [
    { pattern: /\b(public|private|protected|static|final|abstract|class|interface|extends|implements|import|package|if|else|for|while|do|switch|case|default|return|break|continue|try|catch|finally|throw|throws|new|this|super|void|int|double|float|long|short|byte|char|boolean|String)\b/g, class: 'keyword' },
    { pattern: /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'string' },
    { pattern: /\/\/.*$/gm, class: 'comment' },
    { pattern: /\/\*[\s\S]*?\*\//g, class: 'comment' },
    { pattern: /\b\d+(\.\d+)?[fFdDlL]?\b/g, class: 'number' },
    { pattern: /\b(true|false|null)\b/g, class: 'boolean' },
  ],
  css: [
    { pattern: /([.#]?[a-zA-Z][a-zA-Z0-9-]*)\s*{/g, class: 'selector' },
    { pattern: /([a-zA-Z-]+)\s*:/g, class: 'property' },
    { pattern: /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'string' },
    { pattern: /\/\*[\s\S]*?\*\//g, class: 'comment' },
    { pattern: /#[0-9a-fA-F]{3,6}\b/g, class: 'color' },
    { pattern: /\b\d+(\.\d+)?(px|em|rem|%|vh|vw|pt|pc|in|cm|mm|ex|ch|vmin|vmax|fr)\b/g, class: 'number' },
  ],
  html: [
    { pattern: /(<\/?)([\w-]+)/g, class: 'tag' },
    { pattern: /(\w+)=/g, class: 'attribute' },
    { pattern: /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'string' },
    { pattern: /<!--[\s\S]*?-->/g, class: 'comment' },
  ],
  json: [
    { pattern: /(["'])((?:\\.|(?!\1)[^\\])*?)\1(?=\s*:)/g, class: 'property' },
    { pattern: /(["'])((?:\\.|(?!\1)[^\\])*?)\1(?!\s*:)/g, class: 'string' },
    { pattern: /\b\d+(\.\d+)?\b/g, class: 'number' },
    { pattern: /\b(true|false|null)\b/g, class: 'boolean' },
  ],
  sql: [
    { pattern: /\b(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|TABLE|INDEX|DATABASE|JOIN|INNER|LEFT|RIGHT|OUTER|ON|GROUP|ORDER|BY|HAVING|LIMIT|OFFSET|UNION|ALL|DISTINCT|AS|AND|OR|NOT|IN|EXISTS|BETWEEN|LIKE|IS|NULL|PRIMARY|KEY|FOREIGN|REFERENCES|CONSTRAINT|DEFAULT|AUTO_INCREMENT|UNIQUE|CHECK)\b/gi, class: 'keyword' },
    { pattern: /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, class: 'string' },
    { pattern: /--.*$/gm, class: 'comment' },
    { pattern: /\/\*[\s\S]*?\*\//g, class: 'comment' },
    { pattern: /\b\d+(\.\d+)?\b/g, class: 'number' },
  ]
};

/**
 * Aplica syntax highlighting a um elemento de code block
 * Versão simplificada que não interfere na edição
 */
export function applySyntaxHighlighting(element: HTMLElement, language: string) {
  // Verificações de segurança
  if (!element || !element.isConnected || !document.contains(element)) {
    return;
  }

  const patterns = LANGUAGE_PATTERNS[language as keyof typeof LANGUAGE_PATTERNS];
  if (!patterns || language === 'text') return;

  // Não aplicar highlighting se o elemento está sendo editado
  if (element.contentEditable === 'true' || element.querySelector('[contenteditable="true"]')) {
    return;
  }

  const code = element.textContent || '';
  if (!code.trim()) return;

  let highlightedCode = code;

  // Aplicar padrões em ordem
  patterns.forEach(({ pattern, class: className }) => {
    highlightedCode = highlightedCode.replace(pattern, (match, ...groups) => {
      return `<span class="syntax-${className}">${match}</span>`;
    });
  });

  try {
    // Aplicar highlighting apenas se o conteúdo mudou
    if (element.innerHTML !== highlightedCode) {
      element.innerHTML = highlightedCode;
    }
  } catch (error) {
    console.warn('Erro ao aplicar syntax highlighting:', error);
  }
}

/**
 * Observer para aplicar highlighting automaticamente - versão segura
 */
export function createSyntaxHighlightObserver() {
  const observer = new MutationObserver((mutations) => {
    try {
      mutations.forEach((mutation) => {
        // Verificar se a mutação ainda é válida
        if (!mutation.target || !document.contains(mutation.target as Node)) {
          return;
        }

        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.isConnected) {
            const element = node as HTMLElement;

            // Verificar se o elemento ainda está no DOM
            if (!document.contains(element)) return;

            const codeBlocks = element.querySelectorAll('pre[data-language]:not([data-highlighted="true"])');

            codeBlocks.forEach((block) => {
              const language = block.getAttribute('data-language');
              if (language && language !== 'text' && block.isConnected) {
                applySyntaxHighlighting(block as HTMLElement, language);
              }
            });
          }
        });
      });
    } catch (error) {
      console.warn('Erro no MutationObserver:', error);
    }
  });

  return observer;
}

/**
 * Testes de Compatibilidade para Sistema de Quiz
 * 
 * Valida que:
 * 1. Quizzes existentes (formato antigo) continuam funcionando
 * 2. Modo 'assessment' mantém comportamento atual
 * 3. Modo 'survey' funciona corretamente
 * 4. Conversão entre formatos é transparente
 */

import { QuizUtils, QuizConfig, QuizContent } from '@/types/quiz';

describe('Quiz Compatibility Tests', () => {
  
  describe('QuizUtils Functions', () => {
    test('isAssessmentMode should return true for undefined mode (backward compatibility)', () => {
      const config: QuizConfig = {
        title: 'Test Quiz',
        maxAttempts: 3,
        allowRetry: true,
        passingScore: 70,
        showScore: true,
        showCorrectAnswers: true,
        showFeedback: true,
        showDetailedResults: true,
        showTimer: false,
        isRequired: false,
        blockProgressUntilPassed: false,
        shuffleQuestions: false,
        shuffleOptions: false,
        showProgressBar: true,
        allowSaveDraft: true,
        enableAnalytics: true,
        showResultsToUser: true
        // mode is undefined - should default to assessment
      };

      expect(QuizUtils.isAssessmentMode(config)).toBe(true);
      expect(QuizUtils.isSurveyMode(config)).toBe(false);
    });

    test('isAssessmentMode should return true for explicit assessment mode', () => {
      const config: QuizConfig = {
        title: 'Test Quiz',
        mode: 'assessment',
        maxAttempts: 3,
        allowRetry: true,
        passingScore: 70,
        showScore: true,
        showCorrectAnswers: true,
        showFeedback: true,
        showDetailedResults: true,
        showTimer: false,
        isRequired: false,
        blockProgressUntilPassed: false,
        shuffleQuestions: false,
        shuffleOptions: false,
        showProgressBar: true,
        allowSaveDraft: true,
        enableAnalytics: true,
        showResultsToUser: true
      };

      expect(QuizUtils.isAssessmentMode(config)).toBe(true);
      expect(QuizUtils.isSurveyMode(config)).toBe(false);
    });

    test('isSurveyMode should return true for survey mode', () => {
      const config: QuizConfig = {
        title: 'Test Survey',
        mode: 'survey',
        maxAttempts: 1,
        allowRetry: false,
        passingScore: 0,
        showScore: false,
        showCorrectAnswers: false,
        showFeedback: false,
        showDetailedResults: true,
        showTimer: false,
        isRequired: false,
        blockProgressUntilPassed: false,
        shuffleQuestions: false,
        shuffleOptions: false,
        showProgressBar: true,
        allowSaveDraft: true,
        enableAnalytics: true,
        showResultsToUser: true,
        surveySettings: {
          showAggregatedResults: true,
          allowAnonymous: false,
          showOthersResponses: false,
          collectDemographics: false,
          showResultsAfterSubmission: true
        }
      };

      expect(QuizUtils.isSurveyMode(config)).toBe(true);
      expect(QuizUtils.isAssessmentMode(config)).toBe(false);
    });

    test('ensureCompatibility should add default values for missing fields', () => {
      const partialConfig = {
        title: 'Old Quiz',
        maxAttempts: 3,
        passingScore: 70
      };

      const fullConfig = QuizUtils.ensureCompatibility(partialConfig);

      expect(fullConfig.mode).toBe('assessment');
      expect(fullConfig.surveySettings).toBeDefined();
      expect(fullConfig.surveySettings?.showAggregatedResults).toBe(true);
      expect(fullConfig.title).toBe('Old Quiz');
      expect(fullConfig.maxAttempts).toBe(3);
      expect(fullConfig.passingScore).toBe(70);
    });
  });

  describe('Template Creation', () => {
    test('createAssessmentTemplate should create valid assessment quiz', () => {
      const template = QuizUtils.createAssessmentTemplate();

      expect(template.config.mode).toBe('assessment');
      expect(template.config.showScore).toBe(true);
      expect(template.config.showCorrectAnswers).toBe(true);
      expect(template.config.passingScore).toBeGreaterThan(0);
      expect(template.questions).toHaveLength(1);
      expect(template.questions[0].options?.[0].isCorrect).toBeDefined();
    });

    test('createSurveyTemplate should create valid survey quiz', () => {
      const template = QuizUtils.createSurveyTemplate();

      expect(template.config.mode).toBe('survey');
      expect(template.config.showScore).toBe(false);
      expect(template.config.showCorrectAnswers).toBe(false);
      expect(template.config.surveySettings).toBeDefined();
      expect(template.questions).toHaveLength(3);
      
      // Verificar que opções não têm isCorrect definido (ou é undefined)
      const firstQuestion = template.questions[0];
      if (firstQuestion.options) {
        firstQuestion.options.forEach(option => {
          expect(option.isCorrect).toBeUndefined();
        });
      }
    });
  });

  describe('Option Validation', () => {
    test('optionNeedsCorrection should return true for options with isCorrect defined', () => {
      const assessmentOption = { id: 'opt1', text: 'Option 1', isCorrect: true };
      const surveyOption = { id: 'opt2', text: 'Option 2' };

      expect(QuizUtils.optionNeedsCorrection(assessmentOption)).toBe(true);
      expect(QuizUtils.optionNeedsCorrection(surveyOption)).toBe(false);
    });
  });

  describe('Backward Compatibility', () => {
    test('old quiz format should be treated as assessment mode', () => {
      // Simular quiz no formato antigo (sem campo mode)
      const oldQuizConfig: Partial<QuizConfig> = {
        title: 'Old Format Quiz',
        maxAttempts: 3,
        passingScore: 70,
        showScore: true,
        showCorrectAnswers: true
      };

      const compatibleConfig = QuizUtils.ensureCompatibility(oldQuizConfig);

      expect(QuizUtils.isAssessmentMode(compatibleConfig)).toBe(true);
      expect(QuizUtils.isSurveyMode(compatibleConfig)).toBe(false);
    });

    test('quiz without surveySettings should get default survey settings', () => {
      const configWithoutSurveySettings: Partial<QuizConfig> = {
        title: 'Test',
        mode: 'survey'
      };

      const compatibleConfig = QuizUtils.ensureCompatibility(configWithoutSurveySettings);

      expect(compatibleConfig.surveySettings).toBeDefined();
      expect(compatibleConfig.surveySettings?.showAggregatedResults).toBe(true);
      expect(compatibleConfig.surveySettings?.allowAnonymous).toBe(false);
    });
  });
});

// Função auxiliar para testes de integração
export const createTestQuizContent = (mode: 'assessment' | 'survey' = 'assessment'): QuizContent => {
  if (mode === 'survey') {
    return QuizUtils.createSurveyTemplate();
  } else {
    return QuizUtils.createAssessmentTemplate();
  }
};

// Validação de tipos em tempo de compilação
export const validateQuizTypes = () => {
  // Estes devem compilar sem erros
  const assessmentConfig: QuizConfig = QuizUtils.createAssessmentTemplate().config;
  const surveyConfig: QuizConfig = QuizUtils.createSurveyTemplate().config;
  
  // Verificar que campos opcionais funcionam
  const minimalConfig: QuizConfig = {
    title: 'Minimal',
    maxAttempts: 1,
    allowRetry: false,
    passingScore: 70,
    showScore: true,
    showCorrectAnswers: true,
    showFeedback: true,
    showDetailedResults: true,
    showTimer: false,
    isRequired: false,
    blockProgressUntilPassed: false,
    shuffleQuestions: false,
    shuffleOptions: false,
    showProgressBar: true,
    allowSaveDraft: true,
    enableAnalytics: true,
    showResultsToUser: true
    // mode e surveySettings são opcionais
  };

  return { assessmentConfig, surveyConfig, minimalConfig };
};

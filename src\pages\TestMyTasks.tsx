import React, { useState, useEffect } from 'react';
import { useAuth } from '@/auth/useAuth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabaseClient';

export const TestMyTasks = () => {
  const { user } = useAuth();
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const runDiagnostic = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    const testResults = [];

    try {
      // Teste 1: Verificar usuário
      testResults.push({
        test: 'Usuário logado',
        result: `ID: ${user.id}`,
        status: 'success'
      });

      // Teste 2: Verificar task_executors
      const { data: executors, error: execError } = await supabase
        .from('task_executors')
        .select('*')
        .eq('user_id', user.id);

      testResults.push({
        test: 'Tarefas como executor',
        result: `${executors?.length || 0} tarefas encontradas`,
        status: executors?.length ? 'success' : 'warning',
        data: executors
      });

      // Teste 3: Verificar todas as tarefas
      const { data: allTasks, error: allError } = await supabase
        .from('tasks')
        .select('id, title, status')
        .limit(5);

      testResults.push({
        test: 'Tarefas no sistema',
        result: `${allTasks?.length || 0} tarefas encontradas`,
        status: allTasks?.length ? 'success' : 'error',
        data: allTasks
      });

      // Teste 4: Verificar todos os executores
      const { data: allExecutors, error: allExecError } = await supabase
        .from('task_executors')
        .select('*')
        .limit(5);

      testResults.push({
        test: 'Executores no sistema',
        result: `${allExecutors?.length || 0} executores encontrados`,
        status: allExecutors?.length ? 'success' : 'error',
        data: allExecutors
      });

      // Teste 5: Verificar perfil do usuário
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      testResults.push({
        test: 'Perfil do usuário',
        result: `Nome: ${profile?.name || 'N/A'}, Role: ${profile?.role || 'N/A'}`,
        status: profile ? 'success' : 'error',
        data: profile
      });

    } catch (error: any) {
      testResults.push({
        test: 'Erro geral',
        result: error.message,
        status: 'error'
      });
    } finally {
      setResults(testResults);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      runDiagnostic();
    }
  }, [user?.id]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>🔍 Diagnóstico - Minhas Tarefas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button 
              onClick={runDiagnostic} 
              disabled={loading}
              className="mb-4"
            >
              {loading ? 'Executando...' : 'Executar Diagnóstico'}
            </Button>

            {results.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded border-l-4 ${
                  result.status === 'success' 
                    ? 'border-green-500 bg-green-50' 
                    : result.status === 'warning'
                    ? 'border-yellow-500 bg-yellow-50'
                    : 'border-red-500 bg-red-50'
                }`}
              >
                <h3 className="font-semibold">{result.test}</h3>
                <p className="text-sm text-gray-600">{result.result}</p>
                {result.data && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-blue-600">
                      Ver dados
                    </summary>
                    <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

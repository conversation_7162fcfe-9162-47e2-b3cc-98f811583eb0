import React, { useState } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Code,
  Link,
  List,
  ListOrdered,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Undo,
  Redo,
  CodeSquare,
  ChevronDown,
  Highlighter,
  CheckSquare,
  Type,
  Palette,
  Minus,
  FontIcon,
  LineChart,
  Ruler
} from 'lucide-react';
import { CodeBlockLanguageSelector } from './components/CodeBlockLanguageSelector';

interface TiptapToolbarProps {
  editor: Editor | null;
}

// Componente para botão com tooltip
const ToolbarButton = ({
  onClick,
  isActive = false,
  disabled = false,
  tooltip,
  children,
  shortcut
}: {
  onClick: () => void;
  isActive?: boolean;
  disabled?: boolean;
  tooltip: string;
  children: React.ReactNode;
  shortcut?: string;
}) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <Button
        variant={isActive ? "default" : "ghost"}
        size="sm"
        onClick={onClick}
        disabled={disabled}
        className="h-8 w-8 p-0"
      >
        {children}
      </Button>
    </TooltipTrigger>
    <TooltipContent>
      <div className="text-center">
        <div>{tooltip}</div>
        {shortcut && <div className="text-xs text-gray-500">{shortcut}</div>}
      </div>
    </TooltipContent>
  </Tooltip>
);

// Componente para dropdown de cabeçalhos
const HeadingDropdown = ({ editor }: { editor: Editor }) => {
  const headings = [
    { level: 1, label: 'Título 1', icon: <Heading1 className="h-4 w-4" /> },
    { level: 2, label: 'Título 2', icon: <Heading2 className="h-4 w-4" /> },
    { level: 3, label: 'Título 3', icon: <Heading3 className="h-4 w-4" /> },
    { level: 4, label: 'Título 4', icon: <Type className="h-4 w-4" /> },
  ];

  const getCurrentHeading = () => {
    for (let i = 1; i <= 4; i++) {
      if (editor.isActive('heading', { level: i })) {
        return i;
      }
    }
    return 0;
  };

  const currentLevel = getCurrentHeading();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 px-2">
          <Type className="h-4 w-4" />
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        {headings.map((heading) => (
          <DropdownMenuItem
            key={heading.level}
            onClick={() => editor.chain().focus().toggleHeading({ level: heading.level as any }).run()}
            className={`flex items-center gap-2 ${currentLevel === heading.level ? 'bg-blue-50' : ''}`}
          >
            {heading.icon}
            <span className="text-sm">{heading.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Componente para dropdown de listas
const ListDropdown = ({ editor }: { editor: Editor }) => {
  const lists = [
    {
      type: 'bulletList',
      label: 'Lista com marcadores',
      icon: <List className="h-4 w-4" />,
      action: () => editor.chain().focus().toggleBulletList().run()
    },
    {
      type: 'orderedList',
      label: 'Lista numerada',
      icon: <ListOrdered className="h-4 w-4" />,
      action: () => editor.chain().focus().toggleOrderedList().run()
    },
    {
      type: 'taskList',
      label: 'Lista de tarefas',
      icon: <CheckSquare className="h-4 w-4" />,
      action: () => editor.chain().focus().toggleTaskList().run()
    },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 px-2">
          <List className="h-4 w-4" />
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        {lists.map((list) => (
          <DropdownMenuItem
            key={list.type}
            onClick={list.action}
            className={`flex items-center gap-2 ${editor.isActive(list.type) ? 'bg-blue-50' : ''}`}
          >
            {list.icon}
            <span className="text-sm">{list.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Componente para seletor de cores de destaque
const HighlightColorPicker = ({ editor }: { editor: Editor }) => {
  const colors = [
    { color: '#dcfce7', label: 'Verde claro' },
    { color: '#dbeafe', label: 'Azul claro' },
    { color: '#fce7f3', label: 'Rosa claro' },
    { color: '#e9d5ff', label: 'Roxo claro' },
    { color: '#fef3c7', label: 'Amarelo claro' },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Highlighter className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-40">
        <div className="flex gap-1 p-2">
          {colors.map((colorOption) => (
            <button
              key={colorOption.color}
              className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
              style={{ backgroundColor: colorOption.color }}
              onClick={() => editor.chain().focus().setHighlight({ color: colorOption.color }).run()}
              title={colorOption.label}
            />
          ))}
          <button
            className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform flex items-center justify-center"
            onClick={() => editor.chain().focus().unsetHighlight().run()}
            title="Remover destaque"
          >
            <span className="text-xs">×</span>
          </button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Componente para cores de texto
const TextColorPicker = ({ editor }: { editor: Editor }) => {
  const colors = [
    { id: 'black', name: 'Preto', value: '#000000', class: 'bg-black' },
    { id: 'gray', name: 'Cinza', value: '#6b7280', class: 'bg-gray-500' },
    { id: 'red', name: 'Vermelho', value: '#dc2626', class: 'bg-red-600' },
    { id: 'orange', name: 'Laranja', value: '#ea580c', class: 'bg-orange-600' },
    { id: 'yellow', name: 'Amarelo', value: '#ca8a04', class: 'bg-yellow-600' },
    { id: 'green', name: 'Verde', value: '#16a34a', class: 'bg-green-600' },
    { id: 'blue', name: 'Azul', value: '#2563eb', class: 'bg-blue-600' },
    { id: 'purple', name: 'Roxo', value: '#9333ea', class: 'bg-purple-600' },
    { id: 'pink', name: 'Rosa', value: '#ec4899', class: 'bg-pink-600' },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Palette className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        <div className="p-2">
          <div className="text-xs font-medium mb-2">Cor do texto</div>
          <div className="grid grid-cols-3 gap-1">
            {colors.map((color) => (
              <button
                key={color.id}
                className={`w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform ${color.class}`}
                onClick={() => editor.chain().focus().setColor(color.value).run()}
                title={color.name}
              />
            ))}
            <button
              className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform flex items-center justify-center bg-white"
              onClick={() => editor.chain().focus().unsetColor().run()}
              title="Remover cor"
            >
              <span className="text-xs">×</span>
            </button>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Componente para altura da linha
const LineHeightDropdown = ({ editor }: { editor: Editor }) => {
  const lineHeights = [
    { name: '1.0', value: '1.0', label: 'Simples' },
    { name: '1.15', value: '1.15', label: '1.15' },
    { name: '1.5', value: '1.5', label: '1.5 (Padrão)' },
    { name: '2.0', value: '2.0', label: 'Duplo' },
    { name: '2.5', value: '2.5', label: '2.5' },
    { name: '3.0', value: '3.0', label: 'Triplo' },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 px-2">
          <LineChart className="h-4 w-4 mr-1" />
          <ChevronDown className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {lineHeights.map((height) => (
          <DropdownMenuItem
            key={height.name}
            onClick={() => editor.chain().focus().setLineHeight(height.value).run()}
            style={{ lineHeight: height.value }}
          >
            {height.label}
          </DropdownMenuItem>
        ))}
        <DropdownMenuItem
          onClick={() => editor.chain().focus().unsetLineHeight().run()}
        >
          Remover altura personalizada
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export function TiptapToolbar({ editor }: TiptapToolbarProps) {
  if (!editor) {
    return null;
  }

  const addLink = () => {
    const url = window.prompt('URL do link:');
    if (url) {
      editor.chain().focus().setLink({ href: url }).run();
    }
  };

  const removeLink = () => {
    editor.chain().focus().unsetLink().run();
  };



  return (
    <TooltipProvider>
      <div className="rounded-t-md p-2 bg-gray-50 flex flex-wrap gap-1 items-center border-b border-gray-200">
        {/* Histórico */}
        <ToolbarButton
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          tooltip="Desfazer"
          shortcut="Ctrl+Z"
        >
          <Undo className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          tooltip="Refazer"
          shortcut="Ctrl+Y"
        >
          <Redo className="h-4 w-4" />
        </ToolbarButton>

        <Separator orientation="vertical" className="h-6" />

        {/* Dropdown de Cabeçalhos */}
        <HeadingDropdown editor={editor} />

        <Separator orientation="vertical" className="h-6" />

        {/* Dropdown de Listas */}
        <ListDropdown editor={editor} />

        {/* Dica sobre Tab */}
        <ToolbarButton
          onClick={() => {}}
          disabled={true}
          tooltip="Use Tab para indentar e Shift+Tab para desindentar em listas e code blocks"
        >
          <span className="text-xs text-gray-400">Tab</span>
        </ToolbarButton>

        <Separator orientation="vertical" className="h-6" />

        {/* Formatação de texto */}
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleBold().run()}
          isActive={editor.isActive('bold')}
          tooltip="Negrito"
          shortcut="Ctrl+B"
        >
          <Bold className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().toggleItalic().run()}
          isActive={editor.isActive('italic')}
          tooltip="Itálico"
          shortcut="Ctrl+I"
        >
          <Italic className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().toggleStrike().run()}
          isActive={editor.isActive('strike')}
          tooltip="Riscado"
          shortcut="Ctrl+Shift+S"
        >
          <Strikethrough className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().toggleCode().run()}
          isActive={editor.isActive('code')}
          tooltip="Código inline"
          shortcut="Ctrl+E"
        >
          <Code className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          isActive={editor.isActive('underline')}
          tooltip="Sublinhado"
          shortcut="Ctrl+U"
        >
          <Underline className="h-4 w-4" />
        </ToolbarButton>

        <Separator orientation="vertical" className="h-6" />

        {/* Seletor de cores de destaque */}
        <HighlightColorPicker editor={editor} />

        <Separator orientation="vertical" className="h-6" />

        {/* Links */}
        <ToolbarButton
          onClick={addLink}
          isActive={editor.isActive('link')}
          tooltip="Adicionar link"
          shortcut="Ctrl+K"
        >
          <Link className="h-4 w-4" />
        </ToolbarButton>

        <Separator orientation="vertical" className="h-6" />

        {/* Outros elementos */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={editor.isActive('blockquote') ? 'default' : 'ghost'}
              size="sm"
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className="h-8 w-8 p-0"
            >
              <Quote className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div>Blockquote</div>
              <div className="text-xs text-gray-500">Ctrl+Shift+B</div>
            </div>
          </TooltipContent>
        </Tooltip>

        {/* Code Block com seleção de linguagem */}
        <CodeBlockLanguageSelector editor={editor} />

        <Separator orientation="vertical" className="h-6" />

        {/* Cores de texto */}
        <TextColorPicker editor={editor} />

        {/* Altura da linha */}
        <LineHeightDropdown editor={editor} />

        {/* FontFamily removido devido a conflitos de versão */}

        {/* Regra horizontal */}
        <ToolbarButton
          onClick={() => editor.chain().focus().setHorizontalRule().run()}
          tooltip="Inserir linha horizontal"
        >
          <Minus className="h-4 w-4" />
        </ToolbarButton>

        <Separator orientation="vertical" className="h-6" />

        {/* Alinhamento */}
        <ToolbarButton
          onClick={() => editor.chain().focus().setTextAlign('left').run()}
          isActive={editor.isActive({ textAlign: 'left' })}
          tooltip="Alinhar à esquerda"
        >
          <AlignLeft className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().setTextAlign('center').run()}
          isActive={editor.isActive({ textAlign: 'center' })}
          tooltip="Centralizar"
        >
          <AlignCenter className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().setTextAlign('right').run()}
          isActive={editor.isActive({ textAlign: 'right' })}
          tooltip="Alinhar à direita"
        >
          <AlignRight className="h-4 w-4" />
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor.chain().focus().setTextAlign('justify').run()}
          isActive={editor.isActive({ textAlign: 'justify' })}
          tooltip="Justificar"
        >
          <AlignJustify className="h-4 w-4" />
        </ToolbarButton>

        <Separator orientation="vertical" className="h-6" />


      </div>


    </TooltipProvider>
  );
}

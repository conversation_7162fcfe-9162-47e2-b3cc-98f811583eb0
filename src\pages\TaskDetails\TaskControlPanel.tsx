import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar } from 'lucide-react';

interface Responsible {
  id: string;
  name: string;
  avatar_url?: string;
}

interface TaskControlPanelProps {
  status: string;
  onStatusChange: (status: string) => void;
  progress: number;
  responsible: Responsible | null;
  estimatedHours: number;
  actualHours: number;
  dueDate: string;
  formatDate: (dateString?: string) => string;
}

export const TaskControlPanel: React.FC<TaskControlPanelProps> = ({
  status,
  onStatusChange,
  progress,
  responsible,
  estimatedHours,
  actualHours,
  dueDate,
  formatDate
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="text-lg">Controle da Tarefa</CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <div>
        <label className="text-sm font-medium mb-2 block">Status</label>
        <select 
          className="w-full p-2 border border-gray-300 rounded-md"
          value={status}
          onChange={(e) => onStatusChange(e.target.value)}
        >
          <option value="todo">Para Fazer</option>
          <option value="in-progress">Em Andamento</option>
          <option value="review">Em Revisão</option>
          <option value="approved">Aprovada</option>
          <option value="completed">Concluída</option>
        </select>
      </div>
      <div>
        <label className="text-sm font-medium mb-2 block">Progresso</label>
        <div className="space-y-2">
          <Progress value={progress} className="w-full" />
          <div className="flex justify-between text-sm text-gray-600">
            <span>0%</span>
            <span className="font-medium">{progress}%</span>
            <span>100%</span>
          </div>
        </div>
      </div>
      <Separator />
      <div>
        <label className="text-sm font-medium mb-2 block">Com Quem Está</label>
        <div className="bg-task-bg border border-task/20 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <Avatar className="w-8 h-8">
              <AvatarImage src={responsible?.avatar_url || '/placeholder.svg'} />
              <AvatarFallback>{responsible?.name?.[0] || '?'}</AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium text-sm">{responsible?.name || 'Desconhecido'}</p>
              <p className="text-xs text-gray-600">Responsável</p>
            </div>
          </div>
        </div>
      </div>
      <div>
        <label className="text-sm font-medium mb-2 block">Tempo</label>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <p className="text-gray-600">Estimado</p>
            <p className="font-medium">{estimatedHours}h</p>
          </div>
          <div>
            <p className="text-gray-600">Realizado</p>
            <p className="font-medium">{actualHours}h</p>
          </div>
        </div>
      </div>
      <div>
        <label className="text-sm font-medium mb-2 block">Prazo</label>
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-600" />
          <span className="text-sm">{formatDate(dueDate)}</span>
        </div>
      </div>
    </CardContent>
  </Card>
); 
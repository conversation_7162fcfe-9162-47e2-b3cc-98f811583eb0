# ARQUITETURA DE SEGURANÇA - SISTEMA DE GESTÃO DE PROJETOS
**Versão:** 2.0 - Julho 2025  
**Status:** Planejamento Arquitetural Pós-Limpeza RLS  
**Objetivo:** Definir nova arquitetura de segurança sem recursão e com alta performance

---

## 📋 ÍNDICE
1. [Casos de Uso de Acesso](#casos-de-uso)
2. [<PERSON><PERSON>õ<PERSON>](#matriz-de-permissoes)
3. [Arquitetura Proposta](#arquitetura-proposta)
4. [Prevenção de Recursão](#prevencao-recursao)
5. [Plano de Implementação](#plano-implementacao)

---

## 🎯 CASOS DE USO DE ACESSO {#casos-de-uso}

### **PROJETOS**
```yaml
VISUALIZAR_PROJETOS:
  - Admin: Todos os projetos
  - Manager: Projetos onde é owner ou member
  - Member: Apenas projetos onde é member

CRIAR_PROJETOS:
  - Admin: Qualquer projeto
  - Manager: Qualquer projeto
  - Member: Projetos próprios apenas

EDITAR_PROJETOS:
  - Admin: Qualquer projeto
  - Manager: Projetos onde é owner
  - Member: Projetos próprios apenas

EXCLUIR_PROJETOS:
  - Admin: Qualquer projeto
  - Manager: Projetos onde é owner
  - Member: Projetos próprios apenas
```

### **ETAPAS (STAGES)**
```yaml
VISUALIZAR_ETAPAS:
  - Regra: Mesma do projeto pai
  - Adicional: Stage responsibles podem ver a etapa

CRIAR_ETAPAS:
  - Admin: Em qualquer projeto
  - Manager: Em projetos onde pode editar
  - Member: Em projetos próprios

EDITAR_ETAPAS:
  - Admin: Qualquer etapa
  - Manager: Etapas de projetos onde é owner
  - Member: Etapas próprias
  - Stage Responsible: Etapas sob sua responsabilidade

EXCLUIR_ETAPAS:
  - Admin: Qualquer etapa
  - Manager: Etapas de projetos onde é owner
  - Member: Etapas próprias
```

### **TAREFAS (TASKS)**
```yaml
VISUALIZAR_TAREFAS:
  - Regra Base: Mesma do projeto pai
  - Executors: Podem ver tarefas atribuídas
  - Approvers: Podem ver tarefas para aprovação
  - Comentaristas: Podem ver tarefas comentadas

CRIAR_TAREFAS:
  - Admin: Em qualquer projeto/etapa
  - Manager: Em projetos onde pode editar
  - Member: Em projetos/etapas próprias
  - Stage Responsible: Em suas etapas

EDITAR_TAREFAS:
  - Admin: Qualquer tarefa
  - Manager: Tarefas de projetos onde é owner
  - Member: Tarefas próprias
  - Executor: Tarefas atribuídas (status, evidence)
  - Approver: Tarefas para aprovação (approval_status)

EXCLUIR_TAREFAS:
  - Admin: Qualquer tarefa
  - Manager: Tarefas de projetos onde é owner
  - Member: Tarefas próprias
```

### **PERFIS (PROFILES)**
```yaml
VISUALIZAR_PERFIS:
  - Own Profile: Sempre visível
  - Active Users: Visíveis para autocomplete/colaboração
  - Project Members: Detalhes completos para membros do mesmo projeto

EDITAR_PERFIS:
  - Own Profile: Sempre editável
  - Admin: Pode editar qualquer perfil
  - Manager: Não pode editar outros perfis
  - Member: Apenas próprio perfil
```

---

## 📊 MATRIZ DE PERMISSÕES {#matriz-de-permissoes}

### **TABELA: profiles**
| Operação | Admin | Manager | Member | Own Profile | Active User | Project Member |
|----------|-------|---------|--------|-------------|-------------|----------------|
| SELECT   | ✅ ALL| ✅ ACTIVE| ✅ ACTIVE| ✅ SELF    | ✅ BASIC   | ✅ DETAILS    |
| INSERT   | ✅     | ✅      | ✅     | ✅ SELF     | ❌         | ❌             |
| UPDATE   | ✅ ALL| ❌      | ❌     | ✅ SELF     | ❌         | ❌             |
| DELETE   | ✅ ALL| ❌      | ❌     | ❌          | ❌         | ❌             |

### **TABELA: projects**
| Operação | Admin | Manager | Member | Owner | Project Member |
|----------|-------|---------|--------|-------|----------------|
| SELECT   | ✅ ALL| ✅ MEMBER| ✅ MEMBER| ✅ OWNED| ✅ MEMBER     |
| INSERT   | ✅     | ✅      | ✅     | ✅     | ❌             |
| UPDATE   | ✅ ALL| ✅ OWNED| ✅ OWNED| ✅ OWNED| ❌            |
| DELETE   | ✅ ALL| ✅ OWNED| ✅ OWNED| ✅ OWNED| ❌            |

### **TABELA: stages**
| Operação | Admin | Manager | Member | Project Owner | Stage Responsible |
|----------|-------|---------|--------|---------------|-------------------|
| SELECT   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ RESPONSIBLE   |
| INSERT   | ✅     | ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ❌               |
| UPDATE   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ RESPONSIBLE   |
| DELETE   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ❌               |

### **TABELA: tasks**
| Operação | Admin | Manager | Member | Project Owner | Executor | Approver |
|----------|-------|---------|--------|---------------|----------|----------|
| SELECT   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ ASSIGNED| ✅ APPROVAL|
| INSERT   | ✅     | ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ❌        | ❌        |
| UPDATE   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ STATUS | ✅ APPROVAL|
| DELETE   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ❌        | ❌        |

### **TABELAS RELACIONAIS (task_executors, task_approvers, etc.)**
| Operação | Admin | Manager | Member | Project Owner | Task Creator |
|----------|-------|---------|--------|---------------|--------------|
| SELECT   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ CREATED  |
| INSERT   | ✅     | ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ CREATED  |
| UPDATE   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ CREATED  |
| DELETE   | ✅ ALL| ✅ PROJECT| ✅ PROJECT| ✅ PROJECT  | ✅ CREATED  |

---

## 🏗️ ARQUITETURA PROPOSTA {#arquitetura-proposta}

### **PRINCÍPIOS FUNDAMENTAIS**

1. **Zero Recursão**: Políticas não consultam outras tabelas que tenham RLS
2. **Performance First**: Usar índices e consultas diretas sempre que possível
3. **Simplicidade**: Políticas claras e fáceis de debugar
4. **Fallback**: Aplicação deve funcionar mesmo se RLS falhar

### **ESTRATÉGIA DE IMPLEMENTAÇÃO**

#### **NÍVEL 1: Tabelas Base (Sem Dependências)**
```sql
-- Estas tabelas não dependem de outras para RLS
profiles          -> Apenas auth.uid() e campos próprios
projects          -> Apenas created_by e campos próprios
```

#### **NÍVEL 2: Tabelas com Dependência Direta**
```sql
-- Dependem apenas de tabelas do Nível 1
project_members   -> Depende de projects (via project_id)
stages           -> Depende de projects (via project_id)
```

#### **NÍVEL 3: Tabelas com Dependência Indireta**
```sql
-- Dependem de tabelas do Nível 2, mas sem consultas RLS
tasks            -> Depende de stages, mas usa apenas task.stage_id
stage_responsibles -> Depende de stages, mas usa apenas stage_id
```

#### **NÍVEL 4: Tabelas Relacionais**
```sql
-- Dependem de tasks, mas sem consultas complexas
task_executors
task_approvers
task_content_blocks
task_comments
evidence
```

### **PADRÕES DE POLÍTICA**

#### **PADRÃO 1: Acesso Próprio (Own Access)**
```sql
-- Para registros criados pelo usuário
CREATE POLICY "table_own_access" ON table FOR SELECT USING (
  created_by = auth.uid()
);
```

#### **PADRÃO 2: Acesso por Role (Role-Based)**
```sql
-- Para admins e managers
CREATE POLICY "table_admin_access" ON table FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('admin', 'manager')
  )
);
```

#### **PADRÃO 3: Acesso por Membro do Projeto (Project Membership)**
```sql
-- Para membros do projeto (SEM RECURSÃO)
CREATE POLICY "table_project_member" ON table FOR SELECT USING (
  project_id IN (
    SELECT pm.project_id 
    FROM project_members pm 
    WHERE pm.user_id = auth.uid()
  )
);
```

#### **PADRÃO 4: Acesso por Atribuição (Assignment-Based)**
```sql
-- Para executores e aprovadores (SEM RECURSÃO)
CREATE POLICY "table_assigned_access" ON table FOR SELECT USING (
  id IN (
    SELECT te.task_id 
    FROM task_executors te 
    WHERE te.user_id = auth.uid()
  )
  OR 
  id IN (
    SELECT ta.task_id 
    FROM task_approvers ta 
    WHERE ta.user_id = auth.uid()
  )
);
```

---

## ⚡ PREVENÇÃO DE RECURSÃO {#prevencao-recursao}

### **REGRAS ANTI-RECURSÃO**

1. **Nunca consultar tabelas com RLS dentro de políticas RLS**
2. **Usar apenas tabelas relacionais simples (sem RLS) para verificações**
3. **Evitar JOINs complexos em políticas**
4. **Preferir EXISTS com subqueries simples**

### **TABELAS QUE NUNCA TERÃO RLS**
```sql
-- Estas tabelas permanecem sempre acessíveis para evitar recursão
auth.users                    -- Gerenciada pelo Supabase Auth
public.enum_*                 -- Tabelas de enumeração
public.lookup_*               -- Tabelas de lookup
```

### **DEPENDÊNCIAS MAPEADAS**
```
profiles (BASE)
├── projects (usa profiles.id para created_by)
    ├── project_members (usa projects.id)
    ├── stages (usa projects.id)
        ├── stage_responsibles (usa stages.id)
        ├── tasks (usa stages.id)
            ├── task_executors (usa tasks.id)
            ├── task_approvers (usa tasks.id)
            ├── task_content_blocks (usa tasks.id)
            ├── task_comments (usa tasks.id)
            └── evidence (usa tasks.id)
```

### **SOLUÇÕES PARA CASOS COMPLEXOS**

#### **Caso 1: Verificar se usuário pode ver task**
```sql
-- ❌ RECURSÃO (task consulta project que tem RLS)
WHERE task.project_id IN (SELECT id FROM projects WHERE ...)

-- ✅ SEM RECURSÃO (consulta direta project_members)
WHERE task.project_id IN (
  SELECT pm.project_id FROM project_members pm 
  WHERE pm.user_id = auth.uid()
)
```

#### **Caso 2: Autocomplete de usuários**
```sql
-- ❌ RECURSÃO (profiles consulta project_members que consulta projects)
WHERE id IN (SELECT user_id FROM project_members pm ...)

-- ✅ SEM RECURSÃO (lógica no frontend ou view específica)
WHERE is_active = true  -- Autocomplete mostra todos usuários ativos
```

---

## 📅 PLANO DE IMPLEMENTAÇÃO {#plano-implementacao}

### **FASE 1: Preparação (1-2 dias)**
- [x] Remover todas as políticas RLS existentes
- [x] Desabilitar RLS em todas as tabelas
- [x] Criar documentação de arquitetura
- [ ] Testar funcionalidades básicas sem RLS
- [ ] Mapear todas as queries críticas do sistema

### **FASE 2: Implementação Base (3-5 dias)**
```sql
DIA 1: profiles
  - Política básica (own + admin)
  - Política de autocomplete (ativos)
  - Teste de login e perfil

DIA 2: projects + project_members
  - Políticas de projeto por ownership
  - Políticas de membership
  - Teste de CRUD de projetos

DIA 3: stages + stage_responsibles
  - Políticas baseadas em project membership
  - Políticas para responsáveis
  - Teste de CRUD de etapas
```

### **FASE 3: Implementação Avançada (5-7 dias)**
```sql
DIA 4-5: tasks
  - Políticas base por projeto
  - Políticas para executores/aprovadores
  - Teste de CRUD de tarefas

DIA 6-7: Tabelas relacionais
  - task_executors, task_approvers
  - task_content_blocks, task_comments
  - evidence
  - Teste completo do sistema
```

### **FASE 4: Otimização (2-3 dias)**
- Performance tuning
- Índices específicos para RLS
- Monitoramento de queries
- Documentação final

---

## 🎯 CRITÉRIOS DE SUCESSO

### **Funcionalidade**
- [ ] Todos os CRUDs funcionando
- [ ] Autocomplete de usuários funcionando
- [ ] Permissões por role respeitadas
- [ ] Atribuições de tarefa funcionando

### **Performance**
- [ ] Queries RLS < 100ms
- [ ] Sem timeout em autocomplete
- [ ] Dashboard carregando < 2s
- [ ] Sem lock de banco

### **Segurança**
- [ ] Usuários só veem dados permitidos
- [ ] Impossível acessar dados de outros projetos
- [ ] Logs de auditoria funcionando
- [ ] Policies testadas contra bypass

### **Manutenibilidade**
- [ ] Zero recursão em políticas
- [ ] Documentação completa
- [ ] Testes automatizados
- [ ] Rollback plan disponível

---

## 📝 NOTAS DE IMPLEMENTAÇÃO

### **Comandos Úteis Para Debug**
```sql
-- Ver todas as políticas ativas
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies WHERE schemaname = 'public';

-- Verificar RLS habilitado
SELECT tablename, rowsecurity FROM pg_tables 
WHERE schemaname = 'public';

-- Testar query como usuário específico
SET SESSION AUTHORIZATION 'user_id';
SELECT * FROM table_name;
RESET SESSION AUTHORIZATION;
```

### **Fallbacks na Aplicação**
```typescript
// Sempre incluir fallback no código
try {
  const data = await supabase.from('table').select('*');
  return data;
} catch (error) {
  console.warn('RLS Error, using fallback:', error);
  // Fallback para admin ou cached data
  return fallbackData;
}
```

---

**🏁 PRÓXIMO PASSO:** Executar `REMOVE_ALL_RLS.sql` no Supabase e começar Fase 2.

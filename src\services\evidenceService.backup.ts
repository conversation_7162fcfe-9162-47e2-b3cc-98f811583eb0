import { supabase } from '@/lib/supabaseClient';
import { Evidence } from '@/types';

export interface FileValidationResult {
    isValid: boolean;
    error?: string;
}

export interface EvidenceUploadOptions {
    taskId: string;
    blockId: string;
    file: File;
    userId: string;
}

export interface EvidenceUploadResponse {
    success: boolean;
    data?: Evidence;
    error?: string;
}

class EvidenceServiceClass {
    private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private readonly ALLOWED_TYPES = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/csv'
    ];

    constructor() { }

    validateFile(file: File): FileValidationResult {
        if (file.size > this.MAX_FILE_SIZE) {
            return {
                isValid: false,
                error: `Arquivo muito grande. O tamanho máximo permitido é ${this.MAX_FILE_SIZE / 1024 / 1024}MB`
            };
        }

        if (!this.ALLOWED_TYPES.includes(file.type)) {
            return {
                isValid: false,
                error: 'Tipo de arquivo não permitido. Tipos aceitos: imagens, PDF, DOC, DOCX, TXT e CSV'
            };
        }

        return { isValid: true };
    }

    async uploadEvidence(options: EvidenceUploadOptions): Promise<EvidenceUploadResponse> {
        try {
            const { taskId, blockId, file, userId } = options;

            const validationResult = this.validateFile(file);
            if (!validationResult.isValid) {
                return {
                    success: false,
                    error: validationResult.error
                };
            }

            // Upload file to storage
            const filename = `${Date.now()}-${file.name}`;
            const { data: fileData, error: uploadError } = await supabase.storage
                .from('evidence')
                .upload(`${taskId}/${filename}`, file);

            if (uploadError) throw uploadError;

            // Get public URL
            const { data: { publicUrl } } = supabase.storage
                .from('evidence')
                .getPublicUrl(`${taskId}/${filename}`);

            // Create evidence record
            const { data: evidenceData, error: dbError } = await supabase
                .from('evidence')
                .insert({
                    task_id: taskId,
                    block_id: blockId,
                    type: file.type.startsWith('image/') ? 'image' : 'file',
                    content: publicUrl,
                    file_name: file.name,
                    file_size: file.size,
                    mime_type: file.type,
                    uploaded_by: userId,
                    status: 'pending'
                })
                .select()
                .single();

            if (dbError) throw dbError;

            // Return success response
            return {
                success: true,
                data: {
                    id: evidenceData.id,
                    taskId: evidenceData.task_id,
                    type: evidenceData.type as 'file' | 'image' | 'text' | 'url',
                    content: evidenceData.content,
                    fileName: evidenceData.file_name,
                    fileSize: evidenceData.file_size,
                    uploadedAt: evidenceData.created_at,
                    uploadedBy: {
                        id: userId,
                        name: '',
                        email: '',
                        role: 'member',
                        isActive: true
                    },
                    status: 'pending'
                }
            };
        } catch (error) {
            console.error('Error uploading evidence:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    async getDownloadUrl(evidence: Evidence): Promise<string> {
        if (evidence.content.startsWith('http')) {
            return evidence.content;
        }

        const { data: { publicUrl } } = supabase.storage
            .from('evidence')
            .getPublicUrl(`${evidence.taskId}/${evidence.fileName}`);

        return publicUrl;
    }

    async removeEvidence(evidenceId: string): Promise<void> {
        try {
            // Get evidence record to find file path
            const { data: evidence, error: fetchError } = await supabase
                .from('evidence')
                .select('file_name, task_id')
                .eq('id', evidenceId)
                .single();

            if (fetchError) throw fetchError;

            // Remove from storage if file exists
            if (evidence?.file_name && evidence.task_id) {
                const { error: storageError } = await supabase.storage
                    .from('evidence')
                    .remove([`${evidence.task_id}/${evidence.file_name}`]);

                if (storageError) throw storageError;
            }

            // Remove from database
            const { error: deleteError } = await supabase
                .from('evidence')
                .delete()
                .eq('id', evidenceId);

            if (deleteError) throw deleteError;
        } catch (error) {
            console.error('Error removing evidence:', error);
            throw error;
        }
    }
}

export const EvidenceService = new EvidenceServiceClass();

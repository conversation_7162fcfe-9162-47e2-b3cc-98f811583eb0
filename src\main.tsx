import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Filtra erros de PublicKeyCredential (ex: YouTube embed) para não poluir o console
if (typeof window !== 'undefined') {
  window.addEventListener('error', function (e) {
    if (e.message && e.message.includes('PublicKeyCredential')) {
      e.preventDefault();
      return false;
    }
  });
}

console.info('[Aviso] O erro "PublicKeyCredential is not defined" pode aparecer ao usar embeds de vídeo (ex: YouTube). Esse erro é disparado por scripts de terceiros dentro do iframe, não afeta o funcionamento do app e pode ser ignorado com segurança.');

createRoot(document.getElementById("root")!).render(<App />);

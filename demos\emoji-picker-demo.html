<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demonstração do Seletor de Emoji</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .toolbar {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .toolbar-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .toolbar-button:hover {
            background-color: #e9ecef;
        }
        .toolbar-button.active {
            background-color: #007bff;
            color: white;
        }
        .separator {
            width: 1px;
            height: 24px;
            background-color: #dee2e6;
            margin: 0 4px;
        }
        .emoji-picker {
            position: absolute;
            top: 100%;
            left: 0;
            margin-top: 4px;
            padding: 12px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 50;
            width: 320px;
        }
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 4px;
            max-height: 192px;
            overflow-y: auto;
        }
        .emoji-button {
            width: 32px;
            height: 32px;
            font-size: 18px;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        .emoji-button:hover {
            background-color: #f1f3f4;
        }
        .editor {
            min-height: 200px;
            padding: 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            line-height: 1.5;
            outline: none;
            background: white;
        }
        .relative {
            position: relative;
        }
        .hidden {
            display: none;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .description {
            color: #666;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Demonstração do Seletor de Emoji</h1>
        <p class="description">Esta demonstração mostra a funcionalidade do seletor de emoji implementado na barra de ferramentas do editor de texto.</p>
        
        <div class="toolbar">
            <!-- Botões de formatação simulados -->
            <button class="toolbar-button" title="Negrito">B</button>
            <button class="toolbar-button" title="Itálico">I</button>
            <button class="toolbar-button" title="Sublinhado">U</button>
            <div class="separator"></div>
            
            <!-- Botão de emoji funcional -->
            <div class="relative">
                <button class="toolbar-button" id="emojiButton" title="Inserir emoji">
                    😊
                </button>
                <div class="emoji-picker hidden" id="emojiPicker">
                    <div class="emoji-grid" id="emojiGrid">
                        <!-- Emojis serão inseridos aqui pelo JavaScript -->
                    </div>
                </div>
            </div>
            
            <div class="separator"></div>
            <button class="toolbar-button" title="Inserir">+</button>
        </div>
        
        <div class="editor" id="editor" contenteditable="true">
            Clique no botão de emoji (😊) na barra de ferramentas acima para inserir emojis neste texto! 🎉
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 6px; border-left: 4px solid #007bff;">
            <strong>Funcionalidades implementadas:</strong>
            <ul style="margin: 10px 0 0 20px;">
                <li>✅ Botão de emoji na barra de ferramentas</li>
                <li>✅ Seletor de emoji com grade de 10 colunas</li>
                <li>✅ Mais de 160 emojis populares organizados</li>
                <li>✅ Inserção de emoji na posição do cursor</li>
                <li>✅ Fechamento automático ao clicar fora</li>
                <li>✅ Interface responsiva e intuitiva</li>
            </ul>
        </div>
    </div>

    <script>
        // Lista de emojis (mesma do componente React)
        const emojis = [
            '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
            '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
            '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
            '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
            '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
            '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
            '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
            '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
            '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
            '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
            '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏',
            '🙌', '🤲', '🤝', '🙏', '✍️', '💪', '🦾', '🦿', '🦵', '🦶',
            '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
            '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
            '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
            '⭐', '🌟', '✨', '⚡', '☄️', '💥', '🔥', '🌈', '☀️', '🌤️'
        ];

        // Elementos DOM
        const emojiButton = document.getElementById('emojiButton');
        const emojiPicker = document.getElementById('emojiPicker');
        const emojiGrid = document.getElementById('emojiGrid');
        const editor = document.getElementById('editor');

        // Criar botões de emoji
        emojis.forEach(emoji => {
            const button = document.createElement('button');
            button.className = 'emoji-button';
            button.textContent = emoji;
            button.title = emoji;
            button.addEventListener('click', () => insertEmoji(emoji));
            emojiGrid.appendChild(button);
        });

        // Toggle do seletor de emoji
        emojiButton.addEventListener('click', (e) => {
            e.stopPropagation();
            emojiPicker.classList.toggle('hidden');
        });

        // Fechar seletor ao clicar fora
        document.addEventListener('click', (e) => {
            if (!emojiPicker.contains(e.target) && !emojiButton.contains(e.target)) {
                emojiPicker.classList.add('hidden');
            }
        });

        // Inserir emoji no editor
        function insertEmoji(emoji) {
            const selection = window.getSelection();
            const range = selection.getRangeAt(0);
            
            // Se o foco não estiver no editor, colocar o emoji no final
            if (!editor.contains(range.commonAncestorContainer)) {
                editor.focus();
                const newRange = document.createRange();
                newRange.selectNodeContents(editor);
                newRange.collapse(false);
                selection.removeAllRanges();
                selection.addRange(newRange);
            }
            
            // Inserir o emoji
            const textNode = document.createTextNode(emoji);
            range.insertNode(textNode);
            range.setStartAfter(textNode);
            range.setEndAfter(textNode);
            selection.removeAllRanges();
            selection.addRange(range);
            
            // Fechar o seletor
            emojiPicker.classList.add('hidden');
            
            // Manter o foco no editor
            editor.focus();
        }

        // Focar no editor ao carregar a página
        window.addEventListener('load', () => {
            editor.focus();
        });
    </script>
</body>
</html>
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageCircle, Send } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';

interface Comment {
  id: string;
  content: string;
  author: any;
  createdAt: string;
}

interface TaskCommentsProps {
  comments: Comment[];
  newComment: string;
  onNewCommentChange: (value: string) => void;
  onAddComment: () => void;
  userLoading?: boolean;
}

export const TaskComments: React.FC<TaskCommentsProps> = ({ comments, newComment, onNewCommentChange, onAddComment, userLoading }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <MessageCircle className="w-5 h-5" />
        Comentários ({comments.length})
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      {comments.map((comment) => (
        <div key={comment.id} className="flex gap-3">
          <Avatar className="w-8 h-8">
            <AvatarImage src={comment.author?.avatar_url || '/placeholder.svg'} />
            <AvatarFallback>{comment.author?.name?.[0] || '?'}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm">{comment.author?.name || 'Desconhecido'}</span>
                <span className="text-xs text-gray-500">{comment.createdAt}</span>
              </div>
              <p className="text-sm">{comment.content}</p>
            </div>
          </div>
        </div>
      ))}
      <div className="flex gap-3 mt-4">
        <Avatar className="w-8 h-8">
          <AvatarFallback>U</AvatarFallback>
        </Avatar>
        <div className="flex-1 space-y-2">
          <Textarea
            placeholder="Adicionar comentário..."
            value={newComment}
            onChange={(e) => onNewCommentChange(e.target.value)}
            className="min-h-[80px]"
            disabled={userLoading}
          />
          <div className="flex justify-end gap-2">
            <Button size="sm" variant="outline" onClick={() => onNewCommentChange('')} disabled={userLoading}>
              Cancelar
            </Button>
            <Button size="sm" onClick={onAddComment} disabled={userLoading || !newComment.trim()}>
              <Send className="w-4 h-4 mr-1" />
              Enviar
            </Button>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
); 
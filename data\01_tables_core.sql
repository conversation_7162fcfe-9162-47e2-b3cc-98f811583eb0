-- =====================================================
-- TABELAS PRINCIPAIS DO SISTEMA
-- =====================================================
-- Contém: profiles, projects, stages, tasks
-- Dependências: Supabase Auth
-- Versão: 2.0 - Julho 2025

-- Verificar dependências
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'auth') THEN
    RAISE EXCEPTION 'Schema auth não encontrado. Verifique se o Supabase está configurado corretamente.';
  END IF;
  RAISE NOTICE '✅ Dependências verificadas';
END $$;

-- =====================================================
-- LIMPEZA DE TABELAS EXISTENTES
-- =====================================================

-- Remover tabelas em ordem reversa de dependência
DROP TABLE IF EXISTS public.user_notifications CASCADE;
DROP TABLE IF EXISTS public.project_history CASCADE;
DROP TABLE IF EXISTS public.task_comments CASCADE;
DROP TABLE IF EXISTS public.task_attachments CASCADE;
DROP TABLE IF EXISTS public.evidence CASCADE;
DROP TABLE IF EXISTS public.task_content_blocks CASCADE;
DROP TABLE IF EXISTS public.task_approvers CASCADE;
DROP TABLE IF EXISTS public.task_executors CASCADE;
DROP TABLE IF EXISTS public.project_members CASCADE;
DROP TABLE IF EXISTS public.stage_responsibles CASCADE;
DROP TABLE IF EXISTS public.tasks CASCADE;
DROP TABLE IF EXISTS public.stages CASCADE;
DROP TABLE IF EXISTS public.projects CASCADE;
DROP TABLE IF EXISTS public.profiles CASCADE;

-- Remover tipos ENUM existentes
DROP TYPE IF EXISTS public.project_status CASCADE;
DROP TYPE IF EXISTS public.stage_status CASCADE;
DROP TYPE IF EXISTS public.task_status CASCADE;
DROP TYPE IF EXISTS public.project_member_role CASCADE;

-- =====================================================
-- CRIAÇÃO DE TIPOS ENUM
-- =====================================================

-- Status do projeto
CREATE TYPE public.project_status AS ENUM (
  'planning',    -- Planejamento
  'active',      -- Ativo
  'on-hold',     -- Pausado
  'completed',   -- Concluído
  'cancelled'    -- Cancelado
);

-- Status do estágio
CREATE TYPE public.stage_status AS ENUM (
  'not-started', -- Não iniciado
  'in-progress', -- Em andamento
  'completed',   -- Concluído
  'blocked'      -- Bloqueado
);

-- Status da tarefa
CREATE TYPE public.task_status AS ENUM (
  'todo',        -- A fazer
  'in-progress', -- Em andamento
  'review',      -- Em revisão
  'approved',    -- Aprovado
  'completed'    -- Concluído
);

-- Papel do membro no projeto
CREATE TYPE public.project_member_role AS ENUM (
  'admin',       -- Administrador
  'manager',     -- Gerente
  'editor',      -- Editor
  'executor',    -- Executor
  'approver',    -- Aprovador
  'member'       -- Membro
);

-- =====================================================
-- TABELA: profiles
-- =====================================================
-- Perfil do usuário vinculado ao Supabase Auth

CREATE TABLE public.profiles (
  id uuid NOT NULL,
  name text NOT NULL,
  email text NOT NULL,
  role text NOT NULL DEFAULT 'member',
  position text NULL,
  phone text NULL,
  avatar_url text NULL,
  is_active boolean NULL DEFAULT true,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_email_key UNIQUE (email),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users (id) ON DELETE CASCADE,
  CONSTRAINT profiles_role_check CHECK (role IN ('admin', 'manager', 'member'))
);

-- Índices para performance
CREATE INDEX idx_profiles_name ON public.profiles(name);
CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_profiles_role ON public.profiles(role);

-- Comentários
COMMENT ON TABLE public.profiles IS 'Perfis de usuário vinculados ao Supabase Auth';
COMMENT ON COLUMN public.profiles.role IS 'Papel global: admin, manager, member';

-- =====================================================
-- TABELA: projects
-- =====================================================
-- Projetos do sistema

CREATE TABLE public.projects (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  name text NOT NULL,
  description text,
  status public.project_status DEFAULT 'planning'::public.project_status,
  progress integer DEFAULT 0,
  owner_id uuid,
  start_date date,
  end_date date,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT projects_pkey PRIMARY KEY (id),
  CONSTRAINT projects_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES public.profiles(id) ON DELETE SET NULL,
  CONSTRAINT projects_progress_check CHECK (progress >= 0 AND progress <= 100)
);

-- Índices para performance
CREATE INDEX idx_projects_owner ON public.projects(owner_id);
CREATE INDEX idx_projects_status ON public.projects(status);
CREATE INDEX idx_projects_created_at ON public.projects(created_at);

-- Comentários
COMMENT ON TABLE public.projects IS 'Projetos do sistema';
COMMENT ON COLUMN public.projects.progress IS 'Progresso do projeto (0-100%)';

-- =====================================================
-- TABELA: stages
-- =====================================================
-- Estágios/fases dos projetos

CREATE TABLE public.stages (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  project_id uuid NOT NULL,
  name text NOT NULL,
  description text,
  status public.stage_status DEFAULT 'not-started'::public.stage_status,
  progress integer DEFAULT 0,
  start_date date,
  end_date date,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT stages_pkey PRIMARY KEY (id),
  CONSTRAINT stages_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects (id) ON DELETE CASCADE,
  CONSTRAINT stages_progress_check CHECK (progress >= 0 AND progress <= 100)
);

-- Índices para performance
CREATE INDEX idx_stages_project ON public.stages(project_id);
CREATE INDEX idx_stages_status ON public.stages(status);

-- Comentários
COMMENT ON TABLE public.stages IS 'Estágios/fases dos projetos';
COMMENT ON COLUMN public.stages.progress IS 'Progresso do estágio (0-100%)';

-- =====================================================
-- TABELA: tasks
-- =====================================================
-- Tarefas do sistema

CREATE TABLE public.tasks (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  stage_id uuid NOT NULL,
  title text NOT NULL,
  description text,
  content jsonb,
  status public.task_status DEFAULT 'todo'::public.task_status,
  priority integer DEFAULT 0,
  progress integer DEFAULT 0,
  assigned_to uuid,
  created_by uuid,
  estimated_hours integer,
  actual_hours integer,
  due_date date,
  completed_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT tasks_pkey PRIMARY KEY (id),
  CONSTRAINT tasks_stage_id_fkey FOREIGN KEY (stage_id) REFERENCES public.stages (id) ON DELETE CASCADE,
  CONSTRAINT tasks_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.profiles(id) ON DELETE SET NULL,
  CONSTRAINT tasks_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.profiles(id) ON DELETE SET NULL,
  CONSTRAINT tasks_progress_check CHECK (progress >= 0 AND progress <= 100),
  CONSTRAINT tasks_priority_check CHECK (priority >= 0 AND priority <= 10),
  CONSTRAINT tasks_estimated_hours_check CHECK (estimated_hours >= 0),
  CONSTRAINT tasks_actual_hours_check CHECK (actual_hours >= 0)
);

-- Índices para performance
CREATE INDEX idx_tasks_stage ON public.tasks(stage_id);
CREATE INDEX idx_tasks_status ON public.tasks(status);
CREATE INDEX idx_tasks_assigned_to ON public.tasks(assigned_to);
CREATE INDEX idx_tasks_created_by ON public.tasks(created_by);
CREATE INDEX idx_tasks_due_date ON public.tasks(due_date);

-- Comentários
COMMENT ON TABLE public.tasks IS 'Tarefas do sistema';
COMMENT ON COLUMN public.tasks.progress IS 'Progresso da tarefa (0-100%)';
COMMENT ON COLUMN public.tasks.priority IS 'Prioridade da tarefa (0-10)';
COMMENT ON COLUMN public.tasks.content IS 'Conteúdo estruturado da tarefa (JSON)';

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE '✅ Tabelas principais criadas com sucesso!';
  RAISE NOTICE '📋 Criadas: profiles, projects, stages, tasks';
  RAISE NOTICE '🔧 Tipos ENUM criados: project_status, stage_status, task_status, project_member_role';
END $$;

-- =====================================================
-- DIAGNÓSTICO: AUTOCOMPLETE DE USUÁRIOS - LIMITAÇÃO A ADMINISTRADORES
-- =====================================================
-- Investiga por que apenas administradores aparecem no autocomplete

-- 1. VERIFICAR USUÁRIO ATUAL E SEU PAPEL
SELECT 
  'USUÁRIO ATUAL' as tipo,
  auth.uid() as user_id,
  p.name,
  p.email,
  p.role,
  p.is_active
FROM profiles p 
WHERE p.id = auth.uid();

-- 2. VERIFICAR TODOS OS PERFIS VISÍVEIS PELA POLÍTICA ATUAL
SELECT 
  'PERFIS VISÍVEIS' as tipo,
  p.id,
  p.name,
  p.email,
  p.role,
  p.is_active,
  CASE 
    WHEN p.id = auth.uid() THEN 'PRÓPRIO PERFIL'
    WHEN EXISTS (
      SELECT 1 FROM project_members pm1 
      JOIN project_members pm2 ON pm1.project_id = pm2.project_id 
      WHERE pm1.user_id = p.id AND pm2.user_id = auth.uid()
    ) THEN 'PROJETO COMUM'
    ELSE 'OUTRO MOTIVO'
  END as razao_acesso
FROM profiles p 
ORDER BY p.role DESC, p.name;

-- 3. VERIFICAR MEMBROS DE PROJETOS COMUNS
SELECT 
  'MEMBROS PROJETOS COMUNS' as tipo,
  proj.id as project_id,
  proj.name as project_name,
  p.id as member_id,
  p.name as member_name,
  p.email,
  p.role,
  pm.role as project_role
FROM projects proj
JOIN project_members pm ON proj.id = pm.project_id
JOIN profiles p ON pm.user_id = p.id
WHERE proj.id IN (
  SELECT DISTINCT pm_user.project_id 
  FROM project_members pm_user 
  WHERE pm_user.user_id = auth.uid()
)
ORDER BY proj.name, p.role DESC, p.name;

-- 4. VERIFICAR POLÍTICAS RLS ATIVAS
SELECT 
  'POLÍTICAS ATIVAS' as tipo,
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'project_members', 'projects')
ORDER BY tablename, policyname;

-- 5. TESTE DE CONSULTA ESPECÍFICA PARA AUTOCOMPLETE
SELECT 
  'TESTE AUTOCOMPLETE' as tipo,
  p.id,
  p.name,
  p.email,
  p.role,
  'SERIA EXIBIDO NO AUTOCOMPLETE' as status
FROM profiles p
WHERE p.name ILIKE '%a%' -- Filtro comum de busca
ORDER BY p.name
LIMIT 10;

-- 6. CONTAGEM POR PAPEL
SELECT 
  'CONTAGEM POR PAPEL' as tipo,
  p.role,
  COUNT(*) as total,
  COUNT(*) FILTER (WHERE p.is_active = true) as ativos
FROM profiles p
GROUP BY p.role
ORDER BY p.role;

-- 7. VERIFICAR SE HÁ FILTROS ADICIONAIS NO CÓDIGO
-- (Esta parte é apenas informativa - não executável)
/*
VERIFIQUE NO CÓDIGO:

1. UserAutocomplete.tsx:
   - Se há filtros por role na busca
   - Se users (projectMembers) está sendo filtrado

2. projectService.getProjectMembers():
   - Se há filtros por role na consulta
   - Se está retornando apenas admins

3. Políticas RLS em profiles:
   - Se há condições que limitam por role
   - Se a política está muito restritiva
*/

-- 8. TESTE DE BUSCA DIRETA (simular UserAutocomplete)
SELECT 
  'BUSCA DIRETA SIMULADA' as tipo,
  p.id,
  p.name,
  p.email,
  p.role,
  p.avatar_url
FROM profiles p
WHERE p.name ILIKE '%' || 'a' || '%'
   OR p.email ILIKE '%' || 'a' || '%'
ORDER BY p.name
LIMIT 10;

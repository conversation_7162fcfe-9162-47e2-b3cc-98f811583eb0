import React from "react";
import { useAuth } from "./useAuth";
import { Navigate, useLocation } from "react-router-dom";

interface RequireAuthProps {
  children: React.ReactNode;
}

const RequireAuth: React.FC<RequireAuthProps> = ({ children }) => {
  const { user, loading, error, session } = useAuth();
  const location = useLocation();

  // Mostrar loading enquanto verifica autenticação
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verificando autenticação...</p>
        </div>
      </div>
    );
  }

  // Se há erro de autenticação, redirecionar para login
  if (error) {
    console.warn('Erro de autenticação detectado:', error);
    return <Navigate to="/login" state={{ from: location, error }} replace />;
  }

  // Se não há usuário ou sessão, redirecionar para login
  if (!user || !session) {
    console.log('Usuário não autenticado, redirecionando para login');
    // Salvar URL atual para redirecionamento após login
    if (location.pathname !== '/login') {
      localStorage.setItem('redirectAfterLogin', location.pathname);
    }
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Usuário autenticado, renderizar conteúdo
  return <>{children}</>;
};

export default RequireAuth; 
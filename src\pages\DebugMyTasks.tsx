import React, { useState, useEffect } from 'react';
import { useAuth } from '@/auth/useAuth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabaseClient';

export const DebugMyTasks = () => {
  const { user } = useAuth();
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [rawQueries, setRawQueries] = useState<any[]>([]);

  const runDetailedDiagnostic = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    const testResults = [];
    const queries = [];

    try {
      // Query 1: Verificar se a tabela task_executors existe e tem dados
      console.log('🔍 Executando Query 1: Dados task_executors');
      const { data: allExecutors, error: allExecError } = await supabase
        .from('task_executors')
        .select('*')
        .limit(10);

      testResults.push({
        test: 'Tabela task_executors',
        result: `${allExecutors?.length || 0} registros encontrados`,
        status: allExecutors?.length ? 'success' : 'error',
        data: allExecutors
      });

      // Query 2: Verificar especificamente para o usuário atual
      console.log('🔍 Executando Query 2: Executores do usuário atual');
      const { data: userExecutors, error: userExecError } = await supabase
        .from('task_executors')
        .select('*')
        .eq('user_id', user.id);

      testResults.push({
        test: 'Executores do usuário atual',
        result: `${userExecutors?.length || 0} registros para user_id: ${user.id}`,
        status: userExecutors?.length ? 'success' : 'warning',
        data: userExecutors
      });

      // Query 3: Verificar se há tarefas no sistema
      console.log('🔍 Executando Query 3: Tarefas no sistema');
      const { data: allTasks, error: allTasksError } = await supabase
        .from('tasks')
        .select('id, title, status, stage_id')
        .limit(10);

      testResults.push({
        test: 'Tarefas no sistema',
        result: `${allTasks?.length || 0} tarefas encontradas`,
        status: allTasks?.length ? 'success' : 'error',
        data: allTasks
      });

      // Query 4: Verificar se há estágios
      console.log('🔍 Executando Query 4: Estágios no sistema');
      const { data: allStages, error: allStagesError } = await supabase
        .from('stages')
        .select('id, name, project_id')
        .limit(10);

      testResults.push({
        test: 'Estágios no sistema',
        result: `${allStages?.length || 0} estágios encontrados`,
        status: allStages?.length ? 'success' : 'error',
        data: allStages
      });

      // Query 5: Verificar se há projetos
      console.log('🔍 Executando Query 5: Projetos no sistema');
      const { data: allProjects, error: allProjectsError } = await supabase
        .from('projects')
        .select('id, name')
        .limit(10);

      testResults.push({
        test: 'Projetos no sistema',
        result: `${allProjects?.length || 0} projetos encontrados`,
        status: allProjects?.length ? 'success' : 'error',
        data: allProjects
      });

      // Query 6: Verificar usuários e perfis
      console.log('🔍 Executando Query 6: Usuários e perfis');
      const { data: allUsers, error: allUsersError } = await supabase
        .from('profiles')
        .select('id, name, email, role')
        .limit(10);

      testResults.push({
        test: 'Usuários no sistema',
        result: `${allUsers?.length || 0} usuários encontrados`,
        status: allUsers?.length ? 'success' : 'error',
        data: allUsers
      });

      // Query 7: Query completa simulando o que a página faz
      console.log('🔍 Executando Query 7: Simulação completa');
      try {
        const { data: executorTasks, error: executorError } = await supabase
          .from('task_executors')
          .select('task_id, created_at')
          .eq('user_id', user.id);

        if (executorError) throw executorError;

        if (executorTasks && executorTasks.length > 0) {
          const taskIds = executorTasks.map(et => et.task_id);
          const { data: taskDetails, error: taskError } = await supabase
            .from('tasks')
            .select(`
              id,
              title,
              description,
              status,
              progress,
              due_date,
              estimated_hours,
              assigned_to,
              stage_id
            `)
            .in('id', taskIds);

          testResults.push({
            test: 'Simulação completa - Tarefas do usuário',
            result: `${taskDetails?.length || 0} tarefas encontradas para o usuário`,
            status: taskDetails?.length ? 'success' : 'warning',
            data: taskDetails
          });
        } else {
          testResults.push({
            test: 'Simulação completa - Tarefas do usuário',
            result: 'Nenhuma tarefa encontrada para o usuário',
            status: 'warning',
            data: null
          });
        }
      } catch (simError: any) {
        testResults.push({
          test: 'Simulação completa - Erro',
          result: `Erro: ${simError.message}`,
          status: 'error',
          data: null
        });
      }

    } catch (error: any) {
      testResults.push({
        test: 'Erro geral',
        result: error.message,
        status: 'error'
      });
    } finally {
      setResults(testResults);
      setRawQueries(queries);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      runDetailedDiagnostic();
    }
  }, [user?.id]);

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>🔍 Diagnóstico Detalhado - Minhas Tarefas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded border-l-4 border-blue-500">
              <h3 className="font-semibold">Informações do Usuário</h3>
              <p><strong>ID:</strong> {user?.id}</p>
              <p><strong>Email:</strong> {user?.email}</p>
            </div>

            <Button 
              onClick={runDetailedDiagnostic} 
              disabled={loading}
              className="mb-4"
            >
              {loading ? 'Executando Diagnóstico...' : 'Executar Diagnóstico Detalhado'}
            </Button>

            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded border-l-4 ${
                    result.status === 'success' 
                      ? 'border-green-500 bg-green-50' 
                      : result.status === 'warning'
                      ? 'border-yellow-500 bg-yellow-50'
                      : 'border-red-500 bg-red-50'
                  }`}
                >
                  <h3 className="font-semibold text-lg">{result.test}</h3>
                  <p className="text-sm text-gray-600 mt-1">{result.result}</p>
                  {result.data && (
                    <details className="mt-3">
                      <summary className="cursor-pointer text-sm text-blue-600 hover:text-blue-800">
                        👀 Ver dados detalhados ({Array.isArray(result.data) ? result.data.length : 1} registros)
                      </summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-40 border">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>

            {rawQueries.length > 0 && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">📊 Queries Executadas</h3>
                <div className="space-y-3">
                  {rawQueries.map((query, index) => (
                    <details key={index} className="border rounded p-3">
                      <summary className="cursor-pointer font-medium">
                        {query.query}
                      </summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {query.sql}
                      </pre>
                      {query.result && (
                        <pre className="mt-2 text-xs bg-green-100 p-2 rounded overflow-auto">
                          {JSON.stringify(query.result, null, 2)}
                        </pre>
                      )}
                    </details>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

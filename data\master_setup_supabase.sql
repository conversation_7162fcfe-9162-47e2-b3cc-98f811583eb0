-- =====================================================
-- SCRIPT PRINCIPAL: SETUP COMPLETO DO SUPABASE
-- =====================================================
-- Execute este script para recriar toda a infraestrutura
-- Versão: 2.0 - Julho 2025
-- Projeto: Haiku Project Flow
-- COMPATÍVEL COM SUPABASE SQL EDITOR

-- Verificar conexão com Supabase
DO $$
BEGIN
  RAISE NOTICE '🚀 Iniciando setup do Supabase...';
  RAISE NOTICE '📅 Data/Hora: %', NOW();
  RAISE NOTICE '🔧 Versão do PostgreSQL: %', version();
END $$;

-- =====================================================
-- EXECUTAR TODOS OS SCRIPTS EM SEQUÊNCIA
-- =====================================================

-- Para usar no Supabase SQL Editor:
-- 1. Execute primeiro o rollback.sql para limpar
-- 2. Execute cada script individual na ordem:
--    - 01_tables_core.sql
--    - 02_tables_relations.sql  
--    - 03_tables_content.sql
--    - 04_views_functions.sql
--    - 05_rls_policies.sql
--    - 06_storage_buckets.sql
--    - 07_test_data.sql
-- 3. Execute a validação abaixo

-- =====================================================
-- VALIDAÇÃO FINAL
-- =====================================================

-- Verificar se todas as tabelas foram criadas
DO $$
DECLARE
  table_count INTEGER;
  policy_count INTEGER;
  missing_tables TEXT[];
BEGIN
  -- Contar tabelas criadas
  SELECT COUNT(*) INTO table_count 
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name IN (
    'profiles', 'projects', 'stages', 'tasks', 
    'project_members', 'task_executors', 'task_approvers', 'stage_responsibles',
    'task_content_blocks', 'task_attachments', 'evidence',
    'task_comments', 'project_history', 'user_notifications',
    'quizzes', 'quiz_attempts', 'quiz_answers', 'user_quiz_progress', 'quiz_statistics'
  );

  -- Identificar tabelas faltantes
  SELECT ARRAY_AGG(expected_table) INTO missing_tables
  FROM (
    SELECT unnest(ARRAY[
      'profiles', 'projects', 'stages', 'tasks', 
      'project_members', 'task_executors', 'task_approvers', 'stage_responsibles',
      'task_content_blocks', 'task_attachments', 'evidence',
      'task_comments', 'project_history', 'user_notifications',
      'quizzes', 'quiz_attempts', 'quiz_answers', 'user_quiz_progress', 'quiz_statistics'
    ]) as expected_table
  ) expected
  WHERE NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = expected.expected_table
  );

  -- Contar políticas RLS
  SELECT COUNT(*) INTO policy_count 
  FROM pg_policies 
  WHERE schemaname = 'public';

  RAISE NOTICE '📊 RELATÓRIO FINAL:';
  RAISE NOTICE '✅ Tabelas criadas: %/19', table_count;
  RAISE NOTICE '🔒 Políticas RLS: %', policy_count;
  
  IF missing_tables IS NOT NULL THEN
    RAISE NOTICE '⚠️ Tabelas faltantes: %', array_to_string(missing_tables, ', ');
  END IF;
  
  IF table_count >= 19 THEN
    RAISE NOTICE '🎉 Setup concluído com sucesso!';
  ELSE
    RAISE WARNING '⚠️ Algumas tabelas podem não ter sido criadas';
  END IF;
END $$;

-- =====================================================
-- INSTRUÇÕES FINAIS
-- =====================================================

-- Verificar logs de erro
SELECT 
  'Para verificar se há erros, execute:' as instrucoes,
  'SELECT * FROM pg_stat_activity WHERE state = ''active'';' as comando
UNION ALL
SELECT 
  'Para ver todas as tabelas criadas:' as instrucoes,
  'SELECT tablename FROM pg_tables WHERE schemaname = ''public'';' as comando;

DO $$
BEGIN
  RAISE NOTICE '📚 Documentação completa disponível em data/README.md';
  RAISE NOTICE '🔧 Para rollback, execute: data/rollback.sql';
  RAISE NOTICE '✨ Setup finalizado!';
END $$;

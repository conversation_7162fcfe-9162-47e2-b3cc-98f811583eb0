-- =====================================================
-- SCRIPT EMERGENCIAL - FORÇA LIMPEZA POLÍTICAS
-- =====================================================

-- Remover políticas específicas que podem existir
DROP POLICY IF EXISTS "Users can view owned projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view member projects" ON public.projects;  
DROP POLICY IF EXISTS "Users can view accessible projects" ON public.projects;
DROP POLICY IF EXISTS "Users can create projects" ON public.projects;
DROP POLICY IF EXISTS "Users can update owned projects" ON public.projects;
DROP POLICY IF EXISTS "Users can update accessible projects" ON public.projects;
DROP POLICY IF EXISTS "Users can delete owned projects" ON public.projects;

DROP POLICY IF EXISTS "Users can view stages of owned projects" ON public.stages;
DROP POLICY IF EXISTS "Users can view stages of member projects" ON public.stages;
DROP POLICY IF EXISTS "Users can view accessible stages" ON public.stages;

DROP POLICY IF EXISTS "Users can view owned tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can view tasks of owned projects" ON public.tasks;
DROP POLICY IF EXISTS "Users can view tasks as executor" ON public.tasks;
DROP POLICY IF EXISTS "Users can view tasks as approver" ON public.tasks;
DROP POLICY IF EXISTS "Users can view accessible tasks" ON public.tasks;

-- Remover políticas de task_executors
DROP POLICY IF EXISTS "Users can view task executors" ON public.task_executors;
DROP POLICY IF EXISTS "Users can add themselves as executors" ON public.task_executors;
DROP POLICY IF EXISTS "Users can update own executor records" ON public.task_executors;
DROP POLICY IF EXISTS "Users can remove themselves as executors" ON public.task_executors;

-- Remover políticas de project_members
DROP POLICY IF EXISTS "Users can view members of owned projects" ON public.project_members;
DROP POLICY IF EXISTS "Users can add members to owned projects" ON public.project_members;
DROP POLICY IF EXISTS "Users can update members of owned projects" ON public.project_members;
DROP POLICY IF EXISTS "Users can remove members from owned projects" ON public.project_members;

-- Recriar políticas simples
CREATE POLICY "simple_view_projects"
  ON public.projects FOR SELECT
  USING (owner_id = auth.uid());

CREATE POLICY "simple_view_stages"  
  ON public.stages FOR SELECT
  USING (project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid()));

CREATE POLICY "simple_view_tasks"
  ON public.tasks FOR SELECT
  USING (assigned_to = auth.uid() OR created_by = auth.uid());

-- Políticas para task_executors (resolver erro 403)
CREATE POLICY "simple_view_task_executors"
  ON public.task_executors FOR SELECT
  USING (
    user_id = auth.uid() OR
    task_id IN (
      SELECT id FROM public.tasks 
      WHERE assigned_to = auth.uid() OR created_by = auth.uid()
    ) OR
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      WHERE s.project_id IN (
        SELECT id FROM public.projects WHERE owner_id = auth.uid()
      )
    )
  );

CREATE POLICY "simple_insert_task_executors"
  ON public.task_executors FOR INSERT
  WITH CHECK (
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      WHERE s.project_id IN (
        SELECT id FROM public.projects WHERE owner_id = auth.uid()
      )
    )
  );

CREATE POLICY "simple_update_task_executors"
  ON public.task_executors FOR UPDATE
  USING (
    user_id = auth.uid() OR
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      WHERE s.project_id IN (
        SELECT id FROM public.projects WHERE owner_id = auth.uid()
      )
    )
  );

CREATE POLICY "simple_delete_task_executors"
  ON public.task_executors FOR DELETE
  USING (
    user_id = auth.uid() OR
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      WHERE s.project_id IN (
        SELECT id FROM public.projects WHERE owner_id = auth.uid()
      )
    )
  );

-- Políticas para project_members (necessárias para membership)
CREATE POLICY "simple_view_project_members"
  ON public.project_members FOR SELECT
  USING (
    user_id = auth.uid() OR
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    )
  );

CREATE POLICY "simple_insert_project_members"
  ON public.project_members FOR INSERT
  WITH CHECK (
    project_id IN (
      SELECT id FROM public.projects WHERE owner_id = auth.uid()
    )
  );

-- Teste de múltiplas tabelas
SELECT 'PROJECTS' as tabela, COUNT(*) as total FROM public.projects
UNION ALL
SELECT 'STAGES' as tabela, COUNT(*) as total FROM public.stages  
UNION ALL
SELECT 'TASKS' as tabela, COUNT(*) as total FROM public.tasks
UNION ALL
SELECT 'TASK_EXECUTORS' as tabela, COUNT(*) as total FROM public.task_executors;

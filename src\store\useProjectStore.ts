import { create } from 'zustand';
import { Project, ProjectStatus, DashboardMetrics } from '@/types';
import { projectService } from '@/services/projectService';

interface ProjectStore {
  // Estado
  projects: Project[];
  selectedProject: Project | null;
  dashboardMetrics: DashboardMetrics | null;
  isLoading: boolean;
  error: string | null;
  
  // Ações
  loadProjects: (filters?: {
    status?: ProjectStatus[];
    responsibleId?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
  }) => Promise<void>;
  loadProjectById: (id: string) => Promise<void>;
  createProject: (projectData: Partial<Project>) => Promise<Project>;
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>;
  loadDashboardMetrics: () => Promise<void>;
  setSelectedProject: (project: Project | null) => void;
  clearError: () => void;
}

export const useProjectStore = create<ProjectStore>((set, get) => ({
  // Estado inicial
  projects: [],
  selectedProject: null,
  dashboardMetrics: null,
  isLoading: false,
  error: null,
  
  // Ações
  loadProjects: async (filters) => {
    set({ isLoading: true, error: null });
    
    try {
      const result = await projectService.getProjects(filters);
      const projects: Project[] = Array.isArray(result) ? result as Project[] : [];
      set({ projects, isLoading: false });
    } catch (error) {
      console.error('Erro ao carregar projetos:', error);
      set({ 
        error: 'Erro ao carregar projetos. Tente novamente.', 
        isLoading: false 
      });
    }
  },
  
  loadProjectById: async (id) => {
    set({ isLoading: true, error: null });
    
    try {
      const project = await projectService.getById(id);
      set({ selectedProject: project, isLoading: false });
    } catch (error) {
      console.error('Erro ao carregar projeto:', error);
      set({ 
        error: 'Erro ao carregar projeto. Tente novamente.', 
        isLoading: false 
      });
    }
  },
  
  createProject: async (projectData) => {
    set({ isLoading: true, error: null });
    
    try {
      const newProject = await projectService.create(projectData) as Project;
      const currentProjects = get().projects;
      set({ 
        projects: [newProject, ...currentProjects], 
        isLoading: false 
      });
      return newProject;
    } catch (error) {
      console.error('Erro ao criar projeto:', error);
      set({ 
        error: 'Erro ao criar projeto. Tente novamente.', 
        isLoading: false 
      });
      throw error;
    }
  },
  
  updateProject: async (id, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedProject = await projectService.update(id, updates);
      const currentProjects = get().projects;
      const updatedProjects = currentProjects.map(p => 
        p.id === id ? updatedProject : p
      );
      
      set({ 
        projects: updatedProjects,
        selectedProject: get().selectedProject?.id === id ? updatedProject : get().selectedProject,
        isLoading: false 
      });
    } catch (error) {
      console.error('Erro ao atualizar projeto:', error);
      set({ 
        error: 'Erro ao atualizar projeto. Tente novamente.', 
        isLoading: false 
      });
    }
  },
  
  loadDashboardMetrics: async () => {
    try {
      const metrics = await projectService.getDashboardMetrics();
      set({ dashboardMetrics: metrics });
    } catch (error) {
      console.error('Erro ao carregar métricas:', error);
    }
  },
  
  setSelectedProject: (project) => {
    set({ selectedProject: project });
  },
  
  clearError: () => {
    set({ error: null });
  }
}));

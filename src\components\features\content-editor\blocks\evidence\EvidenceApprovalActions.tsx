import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { Evidence } from '@/types';
import { EvidenceApprovalService } from '@/services/evidenceApprovalService';
import { useToast } from '@/hooks/ui/use-toast';

interface EvidenceApprovalActionsProps {
  evidence: Evidence;
  taskId: string;
  blockId: string;
  userId: string;
  canApprove: boolean;
  onApprovalChange: (evidence: Evidence) => void;
}

/**
 * Componente para ações de aprovação/rejeição de evidências
 */
export const EvidenceApprovalActions: React.FC<EvidenceApprovalActionsProps> = ({
  evidence,
  taskId,
  blockId,
  userId,
  canApprove,
  onApprovalChange
}) => {
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Só mostrar ações se o usuário pode aprovar e a evidência está pendente
  if (!canApprove || evidence.status !== 'pending') {
    return null;
  }

  const handleApprove = async () => {
    setIsProcessing(true);
    try {
      const updatedEvidence = await EvidenceApprovalService.updateApprovalStatus({
        evidenceId: evidence.id,
        taskId,
        blockId,
        status: 'approved',
        approvedBy: userId
      });

      onApprovalChange(updatedEvidence);
      
      toast({
        title: 'Evidência aprovada',
        description: 'A evidência foi aprovada com sucesso.',
        variant: 'default'
      });
    } catch (error) {
      console.error('Erro ao aprovar evidência:', error);
      toast({
        title: 'Erro ao aprovar',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      toast({
        title: 'Motivo obrigatório',
        description: 'É necessário informar o motivo da reprovação.',
        variant: 'destructive'
      });
      return;
    }

    setIsProcessing(true);
    try {
      const updatedEvidence = await EvidenceApprovalService.updateApprovalStatus({
        evidenceId: evidence.id,
        taskId,
        blockId,
        status: 'rejected',
        rejectionReason: rejectionReason.trim(),
        approvedBy: userId
      });

      onApprovalChange(updatedEvidence);
      setShowRejectDialog(false);
      setRejectionReason('');
      
      toast({
        title: 'Evidência reprovada',
        description: 'A evidência foi reprovada com sucesso.',
        variant: 'default'
      });
    } catch (error) {
      console.error('Erro ao reprovar evidência:', error);
      toast({
        title: 'Erro ao reprovar',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const openRejectDialog = () => {
    setRejectionReason('');
    setShowRejectDialog(true);
  };

  return (
    <>
      {/* Botões de ação */}
      <div className="flex gap-2">
        <Button
          size="sm"
          variant="default"
          onClick={handleApprove}
          disabled={isProcessing}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          <CheckCircle className="h-4 w-4 mr-1" />
          Aprovar
        </Button>
        
        <Button
          size="sm"
          variant="outline"
          onClick={openRejectDialog}
          disabled={isProcessing}
          className="border-red-300 text-red-700 hover:bg-red-50"
        >
          <XCircle className="h-4 w-4 mr-1" />
          Reprovar
        </Button>
      </div>

      {/* Dialog de reprovação */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Reprovar Evidência
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              Você está prestes a reprovar a evidência "{evidence.fileName}". 
              É obrigatório informar o motivo da reprovação.
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="rejection-reason">
                Motivo da reprovação *
              </Label>
              <Textarea
                id="rejection-reason"
                placeholder="Descreva o motivo da reprovação..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                rows={4}
                className="resize-none"
              />
              <div className="text-xs text-gray-500">
                Este motivo será exibido para o executor da tarefa.
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRejectDialog(false)}
              disabled={isProcessing}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={isProcessing || !rejectionReason.trim()}
            >
              {isProcessing ? 'Reprovando...' : 'Reprovar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

/**
 * Componente compacto para ações inline
 */
export const EvidenceApprovalActionsCompact: React.FC<EvidenceApprovalActionsProps> = (props) => {
  if (!props.canApprove || props.evidence.status !== 'pending') {
    return null;
  }

  return (
    <div className="flex gap-1">
      <Button
        size="sm"
        variant="ghost"
        onClick={() => {
          // Implementar aprovação rápida
        }}
        className="h-6 px-2 text-green-700 hover:bg-green-50"
      >
        <CheckCircle className="h-3 w-3" />
      </Button>
      
      <Button
        size="sm"
        variant="ghost"
        onClick={() => {
          // Implementar rejeição rápida
        }}
        className="h-6 px-2 text-red-700 hover:bg-red-50"
      >
        <XCircle className="h-3 w-3" />
      </Button>
    </div>
  );
};

// Script de debug para verificar o problema da aba "Editar Conteúdo"
// Coloque este código no console do navegador quando estiver na página problemática

console.log('=== DEBUG: Problema Aba "Editar Conteúdo" ===');

// 1. Verificar se há contexto de autenticação
console.log('1. Verificando contexto de autenticação...');
const authDiv = document.querySelector('[data-auth]');
console.log('AuthProvider presente:', !!authDiv);

// 2. Verificar dados do usuário no localStorage/sessionStorage
console.log('2. Verificando dados armazenados...');
console.log('LocalStorage keys:', Object.keys(localStorage));
console.log('SessionStorage keys:', Object.keys(sessionStorage));

// 3. Verificar se há dados do Supabase
console.log('3. Verificando Supabase...');
const supabaseData = localStorage.getItem('sb-gcdtchxyxawtiroxuifs-auth-token');
if (supabaseData) {
  try {
    const parsed = JSON.parse(supabaseData);
    console.log('Supabase user:', parsed.user);
  } catch (e) {
    console.log('Erro ao parsear dados Supabase:', e);
  }
}

// 4. Verificar se há um objeto global do React DevTools
console.log('4. Verificando React DevTools...');
if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  console.log('React DevTools disponível');
}

// 5. Verificar se há elementos da página
console.log('5. Verificando elementos da página...');
const tabsList = document.querySelector('[role="tablist"]');
const editTab = document.querySelector('[data-value="edit"]');
const tabs = document.querySelectorAll('[role="tab"]');

console.log('TabsList encontrado:', !!tabsList);
console.log('Tab "edit" encontrada:', !!editTab);
console.log('Total de tabs:', tabs.length);
tabs.forEach((tab, index) => {
  console.log(`Tab ${index}:`, tab.textContent?.trim(), 'value:', tab.getAttribute('data-value'));
});

// 6. Verificar se há erros no console
console.log('6. Verificando erros...');
const errors = [];
const originalError = console.error;
console.error = function(...args) {
  errors.push(args);
  originalError.apply(console, args);
};

// 7. Verificar se há componentes React na página
console.log('7. Verificando componentes React...');
const reactFiber = document.querySelector('#root')?._reactInternalInstance || 
                  document.querySelector('#root')?._reactInternalFiber;
console.log('React Fiber encontrado:', !!reactFiber);

// Instruções para o usuário
console.log('\n=== INSTRUÇÕES PARA DIAGNÓSTICO ===');
console.log('1. Abra o console do navegador (F12)');
console.log('2. Cole e execute este script');
console.log('3. Anote os valores mostrados');
console.log('4. Verifique se há erros em vermelho no console');
console.log('5. Informe ao desenvolvedor os resultados');

// Verificar se há dados do useAuth disponíveis
console.log('\n=== VERIFICAR DADOS DO USEAUTH ===');
console.log('Para verificar o estado do useAuth, você pode:');
console.log('1. Instalar React DevTools');
console.log('2. Procurar pelo componente TaskDetailsV2');
console.log('3. Verificar o hook useAuth nos devtools');

// Verificar URL e parâmetros
console.log('\n=== VERIFICAR URL E PARÂMETROS ===');
console.log('URL atual:', window.location.href);
console.log('Parâmetros URL:', new URLSearchParams(window.location.search));
console.log('Task ID:', window.location.pathname.split('/').pop());

console.log('\n=== FIM DEBUG ===');

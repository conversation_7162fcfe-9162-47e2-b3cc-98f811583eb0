/**
 * Presets específicos para blocos de imagem
 */

// Definição local do tipo para evitar dependência circular
interface BlockTypePreset {
  icon: {
    name: string;
    position: string;
    type: string;
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  card: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  button?: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
}

export type ImageBlockVariant = 'default' | 'gallery' | 'hero' | 'thumbnail' | 'avatar' | 'banner';

export const imageBlockPresets: Record<ImageBlockVariant, BlockTypePreset> = {
  default: {
    icon: {
      name: 'Image',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#6b7280',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#6b7280', width: 1 },
      shadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      hover: {
        backgroundColor: '#4b5563',
        color: '#fff',
        shadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
        borderColor: '#4b5563',
      },
    },
    card: {
      backgroundColor: '#ffffff',
      color: '#374151',
      format: 'rounded',
      border: { enabled: true, color: '#e5e7eb', width: 1 },
      shadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      hover: {
        backgroundColor: '#f9fafb',
        color: '#374151',
        shadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
        borderColor: '#d1d5db',
      },
    },
    button: {
      backgroundColor: '#6b7280',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#6b7280', width: 1 },
      shadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      hover: {
        backgroundColor: '#4b5563',
        color: '#fff',
        shadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
        borderColor: '#4b5563',
      },
    },
  },
  gallery: {
    icon: {
      name: 'Images',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#8b5cf6',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#8b5cf6', width: 1 },
      shadow: '0 2px 4px rgba(139, 92, 246, 0.3)',
      hover: {
        backgroundColor: '#7c3aed',
        color: '#fff',
        shadow: '0 4px 8px rgba(139, 92, 246, 0.4)',
        borderColor: '#7c3aed',
      },
    },
    card: {
      backgroundColor: '#faf5ff',
      color: '#8b5cf6',
      format: 'rounded',
      border: { enabled: true, color: '#c4b5fd', width: 1 },
      shadow: '0 2px 4px rgba(139, 92, 246, 0.1)',
      hover: {
        backgroundColor: '#f3e8ff',
        color: '#7c3aed',
        shadow: '0 4px 8px rgba(139, 92, 246, 0.2)',
        borderColor: '#a78bfa',
      },
    },
    button: {
      backgroundColor: '#8b5cf6',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#8b5cf6', width: 1 },
      shadow: '0 2px 4px rgba(139, 92, 246, 0.3)',
      hover: {
        backgroundColor: '#7c3aed',
        color: '#fff',
        shadow: '0 4px 8px rgba(139, 92, 246, 0.4)',
        borderColor: '#7c3aed',
      },
    },
  },
  hero: {
    icon: {
      name: 'Monitor',
      position: 'center',
      type: 'predefined',
      backgroundColor: '#059669',
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#059669', width: 2 },
      shadow: '0 4px 12px rgba(5, 150, 105, 0.3)',
      hover: {
        backgroundColor: '#047857',
        color: '#fff',
        shadow: '0 6px 16px rgba(5, 150, 105, 0.4)',
        borderColor: '#047857',
      },
    },
    card: {
      backgroundColor: '#ecfdf5',
      color: '#059669',
      format: 'rounded',
      border: { enabled: true, color: '#a7f3d0', width: 2 },
      shadow: '0 4px 12px rgba(5, 150, 105, 0.1)',
      hover: {
        backgroundColor: '#d1fae5',
        color: '#047857',
        shadow: '0 6px 16px rgba(5, 150, 105, 0.2)',
        borderColor: '#6ee7b7',
      },
    },
    button: {
      backgroundColor: '#059669',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#059669', width: 1 },
      shadow: '0 4px 12px rgba(5, 150, 105, 0.3)',
      hover: {
        backgroundColor: '#047857',
        color: '#fff',
        shadow: '0 6px 16px rgba(5, 150, 105, 0.4)',
        borderColor: '#047857',
      },
    },
  },
  thumbnail: {
    icon: {
      name: 'ImageIcon',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#f59e0b',
      color: '#fff',
      format: 'square',
      border: { enabled: false, color: '#f59e0b', width: 1 },
      shadow: '0 1px 3px rgba(245, 158, 11, 0.3)',
      hover: {
        backgroundColor: '#d97706',
        color: '#fff',
        shadow: '0 2px 6px rgba(245, 158, 11, 0.4)',
        borderColor: '#d97706',
      },
    },
    card: {
      backgroundColor: '#fffbeb',
      color: '#f59e0b',
      format: 'square',
      border: { enabled: true, color: '#fde68a', width: 1 },
      shadow: '0 1px 3px rgba(245, 158, 11, 0.1)',
      hover: {
        backgroundColor: '#fef3c7',
        color: '#d97706',
        shadow: '0 2px 6px rgba(245, 158, 11, 0.2)',
        borderColor: '#fcd34d',
      },
    },
    button: {
      backgroundColor: '#f59e0b',
      color: '#fff',
      format: 'square',
      border: { enabled: false, color: '#f59e0b', width: 1 },
      shadow: '0 1px 3px rgba(245, 158, 11, 0.3)',
      hover: {
        backgroundColor: '#d97706',
        color: '#fff',
        shadow: '0 2px 6px rgba(245, 158, 11, 0.4)',
        borderColor: '#d97706',
      },
    },
  },
  avatar: {
    icon: {
      name: 'User',
      position: 'center',
      type: 'predefined',
      backgroundColor: '#ec4899',
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#ec4899', width: 2 },
      shadow: '0 2px 8px rgba(236, 72, 153, 0.3)',
      hover: {
        backgroundColor: '#db2777',
        color: '#fff',
        shadow: '0 4px 12px rgba(236, 72, 153, 0.4)',
        borderColor: '#db2777',
      },
    },
    card: {
      backgroundColor: '#fdf2f8',
      color: '#ec4899',
      format: 'circle',
      border: { enabled: true, color: '#f9a8d4', width: 2 },
      shadow: '0 2px 8px rgba(236, 72, 153, 0.1)',
      hover: {
        backgroundColor: '#fce7f3',
        color: '#db2777',
        shadow: '0 4px 12px rgba(236, 72, 153, 0.2)',
        borderColor: '#f472b6',
      },
    },
    button: {
      backgroundColor: '#ec4899',
      color: '#fff',
      format: 'circle',
      border: { enabled: false, color: '#ec4899', width: 1 },
      shadow: '0 2px 8px rgba(236, 72, 153, 0.3)',
      hover: {
        backgroundColor: '#db2777',
        color: '#fff',
        shadow: '0 4px 12px rgba(236, 72, 153, 0.4)',
        borderColor: '#db2777',
      },
    },
  },
  banner: {
    icon: {
      name: 'Layout',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#0891b2',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#0891b2', width: 1 },
      shadow: '0 2px 8px rgba(8, 145, 178, 0.3)',
      hover: {
        backgroundColor: '#0e7490',
        color: '#fff',
        shadow: '0 4px 12px rgba(8, 145, 178, 0.4)',
        borderColor: '#0e7490',
      },
    },
    card: {
      backgroundColor: '#f0f9ff',
      color: '#0891b2',
      format: 'rounded',
      border: { enabled: true, color: '#7dd3fc', width: 1 },
      shadow: '0 2px 8px rgba(8, 145, 178, 0.1)',
      hover: {
        backgroundColor: '#e0f2fe',
        color: '#0e7490',
        shadow: '0 4px 12px rgba(8, 145, 178, 0.2)',
        borderColor: '#38bdf8',
      },
    },
    button: {
      backgroundColor: '#0891b2',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#0891b2', width: 1 },
      shadow: '0 2px 8px rgba(8, 145, 178, 0.3)',
      hover: {
        backgroundColor: '#0e7490',
        color: '#fff',
        shadow: '0 4px 12px rgba(8, 145, 178, 0.4)',
        borderColor: '#0e7490',
      },
    },
  },
};

// Variantes disponíveis para blocos de imagem
export const imageBlockVariants: ImageBlockVariant[] = [
  'default',
  'gallery',
  'hero',
  'thumbnail',
  'avatar',
  'banner'
];

// Ícones específicos para blocos de imagem
export const imageBlockIcons = [
  'Image',
  'Images',
  'Camera',
  'Monitor',
  'ImageIcon',
  'User',
  'Layout',
  'Frame',
  'Crop',
  'Aperture',
  'Focus',
  'Palette'
];
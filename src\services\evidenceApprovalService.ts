import { supabase } from '@/lib/supabaseClient';
import { Evidence } from '@/types';
import { requireAuth } from '@/lib/authUtils';

export interface ApprovalOptions {
  evidenceId: string;
  taskId: string;
  blockId: string;
  status: 'approved' | 'rejected';
  rejectionReason?: string;
  approvedBy: string;
}

export interface EvidenceWithApproval extends Evidence {
  canApprove: boolean;
  canDelete: boolean;
  canResubmit: boolean;
}

export class EvidenceApprovalService {
  /**
   * Aprova ou rejeita uma evidência
   */
  static async updateApprovalStatus(options: ApprovalOptions): Promise<Evidence> {
    const { evidenceId, taskId, blockId, status, rejectionReason, approvedBy } = options;

    // Verificar autenticação
    const user = await requireAuth();
    
    // Verificar se o usuário tem permissão de aprovador na tarefa
    const { data: taskData, error: taskError } = await supabase
      .from('tasks')
      .select(`
        id,
        approvers:task_approvers(user_id, profiles(id, name, email))
      `)
      .eq('id', taskId)
      .single();

    if (taskError) {
      throw new Error(`Erro ao verificar tarefa: ${taskError.message}`);
    }

    const isApprover = taskData.approvers?.some((approver: any) => 
      approver.user_id === user.id
    );

    if (!isApprover) {
      throw new Error('Usuário não tem permissão para aprovar evidências nesta tarefa');
    }

    // Validar campos obrigatórios
    if (status === 'rejected' && !rejectionReason?.trim()) {
      throw new Error('Motivo da reprovação é obrigatório');
    }

    // Atualizar status da evidência
    const updateData: any = {
      status,
      approved_by: approvedBy,
      approved_at: new Date().toISOString(),
    };

    if (status === 'rejected') {
      updateData.rejection_reason = rejectionReason;
    } else {
      updateData.rejection_reason = null;
    }

    const { data, error } = await supabase
      .from('evidence')
      .update(updateData)
      .eq('id', evidenceId)
      .eq('task_id', taskId)
      .eq('block_id', blockId)
      .select(`
        *,
        uploaded_by_profile:profiles!uploaded_by(id, name, email),
        approved_by_profile:profiles!approved_by(id, name, email)
      `)
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar aprovação: ${error.message}`);
    }

    return {
      id: data.id,
      taskId: data.task_id,
      type: data.type,
      content: data.content,
      fileName: data.file_name,
      fileSize: data.file_size,
      uploadedBy: data.uploaded_by_profile,
      uploadedAt: data.created_at,
      status: data.status,
      approvedBy: data.approved_by_profile,
      approvedAt: data.approved_at,
      rejectionReason: data.rejection_reason,
    };
  }

  /**
   * Lista evidências com informações de aprovação e permissões
   */
  static async getEvidencesWithApproval(
    taskId: string, 
    blockId: string, 
    userId: string
  ): Promise<EvidenceWithApproval[]> {
    // Buscar evidências
    const { data: evidences, error: evidencesError } = await supabase
      .from('evidence')
      .select(`
        *,
        uploaded_by_profile:profiles!uploaded_by(id, name, email),
        approved_by_profile:profiles!approved_by(id, name, email)
      `)
      .eq('task_id', taskId)
      .eq('block_id', blockId)
      .order('created_at', { ascending: false });

    if (evidencesError) {
      throw new Error(`Erro ao buscar evidências: ${evidencesError.message}`);
    }

    // Buscar permissões do usuário na tarefa
    const { data: taskData, error: taskError } = await supabase
      .from('tasks')
      .select(`
        id,
        executors:task_executors(user_id),
        approvers:task_approvers(user_id)
      `)
      .eq('id', taskId)
      .single();

    if (taskError) {
      throw new Error(`Erro ao verificar permissões: ${taskError.message}`);
    }

    const isExecutor = taskData.executors?.some((executor: any) => executor.user_id === userId);
    const isApprover = taskData.approvers?.some((approver: any) => approver.user_id === userId);

    // Mapear evidências com permissões
    return evidences.map((evidence: any): EvidenceWithApproval => {
      const isOwner = evidence.uploaded_by_profile?.id === userId;

      return {
        id: evidence.id,
        taskId: evidence.task_id,
        blockId: evidence.block_id,
        type: evidence.type,
        content: evidence.content,
        fileName: evidence.file_name,
        fileSize: evidence.file_size,
        uploadedBy: evidence.uploaded_by_profile,
        uploadedAt: evidence.created_at,
        status: evidence.status || 'pending',
        approvedBy: evidence.approved_by_profile,
        approvedAt: evidence.approved_at,
        rejectionReason: evidence.rejection_reason,
        // Permissões calculadas
        canApprove: isApprover && (evidence.status === 'pending' || !evidence.status) && !isOwner,
        canDelete: isExecutor && isOwner && evidence.status !== 'approved',
        canResubmit: isExecutor && isOwner && evidence.status === 'rejected',
      };
    });
  }

  /**
   * Verifica se usuário pode fazer upload de evidências
   */
  static async canUploadEvidence(taskId: string, userId: string): Promise<boolean> {
    const { data: taskData, error } = await supabase
      .from('tasks')
      .select(`
        id,
        executors:task_executors(user_id)
      `)
      .eq('id', taskId)
      .single();

    if (error) {
      return false;
    }

    return taskData.executors?.some((executor: any) => executor.user_id === userId) || false;
  }

  /**
   * Exclui evidência (apenas se permitido)
   */
  static async deleteEvidence(
    evidenceId: string, 
    taskId: string, 
    blockId: string, 
    userId: string
  ): Promise<void> {
    // Verificar permissões
    const evidences = await this.getEvidencesWithApproval(taskId, blockId, userId);
    const evidence = evidences.find(e => e.id === evidenceId);

    if (!evidence) {
      throw new Error('Evidência não encontrada');
    }

    if (!evidence.canDelete) {
      throw new Error('Você não tem permissão para excluir esta evidência');
    }

    // Excluir arquivo do storage se existir
    if (evidence.type === 'file' || evidence.type === 'image') {
      const fileName = evidence.content.split('/').pop();
      if (fileName) {
        await supabase.storage
          .from('evidence')
          .remove([`${taskId}/${fileName}`]);
      }
    }

    // Excluir registro da evidência
    const { error } = await supabase
      .from('evidence')
      .delete()
      .eq('id', evidenceId)
      .eq('task_id', taskId)
      .eq('block_id', blockId);

    if (error) {
      throw new Error(`Erro ao excluir evidência: ${error.message}`);
    }
  }
}

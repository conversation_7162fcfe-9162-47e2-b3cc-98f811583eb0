<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modelo Real do Configurador de Blocos</title>
  <style>
    body { font-family: 'Inter', Arial, sans-serif; background: #f3f4f6; margin: 0; padding: 32px; }
    .container { max-width: 900px; margin: 0 auto; }
    h1 { font-size: 2rem; margin-bottom: 24px; }
    .premissa { background: #eef2ff; border-left: 6px solid #6366f1; border-radius: 8px; padding: 20px 28px; margin-bottom: 32px; color: #3730a3; }
    .premissa-title { font-size: 1.15rem; font-weight: bold; margin-bottom: 8px; color: #3730a3; }
    .premissa-list { margin: 0 0 0 18px; padding: 0; font-size: 1rem; }
    .block-type-selector { display: flex; gap: 10px; margin-bottom: 32px; }
    .block-type-btn { padding: 8px 18px; border-radius: 8px; border: 1px solid #e5e7eb; background: #f3f4f6; cursor: pointer; font-size: 1rem; font-weight: 500; color: #6366f1; transition: background 0.15s, color 0.15s; }
    .block-type-btn.active { background: #6366f1; color: #fff; border-color: #6366f1; }
    .block-config { background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 32px; margin-bottom: 40px; }
    .block-title { font-size: 1.3rem; font-weight: bold; margin-bottom: 16px; color: #6366f1; }
    .preview { background: #f8fafc; border-radius: 8px; padding: 16px; margin-bottom: 24px; border: 1px solid #e5e7eb; }
    .tabs { display: flex; gap: 8px; margin-bottom: 24px; }
    .tab-btn { padding: 8px 20px; border-radius: 8px 8px 0 0; border: 1px solid #e5e7eb; background: #f3f4f6; cursor: pointer; font-size: 1rem; font-weight: 500; color: #6366f1; border-bottom: none; }
    .tab-btn.active { background: #fff; color: #1e293b; border-bottom: 1px solid #fff; }
    .tab-content { background: #fff; border-radius: 0 0 8px 8px; border: 1px solid #e5e7eb; border-top: none; padding: 24px; }
    .section { margin-bottom: 28px; }
    .section-title { font-size: 1.1rem; font-weight: 600; margin-bottom: 10px; color: #374151; }
    .fields { display: flex; flex-wrap: wrap; gap: 16px; }
    .field { flex: 1 1 220px; min-width: 180px; display: flex; flex-direction: column; margin-bottom: 8px; }
    .field label { font-size: 0.95rem; font-weight: 500; margin-bottom: 4px; color: #4b5563; }
    .field input, .field select { padding: 6px 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 1rem; background: #f9fafb; }
    .preset-list { display: flex; gap: 8px; flex-wrap: wrap; }
    .preset-btn { padding: 6px 16px; border-radius: 8px; border: 1px solid #d1d5db; background: #f3f4f6; cursor: pointer; font-size: 0.95rem; }
    .preset-btn.selected { background: #6366f1; color: #fff; border-color: #6366f1; }
    .divider { border-bottom: 1px solid #e5e7eb; margin: 24px 0; }
    .hidden { display: none; }
    @media (max-width: 700px) {
      .fields { flex-direction: column; }
      .block-config { padding: 16px; }
      .tab-content { padding: 12px; }
    }
  </style>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Blocos disponíveis e suas abas/campos
      const blockTypes = {
        alert: { label: 'Alerta', tabs: ['Aparência', 'Ícone', 'Botão', 'Presets'] },
        colored: { label: 'Colorido', tabs: ['Aparência', 'Ícone', 'Botão', 'Presets'] },
        file: { label: 'Arquivo', tabs: ['Aparência', 'Ícone', 'Botão', 'Presets', 'Upload'] },
        image: { label: 'Imagem', tabs: ['Aparência', 'Ícone', 'Presets', 'Conteúdo'] },
        quiz: { label: 'Quiz', tabs: ['Aparência', 'Ícone', 'Presets', 'Pergunta'] },
        text: { label: 'Texto', tabs: ['Aparência', 'Ícone', 'Presets'] },
        video: { label: 'Vídeo', tabs: ['Aparência', 'Ícone', 'Botão', 'Presets', 'Conteúdo'] },
      };
      let currentBlock = 'alert';
      // Seletor de tipo de bloco
      const typeBtns = document.querySelectorAll('.block-type-btn');
      const configPanels = document.querySelectorAll('.block-config-panel');
      typeBtns.forEach((btn, idx) => {
        btn.addEventListener('click', () => {
          typeBtns.forEach(b => b.classList.remove('active'));
          btn.classList.add('active');
          configPanels.forEach((panel, pidx) => {
            panel.classList.toggle('hidden', idx !== pidx);
          });
          currentBlock = btn.dataset.type;
        });
      });
      // Tabs dinâmicas para cada painel
      document.querySelectorAll('.block-config-panel').forEach(panel => {
        const tabs = panel.querySelectorAll('.tab-btn');
        const contents = panel.querySelectorAll('.tab-content');
        tabs.forEach((tab, idx) => {
          tab.addEventListener('click', () => {
            tabs.forEach(t => t.classList.remove('active'));
            contents.forEach(c => c.classList.add('hidden'));
            tab.classList.add('active');
            contents[idx].classList.remove('hidden');
          });
        });
        if (tabs.length) {
          tabs[0].classList.add('active');
          contents[0].classList.remove('hidden');
        }
      });
      // Campos condicionais
      document.querySelectorAll('.toggle-btn').forEach(input => {
        input.addEventListener('change', function() {
          const target = document.getElementById(this.dataset.target);
          if (target) target.style.display = this.checked ? '' : 'none';
        });
        const target = document.getElementById(input.dataset.target);
        if (target) target.style.display = input.checked ? '' : 'none';
      });
      // Inicialização: mostrar apenas o painel do tipo padrão
      configPanels.forEach((panel, idx) => {
        panel.classList.toggle('hidden', idx !== 0);
      });
      typeBtns[0].classList.add('active');
    });
  </script>
</head>
<body>
  <div class="container">
    <h1>Modelo Real do Configurador de Blocos</h1>
    <div class="premissa">
      <div class="premissa-title">Premissa dos Blocos do Editor de Conteúdo</div>
      <ul class="premissa-list">
        <li><b>Modularidade e Consistência:</b> Todos os blocos seguem arquitetura modular e experiência de configuração consistente.</li>
        <li><b>Personalização Visual Controlada:</b> Aparência, ícone, botão e presets são personalizáveis, mas sempre dentro de padrões visuais definidos.</li>
        <li><b>Campos Condicionais e UX Inteligente:</b> Opções avançadas só aparecem quando relevantes, organizadas em abas para facilitar a navegação.</li>
        <li><b>Preview Imediato:</b> O usuário visualiza em tempo real o resultado das opções selecionadas.</li>
        <li><b>Padronização e Escalabilidade:</b> Todos os tipos de bloco compartilham a mesma base de configuração visual, facilitando manutenção e evolução.</li>
        <li><b>Acessibilidade e Responsividade:</b> O painel é responsivo e acessível para todos os usuários e dispositivos.</li>
      </ul>
    </div>
    <div class="block-type-selector">
      <button class="block-type-btn" data-type="alert">Alerta</button>
      <button class="block-type-btn" data-type="colored">Colorido</button>
      <button class="block-type-btn" data-type="file">Arquivo</button>
      <button class="block-type-btn" data-type="image">Imagem</button>
      <button class="block-type-btn" data-type="quiz">Quiz</button>
      <button class="block-type-btn" data-type="text">Texto</button>
      <button class="block-type-btn" data-type="video">Vídeo</button>
    </div>
    <!-- Painel de configuração para cada tipo de bloco -->
    <!-- ALERT BLOCK -->
    <div class="block-config block-config-panel">
      <div class="block-title">Configuração: Alerta</div>
      <div class="preview"><b>Preview do bloco de alerta</b></div>
      <div class="tabs">
        <button class="tab-btn">Aparência</button>
        <button class="tab-btn">Ícone</button>
        <button class="tab-btn">Botão</button>
        <button class="tab-btn">Presets</button>
      </div>
      <div class="tab-content hidden">
        <!-- Aparência -->
        <div class="section">
          <div class="section-title">Aparência do Card</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo do card</label><input type="color" value="#fef2f2"></div>
            <div class="field"><label>Formato do card</label><select><option>Arredondado</option><option>Quadrado</option><option>Pílula</option></select></div>
            <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-border-fields"> Ativar borda</label></div>
            <div class="field" id="alert-border-fields" style="display:none;">
              <label>Cor da borda</label><input type="color" value="#e5e5e5">
              <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
            </div>
            <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-shadow-fields"> Ativar sombra</label></div>
            <div class="field" id="alert-shadow-fields" style="display:none;">
              <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
            </div>
            <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-hover-fields"> Ativar hover</label></div>
            <div class="field" id="alert-hover-fields" style="display:none;">
              <label>Sombra extra no hover</label><input type="checkbox"></div>
            <div class="field"><label>Tamanho da fonte</label><input type="number" min="10" max="32" value="16"> px</div>
            <div class="field"><label>Cor da fonte</label><input type="color" value="#222"></div>
            <div class="field"><label>Estilo da fonte</label><select><option>Normal</option><option>Itálico</option><option>Negrito</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Ícone -->
        <div class="section">
          <div class="section-title">Configuração do Ícone</div>
          <div class="fields">
            <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-icon-fields" checked> Ativar ícone</label></div>
            <div class="field" id="alert-icon-fields">
              <label>Posição</label>
              <select><option>Esquerda do título</option><option>Direita do título</option><option>Topo</option><option>Centro</option><option>Inferior</option></select>
              <label>Tipo</label>
              <select><option>Predefinido</option><option>Customizado</option></select>
              <label>Nome do ícone (Lucide)</label><input type="text" value="Info">
              <label>Cor de fundo do ícone</label><input type="color" value="#f3f4f6">
              <label>Cor do ícone</label><input type="color" value="#2563eb">
              <label>Formato do ícone</label><select><option>Circular</option><option>Quadrado</option></select>
              <label><input type="checkbox" class="toggle-btn" data-target="alert-icon-border-fields"> Ativar borda</label>
              <div id="alert-icon-border-fields" style="display:none;">
                <label>Cor da borda</label><input type="color" value="#e5e5e5">
                <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
              </div>
              <label><input type="checkbox" class="toggle-btn" data-target="alert-icon-shadow-fields"> Ativar sombra</label>
              <div id="alert-icon-shadow-fields" style="display:none;">
                <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
              </div>
              <label><input type="checkbox" class="toggle-btn" data-target="alert-icon-hover-fields"> Ativar hover</label>
              <div id="alert-icon-hover-fields" style="display:none;">
                <label>Sombra extra no hover</label><input type="checkbox"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Botão -->
        <div class="section">
          <div class="section-title">Configuração do Botão</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#3b82f6"></div>
            <div class="field"><label>Cor do texto</label><input type="color" value="#fff"></div>
            <div class="field"><label>Estilo</label><select><option>Arredondado</option><option>Plano</option><option>Preenchido</option><option>Outlined</option><option>Text</option></select></div>
            <div class="field"><label>Tamanho</label><select><option>Pequeno</option><option>Médio</option><option>Grande</option></select></div>
            <div class="field"><label>Posição</label><select><option>Superior esquerda</option><option>Superior direita</option><option>Inferior esquerda</option><option>Inferior direita</option><option>Centro</option></select></div>
            <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-button-border-fields"> Ativar borda</label></div>
            <div class="field" id="alert-button-border-fields" style="display:none;">
              <label>Cor da borda</label><input type="color" value="#e5e5e5">
              <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
            </div>
            <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-button-shadow-fields"> Ativar sombra</label></div>
            <div class="field" id="alert-button-shadow-fields" style="display:none;">
              <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
            </div>
            <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-button-hover-fields"> Ativar hover</label></div>
            <div class="field" id="alert-button-hover-fields" style="display:none;">
              <label>Sombra extra no hover</label><input type="checkbox"></div>
            <div class="field"><label>Texto do botão</label><input type="text" value="Saiba mais"></div>
            <div class="field"><label>URL</label><input type="url" value="https://exemplo.com"></div>
            <div class="field"><label>Ícone do botão</label><input type="text" value="ArrowRight"></div>
            <div class="field"><label>Posição do ícone</label><select><option>Antes do texto</option><option>Depois do texto</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Presets -->
        <div class="section">
          <div class="section-title">Presets de Estilo</div>
          <div class="preset-list">
            <button class="preset-btn selected">Success</button>
            <button class="preset-btn">Info</button>
            <button class="preset-btn">Warning</button>
            <button class="preset-btn">Error</button>
            <button class="preset-btn">Layout 1</button>
            <button class="preset-btn">Layout 2</button>
          </div>
          <div style="margin-top:16px;">
            <button class="preset-btn">Restaurar Tudo</button>
            <button class="preset-btn">Cancelar</button>
          </div>
          <div style="margin-top:12px; color:#64748b; font-size:0.95rem;">Selecionar um preset aplica automaticamente cor, ícone e layout.</div>
        </div>
      </div>
    </div>
    <!-- COLORED BLOCK (exemplo: igual ao alerta, mas com presets e campos diferentes) -->
    <div class="block-config block-config-panel hidden">
      <div class="block-title">Configuração: Colorido</div>
      <div class="preview"><b>Preview do bloco colorido</b></div>
      <div class="tabs">
        <button class="tab-btn">Aparência</button>
        <button class="tab-btn">Ícone</button>
        <button class="tab-btn">Botão</button>
        <button class="tab-btn">Presets</button>
      </div>
      <div class="tab-content hidden">
        <!-- Aparência -->
        <div class="section">
          <div class="section-title">Aparência do Card</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#eef2ff"></div>
            <div class="field"><label>Formato</label><select><option>Arredondado</option><option>Quadrado</option><option>Circular</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Ícone -->
        <div class="section">
          <div class="section-title">Configuração do Ícone</div>
          <div class="fields">
            <div class="field"><label>Nome do ícone</label><input type="text" value="Info"></div>
            <div class="field"><label>Cor do ícone</label><input type="color" value="#6366f1"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Botão -->
        <div class="section">
          <div class="section-title">Configuração do Botão</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#6366f1"></div>
            <div class="field"><label>Cor do texto</label><input type="color" value="#fff"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Presets -->
        <div class="section">
          <div class="section-title">Presets de Estilo</div>
          <div class="preset-list">
            <button class="preset-btn selected">Info</button>
            <button class="preset-btn">Warning</button>
            <button class="preset-btn">Error</button>
            <button class="preset-btn">Success</button>
          </div>
        </div>
      </div>
    </div>
    <!-- FILE BLOCK -->
    <div class="block-config block-config-panel hidden">
      <div class="block-title">Configuração: Arquivo</div>
      <div class="preview"><b>Preview do bloco de arquivo</b></div>
      <div class="tabs">
        <button class="tab-btn">Aparência</button>
        <button class="tab-btn">Ícone</button>
        <button class="tab-btn">Botão</button>
        <button class="tab-btn">Presets</button>
        <button class="tab-btn">Upload</button>
      </div>
      <div class="tab-content hidden">
        <!-- Aparência -->
        <div class="section">
          <div class="section-title">Aparência do Card</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#fff7ed"></div>
            <div class="field"><label>Formato</label><select><option>Arredondado</option><option>Quadrado</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Ícone -->
        <div class="section">
          <div class="section-title">Configuração do Ícone</div>
          <div class="fields">
            <div class="field"><label>Nome do ícone</label><input type="text" value="FileText"></div>
            <div class="field"><label>Cor do ícone</label><input type="color" value="#f59e42"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Botão -->
        <div class="section">
          <div class="section-title">Configuração do Botão</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#f59e42"></div>
            <div class="field"><label>Cor do texto</label><input type="color" value="#fff"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Presets -->
        <div class="section">
          <div class="section-title">Presets de Estilo</div>
          <div class="preset-list">
            <button class="preset-btn selected">Default</button>
            <button class="preset-btn">PDF</button>
            <button class="preset-btn">Image</button>
            <button class="preset-btn">Document</button>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Upload -->
        <div class="section">
          <div class="section-title">Upload de Arquivo</div>
          <div class="fields">
            <div class="field"><label>Arquivo</label><input type="file"></div>
            <div class="field"><label>Permitir download</label><input type="checkbox" checked></div>
            <div class="field"><label>Permitir visualização inline</label><input type="checkbox"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- IMAGE BLOCK -->
    <div class="block-config block-config-panel hidden">
      <div class="block-title">Configuração: Imagem</div>
      <div class="preview"><b>Preview do bloco de imagem</b></div>
      <div class="tabs">
        <button class="tab-btn">Aparência</button>
        <button class="tab-btn">Ícone</button>
        <button class="tab-btn">Presets</button>
        <button class="tab-btn">Conteúdo</button>
      </div>
      <div class="tab-content hidden">
        <!-- Aparência -->
        <div class="section">
          <div class="section-title">Aparência do Card</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#ffffff"></div>
            <div class="field"><label>Formato</label><select><option>Arredondado</option><option>Quadrado</option><option>Circular</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Ícone -->
        <div class="section">
          <div class="section-title">Configuração do Ícone</div>
          <div class="fields">
            <div class="field"><label>Nome do ícone</label><input type="text" value="Image"></div>
            <div class="field"><label>Cor do ícone</label><input type="color" value="#6b7280"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Presets -->
        <div class="section">
          <div class="section-title">Presets de Estilo</div>
          <div class="preset-list">
            <button class="preset-btn selected">Default</button>
            <button class="preset-btn">Gallery</button>
            <button class="preset-btn">Hero</button>
            <button class="preset-btn">Thumbnail</button>
            <button class="preset-btn">Avatar</button>
            <button class="preset-btn">Banner</button>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Conteúdo -->
        <div class="section">
          <div class="section-title">Conteúdo da Imagem</div>
          <div class="fields">
            <div class="field"><label>Título (alt)</label><input type="text" value="Título da imagem"></div>
            <div class="field"><label>URL da imagem</label><input type="url" value="https://exemplo.com/imagem.jpg"></div>
            <div class="field"><label>Legenda</label><input type="text" value="Legenda da imagem"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- QUIZ BLOCK -->
    <div class="block-config block-config-panel hidden">
      <div class="block-title">Configuração: Quiz</div>
      <div class="preview"><b>Preview do bloco de quiz</b></div>
      <div class="tabs">
        <button class="tab-btn">Aparência</button>
        <button class="tab-btn">Ícone</button>
        <button class="tab-btn">Presets</button>
        <button class="tab-btn">Pergunta</button>
      </div>
      <div class="tab-content hidden">
        <!-- Aparência -->
        <div class="section">
          <div class="section-title">Aparência do Card</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#fdf2f8"></div>
            <div class="field"><label>Formato</label><select><option>Arredondado</option><option>Quadrado</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Ícone -->
        <div class="section">
          <div class="section-title">Configuração do Ícone</div>
          <div class="fields">
            <div class="field"><label>Nome do ícone</label><input type="text" value="HelpCircle"></div>
            <div class="field"><label>Cor do ícone</label><input type="color" value="#ec4899"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Presets -->
        <div class="section">
          <div class="section-title">Presets de Estilo</div>
          <div class="preset-list">
            <button class="preset-btn selected">Default</button>
            <button class="preset-btn">Multiple Choice</button>
            <button class="preset-btn">True/False</button>
            <button class="preset-btn">Open Ended</button>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Pergunta -->
        <div class="section">
          <div class="section-title">Pergunta e Opções</div>
          <div class="fields">
            <div class="field"><label>Pergunta</label><input type="text" value="Qual a capital do Brasil?"></div>
            <div class="field"><label>Opção 1</label><input type="text" value="Brasília"></div>
            <div class="field"><label>Opção 2</label><input type="text" value="Rio de Janeiro"></div>
            <div class="field"><label>Opção 3</label><input type="text" value="São Paulo"></div>
            <div class="field"><label>Opção 4</label><input type="text" value="Salvador"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- TEXT BLOCK -->
    <div class="block-config block-config-panel hidden">
      <div class="block-title">Configuração: Texto</div>
      <div class="preview"><b>Preview do bloco de texto</b></div>
      <div class="tabs">
        <button class="tab-btn">Aparência</button>
        <button class="tab-btn">Ícone</button>
        <button class="tab-btn">Presets</button>
      </div>
      <div class="tab-content hidden">
        <!-- Aparência -->
        <div class="section">
          <div class="section-title">Aparência do Card</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#f8fafc"></div>
            <div class="field"><label>Formato</label><select><option>Arredondado</option><option>Quadrado</option></select></div>
            <div class="field"><label>Fonte</label><select><option>Inter</option><option>Roboto</option><option>Open Sans</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Ícone -->
        <div class="section">
          <div class="section-title">Configuração do Ícone</div>
          <div class="fields">
            <div class="field"><label>Nome do ícone</label><input type="text" value="AlignLeft"></div>
            <div class="field"><label>Cor do ícone</label><input type="color" value="#3b82f6"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Presets -->
        <div class="section">
          <div class="section-title">Presets de Estilo</div>
          <div class="preset-list">
            <button class="preset-btn selected">Default</button>
            <button class="preset-btn">Minimal</button>
          </div>
        </div>
      </div>
    </div>
    <!-- VIDEO BLOCK -->
    <div class="block-config block-config-panel hidden">
      <div class="block-title">Configuração: Vídeo</div>
      <div class="preview"><b>Preview do bloco de vídeo</b></div>
      <div class="tabs">
        <button class="tab-btn">Aparência</button>
        <button class="tab-btn">Ícone</button>
        <button class="tab-btn">Botão</button>
        <button class="tab-btn">Presets</button>
        <button class="tab-btn">Conteúdo</button>
      </div>
      <div class="tab-content hidden">
        <!-- Aparência -->
        <div class="section">
          <div class="section-title">Aparência do Card</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#f3f0ff"></div>
            <div class="field"><label>Formato</label><select><option>Arredondado</option><option>Quadrado</option></select></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Ícone -->
        <div class="section">
          <div class="section-title">Configuração do Ícone</div>
          <div class="fields">
            <div class="field"><label>Nome do ícone</label><input type="text" value="Play"></div>
            <div class="field"><label>Cor do ícone</label><input type="color" value="#a78bfa"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Botão -->
        <div class="section">
          <div class="section-title">Configuração do Botão</div>
          <div class="fields">
            <div class="field"><label>Cor de fundo</label><input type="color" value="#a78bfa"></div>
            <div class="field"><label>Cor do texto</label><input type="color" value="#fff"></div>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Presets -->
        <div class="section">
          <div class="section-title">Presets de Estilo</div>
          <div class="preset-list">
            <button class="preset-btn selected">Default</button>
            <button class="preset-btn">YouTube</button>
            <button class="preset-btn">Vimeo</button>
          </div>
        </div>
      </div>
      <div class="tab-content hidden">
        <!-- Conteúdo -->
        <div class="section">
          <div class="section-title">Conteúdo do Vídeo</div>
          <div class="fields">
            <div class="field"><label>Título</label><input type="text" value="Título do vídeo"></div>
            <div class="field"><label>URL do vídeo</label><input type="url" value="https://youtube.com/watch?v=abc"></div>
            <div class="field"><label>Legenda</label><input type="text" value="Legenda do vídeo"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 
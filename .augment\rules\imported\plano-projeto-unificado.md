---
type: "manual"
---

# 📋 PLANO DE PROJETO UNIFICADO V3.0
**Sistema de Gerenciamento de Projetos SaaS com Conteúdo Interativo**

**Versão:** 3.0 (Unificado)  
**Status:** Aprovado para Início

---

## 1. VISÃO GERAL DO PROJETO

### 1.1 Proposta de Valor
Somos um sistema SaaS de gerenciamento de projetos que se diferencia pela **integração profunda de conteúdo rico e interativo**. Nossa plataforma organiza o trabalho na hierarquia **Projeto > Etapa > Tarefa**, permitindo que equipes transformem a gestão de projetos em uma experiência de aprendizado e colaboração, com foco estratégico na usabilidade (UX/UI) para maximizar a produtividade.

### 1.2 Objetivos de Negócio
- **Curto Prazo (3 meses):** Lançar o MVP e atingir 150 usuários ativos.
- **<PERSON><PERSON><PERSON>o (6 meses):** Alcançar 500 usuários ativos com 70% de retenção e validar o Product-Market Fit.
- **Longo Prazo (12 meses):** Escalar para 2.000 usuários ativos, com um ARR (Annual Recurring Revenue) de $240k.

### 1.3 Público-Alvo
- **Primário:** Agências de Marketing Digital, Startups de Tecnologia, Consultores Freelancers.
- **Secundário:** Empresas de Software, Agências de Design, Equipes de Produto.

---

## 2. ESTRATÉGIA DE DESENVOLVIMENTO

### 2.1 Abordagem Híbrida com IA
A estratégia combina o uso de ferramentas de IA para acelerar o desenvolvimento, mantendo o controle técnico e a qualidade.

| Ferramenta  | Fase        | Responsabilidade                      |
|-------------|-------------|---------------------------------------|
| **Trae.ai**   | Fundação    | Arquitetura base, Scaffolding, Modelos de Dados |
| **Cursor.ai** | Core & Avançado | Implementação de features, UI, Refinamento |

### 2.2 Fases do Projeto

| Fase             | Duração    | Foco Principal                        | Entregável Chave           |
|------------------|------------|---------------------------------------|----------------------------|
| **1. Fundação**  | 4 semanas  | Backend Supabase, Segurança, Infraestrutura    | Base de API funcional      |
| **2. MVP Core**  | 8 semanas  | Funcionalidades Essenciais, UI Básica  | MVP para clientes piloto   |
| **3. Beta**      | 8 semanas  | Conteúdo Rico, Colaboração, UX Polida | Produto Beta para 50+ users|
| **4. Produção**  | 6 semanas  | Escalabilidade, API Pública, Otimização | Lançamento Comercial       |

---

## 3. ARQUITETURA TÉCNICA

### 3.1 Stack Tecnológico
A stack foi escolhida para otimizar a produtividade, escalabilidade e manutenibilidade.

#### **Backend**
- **Plataforma:** Supabase (PostgreSQL gerenciado, Auth, Storage, Edge Functions, APIs automáticas)
- **Banco de Dados:** Supabase PostgreSQL (usando JSONB para conteúdo dinâmico)
- **Cache & Filas:** Gerenciado pelo Supabase
- **Storage:** Supabase Storage
- **Autenticação:** Supabase Auth (JWT, OAuth, Magic Link)

#### **Frontend**
- **Framework:** React 18+ com TypeScript
- **Build Tool:** Vite
- **UI Library:** **shadcn-ui** ou Mantine UI.
  - **Justificativa:** Ambas são modernas, completas e leves. shadcn-ui é baseada em Radix, Mantine oferece excelente suporte a TypeScript e foco em UX/UI.
- **State Management:** Zustand (para estado de cliente) + React Query (para estado de servidor/cache de API). Essa abordagem segue o princípio SSOT, separando claramente o estado da UI do estado que vem do servidor.
- **Integração:** `@supabase/supabase-js` para comunicação com o backend.

#### **Infraestrutura e DevOps**
- **Cloud:** Supabase Cloud (serverless, multi-AZ, backups automáticos)
- **Containers:** Não necessário para backend; frontend pode ser hospedado em Vercel, Netlify, etc.
- **CI/CD:** GitHub Actions para automação de testes e deploy do frontend
- **Monitoramento:** Supabase Dashboard para backend, Sentry para rastreamento de erros no frontend

### 3.2 Princípios Arquiteturais
O desenvolvimento seguirá rigorosamente os princípios de **SRP, DRY, SSOT, KISS, e YAGNI**, conforme detalhado no documento `software_architecture_principles.md`. A separação entre UI (Componentes React) e lógica (Serviços TypeScript/Supabase) será estritamente aplicada.

### 3.3 Estrutura de Diretórios

#### **Backend (Supabase)**
- Gerenciado via painel do Supabase
- Tabelas: users, projects, stages, tasks, etc.
- Políticas de RLS para segurança multi-tenant
- Edge Functions para lógica customizada (se necessário)

#### **Frontend (React)**
```
src/
├── components/           # Componentes de UI puros e reutilizáveis (DRY)
│   ├── ui/               # Componentes base (Button, Input, Card)
│   └── layout/           # Estrutura (Header, Sidebar, etc.)
├── features/             # Módulos de funcionalidade (SRP)
│   ├── projects/
│   ├── tasks/
│   └── dashboard/
├── services/             # Lógica de API e de negócio (SRP, usando Supabase)
├── store/                # Estado global da UI (Zustand - SSOT)
├── hooks/                # Hooks customizados para lógica reutilizável
├── lib/                  # Supabase Client e utilitários
└── types/                # Tipos compartilhados
```

---

## 4. ROADMAP DE FEATURES (POR FASE)

### 4.1 Fase 1: Fundação (Semanas 1-4)
- [ ] Setup do projeto Supabase e React (Vite)
- [ ] Criação das tabelas e políticas de RLS no Supabase
- [ ] Implementação do modelo de dados customizado para `User`, `Project`, `Stage`, `Task`
- [ ] Setup da autenticação com Supabase Auth (endpoints de login, registro)
- [ ] Integração do frontend com Supabase
- **Critério de Sucesso:** API de autenticação funcional e ambiente de dev rodando.

### 4.2 Fase 2: MVP Core (Semanas 5-12)
- [ ] CRUD completo via Supabase para Projetos, Etapas e Tarefas.
- [ ] Implementação do sistema de permissões (RLS) básico.
- [ ] Frontend: Criação do **Dashboard Principal** com cards de projeto (cor primária: azul `#2563EB`).
- [ ] Frontend: Implementação da navegação hierárquica `Projeto > Etapa > Tarefa`.
- [ ] Frontend: Tela de **Detalhes da Tarefa** com editor de texto simples e painel de controle (status, responsáveis).
- [ ] Interface responsiva básica (Mobile-first).
- **Critério de Sucesso:** MVP funcional para onboarding de 3 clientes piloto.

### 4.3 Fase 3: Beta (Semanas 13-20)
- [ ] Backend: Sistema de notificações (in-app e email via Supabase Realtime).
- [ ] Frontend: Implementação do **Editor WYSIWYG completo** na tela de Tarefa, com blocos de conteúdo (vídeo, blocos coloridos, anexos).
- [ ] Frontend: Implementação de colaboração em tempo real (ex: visualização de quem está editando).
- [ ] Frontend: **Dashboard com métricas e gráficos** (progresso, distribuição de tarefas).
- [ ] Frontend: Polimento da UX/UI, seguindo as cores e componentes do `ux-ui.md`.
- **Critério de Sucesso:** Produto pronto para beta testers, com 50 usuários ativos semanais.

### 4.4 Fase 4: Produção (Semanas 21-26)
- [ ] Backend: Edge Functions e integrações externas (ex: webhooks via Supabase Functions).
- [ ] Backend: API pública com documentação (Swagger/OpenAPI via Supabase).
- [ ] Frontend: Criação de **Relatórios customizáveis** e templates de projeto.
- [ ] Otimização de performance (queries, bundle size) e testes de carga.
- [ ] Auditoria de segurança e implementação de hardening.
- **Critério de Sucesso:** Produto `production-ready`, ARR de $36k, churn < 5%.

---

## 5. GESTÃO DE RISCOS

| Risco                       | Probabilidade | Impacto   | Mitigação                                                              |
|-----------------------------|---------------|-----------|------------------------------------------------------------------------|
| **Complexidade vs. Prazo**  | Alta          | Alto      | Foco no escopo do MVP, desenvolvimento incremental, buffer de 20% no cronograma. |
| **Validação de Mercado**    | Média         | Crítico   | Validação contínua com clientes piloto, métricas de engajamento desde o início. |
| **Performance/Escalabilidade** | Média         | Alto      | Arquitetura Cloud-native (Supabase auto-scaling), testes de carga desde a Fase 3.  |
| **Segurança e LGPD/GDPR**   | Baixa         | Crítico   | Security audit na Fase 4, compliance by design, penetration testing pré-lançamento. |
| **Débito Técnico por IA**   | Baixa         | Médio     | Code review rigoroso de todo código gerado, documentação e testes unitários. |

---

## 6. ESTRATÉGIA DE GO-TO-MARKET

### 6.1 Modelo de Pricing (Freemium)
- **Free:** 1 projeto, 3 usuários, 1GB de storage.
- **Starter ($19/mês):** 5 projetos, 10 usuários, 10GB.
- **Professional ($49/mês):** Projetos ilimitados, 25 usuários, 100GB.
- **Enterprise (Custom):** Features avançadas, suporte dedicado.

### 6.2 Lançamento
- **Meses 1-3 (Pós-MVP):** Acesso gratuito para early adopters em troca de feedback.
- **Meses 4-6:** Desconto de 50% para os primeiros 100 clientes pagantes.
- **Mês 7+:** Lançamento comercial completo.

---

## 7. MÉTRICAS DE SUCESSO E KPIS

- **Produto:** Adoção de features (>60%), Retenção D30 (>25%), NPS (>50).
- **Negócio:** Crescimento do MRR (20% MoM), LTV/CAC > 3:1.
- **Técnico:** Page Load < 2s, API Response < 200ms, Uptime > 99.9%, Cobertura de Testes > 80%.

---

*Este documento é a única fonte da verdade para o planejamento do projeto e deve ser mantido atualizado pela equipe.* 
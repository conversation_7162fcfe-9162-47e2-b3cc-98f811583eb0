name: <PERSON><PERSON><PERSON>

on:
  push:
    paths:
      - '.project-rules/**/*.md'
      - 'README.md'
  pull_request:
    paths:
      - '.project-rules/**/*.md'
      - 'README.md'

jobs:
  markdown-lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install markdownlint-cli
        run: npm install -g markdownlint-cli
      - name: Lint markdown files
        run: markdownlint .project-rules/**/*.md README.md 
import { Editor } from '@tiptap/react';

export interface TableConfig {
  id: string;
  dimensions: {
    width?: string;
    height?: string;
    columnWidths?: string[];
    rowHeights?: string[];
  };
  colors: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    cellColors?: { [key: string]: string };
    rowColors?: { [key: string]: string };
    columnColors?: { [key: string]: string };
  };
  styles: {
    borderWidth?: number;
    borderRadius?: number;
    padding?: number;
    boxShadow?: boolean;
    shadowIntensity?: number;
  };
  responsive: {
    mobileColumnWidths?: string[];
    mobileRowHeights?: string[];
    hideColumnsOnMobile?: number[];
  };
}

export class TableConfigPersistence {
  private static readonly CONFIG_ATTRIBUTE = 'data-table-config';
  private static readonly VERSION = '1.0';

  /**
   * Salva configurações da tabela no elemento HTML
   */
  static saveTableConfig(tableElement: HTMLTableElement, config: TableConfig): void {
    try {
      const configData = {
        version: this.VERSION,
        timestamp: Date.now(),
        config
      };
      
      tableElement.setAttribute(this.CONFIG_ATTRIBUTE, JSON.stringify(configData));
      
      // Aplicar estilos inline para garantir persistência
      this.applyConfigToElement(tableElement, config);
      
    } catch (error) {
      console.warn('Erro ao salvar configuração da tabela:', error);
    }
  }

  /**
   * Carrega configurações da tabela do elemento HTML
   */
  static loadTableConfig(tableElement: HTMLTableElement): TableConfig | null {
    try {
      const configAttr = tableElement.getAttribute(this.CONFIG_ATTRIBUTE);
      if (!configAttr) return null;

      const configData = JSON.parse(configAttr);
      
      // Verificar versão para compatibilidade
      if (configData.version !== this.VERSION) {
        console.warn('Versão de configuração incompatível, usando padrões');
        return null;
      }

      return configData.config;
      
    } catch (error) {
      console.warn('Erro ao carregar configuração da tabela:', error);
      return null;
    }
  }

  /**
   * Aplica configurações ao elemento HTML
   */
  private static applyConfigToElement(tableElement: HTMLTableElement, config: TableConfig): void {
    // Aplicar estilos da tabela
    if (config.dimensions.width) {
      tableElement.style.width = config.dimensions.width;
    }
    
    if (config.colors.backgroundColor) {
      tableElement.style.backgroundColor = config.colors.backgroundColor;
    }
    
    if (config.colors.borderColor) {
      tableElement.style.borderColor = config.colors.borderColor;
    }
    
    if (config.styles.borderWidth !== undefined) {
      tableElement.style.borderWidth = `${config.styles.borderWidth}px`;
    }
    
    if (config.styles.borderRadius !== undefined) {
      tableElement.style.borderRadius = `${config.styles.borderRadius}px`;
    }
    
    if (config.styles.boxShadow && config.styles.shadowIntensity) {
      const shadow = `0 ${config.styles.shadowIntensity}px ${config.styles.shadowIntensity * 2}px rgba(0,0,0,0.1)`;
      tableElement.style.boxShadow = shadow;
    }

    // Aplicar larguras de colunas
    if (config.dimensions.columnWidths) {
      const rows = tableElement.querySelectorAll('tr');
      rows.forEach((row) => {
        Array.from(row.children).forEach((cell, index) => {
          const width = config.dimensions.columnWidths?.[index];
          if (width) {
            (cell as HTMLElement).style.width = width;
            (cell as HTMLElement).style.minWidth = width;
          }
        });
      });
    }

    // Aplicar alturas de linhas
    if (config.dimensions.rowHeights) {
      const rows = tableElement.querySelectorAll('tr');
      rows.forEach((row, index) => {
        const height = config.dimensions.rowHeights?.[index];
        if (height) {
          (row as HTMLElement).style.height = height;
        }
      });
    }

    // Aplicar cores de células
    if (config.colors.cellColors) {
      Object.entries(config.colors.cellColors).forEach(([cellId, color]) => {
        const cell = tableElement.querySelector(`[data-cell-id="${cellId}"]`) as HTMLElement;
        if (cell) {
          cell.style.backgroundColor = color;
        }
      });
    }

    // Aplicar cores de linhas
    if (config.colors.rowColors) {
      Object.entries(config.colors.rowColors).forEach(([rowIndex, color]) => {
        const row = tableElement.querySelector(`tr:nth-child(${parseInt(rowIndex) + 1})`) as HTMLElement;
        if (row) {
          Array.from(row.children).forEach((cell) => {
            (cell as HTMLElement).style.backgroundColor = color;
          });
        }
      });
    }

    // Aplicar cores de colunas
    if (config.colors.columnColors) {
      Object.entries(config.colors.columnColors).forEach(([columnIndex, color]) => {
        const rows = tableElement.querySelectorAll('tr');
        rows.forEach((row) => {
          const cell = row.children[parseInt(columnIndex)] as HTMLElement;
          if (cell) {
            cell.style.backgroundColor = color;
          }
        });
      });
    }
  }

  /**
   * Gera configuração responsiva baseada no tamanho da tela
   */
  static generateResponsiveConfig(config: TableConfig): TableConfig {
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile && config.responsive) {
      return {
        ...config,
        dimensions: {
          ...config.dimensions,
          columnWidths: config.responsive.mobileColumnWidths || config.dimensions.columnWidths,
          rowHeights: config.responsive.mobileRowHeights || config.dimensions.rowHeights,
        }
      };
    }
    
    return config;
  }

  /**
   * Otimiza configurações para performance
   */
  static optimizeConfig(config: TableConfig): TableConfig {
    // Remover configurações desnecessárias ou duplicadas
    const optimized = { ...config };
    
    // Remover cores padrão
    if (optimized.colors.backgroundColor === '#ffffff') {
      delete optimized.colors.backgroundColor;
    }
    
    if (optimized.colors.textColor === '#000000') {
      delete optimized.colors.textColor;
    }
    
    if (optimized.colors.borderColor === '#e5e7eb') {
      delete optimized.colors.borderColor;
    }
    
    // Remover estilos padrão
    if (optimized.styles.borderWidth === 1) {
      delete optimized.styles.borderWidth;
    }
    
    if (optimized.styles.borderRadius === 0) {
      delete optimized.styles.borderRadius;
    }
    
    if (optimized.styles.padding === 8) {
      delete optimized.styles.padding;
    }
    
    return optimized;
  }

  /**
   * Exporta configurações para JSON
   */
  static exportConfig(tableElement: HTMLTableElement): string {
    const config = this.loadTableConfig(tableElement);
    if (!config) return '{}';
    
    return JSON.stringify({
      version: this.VERSION,
      exported: new Date().toISOString(),
      config: this.optimizeConfig(config)
    }, null, 2);
  }

  /**
   * Importa configurações de JSON
   */
  static importConfig(tableElement: HTMLTableElement, jsonConfig: string): boolean {
    try {
      const data = JSON.parse(jsonConfig);
      
      if (data.version !== this.VERSION) {
        console.warn('Versão incompatível ao importar configuração');
        return false;
      }
      
      this.saveTableConfig(tableElement, data.config);
      return true;
      
    } catch (error) {
      console.error('Erro ao importar configuração:', error);
      return false;
    }
  }

  /**
   * Gera ID único para célula
   */
  static generateCellId(rowIndex: number, columnIndex: number): string {
    return `cell-${rowIndex}-${columnIndex}`;
  }

  /**
   * Adiciona IDs às células para rastreamento
   */
  static addCellIds(tableElement: HTMLTableElement): void {
    const rows = tableElement.querySelectorAll('tr');
    rows.forEach((row, rowIndex) => {
      Array.from(row.children).forEach((cell, columnIndex) => {
        const cellId = this.generateCellId(rowIndex, columnIndex);
        cell.setAttribute('data-cell-id', cellId);
      });
    });
  }
}

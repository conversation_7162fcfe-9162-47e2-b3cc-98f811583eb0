#!/usr/bin/env node

/**
 * Script para analisar dependências do Lexical que podem ser removidas
 * após a migração completa para Tiptap
 */

const fs = require('fs');
const path = require('path');

// Ler package.json
const packageJsonPath = path.join(__dirname, '..', 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Dependências do Lexical
const lexicalDependencies = Object.keys(packageJson.dependencies || {})
  .filter(dep => dep.startsWith('@lexical/') || dep === 'lexical');

console.log('🔍 Análise de Dependências do Lexical\n');
console.log('📦 Dependências encontradas:');
lexicalDependencies.forEach(dep => {
  console.log(`  - ${dep}: ${packageJson.dependencies[dep]}`);
});

console.log(`\n📊 Total: ${lexicalDependencies.length} dependências`);

// Calcular tamanho estimado do bundle
const estimatedSizes = {
  '@lexical/code': '15KB',
  '@lexical/history': '8KB',
  '@lexical/html': '12KB',
  '@lexical/link': '10KB',
  '@lexical/list': '18KB',
  '@lexical/markdown': '25KB',
  '@lexical/react': '45KB',
  '@lexical/rich-text': '20KB',
  '@lexical/selection': '12KB',
  '@lexical/table': '30KB',
  'lexical': '120KB'
};

let totalEstimatedSize = 0;
console.log('\n📏 Tamanho estimado do bundle:');
lexicalDependencies.forEach(dep => {
  const size = estimatedSizes[dep] || '5KB';
  console.log(`  - ${dep}: ~${size}`);
  totalEstimatedSize += parseInt(size.replace('KB', ''));
});

console.log(`\n🎯 Total estimado: ~${totalEstimatedSize}KB`);

console.log('\n🗑️  PLANO DE REMOÇÃO DO LEXICAL');
console.log('=====================================');

console.log('\n1. Dependências para remover:');
lexicalDependencies.forEach(dep => {
  console.log(`  npm uninstall ${dep}`);
});

console.log('\n2. Comando completo de remoção:');
console.log(`  npm uninstall ${lexicalDependencies.join(' ')}`);

console.log('\n3. Arquivos Lexical que podem ser removidos após validação:');
const filesToRemove = [
  'src/components/features/content-editor/blocks/text/TextBlockEditor.tsx',
  'src/components/features/content-editor/blocks/text/TextToolbar.tsx',
  'src/components/features/content-editor/blocks/text/AutoFocusPlugin.tsx',
  'src/components/features/content-editor/blocks/text/AutoLinkPlugin.tsx',
  'src/components/features/content-editor/blocks/text/HighlightPlugin.tsx',
  'src/components/features/content-editor/blocks/text/HashtagPlugin.tsx',
  'src/components/features/content-editor/blocks/text/FloatingTextFormatToolbarPlugin.tsx',
  'src/components/features/content-editor/blocks/text/CodeHighlightPlugin.tsx',
  'src/components/features/content-editor/blocks/text/LineBreakPlugin.tsx',
];

filesToRemove.forEach(file => {
  console.log(`  - ${file}`);
});

console.log('\n4. Arquivos Tiptap para manter:');
const filesToKeep = [
  'src/components/features/content-editor/blocks/text/TiptapTextEditor.tsx',
  'src/components/features/content-editor/blocks/text/TiptapTextBlockEditor.tsx',
  'src/components/features/content-editor/blocks/text/TiptapToolbar.tsx',
  'src/components/features/content-editor/blocks/text/TextEditorWrapper.tsx',
  'src/components/features/content-editor/blocks/text/utils/lexical-tiptap-converter.ts',
];

filesToKeep.forEach(file => {
  console.log(`  ✅ ${file}`);
});

console.log('\n5. Benefícios esperados:');
console.log(`  - Redução do bundle: ~${totalEstimatedSize}KB`);
console.log('  - Menos dependências: ' + lexicalDependencies.length + ' pacotes');
console.log('  - Código mais limpo e maintível');
console.log('  - Melhor performance de build');

console.log('\n⚠️  ATENÇÃO:');
console.log('  - Execute testes completos antes da remoção');
console.log('  - Mantenha backup dos arquivos Lexical');
console.log('  - Valide que todos os dados existentes funcionam');
console.log('  - Considere manter o conversor por um período');

console.log('\n✅ Análise concluída!');

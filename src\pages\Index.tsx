import React from 'react';
import { Dashboard } from '@/components/features/dashboard';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { Header } from '@/components/layout/Header';

const Index = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
  return (
    <>
      <Header showSidebarButton={true} />
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
        <Dashboard />
      </div>
    </>
  );
};

export default Index;
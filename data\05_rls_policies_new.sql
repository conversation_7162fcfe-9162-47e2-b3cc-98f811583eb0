-- =====================================================
-- POLÍTICAS RLS - ROW LEVEL SECURITY
-- =====================================================
-- Contém: Políticas de segurança para todas as tabelas
-- Dependências: Todas as tabelas criadas
-- Versão: 2.1 - Julho 2025 - SEM RECURSÃO
-- IMPORTANTE: Nenhuma política pode referenciar profiles.role para evitar recursão

-- Verificar dependências
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles') THEN
    RAISE EXCEPTION 'Tabelas não encontradas. Execute os scripts anteriores primeiro.';
  END IF;
  RAISE NOTICE '✅ Dependências verificadas';
END $$;

-- =====================================================
-- LIMPEZA DE POLÍTICAS EXISTENTES
-- =====================================================

-- Function para remover todas as políticas de uma tabela
CREATE OR REPLACE FUNCTION public.drop_all_policies(target_table text)
RETURNS void AS $$
DECLARE
  policy_record RECORD;
BEGIN
  FOR policy_record IN
    SELECT policyname
    FROM pg_policies p
    WHERE p.tablename = target_table AND p.schemaname = 'public'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I', policy_record.policyname, target_table);
  END LOOP;
  RAISE NOTICE '✅ Políticas da tabela % removidas', target_table;
END;
$$ LANGUAGE plpgsql;

-- Remover todas as políticas existentes
SELECT public.drop_all_policies('profiles');
SELECT public.drop_all_policies('projects');
SELECT public.drop_all_policies('stages');
SELECT public.drop_all_policies('tasks');
SELECT public.drop_all_policies('project_members');
SELECT public.drop_all_policies('task_executors');
SELECT public.drop_all_policies('task_approvers');
SELECT public.drop_all_policies('stage_responsibles');
SELECT public.drop_all_policies('task_content_blocks');
SELECT public.drop_all_policies('task_attachments');
SELECT public.drop_all_policies('evidence');
SELECT public.drop_all_policies('task_comments');
SELECT public.drop_all_policies('project_history');
SELECT public.drop_all_policies('user_notifications');
SELECT public.drop_all_policies('quizzes');
SELECT public.drop_all_policies('quiz_attempts');
SELECT public.drop_all_policies('quiz_answers');
SELECT public.drop_all_policies('user_quiz_progress');
SELECT public.drop_all_policies('quiz_statistics');

-- =====================================================
-- HABILITAR RLS
-- =====================================================

-- Habilitar RLS em todas as tabelas
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_executors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_approvers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stage_responsibles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.evidence ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_quiz_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_statistics ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLÍTICAS PARA PROFILES (SEM RECURSÃO)
-- =====================================================

-- Usuários podem ver todos os perfis (para autocomplete e referências)
CREATE POLICY "Users can view all profiles"
  ON public.profiles
  FOR SELECT
  USING (true);

-- Usuários podem atualizar seu próprio perfil
CREATE POLICY "Users can update own profile"
  ON public.profiles
  FOR UPDATE
  USING (id = auth.uid());

-- Inserção de perfil permitida apenas para o próprio usuário
CREATE POLICY "Users can insert own profile"
  ON public.profiles
  FOR INSERT
  WITH CHECK (id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA PROJECTS (SIMPLES)
-- =====================================================

-- Usuários podem ver projetos onde são donos
CREATE POLICY "Users can view owned projects"
  ON public.projects
  FOR SELECT
  USING (owner_id = auth.uid());

-- Usuários podem criar projetos
CREATE POLICY "Users can create projects"
  ON public.projects
  FOR INSERT
  WITH CHECK (owner_id = auth.uid());

-- Usuários podem atualizar projetos próprios
CREATE POLICY "Users can update owned projects"
  ON public.projects
  FOR UPDATE
  USING (owner_id = auth.uid());

-- Usuários podem deletar projetos próprios
CREATE POLICY "Users can delete owned projects"
  ON public.projects
  FOR DELETE
  USING (owner_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA STAGES (SIMPLES)
-- =====================================================

-- Usuários podem ver estágios de projetos próprios
CREATE POLICY "Users can view stages of owned projects"
  ON public.stages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem criar estágios em projetos próprios
CREATE POLICY "Users can create stages in owned projects"
  ON public.stages
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem atualizar estágios de projetos próprios
CREATE POLICY "Users can update stages of owned projects"
  ON public.stages
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem deletar estágios de projetos próprios
CREATE POLICY "Users can delete stages of owned projects"
  ON public.stages
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- =====================================================
-- POLÍTICAS PARA TASKS (SIMPLES)
-- =====================================================

-- Usuários podem ver tarefas de projetos próprios ou tarefas atribuídas a eles
CREATE POLICY "Users can view accessible tasks"
  ON public.tasks
  FOR SELECT
  USING (
    assigned_to = auth.uid() OR
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem criar tarefas em projetos próprios
CREATE POLICY "Users can create tasks in owned projects"
  ON public.tasks
  FOR INSERT
  WITH CHECK (
    created_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem atualizar tarefas próprias ou de projetos próprios
CREATE POLICY "Users can update accessible tasks"
  ON public.tasks
  FOR UPDATE
  USING (
    assigned_to = auth.uid() OR
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem deletar tarefas de projetos próprios
CREATE POLICY "Users can delete tasks in owned projects"
  ON public.tasks
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    )
  );

-- =====================================================
-- POLÍTICAS PARA PROJECT_MEMBERS (SIMPLES)
-- =====================================================

-- Usuários podem ver membros de projetos próprios
CREATE POLICY "Users can view members of owned projects"
  ON public.project_members
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem adicionar membros em projetos próprios
CREATE POLICY "Users can add members to owned projects"
  ON public.project_members
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem atualizar membros de projetos próprios
CREATE POLICY "Users can update members of owned projects"
  ON public.project_members
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem remover membros de projetos próprios
CREATE POLICY "Users can remove members from owned projects"
  ON public.project_members
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- =====================================================
-- POLÍTICAS PARA TASK_EXECUTORS (SIMPLES)
-- =====================================================

-- Usuários podem ver executores de tarefas acessíveis
CREATE POLICY "Users can view task executors"
  ON public.task_executors
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem se adicionar como executores
CREATE POLICY "Users can add themselves as executors"
  ON public.task_executors
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Usuários podem atualizar próprios registros de executor
CREATE POLICY "Users can update own executor records"
  ON public.task_executors
  FOR UPDATE
  USING (user_id = auth.uid());

-- Usuários podem se remover como executores
CREATE POLICY "Users can remove themselves as executors"
  ON public.task_executors
  FOR DELETE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA TASK_APPROVERS (SIMPLES)
-- =====================================================

-- Usuários podem ver aprovadores de tarefas acessíveis
CREATE POLICY "Users can view task approvers"
  ON public.task_approvers
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem se adicionar como aprovadores
CREATE POLICY "Users can add themselves as approvers"
  ON public.task_approvers
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Usuários podem atualizar próprios registros de aprovador
CREATE POLICY "Users can update own approver records"
  ON public.task_approvers
  FOR UPDATE
  USING (user_id = auth.uid());

-- Usuários podem se remover como aprovadores
CREATE POLICY "Users can remove themselves as approvers"
  ON public.task_approvers
  FOR DELETE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA STAGE_RESPONSIBLES (SIMPLES)
-- =====================================================

-- Usuários podem ver responsáveis de estágios acessíveis
CREATE POLICY "Users can view stage responsibles"
  ON public.stage_responsibles
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.stages s
      JOIN public.projects p ON s.project_id = p.id
      WHERE s.id = stage_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem se adicionar como responsáveis
CREATE POLICY "Users can add themselves as responsibles"
  ON public.stage_responsibles
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Usuários podem atualizar próprios registros de responsável
CREATE POLICY "Users can update own responsible records"
  ON public.stage_responsibles
  FOR UPDATE
  USING (user_id = auth.uid());

-- Usuários podem se remover como responsáveis
CREATE POLICY "Users can remove themselves as responsibles"
  ON public.stage_responsibles
  FOR DELETE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA TASK_CONTENT_BLOCKS (SIMPLES)
-- =====================================================

-- Usuários podem ver blocos de conteúdo de tarefas acessíveis
CREATE POLICY "Users can view task content blocks"
  ON public.task_content_blocks
  FOR SELECT
  USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem criar blocos de conteúdo
CREATE POLICY "Users can create task content blocks"
  ON public.task_content_blocks
  FOR INSERT
  WITH CHECK (created_by = auth.uid());

-- Usuários podem atualizar próprios blocos de conteúdo
CREATE POLICY "Users can update own content blocks"
  ON public.task_content_blocks
  FOR UPDATE
  USING (created_by = auth.uid());

-- Usuários podem deletar próprios blocos de conteúdo
CREATE POLICY "Users can delete own content blocks"
  ON public.task_content_blocks
  FOR DELETE
  USING (created_by = auth.uid());

-- =====================================================
-- POLÍTICAS PARA TASK_ATTACHMENTS (SIMPLES)
-- =====================================================

-- Usuários podem ver anexos de tarefas acessíveis
CREATE POLICY "Users can view task attachments"
  ON public.task_attachments
  FOR SELECT
  USING (
    uploaded_by = auth.uid() OR
    approved_by = auth.uid()
  );

-- Usuários podem fazer upload de anexos
CREATE POLICY "Users can upload task attachments"
  ON public.task_attachments
  FOR INSERT
  WITH CHECK (uploaded_by = auth.uid());

-- Usuários podem atualizar próprios anexos
CREATE POLICY "Users can update own attachments"
  ON public.task_attachments
  FOR UPDATE
  USING (uploaded_by = auth.uid() OR approved_by = auth.uid());

-- Usuários podem deletar próprios anexos
CREATE POLICY "Users can delete own attachments"
  ON public.task_attachments
  FOR DELETE
  USING (uploaded_by = auth.uid());

-- =====================================================
-- POLÍTICAS PARA EVIDENCE (SIMPLES)
-- =====================================================

-- Usuários podem ver evidências próprias
CREATE POLICY "Users can view own evidence"
  ON public.evidence
  FOR SELECT
  USING (uploaded_by = auth.uid() OR approved_by = auth.uid());

-- Usuários podem criar evidências
CREATE POLICY "Users can create evidence"
  ON public.evidence
  FOR INSERT
  WITH CHECK (uploaded_by = auth.uid());

-- Usuários podem atualizar próprias evidências
CREATE POLICY "Users can update own evidence"
  ON public.evidence
  FOR UPDATE
  USING (uploaded_by = auth.uid() OR approved_by = auth.uid());

-- Usuários podem deletar próprias evidências
CREATE POLICY "Users can delete own evidence"
  ON public.evidence
  FOR DELETE
  USING (uploaded_by = auth.uid());

-- =====================================================
-- POLÍTICAS PARA TASK_COMMENTS (SIMPLES)
-- =====================================================

-- Usuários podem ver comentários de tarefas acessíveis
CREATE POLICY "Users can view task comments"
  ON public.task_comments
  FOR SELECT
  USING (
    author = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem criar comentários
CREATE POLICY "Users can create task comments"
  ON public.task_comments
  FOR INSERT
  WITH CHECK (author = auth.uid());

-- Usuários podem atualizar próprios comentários
CREATE POLICY "Users can update own comments"
  ON public.task_comments
  FOR UPDATE
  USING (author = auth.uid());

-- Usuários podem deletar próprios comentários
CREATE POLICY "Users can delete own comments"
  ON public.task_comments
  FOR DELETE
  USING (author = auth.uid());

-- =====================================================
-- POLÍTICAS PARA PROJECT_HISTORY (SIMPLES)
-- =====================================================

-- Usuários podem ver histórico de projetos próprios
CREATE POLICY "Users can view project history"
  ON public.project_history
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.owner_id = auth.uid()
    )
  );

-- Sistema pode criar histórico (sem restrição específica)
CREATE POLICY "System can create project history"
  ON public.project_history
  FOR INSERT
  WITH CHECK (true);

-- =====================================================
-- POLÍTICAS PARA USER_NOTIFICATIONS (SIMPLES)
-- =====================================================

-- Usuários podem ver apenas suas próprias notificações
CREATE POLICY "Users can view own notifications"
  ON public.user_notifications
  FOR SELECT
  USING (user_id = auth.uid());

-- Sistema pode criar notificações
CREATE POLICY "System can create notifications"
  ON public.user_notifications
  FOR INSERT
  WITH CHECK (true);

-- Usuários podem atualizar suas próprias notificações
CREATE POLICY "Users can update own notifications"
  ON public.user_notifications
  FOR UPDATE
  USING (user_id = auth.uid());

-- Usuários podem deletar suas próprias notificações
CREATE POLICY "Users can delete own notifications"
  ON public.user_notifications
  FOR DELETE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA QUIZZES (SIMPLES)
-- =====================================================

-- Usuários podem ver quizzes de tarefas acessíveis
CREATE POLICY "Users can view quizzes"
  ON public.quizzes
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem criar quizzes em projetos próprios
CREATE POLICY "Users can create quizzes"
  ON public.quizzes
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem atualizar quizzes de projetos próprios
CREATE POLICY "Users can update quizzes"
  ON public.quizzes
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- Usuários podem deletar quizzes de projetos próprios
CREATE POLICY "Users can delete quizzes"
  ON public.quizzes
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE t.id = task_id AND p.owner_id = auth.uid()
    )
  );

-- =====================================================
-- POLÍTICAS PARA QUIZ_ATTEMPTS (SIMPLES)
-- =====================================================

-- Usuários podem ver suas próprias tentativas
CREATE POLICY "Users can view own quiz attempts"
  ON public.quiz_attempts
  FOR SELECT
  USING (user_id = auth.uid());

-- Usuários podem criar suas próprias tentativas
CREATE POLICY "Users can create own quiz attempts"
  ON public.quiz_attempts
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Usuários podem atualizar suas próprias tentativas
CREATE POLICY "Users can update own quiz attempts"
  ON public.quiz_attempts
  FOR UPDATE
  USING (user_id = auth.uid());

-- Usuários podem deletar suas próprias tentativas
CREATE POLICY "Users can delete own quiz attempts"
  ON public.quiz_attempts
  FOR DELETE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA QUIZ_ANSWERS (SIMPLES)
-- =====================================================

-- Usuários podem ver respostas de suas tentativas
CREATE POLICY "Users can view own quiz answers"
  ON public.quiz_answers
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.quiz_attempts qa
      WHERE qa.id = attempt_id AND qa.user_id = auth.uid()
    )
  );

-- Usuários podem criar respostas em suas tentativas
CREATE POLICY "Users can create own quiz answers"
  ON public.quiz_answers
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.quiz_attempts qa
      WHERE qa.id = attempt_id AND qa.user_id = auth.uid()
    )
  );

-- Usuários podem atualizar respostas de suas tentativas
CREATE POLICY "Users can update own quiz answers"
  ON public.quiz_answers
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.quiz_attempts qa
      WHERE qa.id = attempt_id AND qa.user_id = auth.uid()
    )
  );

-- Usuários podem deletar respostas de suas tentativas
CREATE POLICY "Users can delete own quiz answers"
  ON public.quiz_answers
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.quiz_attempts qa
      WHERE qa.id = attempt_id AND qa.user_id = auth.uid()
    )
  );

-- =====================================================
-- POLÍTICAS PARA USER_QUIZ_PROGRESS (SIMPLES)
-- =====================================================

-- Usuários podem ver seu próprio progresso
CREATE POLICY "Users can view own quiz progress"
  ON public.user_quiz_progress
  FOR SELECT
  USING (user_id = auth.uid());

-- Usuários podem criar seu próprio progresso
CREATE POLICY "Users can create own quiz progress"
  ON public.user_quiz_progress
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Usuários podem atualizar seu próprio progresso
CREATE POLICY "Users can update own quiz progress"
  ON public.user_quiz_progress
  FOR UPDATE
  USING (user_id = auth.uid());

-- Usuários podem deletar seu próprio progresso
CREATE POLICY "Users can delete own quiz progress"
  ON public.user_quiz_progress
  FOR DELETE
  USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS PARA QUIZ_STATISTICS (SIMPLES)
-- =====================================================

-- Usuários podem ver estatísticas de quizzes de projetos próprios
CREATE POLICY "Users can view quiz statistics"
  ON public.quiz_statistics
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.quizzes q
      JOIN public.tasks t ON q.task_id = t.id
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE q.id = quiz_id AND p.owner_id = auth.uid()
    )
  );

-- Sistema pode criar estatísticas
CREATE POLICY "System can create quiz statistics"
  ON public.quiz_statistics
  FOR INSERT
  WITH CHECK (true);

-- Sistema pode atualizar estatísticas
CREATE POLICY "System can update quiz statistics"
  ON public.quiz_statistics
  FOR UPDATE
  USING (true);

-- Sistema pode deletar estatísticas
CREATE POLICY "System can delete quiz statistics"
  ON public.quiz_statistics
  FOR DELETE
  USING (true);

-- =====================================================
-- LIMPEZA
-- =====================================================

-- Remover função auxiliar
DROP FUNCTION IF EXISTS public.drop_all_policies(text);

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
DECLARE
  policy_count INTEGER;
  table_count INTEGER;
BEGIN
  -- Contar políticas criadas
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies
  WHERE schemaname = 'public';
  
  -- Contar tabelas com RLS habilitado
  SELECT COUNT(*) INTO table_count
  FROM pg_tables t
  JOIN pg_class c ON c.relname = t.tablename
  WHERE t.schemaname = 'public' 
    AND c.relrowsecurity = true;
  
  RAISE NOTICE '✅ Políticas RLS criadas sem recursão!';
  RAISE NOTICE '🔒 Total de políticas: %', policy_count;
  RAISE NOTICE '🛡️ Tabelas com RLS: %', table_count;
  RAISE NOTICE '⚠️ IMPORTANTE: Todas as políticas foram simplificadas para evitar recursão';
  RAISE NOTICE '👤 Funcionalidade admin será implementada via aplicação, não via RLS';
END $$;

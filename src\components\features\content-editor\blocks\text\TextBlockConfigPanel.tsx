import React, { useState, useEffect } from 'react';
import { BlockConfig } from '@/types';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipProvider, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import { RotateCcw, FileText } from 'lucide-react';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  CardAppearanceConfig,
  IconAppearanceConfig
} from '../shared/config-panel';
import { presetToBlockConfig } from '../shared/config-panel/constants/migration';
import { blockTypePresetsMap } from '../shared/config-panel/constants/block-types';

interface TextBlockConfigPanelProps {
  config: BlockConfig;
  onChange: (config: BlockConfig) => void;
  onReset: () => void;
  preview?: React.ReactNode;
}

/**
 * Configurador específico para blocos de texto
 * Remove tabs irrelevantes como "Botão" e foca em configurações de texto
 */
export const TextBlockConfigPanel: React.FC<TextBlockConfigPanelProps> = ({
  config,
  onChange,
  onReset,
  preview
}) => {
  const [feedback, setFeedback] = useState<string>('');

  // Limpar feedback após 3 segundos
  useEffect(() => {
    if (feedback) {
      const timer = setTimeout(() => setFeedback(''), 3000);
      return () => clearTimeout(timer);
    }
  }, [feedback]);

  // Obter presets de texto do sistema
  const textBlockPresets = blockTypePresetsMap.text || {};

  // Reset específico para card
  const handleResetCard = () => {
    const defaultConfig = presetToBlockConfig(textBlockPresets.default);
    onChange({
      ...config,
      card: defaultConfig.card
    });
    setFeedback('Aparência do card restaurada');
  };

  // Reset específico para ícone
  const handleResetIcon = () => {
    const defaultConfig = presetToBlockConfig(textBlockPresets.default);
    onChange({
      ...config,
      icon: defaultConfig.icon
    });
    setFeedback('Configurações do ícone restauradas');
  };

  // Reset geral
  const handleResetAll = () => {
    onReset();
    setFeedback('Todas as configurações restauradas');
  };

  return (
    <div className="w-full max-w-3xl mx-auto p-4 md:p-8">
      <div className="flex items-center gap-3 mb-2">
        <span className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-700 text-xl">
          <FileText className="h-5 w-5" />
        </span>
        <div>
          <div className="font-bold text-lg leading-tight">Configuração Visual - Bloco de Texto</div>
          <div className="text-xs text-gray-500">Personalize a aparência do bloco de texto para destacar seu conteúdo.</div>
        </div>
      </div>
      
      {preview && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
          <div className="text-sm font-medium text-gray-700 mb-2">Pré-visualização:</div>
          {preview}
        </div>
      )}
      
      {feedback && (
        <div className="mb-4 p-2 bg-green-100 text-green-800 rounded text-sm">
          {feedback}
        </div>
      )}

      <Tabs defaultValue="appearance">
        <div className="flex items-center justify-between mb-4">
          <TabsList>
            <TabsTrigger value="appearance">Aparência</TabsTrigger>
            <TabsTrigger value="icon">Ícone</TabsTrigger>
            <TabsTrigger value="presets">Presets</TabsTrigger>
            {/* Removida tab "Botão" - não relevante para texto */}
          </TabsList>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetAll}
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  Restaurar Padrão
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Restaura todas as configurações (Aparência e Ícone)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        <TabsContent value="appearance">
          <CardAppearanceConfig
            config={config}
            onChange={onChange}
            onReset={handleResetCard}
            setFeedback={setFeedback}
            blockType="text"
          />
        </TabsContent>
        
        <TabsContent value="icon">
          <IconAppearanceConfig 
            config={config}
            onChange={onChange}
            onReset={handleResetIcon}
            setFeedback={setFeedback}
            blockType="text"
          />
        </TabsContent>
        
        <TabsContent value="presets">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Presets para Blocos de Texto</h3>
              <p className="text-sm text-gray-600 mb-4">
                Escolha um preset otimizado para blocos de texto
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(textBlockPresets).map(([key, preset]) => (
                <div
                  key={key}
                  className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => {
                    onChange(presetToBlockConfig(preset));
                    setFeedback(`Preset "${key}" aplicado`);
                  }}
                >
                  <div className="font-medium capitalize mb-2">{key}</div>
                  <div className="text-sm text-gray-600">
                    {key === 'default' && 'Estilo padrão para blocos de texto'}
                    {key === 'minimal' && 'Estilo minimalista e limpo'}
                    {key === 'highlight' && 'Estilo destacado para conteúdo importante'}
                    {key === 'clean' && 'Estilo completamente limpo'}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

-- =====================
-- CRIAR PROJETO DE TESTE
-- Execute este script para criar dados de teste
-- =====================

-- VERIFICAR SE PROJETO EXISTE
SELECT 'VERIFICANDO PROJETO ATUAL:' as status;
SELECT 
    id,
    name,
    status,
    owner_id,
    created_at
FROM public.projects 
WHERE id = '17b4065f-7179-4c95-868d-4fa168c368f9';

-- LISTAR PROJETOS EXISTENTES
SELECT 'PROJETOS EXISTENTES:' as status;
SELECT 
    id,
    name,
    status,
    owner_id,
    created_at
FROM public.projects 
ORDER BY created_at DESC
LIMIT 10;

-- VERIFICAR USUÁRIO ATUAL
SELECT 'USUÁRIO ATUAL:' as status;
SELECT 
    id,
    email,
    name,
    role
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- CRIAR PROJETO DE TESTE SE NÃO EXISTIR
INSERT INTO public.projects (
    id,
    name,
    description,
    status,
    owner_id,
    start_date,
    end_date,
    created_at,
    updated_at
) 
SELECT 
    '17b4065f-7179-4c95-868d-4fa168c368f9',
    'Projeto de Teste - Sistema de Gestão',
    'Projeto criado automaticamente para testes do sistema',
    'active',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    current_date,
    current_date + interval '30 days',
    now(),
    now()
WHERE NOT EXISTS (
    SELECT 1 FROM public.projects 
    WHERE id = '17b4065f-7179-4c95-868d-4fa168c368f9'
);

-- CRIAR ETAPA DE TESTE
INSERT INTO public.stages (
    id,
    project_id,
    name,
    description,
    status,
    progress,
    start_date,
    end_date,
    created_at,
    updated_at
) 
SELECT 
    gen_random_uuid(),
    '17b4065f-7179-4c95-868d-4fa168c368f9',
    'Etapa 1 - Planejamento',
    'Etapa inicial de planejamento do projeto',
    'in-progress',
    25,
    current_date,
    current_date + interval '10 days',
    now(),
    now()
WHERE EXISTS (
    SELECT 1 FROM public.projects 
    WHERE id = '17b4065f-7179-4c95-868d-4fa168c368f9'
)
AND NOT EXISTS (
    SELECT 1 FROM public.stages 
    WHERE project_id = '17b4065f-7179-4c95-868d-4fa168c368f9'
);

-- CRIAR TAREFA DE TESTE
INSERT INTO public.tasks (
    id,
    stage_id,
    title,
    description,
    status,
    priority,
    assigned_to,
    created_by,
    estimated_hours,
    due_date,
    created_at,
    updated_at
) 
SELECT 
    gen_random_uuid(),
    s.id,
    'Tarefa 1 - Definir Requisitos',
    'Definir os requisitos funcionais e não funcionais do sistema',
    'in-progress',
    'high',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    8,
    current_date + interval '5 days',
    now(),
    now()
FROM public.stages s
WHERE s.project_id = '17b4065f-7179-4c95-868d-4fa168c368f9'
AND NOT EXISTS (
    SELECT 1 FROM public.tasks t
    WHERE t.stage_id = s.id
)
LIMIT 1;

-- ADICIONAR USUÁRIO COMO EXECUTOR DA TAREFA
INSERT INTO public.task_executors (
    task_id,
    user_id,
    assigned_at
)
SELECT 
    t.id,
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    now()
FROM public.tasks t
JOIN public.stages s ON t.stage_id = s.id
WHERE s.project_id = '17b4065f-7179-4c95-868d-4fa168c368f9'
AND NOT EXISTS (
    SELECT 1 FROM public.task_executors te
    WHERE te.task_id = t.id AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4'
)
LIMIT 1;

-- VERIFICAR RESULTADO FINAL
SELECT 'RESULTADO FINAL:' as status;

-- Projeto criado
SELECT 
    'PROJETO:' as type,
    id,
    name,
    status
FROM public.projects 
WHERE id = '17b4065f-7179-4c95-868d-4fa168c368f9';

-- Etapas criadas
SELECT 
    'ETAPAS:' as type,
    id,
    name,
    status
FROM public.stages 
WHERE project_id = '17b4065f-7179-4c95-868d-4fa168c368f9';

-- Tarefas criadas
SELECT 
    'TAREFAS:' as type,
    t.id,
    t.title,
    t.status
FROM public.tasks t
JOIN public.stages s ON t.stage_id = s.id
WHERE s.project_id = '17b4065f-7179-4c95-868d-4fa168c368f9';

-- Executores atribuídos
SELECT 
    'EXECUTORES:' as type,
    te.task_id,
    te.user_id,
    p.name as executor_name
FROM public.task_executors te
JOIN public.tasks t ON te.task_id = t.id
JOIN public.stages s ON t.stage_id = s.id
JOIN public.profiles p ON te.user_id = p.id
WHERE s.project_id = '17b4065f-7179-4c95-868d-4fa168c368f9';

SELECT '🎉 PROJETO DE TESTE CRIADO COM SUCESSO!' as resultado;
SELECT 'Agora recarregue a página e teste o ProjectDetails' as instrucao;

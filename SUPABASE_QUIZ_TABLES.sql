-- Script SQL para criar tabelas do Quiz no Supabase
-- Execute este script no SQL Editor do Supabase

-- 1. <PERSON><PERSON><PERSON> de Quizzes
CREATE TABLE IF NOT EXISTS quizzes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    task_id TEXT NOT NULL,
    block_id TEXT NOT NULL,
    content JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Índices para performance
    UNIQUE(task_id, block_id)
);

-- 2. Tabela de Tentativas de Quiz
CREATE TABLE IF NOT EXISTS quiz_attempts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quiz_id UUID REFERENCES quizzes(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    submitted_at TIMESTAMP WITH TIME ZONE,
    score INTEGER DEFAULT 0,
    max_score INTEGER DEFAULT 0,
    percentage DECIMAL(5,2) DEFAULT 0,
    passed BOOLEAN DEFAULT false,
    status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),
    time_spent INTEGER DEFAULT 0, -- em segundos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Tabela de Respostas
CREATE TABLE IF NOT EXISTS quiz_answers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    attempt_id UUID REFERENCES quiz_attempts(id) ON DELETE CASCADE,
    question_id TEXT NOT NULL,
    question_type TEXT NOT NULL,
    selected_options TEXT[], -- para multiple choice e single choice
    text_answer TEXT, -- para open text
    boolean_answer BOOLEAN, -- para true/false
    ordering_answer JSONB, -- para ordering questions
    matching_answer JSONB, -- para matching questions
    is_correct BOOLEAN DEFAULT false,
    points_earned INTEGER DEFAULT 0,
    time_spent INTEGER DEFAULT 0, -- em segundos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Tabela de Progresso do Usuário
CREATE TABLE IF NOT EXISTS user_quiz_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quiz_id UUID REFERENCES quizzes(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    total_attempts INTEGER DEFAULT 0,
    best_score INTEGER DEFAULT 0,
    best_percentage DECIMAL(5,2) DEFAULT 0,
    passed BOOLEAN DEFAULT false,
    first_attempt_at TIMESTAMP WITH TIME ZONE,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Único por quiz e usuário
    UNIQUE(quiz_id, user_id)
);

-- 5. Índices para performance
CREATE INDEX IF NOT EXISTS idx_quizzes_task_block ON quizzes(task_id, block_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_quiz_user ON quiz_attempts(quiz_id, user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_user ON quiz_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_attempt ON quiz_answers(attempt_id);
CREATE INDEX IF NOT EXISTS idx_user_quiz_progress_user ON user_quiz_progress(user_id);

-- 6. Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_quizzes_updated_at BEFORE UPDATE ON quizzes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quiz_attempts_updated_at BEFORE UPDATE ON quiz_attempts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quiz_answers_updated_at BEFORE UPDATE ON quiz_answers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_quiz_progress_updated_at BEFORE UPDATE ON user_quiz_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. RLS (Row Level Security) - Opcional
-- Descomente se quiser ativar RLS

-- ALTER TABLE quizzes ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE quiz_attempts ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE quiz_answers ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE user_quiz_progress ENABLE ROW LEVEL SECURITY;

-- Políticas de exemplo (ajuste conforme necessário)
-- CREATE POLICY "Users can view quizzes" ON quizzes FOR SELECT USING (true);
-- CREATE POLICY "Users can insert quiz attempts" ON quiz_attempts FOR INSERT WITH CHECK (auth.uid() = user_id);
-- CREATE POLICY "Users can view their attempts" ON quiz_attempts FOR SELECT USING (auth.uid() = user_id);
-- CREATE POLICY "Users can insert their answers" ON quiz_answers FOR INSERT WITH CHECK (
--     EXISTS (SELECT 1 FROM quiz_attempts WHERE id = attempt_id AND user_id = auth.uid())
-- );
-- CREATE POLICY "Users can view their answers" ON quiz_answers FOR SELECT USING (
--     EXISTS (SELECT 1 FROM quiz_attempts WHERE id = attempt_id AND user_id = auth.uid())
-- );
-- CREATE POLICY "Users can view their progress" ON user_quiz_progress FOR SELECT USING (auth.uid() = user_id);
-- CREATE POLICY "Users can update their progress" ON user_quiz_progress FOR ALL USING (auth.uid() = user_id);

-- 8. Comentários para documentação
COMMENT ON TABLE quizzes IS 'Armazena a configuração e conteúdo dos quizzes';
COMMENT ON TABLE quiz_attempts IS 'Armazena as tentativas de execução dos quizzes pelos usuários';
COMMENT ON TABLE quiz_answers IS 'Armazena as respostas individuais de cada tentativa';
COMMENT ON TABLE user_quiz_progress IS 'Armazena o progresso geral do usuário em cada quiz';

COMMENT ON COLUMN quizzes.content IS 'Estrutura JSON completa do quiz (perguntas, configurações, etc.)';
COMMENT ON COLUMN quiz_attempts.time_spent IS 'Tempo total gasto na tentativa em segundos';
COMMENT ON COLUMN quiz_answers.selected_options IS 'Array de IDs das opções selecionadas para perguntas de múltipla escolha';
COMMENT ON COLUMN quiz_answers.ordering_answer IS 'JSON com a ordem das opções para perguntas de ordenação';
COMMENT ON COLUMN quiz_answers.matching_answer IS 'JSON com os pares correspondentes para perguntas de correspondência';

-- Verificar se as tabelas foram criadas
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename IN ('quizzes', 'quiz_attempts', 'quiz_answers', 'user_quiz_progress')
ORDER BY tablename;

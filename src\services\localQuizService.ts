import {
  QuizContent,
  Quiz<PERSON>ttempt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  UserQuizProgress,
  QuizUtils
} from '@/types/quiz';

// Serviço local para Quiz quando Supabase não está disponível
export class LocalQuizService {
  private static readonly STORAGE_PREFIX = 'quiz_local_';

  // Salvar dados no localStorage
  private static saveToStorage(key: string, data: any): void {
    try {
      localStorage.setItem(`${this.STORAGE_PREFIX}${key}`, JSON.stringify(data));
    } catch (error) {
      console.warn('Erro ao salvar no localStorage:', error);
    }
  }

  // Carregar dados do localStorage
  private static loadFromStorage<T>(key: string): T | null {
    try {
      const data = localStorage.getItem(`${this.STORAGE_PREFIX}${key}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('Erro ao carregar do localStorage:', error);
      return null;
    }
  }

  // Gerar ID único
  private static generateId(): string {
    return `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Salvar quiz localmente
  static saveQuiz(taskId: string, blockId: string, content: QuizContent): void {
    const key = `quiz_${taskId}_${blockId}`;
    this.saveToStorage(key, {
      taskId,
      blockId,
      content,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }

  // Carregar quiz localmente
  static loadQuiz(taskId: string, blockId: string): QuizContent | null {
    const key = `quiz_${taskId}_${blockId}`;
    const data = this.loadFromStorage<any>(key);
    return data?.content || null;
  }

  // Iniciar tentativa local
  static startAttempt(taskId: string, blockId: string, userId: string, quizContent: QuizContent): QuizAttempt {
    const attemptId = this.generateId();
    const maxScore = quizContent.questions.reduce((sum, q) => sum + q.points, 0);
    
    // Carregar tentativas anteriores
    const progressKey = `progress_${taskId}_${blockId}_${userId}`;
    const existingProgress = this.loadFromStorage<any>(progressKey) || { totalAttempts: 0 };
    
    const attempt: QuizAttempt = {
      id: attemptId,
      userId,
      quizId: `${taskId}:${blockId}`,
      attemptNumber: existingProgress.totalAttempts + 1,
      startedAt: new Date(),
      timeSpent: 0,
      score: 0,
      maxScore,
      percentage: 0,
      passed: false,
      answers: [],
      status: 'draft'
    };

    // Salvar tentativa
    const attemptKey = `attempt_${attemptId}`;
    this.saveToStorage(attemptKey, attempt);

    return attempt;
  }

  // Salvar resposta local
  static saveAnswer(attemptId: string, answer: QuizAnswer): void {
    const answerKey = `answer_${attemptId}_${answer.questionId}`;
    this.saveToStorage(answerKey, {
      ...answer,
      savedAt: new Date().toISOString()
    });
  }

  // Carregar respostas de uma tentativa
  static loadAnswers(attemptId: string): QuizAnswer[] {
    const answers: QuizAnswer[] = [];
    
    // Buscar todas as chaves de resposta para esta tentativa
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(`${this.STORAGE_PREFIX}answer_${attemptId}_`)) {
        const answer = this.loadFromStorage<QuizAnswer>(key.replace(this.STORAGE_PREFIX, ''));
        if (answer) {
          answers.push(answer);
        }
      }
    }
    
    return answers;
  }

  // Finalizar tentativa local
  static submitAttempt(attemptId: string, answers: QuizAnswer[], timeSpent: number, quizContent?: QuizContent): QuizAttempt {
    // Carregar tentativa
    const attemptKey = `attempt_${attemptId}`;
    const attempt = this.loadFromStorage<QuizAttempt>(attemptKey);

    if (!attempt) {
      throw new Error('Tentativa não encontrada');
    }

    // Calcular pontuação
    const totalScore = answers.reduce((sum, answer) => sum + answer.pointsEarned, 0);
    const percentage = attempt.maxScore > 0 ? (totalScore / attempt.maxScore) * 100 : 0;

    // Lógica de aprovação baseada no modo do quiz
    let passed = false;
    if (quizContent && QuizUtils.isSurveyMode(quizContent.config)) {
      // Pesquisas sempre são "aprovadas" (participação completa)
      passed = true;
    } else {
      // Avaliações: usar nota mínima configurada ou padrão de 70%
      const passingScore = quizContent?.config.passingScore || 70;
      passed = percentage >= passingScore;
    }

    const finalAttempt: QuizAttempt = {
      ...attempt,
      submittedAt: new Date(),
      answers,
      score: totalScore,
      percentage,
      passed,
      status: 'graded',
      timeSpent
    };

    // Salvar tentativa finalizada
    this.saveToStorage(attemptKey, finalAttempt);

    // Atualizar progresso do usuário
    this.updateUserProgress(attempt.quizId, attempt.userId, totalScore, percentage, passed);

    return finalAttempt;
  }

  // Atualizar progresso do usuário
  private static updateUserProgress(
    quizId: string, 
    userId: string, 
    score: number, 
    percentage: number, 
    passed: boolean
  ): void {
    const [taskId, blockId] = quizId.split(':');
    const progressKey = `progress_${taskId}_${blockId}_${userId}`;
    const existingProgress = this.loadFromStorage<any>(progressKey) || {
      totalAttempts: 0,
      bestScore: 0,
      bestPercentage: 0,
      passed: false,
      firstAttemptAt: null,
      lastAttemptAt: null
    };

    const now = new Date().toISOString();
    const updatedProgress = {
      userId,
      quizId,
      totalAttempts: existingProgress.totalAttempts + 1,
      bestScore: Math.max(existingProgress.bestScore, score),
      bestPercentage: Math.max(existingProgress.bestPercentage, percentage),
      passed: existingProgress.passed || passed,
      firstAttemptAt: existingProgress.firstAttemptAt || now,
      lastAttemptAt: now
    };

    this.saveToStorage(progressKey, updatedProgress);
  }

  // Carregar progresso do usuário
  static getUserProgress(taskId: string, blockId: string, userId: string): UserQuizProgress | null {
    const progressKey = `progress_${taskId}_${blockId}_${userId}`;
    const progress = this.loadFromStorage<any>(progressKey);
    
    if (!progress) {
      return null;
    }

    // Carregar tentativas do usuário
    const attempts: QuizAttempt[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(`${this.STORAGE_PREFIX}attempt_`) && key.includes(userId)) {
        const attempt = this.loadFromStorage<QuizAttempt>(key.replace(this.STORAGE_PREFIX, ''));
        if (attempt && attempt.quizId === `${taskId}:${blockId}`) {
          // Carregar respostas da tentativa
          attempt.answers = this.loadAnswers(attempt.id);
          attempts.push(attempt);
        }
      }
    }

    return {
      userId: progress.userId,
      quizId: progress.quizId,
      attempts: attempts.sort((a, b) => a.attemptNumber - b.attemptNumber),
      bestScore: progress.bestScore,
      bestPercentage: progress.bestPercentage,
      totalAttempts: progress.totalAttempts,
      passed: progress.passed,
      lastAttemptAt: new Date(progress.lastAttemptAt)
    };
  }

  // Obter todas as tentativas do usuário para um quiz específico
  static getUserAttempts(taskId: string, blockId: string, userId: string): QuizAttempt[] {
    const attempts: QuizAttempt[] = [];
    const quizId = `${taskId}:${blockId}`;

    console.log('🔍 LocalQuizService.getUserAttempts - Buscando tentativas:', {
      taskId,
      blockId,
      userId,
      quizId,
      localStorageLength: localStorage.length
    });

    // Buscar todas as tentativas no localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(`${this.STORAGE_PREFIX}attempt_`)) {
        const attempt = this.loadFromStorage<QuizAttempt>(key.replace(this.STORAGE_PREFIX, ''));
        console.log(`🔍 Tentativa encontrada na chave ${key}:`, {
          attemptId: attempt?.id,
          attemptQuizId: attempt?.quizId,
          attemptUserId: attempt?.userId,
          attemptNumber: attempt?.attemptNumber,
          percentage: attempt?.percentage,
          matches: attempt && attempt.quizId === quizId && attempt.userId === userId
        });

        if (attempt && attempt.quizId === quizId && attempt.userId === userId) {
          // Carregar respostas da tentativa
          attempt.answers = this.loadAnswers(attempt.id);
          attempts.push(attempt);
        }
      }
    }

    console.log('📊 LocalQuizService.getUserAttempts - Resultado:', {
      totalFound: attempts.length,
      attempts: attempts.map(a => ({
        id: a.id,
        attemptNumber: a.attemptNumber,
        percentage: a.percentage,
        passed: a.passed
      }))
    });

    // Ordenar por número da tentativa
    return attempts.sort((a, b) => a.attemptNumber - b.attemptNumber);
  }

  // Verificar se usuário pode fazer nova tentativa
  static canUserAttempt(taskId: string, blockId: string, userId: string, maxAttempts: number = -1): {
    canAttempt: boolean;
    reason?: string;
    remainingAttempts?: number;
  } {
    const progress = this.getUserProgress(taskId, blockId, userId);
    
    // Se não há progresso, pode tentar
    if (!progress) {
      return { 
        canAttempt: true, 
        remainingAttempts: maxAttempts === -1 ? Infinity : maxAttempts 
      };
    }

    // Verificar número máximo de tentativas
    if (maxAttempts !== -1 && progress.totalAttempts >= maxAttempts) {
      return { canAttempt: false, reason: 'Número máximo de tentativas excedido' };
    }

    const remainingAttempts = maxAttempts === -1 
      ? Infinity 
      : maxAttempts - progress.totalAttempts;

    return { canAttempt: true, remainingAttempts };
  }

  // Limpar dados locais (para debug)
  static clearLocalData(): void {
    const keysToRemove: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(this.STORAGE_PREFIX)) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log(`Removidos ${keysToRemove.length} itens do localStorage do Quiz`);
  }

  // Obter estatísticas básicas
  static getBasicStats(taskId: string, blockId: string): any {
    const stats = {
      totalAttempts: 0,
      uniqueUsers: new Set<string>(),
      totalScore: 0,
      passedAttempts: 0
    };

    // Buscar todas as tentativas para este quiz
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(`${this.STORAGE_PREFIX}attempt_`)) {
        const attempt = this.loadFromStorage<QuizAttempt>(key.replace(this.STORAGE_PREFIX, ''));
        if (attempt && attempt.quizId === `${taskId}:${blockId}` && attempt.status === 'graded') {
          stats.totalAttempts++;
          stats.uniqueUsers.add(attempt.userId);
          stats.totalScore += attempt.score;
          if (attempt.passed) {
            stats.passedAttempts++;
          }
        }
      }
    }

    return {
      totalAttempts: stats.totalAttempts,
      uniqueUsers: stats.uniqueUsers.size,
      averageScore: stats.totalAttempts > 0 ? stats.totalScore / stats.totalAttempts : 0,
      passRate: stats.totalAttempts > 0 ? (stats.passedAttempts / stats.totalAttempts) * 100 : 0
    };
  }
}

-- =====================================================
-- TESTE DE POLÍTICAS RLS PARA UPLOAD DE AVATAR
-- =====================================================

-- 1. Verificar a política atual para avatars
SELECT 
    'POLÍTICA AVATARS INSERT' as info,
    policyname,
    with_check
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects' 
AND policyname = 'avatars_insert';

-- 2. Testar padrões de nomes de arquivo
SELECT 
    'TESTE PADRÕES' as info,
    '50580ad5-7c45-4d7f-9446-69f069205ad0/avatar.png' ~ '^[0-9a-f-]+/avatar\.(jpg|jpeg|png|webp)$' as formato_correto,
    '50580ad5-7c45-4d7f-9446-69f069205ad0-1753193128115.png' ~ '^[0-9a-f-]+/avatar\.(jpg|jpeg|png|webp)$' as formato_antigo_incorreto,
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4/avatar.jpg' ~ '^[0-9a-f-]+/avatar\.(jpg|jpeg|png|webp)$' as formato_jpg_correto,
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4/avatar.webp' ~ '^[0-9a-f-]+/avatar\.(jpg|jpeg|png|webp)$' as formato_webp_correto;

-- 3. Verificar usuários existentes
SELECT 
    'USUÁRIOS PARA TESTE' as info,
    id,
    name,
    email,
    avatar_url
FROM profiles 
WHERE is_active = true
ORDER BY name;

-- 4. Simular check da política (se estivéssemos logados)
SELECT 
    'SIMULAÇÃO POLÍTICA' as info,
    'A política verifica:' as check1,
    'bucket_id = avatars' as check2,
    'owner = auth.uid()' as check3,
    'nome ~ ^[0-9a-f-]+/avatar\.(jpg|jpeg|png|webp)$' as check4;

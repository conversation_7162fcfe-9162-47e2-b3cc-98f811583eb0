-- =====================================================
-- SCRIPT FINAL CONSOLIDADO: RESOLVER ACESSO IMEDIATO
-- Execute este script no Supabase SQL Editor
-- =====================================================

-- STEP 1: CONFIRMAR PROFILE EXISTE
SELECT 
    '🔍 VERIFICANDO PROFILE' as etapa,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ Profile existe'
        ELSE '❌ Profile não encontrado'
    END as status,
    MAX(p.email) as email
FROM profiles p
WHERE p.id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 2: VERIFICAR STATUS ATUAL COMO EXECUTOR
SELECT 
    '🔍 VERIFICANDO EXECUTOR' as etapa,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ Já é executor'
        ELSE '❌ Não é executor'
    END as status,
    COUNT(*) as total_executors
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 3: ADICIONAR COMO EXECUTOR (SE NECESSÁRIO)
INSERT INTO task_executors (task_id, user_id, created_at)
VALUES (
    '7c606667-9391-4660-933d-90d6bd276e88',
    '4b09be1f-5187-44c0-9b53-87b7c57e45b4',
    NOW()
)
ON CONFLICT (task_id, user_id) DO NOTHING;

-- STEP 4: CONFIRMAR ADIÇÃO
SELECT 
    '✅ RESULTADO FINAL' as etapa,
    'Usuário adicionado como executor' as status,
    te.task_id,
    p.name as nome_usuario,
    p.email as email_usuario,
    te.created_at as adicionado_em
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- STEP 5: TESTAR ACESSO AOS CONTENT BLOCKS
SELECT 
    '🧪 TESTE FINAL' as etapa,
    COUNT(*) as blocos_acessiveis,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ Acesso liberado aos content blocks'
        ELSE '⚠️ Nenhum content block encontrado (pode ser normal se não há blocos)'
    END as resultado
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- =====================================================
-- INSTRUÇÕES:
-- 1. Copie todo este script
-- 2. Cole no Supabase SQL Editor 
-- 3. Execute (Run)
-- 4. Recarregue a página da aplicação (F5)
-- 5. Vá na tarefa e clique na aba "Executar"
-- =====================================================

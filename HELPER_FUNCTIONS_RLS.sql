-- =====================================================
-- HELPER FUNCTIONS PARA RLS SEM RECURSÃO
-- =====================================================
-- Cria funções auxiliares que evitam recursão infinita
-- Execute ANTES de implementar as políticas RLS
-- Versão: 2.0 - Julho 2025

-- =====================================================
-- FUNÇÃO 1: OBTER ID DO USUÁRIO AUTENTICADO
-- =====================================================

CREATE OR REPLACE FUNCTION auth_user_id()
RETURNS UUID 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT auth.uid();
$$;

COMMENT ON FUNCTION auth_user_id() IS 'Retorna o ID do usuário autenticado de forma otimizada';

-- =====================================================
-- FUNÇÃO 2: VERIFICAR SE USUÁRIO É ADMIN
-- =====================================================

CREATE OR REPLACE FUNCTION is_admin_user(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id 
    AND role = 'admin'
    AND is_active = true
  );
$$;

COMMENT ON FUNCTION is_admin_user(UUID) IS 'Verifica se o usuário tem role de admin (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 3: VERIFICAR SE USUÁRIO É MANAGER
-- =====================================================

CREATE OR REPLACE FUNCTION is_manager_user(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id 
    AND role IN ('admin', 'manager')
    AND is_active = true
  );
$$;

COMMENT ON FUNCTION is_manager_user(UUID) IS 'Verifica se o usuário é admin ou manager (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 4: OBTER IDs DOS PROJETOS DO USUÁRIO
-- =====================================================

CREATE OR REPLACE FUNCTION user_project_ids(user_id UUID DEFAULT auth.uid())
RETURNS UUID[] 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT ARRAY(
    SELECT DISTINCT project_id 
    FROM project_members 
    WHERE user_id = user_project_ids.user_id
    
    UNION
    
    SELECT DISTINCT id 
    FROM projects 
    WHERE created_by = user_project_ids.user_id
  );
$$;

COMMENT ON FUNCTION user_project_ids(UUID) IS 'Retorna array de IDs dos projetos onde o usuário é membro ou owner (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 5: VERIFICAR SE USUÁRIO É MEMBRO DO PROJETO
-- =====================================================

CREATE OR REPLACE FUNCTION is_project_member(user_id UUID, project_id UUID)
RETURNS BOOLEAN 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM project_members 
    WHERE user_id = is_project_member.user_id 
    AND project_id = is_project_member.project_id
  )
  OR EXISTS (
    SELECT 1 FROM projects 
    WHERE id = is_project_member.project_id 
    AND created_by = is_project_member.user_id
  );
$$;

COMMENT ON FUNCTION is_project_member(UUID, UUID) IS 'Verifica se usuário é membro ou owner do projeto (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 6: OBTER IDs DAS TAREFAS ATRIBUÍDAS AO USUÁRIO
-- =====================================================

CREATE OR REPLACE FUNCTION user_assigned_task_ids(user_id UUID DEFAULT auth.uid())
RETURNS UUID[] 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT ARRAY(
    SELECT DISTINCT task_id 
    FROM task_executors 
    WHERE user_id = user_assigned_task_ids.user_id
    
    UNION
    
    SELECT DISTINCT task_id 
    FROM task_approvers 
    WHERE user_id = user_assigned_task_ids.user_id
  );
$$;

COMMENT ON FUNCTION user_assigned_task_ids(UUID) IS 'Retorna array de IDs das tarefas onde o usuário é executor ou aprovador (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 7: VERIFICAR SE USUÁRIO É EXECUTOR/APROVADOR DA TAREFA
-- =====================================================

CREATE OR REPLACE FUNCTION is_task_participant(user_id UUID, task_id UUID)
RETURNS BOOLEAN 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM task_executors 
    WHERE user_id = is_task_participant.user_id 
    AND task_id = is_task_participant.task_id
  )
  OR EXISTS (
    SELECT 1 FROM task_approvers 
    WHERE user_id = is_task_participant.user_id 
    AND task_id = is_task_participant.task_id
  );
$$;

COMMENT ON FUNCTION is_task_participant(UUID, UUID) IS 'Verifica se usuário é executor ou aprovador da tarefa (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 8: OBTER IDs DAS ETAPAS SOB RESPONSABILIDADE DO USUÁRIO
-- =====================================================

CREATE OR REPLACE FUNCTION user_responsible_stage_ids(user_id UUID DEFAULT auth.uid())
RETURNS UUID[] 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT ARRAY(
    SELECT DISTINCT stage_id 
    FROM stage_responsibles 
    WHERE user_id = user_responsible_stage_ids.user_id
  );
$$;

COMMENT ON FUNCTION user_responsible_stage_ids(UUID) IS 'Retorna array de IDs das etapas sob responsabilidade do usuário (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 9: VERIFICAR SE USUÁRIO PODE ACESSAR PROJETO VIA HIERARQUIA
-- =====================================================

CREATE OR REPLACE FUNCTION can_access_project_hierarchy(user_id UUID, project_id UUID)
RETURNS BOOLEAN 
LANGUAGE sql 
STABLE 
SECURITY DEFINER
AS $$
  SELECT 
    -- É admin
    is_admin_user(user_id)
    OR
    -- É membro do projeto
    is_project_member(user_id, project_id)
    OR
    -- É responsável por alguma etapa do projeto
    EXISTS (
      SELECT 1 FROM stage_responsibles sr
      JOIN stages s ON s.id = sr.stage_id
      WHERE sr.user_id = can_access_project_hierarchy.user_id
      AND s.project_id = can_access_project_hierarchy.project_id
    );
$$;

COMMENT ON FUNCTION can_access_project_hierarchy(UUID, UUID) IS 'Verifica acesso ao projeto considerando toda a hierarquia (sem recursão RLS)';

-- =====================================================
-- FUNÇÃO 10: FUNÇÃO DE CACHE PARA PERFORMANCE
-- =====================================================

CREATE OR REPLACE FUNCTION get_user_permissions_cache(user_id UUID DEFAULT auth.uid())
RETURNS JSONB 
LANGUAGE plpgsql 
STABLE 
SECURITY DEFINER
AS $$
DECLARE
  permissions JSONB;
BEGIN
  SELECT jsonb_build_object(
    'user_id', user_id,
    'is_admin', is_admin_user(user_id),
    'is_manager', is_manager_user(user_id),
    'project_ids', user_project_ids(user_id),
    'assigned_task_ids', user_assigned_task_ids(user_id),
    'responsible_stage_ids', user_responsible_stage_ids(user_id),
    'cached_at', EXTRACT(EPOCH FROM NOW())
  ) INTO permissions;
  
  RETURN permissions;
END;
$$;

COMMENT ON FUNCTION get_user_permissions_cache(UUID) IS 'Cache de permissões do usuário para otimização (sem recursão RLS)';

-- =====================================================
-- ÍNDICES ESPECÍFICOS PARA PERFORMANCE RLS
-- =====================================================

-- Índices para profiles
CREATE INDEX IF NOT EXISTS idx_profiles_role_active 
ON profiles(role) 
WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_profiles_id_role 
ON profiles(id, role) 
WHERE is_active = true;

-- Índices para project_members
CREATE INDEX IF NOT EXISTS idx_project_members_user_project 
ON project_members(user_id, project_id);

CREATE INDEX IF NOT EXISTS idx_project_members_project_user 
ON project_members(project_id, user_id);

-- Índices para projects
CREATE INDEX IF NOT EXISTS idx_projects_created_by 
ON projects(created_by);

CREATE INDEX IF NOT EXISTS idx_projects_id_created_by 
ON projects(id, created_by);

-- Índices para stages
CREATE INDEX IF NOT EXISTS idx_stages_project_id 
ON stages(project_id);

-- Índices para stage_responsibles
CREATE INDEX IF NOT EXISTS idx_stage_responsibles_user_stage 
ON stage_responsibles(user_id, stage_id);

CREATE INDEX IF NOT EXISTS idx_stage_responsibles_stage_user 
ON stage_responsibles(stage_id, user_id);

-- Índices para tasks
CREATE INDEX IF NOT EXISTS idx_tasks_stage_id 
ON tasks(stage_id);

CREATE INDEX IF NOT EXISTS idx_tasks_created_by 
ON tasks(created_by);

-- Índices para task_executors
CREATE INDEX IF NOT EXISTS idx_task_executors_user_task 
ON task_executors(user_id, task_id);

CREATE INDEX IF NOT EXISTS idx_task_executors_task_user 
ON task_executors(task_id, user_id);

-- Índices para task_approvers
CREATE INDEX IF NOT EXISTS idx_task_approvers_user_task 
ON task_approvers(user_id, task_id);

CREATE INDEX IF NOT EXISTS idx_task_approvers_task_user 
ON task_approvers(task_id, user_id);

-- =====================================================
-- VIEWS OTIMIZADAS (OPCIONAL)
-- =====================================================

-- View para projetos acessíveis pelo usuário
CREATE OR REPLACE VIEW user_accessible_projects AS
SELECT p.*, 
       pm.role as member_role,
       pm.joined_at as member_since
FROM projects p
LEFT JOIN project_members pm ON pm.project_id = p.id AND pm.user_id = auth.uid()
WHERE 
  p.created_by = auth.uid()  -- Projetos próprios
  OR pm.user_id IS NOT NULL -- Projetos onde é membro
  OR is_admin_user();        -- Admin vê todos

COMMENT ON VIEW user_accessible_projects IS 'Projetos acessíveis ao usuário atual (otimizada para RLS)';

-- View para tarefas acessíveis pelo usuário
CREATE OR REPLACE VIEW user_accessible_tasks AS
SELECT t.*,
       CASE 
         WHEN te.user_id IS NOT NULL THEN 'executor'
         WHEN ta.user_id IS NOT NULL THEN 'approver'
         WHEN pm.user_id IS NOT NULL THEN 'project_member'
         WHEN p.created_by = auth.uid() THEN 'project_owner'
         ELSE 'admin'
       END as access_type
FROM tasks t
JOIN stages s ON s.id = t.stage_id
JOIN projects p ON p.id = s.project_id
LEFT JOIN project_members pm ON pm.project_id = p.id AND pm.user_id = auth.uid()
LEFT JOIN task_executors te ON te.task_id = t.id AND te.user_id = auth.uid()
LEFT JOIN task_approvers ta ON ta.task_id = t.id AND ta.user_id = auth.uid()
WHERE 
  p.created_by = auth.uid()     -- Projetos próprios
  OR pm.user_id IS NOT NULL    -- Projetos onde é membro
  OR te.user_id IS NOT NULL    -- Tarefas onde é executor
  OR ta.user_id IS NOT NULL    -- Tarefas onde é aprovador
  OR is_admin_user();          -- Admin vê todas

COMMENT ON VIEW user_accessible_tasks IS 'Tarefas acessíveis ao usuário atual com tipo de acesso (otimizada para RLS)';

-- =====================================================
-- CONFIGURAÇÕES DE PERFORMANCE
-- =====================================================

-- Otimizações para as funções
ALTER FUNCTION auth_user_id() SET search_path = public;
ALTER FUNCTION is_admin_user(UUID) SET search_path = public;
ALTER FUNCTION is_manager_user(UUID) SET search_path = public;
ALTER FUNCTION user_project_ids(UUID) SET search_path = public;
ALTER FUNCTION is_project_member(UUID, UUID) SET search_path = public;
ALTER FUNCTION user_assigned_task_ids(UUID) SET search_path = public;
ALTER FUNCTION is_task_participant(UUID, UUID) SET search_path = public;
ALTER FUNCTION user_responsible_stage_ids(UUID) SET search_path = public;
ALTER FUNCTION can_access_project_hierarchy(UUID, UUID) SET search_path = public;
ALTER FUNCTION get_user_permissions_cache(UUID) SET search_path = public;

-- =====================================================
-- TESTES DAS FUNÇÕES
-- =====================================================

DO $$
DECLARE
    test_user_id UUID;
    test_result BOOLEAN;
    test_array UUID[];
    test_permissions JSONB;
BEGIN
    RAISE NOTICE '🧪 ===== TESTANDO HELPER FUNCTIONS =====';
    
    -- Obter usuário para teste
    SELECT id INTO test_user_id FROM profiles WHERE is_active = true LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE '👤 Testando com usuário: %', test_user_id;
        
        -- Testar is_admin_user
        SELECT is_admin_user(test_user_id) INTO test_result;
        RAISE NOTICE '✅ is_admin_user: %', test_result;
        
        -- Testar user_project_ids
        SELECT user_project_ids(test_user_id) INTO test_array;
        RAISE NOTICE '✅ user_project_ids: % projeto(s)', array_length(test_array, 1);
        
        -- Testar user_assigned_task_ids
        SELECT user_assigned_task_ids(test_user_id) INTO test_array;
        RAISE NOTICE '✅ user_assigned_task_ids: % tarefa(s)', COALESCE(array_length(test_array, 1), 0);
        
        -- Testar cache de permissões
        SELECT get_user_permissions_cache(test_user_id) INTO test_permissions;
        RAISE NOTICE '✅ get_user_permissions_cache: % campos', jsonb_object_keys(test_permissions);
        
        RAISE NOTICE '✅ TODAS AS FUNÇÕES FUNCIONANDO CORRETAMENTE';
    ELSE
        RAISE NOTICE '⚠️  Nenhum usuário ativo encontrado para teste';
    END IF;
    
    RAISE NOTICE '🎯 PRÓXIMO PASSO: Implementar políticas RLS usando estas funções';
    RAISE NOTICE '======================================';
END $$;

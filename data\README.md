# 📚 DOCUMENTAÇÃO DO SISTEMA DE BANCO DE DADOS

## 🚀 Visão Geral

Este diretório contém a estrutura modular e organizada do banco de dados Supabase para o sistema Haiku Project Flow. A arquitetura foi projetada para ser:

- **Modular**: Arquivos separados por funcionalidade
- **Escalável**: Suporte a crescimento futuro
- **Segura**: Políticas RLS completas
- **Auditável**: Logs e validações em cada etapa

## 📁 Estrutura de Arquivos

### 🎯 Scripts Principais

| Arquivo | Descrição | Dependências |
|---------|-----------|--------------|
| `master_setup.sql` | Script principal que executa todos os outros | - |
| `01_tables_core.sql` | Tabelas principais (profiles, projects, stages, tasks) | Supabase Auth |
| `02_tables_relations.sql` | Tabelas de relacionamento | 01_tables_core |
| `03_tables_content.sql` | Tabelas de conteúdo e sistema de quiz | 01, 02 |
| `04_views_functions.sql` | Views, functions e triggers | 01, 02, 03 |
| `05_rls_policies.sql` | Políticas de segurança (RLS) | Todas as tabelas |
| `06_storage_buckets.sql` | Configuração de storage | Supabase Storage |
| `07_test_data.sql` | Dados de teste completos | Todos os scripts |
| `rollback.sql` | Script de limpeza completa | - |

### 🔧 Arquivos de Suporte

| Arquivo | Descrição |
|---------|-----------|
| `README.md` | Esta documentação |
| `supabase_schema.sql` | Schema antigo (mantido para compatibilidade) |
| `profiles_rows.sql` | Dados específicos de perfis |
| `quiz_test_data_final.sql` | Dados de teste para quizzes |
| `fix-task-attachments.sql` | Correções específicas |

## 🏗️ Arquitetura do Banco

### 📊 Tabelas Principais

```mermaid
graph TB
    A[profiles] --> B[projects]
    B --> C[stages]
    C --> D[tasks]
    D --> E[task_content_blocks]
    D --> F[task_attachments]
    D --> G[evidence]
    D --> H[quizzes]
```

### 🔐 Segurança (RLS)

- **Princípio de Menor Privilégio**: Usuários acessam apenas dados necessários
- **Baseado em Relacionamentos**: Acesso determinado por membros de projeto, executores, etc.
- **Hierarquia de Permissões**: Admin → Manager → Editor → Executor → Member

### 🎯 Funcionalidades Principais

1. **Gestão de Projetos**
   - Projetos com múltiplos estágios
   - Tarefas com conteúdo rico
   - Sistema de membros com papéis

2. **Sistema de Tarefas**
   - Conteúdo modular (texto, vídeo, quiz, evidências)
   - Executores múltiplos
   - Sistema de aprovação

3. **Sistema de Quiz**
   - Quizzes integrados às tarefas
   - Tentativas múltiplas
   - Estatísticas detalhadas

4. **Sistema de Evidências**
   - Upload de arquivos
   - Aprovação/rejeição
   - Integração com storage

## 🚀 Como Usar

### 🔥 Instalação Completa (RECOMENDADA)

```bash
# No SQL Editor do Supabase, execute:
\i data/master_setup.sql
```

### 🎯 Instalação Modular

```bash
# Execute os scripts na ordem:
\i data/01_tables_core.sql
\i data/02_tables_relations.sql
\i data/03_tables_content.sql
\i data/04_views_functions.sql
\i data/05_rls_policies.sql
\i data/06_storage_buckets.sql
\i data/07_test_data.sql
```

### 🧹 Limpeza Completa

```bash
# CUIDADO: Remove todos os dados!
\i data/rollback.sql
```

## 📋 Checklist de Validação

Após executar o setup, verifique:

- [ ] ✅ Todas as tabelas foram criadas
- [ ] 🔒 RLS está habilitado
- [ ] 👥 Políticas de segurança aplicadas
- [ ] 📊 Views funcionando
- [ ] ⚙️ Functions e triggers ativos
- [ ] 🗂️ Buckets de storage criados
- [ ] 🧪 Dados de teste inseridos

### 🔍 Consultas de Verificação

```sql
-- Verificar tabelas criadas
SELECT tablename FROM pg_tables WHERE schemaname = 'public';

-- Verificar políticas RLS
SELECT tablename, policyname FROM pg_policies WHERE schemaname = 'public';

-- Verificar functions
SELECT proname FROM pg_proc WHERE pronamespace = 'public'::regnamespace;

-- Verificar buckets
SELECT * FROM storage.buckets;
```

## 🔧 Configuração Avançada

### 🌐 Variáveis de Ambiente

```env
# Configurações recomendadas
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 📈 Monitoramento

```sql
-- Verificar performance
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
FROM pg_stat_user_tables 
WHERE schemaname = 'public';

-- Verificar índices
SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch 
FROM pg_stat_user_indexes 
WHERE schemaname = 'public';
```

## 🛠️ Manutenção

### 🔄 Backup Automático

```sql
-- Configurar backup automático das tabelas críticas
SELECT public.create_notification(
  'admin-user-id',
  'Backup Realizado',
  'Backup automático concluído com sucesso',
  'success'
);
```

### 🧹 Limpeza Periódica

```sql
-- Executar limpeza de arquivos temporários
SELECT public.cleanup_temp_files();
```

### 📊 Atualização de Estatísticas

```sql
-- Atualizar estatísticas de todos os quizzes
SELECT public.update_quiz_statistics(id) FROM public.quizzes;
```

## 🔍 Troubleshooting

### ❌ Problemas Comuns

1. **Erro de Permissão**
   - Verificar se RLS está configurado
   - Confirmar se usuário tem papel adequado

2. **Tabelas não Criadas**
   - Verificar dependências
   - Executar scripts na ordem correta

3. **Storage não Funciona**
   - Verificar se Supabase Storage está habilitado
   - Confirmar políticas de storage

### 🆘 Comandos de Diagnóstico

```sql
-- Verificar estado do RLS
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Verificar constraints
SELECT conname, contype FROM pg_constraint 
WHERE connamespace = 'public'::regnamespace;

-- Verificar triggers
SELECT tgname, tgrelid::regclass FROM pg_trigger 
WHERE tgnamespace = 'public'::regnamespace;
```

## 📞 Suporte

Para problemas ou dúvidas:

1. **Verifique os logs** do Supabase
2. **Consulte a documentação** do Supabase
3. **Execute os scripts de diagnóstico** acima
4. **Revisar as políticas RLS** se houver problemas de permissão

## 🎉 Changelog

### v2.0 - Julho 2025
- ✅ Estrutura modular completa
- ✅ Sistema de quiz integrado
- ✅ Políticas RLS abrangentes
- ✅ Storage configurado
- ✅ Dados de teste completos
- ✅ Documentação detalhada

### v1.0 - Versão Anterior
- ❌ Arquivo único muito extenso
- ❌ Políticas RLS incompletas
- ❌ Dados de teste limitados
- ❌ Documentação fragmentada

---

**🚀 Sistema pronto para produção!**

*Para mais informações, consulte a documentação do Supabase ou entre em contato com a equipe de desenvolvimento.* 
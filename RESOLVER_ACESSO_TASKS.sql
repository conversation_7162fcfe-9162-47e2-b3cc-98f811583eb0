-- URGENTE: Desabilitar RLS temporariamente para testar
-- Execute estas linhas uma por vez para identificar o problema

-- 1. Desabilitar RLS na tabela tasks
ALTER TABLE tasks DISABLE ROW LEVEL SECURITY;

-- 2. <PERSON>eri<PERSON><PERSON> se funcionou
SELECT COUNT(*) FROM tasks;

-- 3. <PERSON><PERSON><PERSON><PERSON> as tarefas específicas
SELECT * FROM tasks WHERE id IN (
  '7c606667-9391-4660-933d-90d6bd276e88',
  '408f3082-3cc2-4107-b163-9f86fc7d6e3c',
  'd23bd1ab-1176-43c0-9b58-620a4500b7a5'
);

-- 4. <PERSON> funcion<PERSON>, você pode reabilitar RLS depois (opcional)
-- ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

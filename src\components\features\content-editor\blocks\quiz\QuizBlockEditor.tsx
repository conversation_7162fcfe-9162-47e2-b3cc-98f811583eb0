import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Plus,
  Edit,
  Trash2,
  Copy,
  Eye,
  Settings,
  BarChart3,
  GripVertical,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { Quiz<PERSON>lock<PERSON>ontent, BlockConfig, defaultBlockConfig } from '@/types';
import { QuizContent, QuizQuestion, QuizConfig, QuizEditorState, QuizQuestionType, QuizUtils } from '@/types/quiz';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { BlockCard } from '@/components/ui/BlockCard';
import { QuizConfigPanel } from './components/QuizConfigPanel';
import { QuizPreview } from './components/QuizPreview';
import { QuizReports } from './components/QuizReports';
import { QuestionEditor } from './components/QuestionEditor';
import { QuizAppearanceConfig } from './components/QuizAppearanceConfig';
import { QuizButtonConfig } from './components/QuizButtonConfig';
import { QuizPresetConfig } from './components/QuizPresetConfig';
import { useToast } from '@/hooks/ui/use-toast';

export interface QuizBlockEditorProps {
  editContent: QuizBlockContent;
  setEditContent: (c: QuizBlockContent) => void;
  mode: 'edit' | 'preview';
  config?: BlockConfig;
  setConfig?: (config: BlockConfig) => void;
}

// Componente para item arrastável de pergunta
function SortableQuestionItem({ question, onEdit, onDelete, onDuplicate }: {
  question: QuizQuestion;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: question.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getQuestionTypeLabel = (type: QuizQuestionType): string => {
    const labels = {
      'multiple-choice': 'Múltipla Escolha',
      'single-choice': 'Escolha Única',
      'true-false': 'Verdadeiro/Falso',
      'open-text': 'Resposta Aberta',
      'ordering': 'Ordenação',
      'matching': 'Correspondência'
    };
    return labels[type];
  };

  const getQuestionTypeColor = (type: QuizQuestionType): string => {
    const colors = {
      'multiple-choice': 'bg-blue-500',
      'single-choice': 'bg-green-500',
      'true-false': 'bg-purple-500',
      'open-text': 'bg-orange-500',
      'ordering': 'bg-pink-500',
      'matching': 'bg-indigo-500'
    };
    return colors[type];
  };

  return (
    <div ref={setNodeRef} style={style} className="group">
      <Card className="mb-3 hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <Button
                variant="ghost"
                size="sm"
                className="cursor-grab hover:cursor-grabbing p-1"
                {...attributes}
                {...listeners}
              >
                <GripVertical className="w-4 h-4 text-gray-400" />
              </Button>

              <Badge
                className={`${getQuestionTypeColor(question.type)} text-white flex-shrink-0`}
              >
                {getQuestionTypeLabel(question.type)}
              </Badge>

              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm truncate">{question.title}</h4>
                <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                  <span>{question.points} pts</span>
                  {question.timeLimit && (
                    <>
                      <span>•</span>
                      <Clock className="w-3 h-3" />
                      <span>{question.timeLimit}s</span>
                    </>
                  )}
                  {question.required && (
                    <>
                      <span>•</span>
                      <AlertCircle className="w-3 h-3 text-red-500" />
                      <span className="text-red-500">Obrigatória</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                onClick={onEdit}
                className="h-8 w-8 p-0"
                title="Editar pergunta"
              >
                <Edit className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onDuplicate}
                className="h-8 w-8 p-0"
                title="Duplicar pergunta"
              >
                <Copy className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onDelete}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                title="Excluir pergunta"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Componente do Editor Avançado
function QuizEditorAdvanced({ quizContent, onChange, blockConfig: initialBlockConfig, setConfig }: {
  quizContent: QuizContent;
  onChange: (content: QuizContent) => void;
  blockConfig?: BlockConfig;
  setConfig?: (config: BlockConfig) => void;
}) {
  const [editorState, setEditorState] = useState<QuizEditorState>({
    activeTab: 'general',
    isPreviewMode: false,
    isDirty: false
  });

  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(null);
  const [showQuestionBank, setShowQuestionBank] = useState(false);
  const [blockConfig, setBlockConfig] = useState<BlockConfig>(initialBlockConfig || defaultBlockConfig);

  const { toast } = useToast();
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Handlers para perguntas
  const handleAddQuestion = useCallback((type: QuizQuestionType) => {
    const newQuestion: QuizQuestion = {
      id: `question_${Date.now()}`,
      type,
      title: 'Nova pergunta',
      points: 1,
      required: false,
      options: type === 'multiple-choice' || type === 'single-choice' ? [
        { id: 'opt1', text: 'Opção 1', isCorrect: false },
        { id: 'opt2', text: 'Opção 2', isCorrect: false }
      ] : undefined,
      correctAnswer: type === 'true-false' ? true : undefined,
      orderingItems: type === 'ordering' ? [
        { id: 'item1', text: 'Item 1', correctOrder: 1 },
        { id: 'item2', text: 'Item 2', correctOrder: 2 }
      ] : undefined,
      matchingPairs: type === 'matching' ? [
        { id: 'pair1', left: 'Item A', right: 'Item 1' },
        { id: 'pair2', left: 'Item B', right: 'Item 2' }
      ] : undefined
    };

    const updatedContent = {
      ...quizContent,
      questions: [...quizContent.questions, newQuestion]
    };

    onChange(updatedContent);
    setEditingQuestion(newQuestion);
    setEditorState(prev => ({ ...prev, isDirty: true }));

    toast({
      title: 'Pergunta adicionada',
      description: 'Nova pergunta foi criada com sucesso.',
    });
  }, [quizContent, onChange, toast]);

  const handleEditQuestion = useCallback((question: QuizQuestion) => {
    setEditingQuestion(question);
    setEditorState(prev => ({ ...prev, selectedQuestionId: question.id }));
  }, []);

  const handleUpdateQuestion = useCallback((updatedQuestion: QuizQuestion) => {
    const updatedContent = {
      ...quizContent,
      questions: quizContent.questions.map(q =>
        q.id === updatedQuestion.id ? updatedQuestion : q
      )
    };

    onChange(updatedContent);
    setEditorState(prev => ({ ...prev, isDirty: true }));
  }, [quizContent, onChange]);

  const handleDeleteQuestion = useCallback((questionId: string) => {
    const updatedContent = {
      ...quizContent,
      questions: quizContent.questions.filter(q => q.id !== questionId)
    };

    onChange(updatedContent);
    setEditorState(prev => ({ ...prev, isDirty: true }));

    toast({
      title: 'Pergunta removida',
      description: 'A pergunta foi excluída com sucesso.',
    });
  }, [quizContent, onChange, toast]);

  const handleDuplicateQuestion = useCallback((question: QuizQuestion) => {
    const duplicatedQuestion: QuizQuestion = {
      ...question,
      id: `question_${Date.now()}`,
      title: `${question.title} (Cópia)`
    };

    const updatedContent = {
      ...quizContent,
      questions: [...quizContent.questions, duplicatedQuestion]
    };

    onChange(updatedContent);
    setEditorState(prev => ({ ...prev, isDirty: true }));

    toast({
      title: 'Pergunta duplicada',
      description: 'A pergunta foi copiada com sucesso.',
    });
  }, [quizContent, onChange, toast]);

  const handleDragEnd = useCallback((event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = quizContent.questions.findIndex(q => q.id === active.id);
      const newIndex = quizContent.questions.findIndex(q => q.id === over.id);

      const reorderedQuestions = arrayMove(quizContent.questions, oldIndex, newIndex);

      const updatedContent = {
        ...quizContent,
        questions: reorderedQuestions
      };

      onChange(updatedContent);
      setEditorState(prev => ({ ...prev, isDirty: true }));
    }
  }, [quizContent, onChange]);

  const handleConfigChange = useCallback((newConfig: QuizConfig) => {
    const updatedContent = {
      ...quizContent,
      config: newConfig
    };

    onChange(updatedContent);
    setEditorState(prev => ({ ...prev, isDirty: true }));
  }, [quizContent, onChange]);

  const handleBlockConfigChange = useCallback((newBlockConfig: BlockConfig) => {
    setBlockConfig(newBlockConfig);
    if (setConfig) {
      setConfig(newBlockConfig);
    }
    setEditorState(prev => ({ ...prev, isDirty: true }));
  }, [setConfig]);

  if (editorState.isPreviewMode) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Preview do Quiz</h3>
          <Button
            variant="outline"
            onClick={() => setEditorState(prev => ({ ...prev, isPreviewMode: false }))}
          >
            Voltar ao Editor
          </Button>
        </div>
        <QuizPreview content={quizContent} />
      </div>
    );
  }

  if (editingQuestion) {
    return (
      <div className="w-full">
        <QuestionEditor
          question={editingQuestion}
          onChange={handleUpdateQuestion}
          onSave={() => setEditingQuestion(null)}
          onCancel={() => setEditingQuestion(null)}
        />
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Editor de Quiz</h3>
          <p className="text-sm text-gray-600">
            {quizContent.questions.length} pergunta(s) • {quizContent.questions.reduce((sum, q) => sum + q.points, 0)} pontos total
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setEditorState(prev => ({ ...prev, isPreviewMode: true }))}
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>

      <Tabs
        value={editorState.activeTab}
        onValueChange={(tab) => setEditorState(prev => ({ ...prev, activeTab: tab as any }))}
      >
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">Geral</TabsTrigger>
          <TabsTrigger value="questions">Perguntas</TabsTrigger>
          <TabsTrigger value="correction">Correção</TabsTrigger>
          <TabsTrigger value="validation">Validação</TabsTrigger>
          <TabsTrigger value="appearance">Aparência</TabsTrigger>
          <TabsTrigger value="reports">Relatórios</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <QuizConfigPanel
            config={quizContent.config}
            onChange={handleConfigChange}
            type="general"
          />
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {/* Botões para adicionar perguntas */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Adicionar Nova Pergunta</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleAddQuestion('single-choice')}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-xs">Escolha Única</span>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleAddQuestion('multiple-choice')}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Plus className="w-4 h-4 text-blue-600" />
                  </div>
                  <span className="text-xs">Múltipla Escolha</span>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleAddQuestion('true-false')}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-4 h-4 text-purple-600" />
                  </div>
                  <span className="text-xs">Verdadeiro/Falso</span>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleAddQuestion('open-text')}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <Edit className="w-4 h-4 text-orange-600" />
                  </div>
                  <span className="text-xs">Resposta Aberta</span>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleAddQuestion('ordering')}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                    <GripVertical className="w-4 h-4 text-pink-600" />
                  </div>
                  <span className="text-xs">Ordenação</span>
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleAddQuestion('matching')}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <Copy className="w-4 h-4 text-indigo-600" />
                  </div>
                  <span className="text-xs">Correspondência</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Lista de perguntas */}
          {quizContent.questions.length > 0 ? (
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={quizContent.questions.map(q => q.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-3">
                  {quizContent.questions.map((question) => (
                    <SortableQuestionItem
                      key={question.id}
                      question={question}
                      onEdit={() => handleEditQuestion(question)}
                      onDelete={() => handleDeleteQuestion(question.id)}
                      onDuplicate={() => handleDuplicateQuestion(question)}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          ) : (
            <Card>
              <CardContent className="py-12 text-center">
                <div className="text-gray-400 mb-4">
                  <BarChart3 className="w-12 h-12 mx-auto" />
                </div>
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  Nenhuma pergunta adicionada
                </h3>
                <p className="text-gray-500 mb-4">
                  Comece criando sua primeira pergunta usando os botões acima.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="correction">
          <QuizConfigPanel
            config={quizContent.config}
            onChange={handleConfigChange}
            type="correction"
          />
        </TabsContent>

        <TabsContent value="validation">
          <QuizConfigPanel
            config={quizContent.config}
            onChange={handleConfigChange}
            type="validation"
          />
        </TabsContent>

        <TabsContent value="appearance">
          <Tabs defaultValue="appearance" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="appearance">Aparência</TabsTrigger>
              <TabsTrigger value="button">Botão</TabsTrigger>
              <TabsTrigger value="presets">Presets</TabsTrigger>
            </TabsList>

            <TabsContent value="appearance">
              <QuizAppearanceConfig
                config={blockConfig}
                onChange={handleBlockConfigChange}
                quizMode={QuizUtils.isSurveyMode(quizContent.config) ? 'survey' : 'assessment'}
              />
            </TabsContent>

            <TabsContent value="button">
              <QuizButtonConfig
                config={blockConfig}
                onChange={handleBlockConfigChange}
                quizMode={QuizUtils.isSurveyMode(quizContent.config) ? 'survey' : 'assessment'}
              />
            </TabsContent>

            <TabsContent value="presets">
              <QuizPresetConfig
                config={blockConfig}
                onChange={handleBlockConfigChange}
                quizMode={QuizUtils.isSurveyMode(quizContent.config) ? 'survey' : 'assessment'}
              />
            </TabsContent>
          </Tabs>
        </TabsContent>

        <TabsContent value="reports">
          <QuizReports quizContent={quizContent} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export const QuizBlockEditor: React.FC<QuizBlockEditorProps> = ({ editContent, setEditContent, mode, config, setConfig }) => {
  const safeConfig = config || defaultBlockConfig;

  // Converter o formato antigo para o novo se necessário
  const convertToNewFormat = useCallback((): QuizContent => {
    if (editContent.quiz) {
      return editContent.quiz;
    }

    // Converter formato antigo
    const defaultConfig: QuizConfig = {
      title: editContent.question || 'Quiz',
      maxAttempts: 3,
      allowRetry: true,
      passingScore: 70,
      showScore: true,
      showCorrectAnswers: true,
      showFeedback: true,
      showDetailedResults: true,
      showTimer: false,
      isRequired: false,
      blockProgressUntilPassed: false,
      shuffleQuestions: false,
      shuffleOptions: false,
      showProgressBar: true,
      allowSaveDraft: true,
      enableAnalytics: true,
      showResultsToUser: true
    };

    const questions: QuizQuestion[] = [];

    if (editContent.question) {
      const question: QuizQuestion = {
        id: 'question_1',
        type: 'single-choice',
        title: editContent.question,
        points: 1,
        required: true,
        options: (editContent.options || []).map((opt, idx) => ({
          id: `opt_${idx}`,
          text: opt,
          isCorrect: idx === 0 // Primeira opção como correta por padrão
        }))
      };
      questions.push(question);
    }

    return {
      config: defaultConfig,
      questions
    };
  }, [editContent]);

  const [quizContent, setQuizContent] = useState<QuizContent>(() => convertToNewFormat());
  const { toast } = useToast();

  // Atualizar o conteúdo quando o quiz mudar
  const handleQuizChange = useCallback((newQuizContent: QuizContent) => {
    setQuizContent(newQuizContent);
    setEditContent({ ...editContent, quiz: newQuizContent });
  }, [editContent, setEditContent]);

  // Manter compatibilidade com formato antigo para edição simples
  const handleOptionChange = (idx: number, value: string) => {
    const newOptions = [...(editContent.options || [])];
    newOptions[idx] = value;
    setEditContent({ ...editContent, options: newOptions });
  };

  if (mode === 'edit') {
    return <QuizEditorAdvanced quizContent={quizContent} onChange={handleQuizChange} blockConfig={safeConfig} setConfig={setConfig} />;
  }

  // Configurações de estilo do card
  const cardBgColor = safeConfig.card?.backgroundColor || '#ffffff';
  const cardTextColor = safeConfig.card?.color || '#000000';
  const cardFormat = safeConfig.card?.format || 'rounded';
  const cardBorder = safeConfig.card?.border?.enabled;
  const cardBorderColor = safeConfig.card?.border?.color || '#e5e5e5';
  const cardBorderWidth = safeConfig.card?.border?.width || 1;
  const cardShadow = safeConfig.card?.shadow?.enabled;
  const cardShadowDepth = safeConfig.card?.shadow?.depth || 1;
  const cardHover = safeConfig.card?.hover?.enabled;
  const cardHoverShadowDepth = safeConfig.card?.hover?.shadowDepth || 2;

  // Função para gerar box-shadow
  const getBoxShadow = (depth: number) => `0 ${depth * 2}px ${depth * 4}px rgba(0,0,0,${depth * 0.1})`;

  // Estilos do card
  const cardStyles: React.CSSProperties = {
    background: cardBgColor,
    color: cardTextColor,
    borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
    border: cardBorder ? `${cardBorderWidth}px solid ${cardBorderColor}` : 'none',
    boxShadow: cardShadow ? getBoxShadow(cardShadowDepth) : 'none',
    transition: cardHover ? 'box-shadow 0.3s ease, transform 0.2s ease' : 'none',
    cursor: cardHover ? 'pointer' : 'default'
  };

  // Handlers de hover
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = getBoxShadow(cardHoverShadowDepth);
      e.currentTarget.style.transform = 'translateY(-2px)';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = cardShadow ? getBoxShadow(cardShadowDepth) : 'none';
      e.currentTarget.style.transform = 'translateY(0)';
    }
  };

  // Preview
  return (
    <BlockCard
      className="flex flex-col gap-1 p-4"
      style={cardStyles}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <BlockCardIcon
        config={safeConfig.icon}
        title={quizContent.config.title}
        textColor={cardTextColor}
        content={
          <div className="space-y-2">
            {quizContent.config.description && (
              <p className="text-sm opacity-80">{quizContent.config.description}</p>
            )}
            <div className="text-xs opacity-60">
              {quizContent.questions.length} pergunta(s) • {quizContent.questions.reduce((sum, q) => sum + q.points, 0)} pontos
            </div>
            {quizContent.questions.length > 0 && (
              <div className="space-y-2 mt-3">
                {quizContent.questions.slice(0, 2).map((question, idx) => (
                  <div key={question.id} className="text-sm">
                    <div className="font-medium">{idx + 1}. {question.title}</div>
                    {question.options && (
                      <div className="ml-4 mt-1 space-y-1">
                        {question.options.slice(0, 3).map((opt) => (
                          <div key={opt.id} className="flex items-center gap-2">
                            <input type="radio" name={`quiz-preview-${question.id}`} disabled />
                            <span className="text-xs opacity-60">{opt.text}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                {quizContent.questions.length > 2 && (
                  <div className="text-xs opacity-60">
                    ... e mais {quizContent.questions.length - 2} pergunta(s)
                  </div>
                )}
              </div>
            )}
          </div>
        }
      />
    </BlockCard>
  );
};
import React, { useState } from 'react';
import { BlockConfig } from '@/types';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ColorPicker } from '../../ColorPicker';
import { IconPicker } from '../../IconPicker';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { LucideIcon } from 'lucide-react';

interface IconAppearanceConfigProps {
  config: BlockConfig;
  onChange: (c: BlockConfig) => void;
  onReset?: () => void;
  blockType?: string;
  allowedIcons?: string[];
  ICONS?: any;
  ALERT_ICONS?: any;
  renderIconPreview?: () => React.ReactNode;
}

export const IconAppearanceConfig: React.FC<IconAppearanceConfigProps> = ({
  config,
  onChange,
  onReset,
  blockType,
  allowedIcons = [],
  ICONS = {},
  ALERT_ICONS = {},
  renderIconPreview,
}) => {
  const [iconPickerOpen, setIconPickerOpen] = useState(false);
  
  const handleIconToggle = (enabled: boolean) => {
    onChange({
      ...config,
      icon: {
        ...config.icon,
        enabled,
      },
    });
  };
  
  const handleIconTypeChange = (type: 'predefined' | 'custom') => {
    onChange({
      ...config,
      icon: {
        ...config.icon,
        type,
        ...(type === 'predefined' ? { customIconUrl: undefined } : { iconName: undefined }),
      },
    });
  };
  
  const handleIconSelect = (iconName: string) => {
    onChange({
      ...config,
      icon: {
        ...config.icon,
        iconName,
        type: 'predefined',
      },
    });
    setIconPickerOpen(false);
  };
  
  const handleCustomUrlChange = (customIconUrl: string) => {
    onChange({
      ...config,
      icon: {
        ...config.icon,
        customIconUrl,
        type: 'custom',
      },
    });
  };

  return (
    <div className="appearance-card-panel custom-video-appearance-panel">
      <div className="appearance-header custom-appearance-header">
        <span className="appearance-title custom-appearance-title standard-title">Configurações de Ícone</span>
        {onReset && (
          <button className="restore-btn custom-restore-btn" type="button" onClick={onReset}>Restaurar Padrão</button>
        )}
      </div>
      <hr className="appearance-divider custom-appearance-divider" />
      <div className="appearance-body custom-appearance-body" style={{ padding: '10px' }}>
        <div className="custom-appearance-grid-v3" style={{ gap: '8px 8px' }}>
          {/* Linha 1 */}
          <div className="appearance-row">
            {/* Coluna 1: Ativar ícone (toggle) */}
            <label className="switch-label custom-switch-label standard-label" style={{ gap: 4, justifyContent: 'flex-start', textAlign: 'left', display: 'flex' }}>
              <input type="checkbox" className="switch-input custom-switch standard-checkbox" checked={!!config.icon?.enabled} onChange={e => onChange({ ...config, icon: { ...config.icon, enabled: e.target.checked } })} />
              <span>Ativar ícone</span>
            </label>
            {/* Coluna 2: Radios Predefinido/Personalizado */}
            {config.icon?.enabled && (
              <div className="format-dropdown-group">
                <label className="format-label custom-format-label standard-label" style={{ fontWeight: 400 }}>
                  <input type="radio" name="icon-type" value="predefined" checked={config.icon?.type === 'predefined'} onChange={() => handleIconTypeChange('predefined')} className="standard-radio" /> Predefinido
                </label>
                <label className="format-label custom-format-label standard-label" style={{ fontWeight: 400, marginLeft: 8 }}>
                  <input type="radio" name="icon-type" value="custom" checked={config.icon?.type === 'custom'} onChange={() => handleIconTypeChange('custom')} className="standard-radio" /> Personalizado
                </label>
              </div>
            )}
            {/* Coluna 3: Posição (dropdown) */}
            {config.icon?.enabled && (
              <div className="format-dropdown-group">
                <span className="format-label custom-format-label standard-label" style={{ marginRight: 6 }}>Posição</span>
                <select className="format-dropdown custom-format-dropdown standard-field" value={config.icon?.position || 'left-title'} onChange={e => onChange({ ...config, icon: { ...config.icon, position: e.target.value as any } })}>
                  <option value="left-title">Esquerda do título</option>
                  <option value="right-title">Direita do título</option>
                  <option value="left-title-desc">Esquerda do título e descrição</option>
                  <option value="right-title-desc">Direita do título e descrição</option>
                  <option value="left-content">Esquerda do conteúdo</option>
                  <option value="right-content">Direita do conteúdo</option>
                  <option value="top-center">Topo centralizado</option>
                  <option value="bottom-center">Rodapé centralizado</option>
                </select>
              </div>
            )}
          </div>
          {/* Linha 2 */}
          {config.icon?.enabled && (
            <div className="appearance-row">
              {/* Coluna 1: Cor do Fundo (color picker) */}
              <div className="color-picker-group" style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', textAlign: 'left' }}>
                <div className="flex flex-col gap-2">
                  <input type="color" className="w-8 h-8 rounded border border-gray-200 cursor-pointer" aria-label="Selecionar cor" value={config.icon?.appearance?.background || '#f3f4f6'} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, background: e.target.value } } })} />
                </div>
                <span className="color-label-inline" style={{ fontSize: 11, color: '#22223b', marginLeft: 6 }}>Cor do Fundo</span>
              </div>
              {/* Coluna 2: Botão ou input */}
              <div className="format-dropdown-group" style={{ width: '100%' }}>
                {config.icon?.type === 'predefined' ? (
                  <Button className="w-full standard-button" type="button" style={{ color: '#fff', background: '#0085FC', border: 'none' }} onClick={() => setIconPickerOpen(true)}>
                    <span className="flex items-center gap-2">
                      <span>Escolher ícone</span>
                      {config.icon?.iconName && (() => {
                        const IconComponent = (ICONS as any)[config.icon.iconName];
                        return IconComponent ? <IconComponent className="w-4 h-4" /> : null;
                      })()}
                    </span>
                  </Button>
                ) : (
                  <input type="text" className="input custom-input w-full standard-field" placeholder="URL do ícone" value={config.icon?.customIconUrl || ''} onChange={e => onChange({ ...config, icon: { ...config.icon, customIconUrl: e.target.value } })} />
                )}
              </div>
              {/* Coluna 3: Formato (dropdown) */}
              <div className="format-dropdown-group">
                <span className="format-label custom-format-label standard-label" style={{ marginRight: 6 }}>Formato</span>
                <select className="format-dropdown custom-format-dropdown standard-field" value={config.icon?.appearance?.format || 'circle'} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, format: e.target.value as any } } })}>
                  <option value="circle">Círculo</option>
                  <option value="square">Quadrado</option>
                  <option value="rounded">Arredondado</option>
                </select>
              </div>
            </div>
          )}
          {/* Linha 3 */}
          {config.icon?.enabled && (
            <div className="appearance-row">
              {/* Coluna 1: Cor da Fonte (color picker) */}
              <div className="color-picker-group" style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', textAlign: 'left' }}>
                <div className="flex flex-col gap-2">
                  <input type="color" className="w-8 h-8 rounded border border-gray-200 cursor-pointer" aria-label="Selecionar cor" value={config.icon?.appearance?.color || '#22223b'} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, color: e.target.value } } })} />
                </div>
                <span className="color-label-inline standard-label" style={{ marginLeft: 6 }}>Cor da Fonte</span>
              </div>
              <div></div>
              <div></div>
            </div>
          )}
          {/* Linha 4 */}
          {config.icon?.enabled && (
            <div className="appearance-row">
              {/* Coluna 1: Borda (toggle) */}
              <label className="switch-label custom-switch-label standard-label" style={{ justifyContent: 'flex-start', textAlign: 'left', display: 'flex' }}>
                <input type="checkbox" className="switch-input custom-switch standard-checkbox" checked={!!config.icon?.appearance?.border?.enabled} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, border: { ...config.icon?.appearance?.border, enabled: e.target.checked } } } })} />
                <span>Borda</span>
              </label>
              {/* Coluna 2: Cor da borda (color picker) */}
              <div className="color-picker-group" style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', textAlign: 'left' }}>
                {config.icon?.appearance?.border?.enabled ? (
                  <>
                    <div className="flex flex-col gap-2">
                      <input type="color" className="w-8 h-8 rounded border border-gray-200 cursor-pointer" aria-label="Selecionar cor" value={config.icon?.appearance?.border?.color || '#e5e7eb'} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, border: { ...config.icon?.appearance?.border, color: e.target.value } } } })} />
                    </div>
                    <span className="color-label-inline standard-label" style={{ marginLeft: 6 }}>Cor da Borda</span>
                  </>
                ) : null}
              </div>
              {/* Coluna 3: Largura da Borda (slider) */}
              <div className="slider-group custom-slider-group slider-vertical-label">
                {config.icon?.appearance?.border?.enabled ? (
                  <>
                    <span className="slider-label custom-slider-label standard-label" style={{ marginBottom: 2 }}>Largura da Borda</span>
                    <input type="range" min={1} max={8} className="slider custom-slider" style={{ height: 3, marginTop: 2 }} value={config.icon?.appearance?.border?.width || 1} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, border: { ...config.icon?.appearance?.border, width: Number(e.target.value) } } } })} />
                  </>
                ) : null}
              </div>
            </div>
          )}
          {/* Linha 5 */}
          {config.icon?.enabled && (
            <div className="appearance-row">
              {/* Coluna 1: Sombra (toggle) */}
              <label className="switch-label custom-switch-label standard-label" style={{ justifyContent: 'flex-start', textAlign: 'left', display: 'flex' }}>
                <input type="checkbox" className="switch-input custom-switch standard-checkbox" checked={!!config.icon?.appearance?.shadow?.enabled} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, shadow: { ...config.icon?.appearance?.shadow, enabled: e.target.checked } } } })} />
                <span>Sombra</span>
              </label>
              <div></div>
              {/* Coluna 3: Profundidade da Sombra (slider) */}
              <div className="slider-group custom-slider-group slider-vertical-label">
                {config.icon?.appearance?.shadow?.enabled ? (
                  <>
                    <span className="slider-label custom-slider-label standard-label" style={{ marginBottom: 2 }}>Profundidade da Sombra</span>
                    <input type="range" min={1} max={10} className="slider custom-slider" style={{ height: 3, marginTop: 2 }} value={config.icon?.appearance?.shadow?.depth || 1} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, shadow: { ...config.icon?.appearance?.shadow, depth: Number(e.target.value) } } } })} />
                  </>
                ) : null}
              </div>
            </div>
          )}
          {/* Linha 6 */}
          {config.icon?.enabled && (
            <div className="appearance-row">
              {/* Coluna 1: Hover (toggle) */}
              <label className="switch-label custom-switch-label standard-label" style={{ justifyContent: 'flex-start', textAlign: 'left', display: 'flex' }}>
                <input type="checkbox" className="switch-input custom-switch standard-checkbox" checked={!!config.icon?.appearance?.hover?.enabled} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, hover: { ...config.icon?.appearance?.hover, enabled: e.target.checked } } } })} />
                <span>Hover</span>
              </label>
              <div></div>
              {/* Coluna 3: Profundidade do Hover (slider) */}
              <div className="slider-group custom-slider-group slider-vertical-label">
                {config.icon?.appearance?.hover?.enabled ? (
                  <>
                    <span className="slider-label custom-slider-label standard-label" style={{ marginBottom: 2 }}>Profundidade do Hover</span>
                    <input type="range" min={1} max={10} className="slider custom-slider" style={{ height: 3, marginTop: 2 }} value={config.icon?.appearance?.hover?.shadowDepth || 1} onChange={e => onChange({ ...config, icon: { ...config.icon, appearance: { ...config.icon?.appearance, hover: { ...config.icon?.appearance?.hover, shadowDepth: Number(e.target.value) } } } })} />
                  </>
                ) : null}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* Modal de seleção de ícone Lucide */}
      <Dialog open={iconPickerOpen} onOpenChange={setIconPickerOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Escolher ícone</DialogTitle>
          </DialogHeader>
          <IconPicker
            value={config.icon?.iconName}
            onSelect={handleIconSelect}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};
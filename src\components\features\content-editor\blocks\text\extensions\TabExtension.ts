import { Extension } from '@tiptap/core';

/**
 * Extensão para comportamento de Tab no editor
 * Suporta indentação em listas e code blocks
 */
export const TabExtension = Extension.create({
  name: 'tab<PERSON><PERSON><PERSON>',

  addKeyboardShortcuts() {
    return {
      // Tab - Indentação
      Tab: ({ editor }) => {
        const { state } = editor;
        const { selection } = state;
        const { $from } = selection;

        // Se estiver em um code block, inserir tab literal
        if (editor.isActive('codeBlock')) {
          return editor.commands.insertContent('\t');
        }

        // Se estiver em uma lista, tentar indentar
        if (editor.isActive('listItem')) {
          // Verificar se pode indentar (não é o primeiro item)
          const canSink = editor.can().sinkListItem('listItem');
          if (canSink) {
            return editor.chain().sinkListItem('listItem').run();
          }
          // Se não pode indentar, inserir espaços
          return editor.commands.insertContent('    '); // 4 espaços
        }

        // Se estiver em uma task list, tentar indentar
        if (editor.isActive('taskItem')) {
          const canSink = editor.can().sinkListItem('taskItem');
          if (canSink) {
            return editor.chain().sinkListItem('taskItem').run();
          }
          return editor.commands.insertContent('    ');
        }

        // Para texto normal, inserir espaços de indentação
        return editor.commands.insertContent('    '); // 4 espaços
      },

      // Shift+Tab - Desindentar
      'Shift-Tab': ({ editor }) => {
        // Se estiver em uma lista, tentar desindentar
        if (editor.isActive('listItem')) {
          const canLift = editor.can().liftListItem('listItem');
          if (canLift) {
            return editor.chain().liftListItem('listItem').run();
          }
          return false;
        }

        // Se estiver em uma task list, tentar desindentar
        if (editor.isActive('taskItem')) {
          const canLift = editor.can().liftListItem('taskItem');
          if (canLift) {
            return editor.chain().liftListItem('taskItem').run();
          }
          return false;
        }

        // Para code blocks, remover indentação se houver
        if (editor.isActive('codeBlock')) {
          const { state } = editor;
          const { selection } = state;
          const { from, to } = selection;

          if (from === to) { // Cursor simples
            const textBefore = state.doc.textBetween(Math.max(0, from - 4), from);

            if (textBefore.endsWith('\t')) {
              // Remover um tab
              return editor.commands.deleteRange({ from: from - 1, to: from });
            } else if (textBefore.endsWith('    ')) {
              // Remover 4 espaços
              return editor.commands.deleteRange({ from: from - 4, to: from });
            }
          }
          return false;
        }

        return false;
      },
    };
  },

  // Plugin removido para evitar erros - funcionalidade mantida via keyboard shortcuts
});

export default TabExtension;

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Bloco de Código com Numeração</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }

        h1 {
            color: #24292e;
            border-bottom: 1px solid #e1e4e8;
            padding-bottom: 10px;
        }

        /* Code block wrapper */
        .code-block-wrapper {
            margin: 16px 0;
            display: block;
        }

        /* Code block styles */
        .code-block {
            background-color: #f8f9fa;
            color: #24292e;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.45;
            overflow-x: auto;
            margin: 0;
            position: relative;
            padding: 0;
        }

        .code-block-header {
            background-color: #f1f3f4;
            border-bottom: 1px solid #e1e4e8;
            padding: 8px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #586069;
        }

        .code-block-language {
            font-weight: 600;
        }

        .code-block-copy {
            background: none;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            color: #24292f;
            cursor: pointer;
            font-size: 12px;
            padding: 4px 8px;
            transition: all 0.2s;
        }

        .code-block-copy:hover {
            background-color: #f3f4f6;
            border-color: #d0d7de;
        }

        .code-block-content {
            display: flex;
            padding: 16px 0;
        }

        .code-block-lines {
            background-color: #f6f8fa;
            border-right: 1px solid #e1e4e8;
            color: #656d76;
            font-size: 12px;
            line-height: 1.45;
            min-width: 40px;
            padding: 0 8px;
            text-align: right;
            user-select: none;
            white-space: pre;
        }

        .code-block-code {
            flex: 1;
            padding: 0 16px;
            white-space: pre;
            overflow-x: auto;
        }

        .code-block-code pre {
            margin: 0;
            padding: 0;
        }

        .code-block-code code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
            line-height: inherit;
        }

        /* Syntax highlighting styles */
        .code-keyword {
            color: #d73a49;
            font-weight: 600;
        }

        .code-string {
            color: #032f62;
        }

        .code-comment {
            color: #6a737d;
            font-style: italic;
        }

        .code-number {
            color: #005cc5;
        }

        .code-function {
            color: #6f42c1;
        }

        .code-operator {
            color: #d73a49;
        }

        .code-punctuation {
            color: #24292e;
        }

        .demo-section {
            margin: 30px 0;
        }

        .copy-success {
            background-color: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
        }
    </style>
</head>
<body>
    <h1>🎨 Demo - Bloco de Código com Numeração</h1>
    
    <p>Esta demonstração mostra como os blocos de código são renderizados com numeração de linhas, syntax highlighting e botão de copiar, exatamente como no GitHub.</p>

    <div class="demo-section">
        <h2>JavaScript</h2>
        <div class="code-block-wrapper">
            <div class="code-block">
                <div class="code-block-header">
                    <span class="code-block-language">JavaScript</span>
                    <button class="code-block-copy" onclick="copyCode(this, 'js-code')">
                        📋 Copiar
                    </button>
                </div>
                <div class="code-block-content">
                    <div class="code-block-lines">1
2
3
4</div>
                    <div class="code-block-code">
                        <pre><code id="js-code"><span class="code-keyword">if</span> (<span class="code-number">1</span> = <span class="code-number">1</span>)
{
teste = <span class="code-number">1</span>
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>Python</h2>
        <div class="code-block-wrapper">
            <div class="code-block">
                <div class="code-block-header">
                    <span class="code-block-language">Python</span>
                    <button class="code-block-copy" onclick="copyCode(this, 'python-code')">
                        📋 Copiar
                    </button>
                </div>
                <div class="code-block-content">
                    <div class="code-block-lines">1
2
3
4
5
6</div>
                    <div class="code-block-code">
                        <pre><code id="python-code"><span class="code-keyword">def</span> <span class="code-function">hello_world</span>():
    <span class="code-comment"># Esta é uma função simples</span>
    name = <span class="code-string">"World"</span>
    <span class="code-keyword">print</span>(<span class="code-string">f"Hello, {name}!"</span>)

<span class="code-function">hello_world</span>()</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>TypeScript</h2>
        <div class="code-block-wrapper">
            <div class="code-block">
                <div class="code-block-header">
                    <span class="code-block-language">TypeScript</span>
                    <button class="code-block-copy" onclick="copyCode(this, 'ts-code')">
                        📋 Copiar
                    </button>
                </div>
                <div class="code-block-content">
                    <div class="code-block-lines">1
2
3
4
5
6
7
8</div>
                    <div class="code-block-code">
                        <pre><code id="ts-code"><span class="code-keyword">interface</span> User {
  id: <span class="code-keyword">number</span>;
  name: <span class="code-keyword">string</span>;
  email: <span class="code-keyword">string</span>;
}

<span class="code-keyword">const</span> user: User = {
  id: <span class="code-number">1</span>, name: <span class="code-string">"João"</span>, email: <span class="code-string">"<EMAIL>"</span>
};</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function copyCode(button, codeId) {
            try {
                const codeElement = document.getElementById(codeId);
                const code = codeElement.textContent;
                await navigator.clipboard.writeText(code);
                
                // Feedback visual
                const originalText = button.innerHTML;
                button.innerHTML = '✅ Copiado';
                button.classList.add('copy-success');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('copy-success');
                }, 2000);
            } catch (err) {
                console.error('Erro ao copiar:', err);
                button.innerHTML = '❌ Erro';
                setTimeout(() => {
                    button.innerHTML = '📋 Copiar';
                }, 2000);
            }
        }
    </script>
</body>
</html>
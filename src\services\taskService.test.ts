import { describe, it, expect, vi, beforeEach } from 'vitest';
import { taskService } from './taskService';
import { supabaseMock, mockSingleGetById, mockSingleCreate, mockSingleUpdate, mockEqList } from '../test-utils/supabaseMock';

vi.mock('@/lib/supabaseClient', () => ({ supabase: supabaseMock }));

describe('taskService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('deve retornar lista de tarefas', async () => {
    mockEqList.mockReturnValueOnce({ data: [{ id: '1', title: 'Tarefa Teste' }], error: null });
    const tarefas = await taskService.list('stage-1');
    expect(tarefas).toEqual([{ id: '1', title: 'Tarefa Teste' }]);
  });

  it('deve retornar tarefa por id', async () => {
    mockSingleGetById.mockReturnValueOnce({ data: { id: '1', title: 'Tarefa Teste' }, error: null });
    const tarefa = await taskService.getById('1');
    expect(tarefa).toEqual({ id: '1', title: 'Tarefa Teste' });
  });

  it('deve criar uma nova tarefa', async () => {
    mockSingleCreate.mockReturnValueOnce({ data: { id: '2', title: 'Nova Tarefa' }, error: null });
    const tarefa = await taskService.create({ title: 'Nova Tarefa' });
    expect(tarefa).toEqual({ id: '2', title: 'Nova Tarefa' });
  });

  it('deve atualizar uma tarefa', async () => {
    mockSingleUpdate.mockReturnValueOnce({ data: { id: '1', title: 'Tarefa Atualizada' }, error: null });
    const tarefa = await taskService.update('1', { title: 'Tarefa Atualizada' });
    expect(tarefa).toEqual({ id: '1', title: 'Tarefa Atualizada' });
  });

  it('deve remover uma tarefa', async () => {
    await expect(taskService.remove('1')).resolves.toBeUndefined();
  });

  it('deve lançar erro se houver erro no supabase', async () => {
    mockEqList.mockReturnValueOnce({ data: null, error: new Error('Erro Supabase') });
    await expect(taskService.list('stage-1')).rejects.toThrow('Erro Supabase');
  });
}); 
# Plano de Refatoração: Content Editor Blocks

## Objetivo
Padronizar, centralizar e simplificar a lógica dos blocos do editor de conteúdo, eliminando duplicidades e facilitando a manutenção e evolução do sistema, **sempre respeitando os limites de tamanho de arquivos e funções definidos nas regras do projeto**.

---

## 1. Centralização de Utilitários

### Novos arquivos a serem criados
- `src/components/features/content-editor/blocks/shared/utils/colorUtils.ts`
  - Funções: `darkenColor`, `lightenColor`, `getPureContrastColor`, `adjustColor`, `isValidHexColor`, `normalizeHexColor`
  - **Se o arquivo crescer além de 200-300 linhas, dividir em submódulos como `colorMath.ts`, `colorValidation.ts`, etc.**
- `src/components/features/content-editor/blocks/shared/utils/iconUtils.ts`
  - Funções: `getLucideIconComponent`, `isLucideIcon`, validação e fallback de ícones
  - **Dividir em submódulos se necessário para manter arquivos pequenos e focados.**

### Arquivos a serem alterados
- Todos os blocos que possuem funções de cor ou ícone duplicadas:
  - `AlertBlock.tsx`
  - `ColoredBlockEditor.tsx`
  - Outros blocos que manipulam cor/ícone diretamente

---

## 2. Unificação de Componentes de UI Duplicados

### Novo arquivo a ser criado
- `src/components/features/content-editor/blocks/shared/ColorPicker.tsx`
  - Um único componente flexível, unificando os existentes
  - **Se o componente crescer demais, dividir em subcomponentes auxiliares.**

### Arquivos a serem alterados
- Remover:
  - `src/components/features/content-editor/blocks/colored-block/ColorPicker.tsx`
  - `src/components/features/content-editor/blocks/shared/config-panel/components/ColorPicker.tsx`
- Atualizar imports em todos os blocos e painéis de configuração para usar o novo `ColorPicker`

---

## 3. Extração de Componente Base de Card

### Novo arquivo a ser criado
- `src/components/features/content-editor/blocks/shared/BlockCardBase.tsx`
  - Props: ícone, título, descrição, conteúdo, ações
  - Responsável pelo layout padrão dos cards de bloco
  - **Dividir em subcomponentes se necessário para manter o arquivo enxuto.**

### Arquivos a serem alterados
- Refatorar todos os blocos para usar o `BlockCardBase`:
  - `AlertBlock.tsx`
  - `ColoredBlockEditor.tsx`
  - `FileBlockEditor.tsx`
  - `ImageBlockEditor.tsx`
  - `QuizBlockEditor.tsx`
  - `VideoBlockEditor.tsx`

---

## 4. Unificação e Expansão dos Componentes de Configuração

### Arquivos a serem alterados
- Garantir uso dos componentes compartilhados:
  - `CardAppearanceConfig.tsx`
  - `ButtonConfig.tsx`
  - `IconAppearanceConfig.tsx`
- Expandir esses componentes para cobrir todos os campos necessários
- Remover lógicas duplicadas dos blocos
- **Dividir componentes grandes em subcomponentes conforme necessário.**

---

## 5. Centralização de Tipos e Props

### Arquivo a ser alterado
- `src/components/features/content-editor/blocks/shared/config-panel/types/index.ts`
  - Consolidar todos os tipos de props/interfaces de configuração
  - Atualizar todos os componentes para usar esses tipos
  - **Dividir tipos em múltiplos arquivos se o volume crescer.**

---

## 6. Centralização de Presets e Constantes

### Arquivos a serem alterados
- Garantir que todos os presets estejam em:
  - `src/components/features/content-editor/blocks/shared/config-panel/constants/block-types/`
- Remover definições duplicadas ou locais dos blocos
- **Dividir presets por tipo de bloco para evitar arquivos grandes.**

---

## 7. Criação de Hooks Utilitários

### Novos arquivos a serem criados
- `src/components/features/content-editor/blocks/shared/hooks/useListField.ts`
- `src/components/features/content-editor/blocks/shared/hooks/useInputField.ts`
  - Hooks para manipulação de listas e campos de input
  - **Dividir hooks por domínio se necessário.**

### Arquivos a serem alterados
- Refatorar blocos que manipulam listas/campos para usar esses hooks

---

## 8. Padronização de Nomenclatura

### Arquivos a serem alterados
- Todos os blocos e componentes de configuração:
  - Padronizar nomes de props (ex: sempre `iconName` para nomes de ícones)
  - Padronizar nomes de funções e variáveis

---

## 9. Testes Automatizados

### Novos arquivos a serem criados
- `src/components/features/content-editor/blocks/shared/__tests__/*.test.tsx`
  - Testes unitários para utilitários e componentes base
- `src/components/features/content-editor/blocks/*/index.test.tsx`
  - Testes de integração para blocos principais
  - **Dividir arquivos de teste por domínio/função para facilitar manutenção.**

---

## 10. Documentação

### Novos arquivos a serem criados/alterados
- `docs/REFATORACAO_CONTENT_EDITOR.md`
  - Detalhamento do novo padrão, exemplos de uso, guidelines de integração
- Atualizar comentários JSDoc/TypeScript nos arquivos de utilitários e componentes

---

## 11. Monitoramento de Tamanho e Modularização

- **Durante toda a refatoração, monitorar o tamanho dos arquivos e funções.**
- **Dividir imediatamente qualquer arquivo que se aproxime de 200-300 linhas, ou função que fique longa, em módulos/subcomponentes menores.**
- **Documentar divisões e justificar no commit quando necessário.**

---

## Resumo de Arquivos/Pastas Envolvidos

### A serem criados
- `shared/utils/colorUtils.ts`
- `shared/utils/iconUtils.ts`
- `shared/ColorPicker.tsx`
- `shared/BlockCardBase.tsx`
- `shared/hooks/useListField.ts`
- `shared/hooks/useInputField.ts`
- `shared/__tests__/*.test.tsx`
- `docs/REFATORACAO_CONTENT_EDITOR.md`

### A serem alterados
- Todos os blocos de `blocks/` (Alert, Colored, File, Image, Quiz, Video)
- Todos os painéis de configuração em `shared/config-panel/components/`
- Tipos em `shared/config-panel/types/index.ts`
- Presets em `shared/config-panel/constants/block-types/`

### A serem eliminados
- `colored-block/ColorPicker.tsx`
- `shared/config-panel/components/ColorPicker.tsx`
- Funções duplicadas de cor/ícone nos blocos

---

## Fluxo de Execução Sugerido

1. Criar utilitários centralizados e hooks
2. Refatorar blocos para usar utilitários e hooks
3. Unificar e adotar o novo ColorPicker
4. Criar e adotar o BlockCardBase
5. Refatorar painéis de configuração
6. Centralizar tipos e presets
7. Padronizar nomenclatura
8. Adicionar testes
9. Atualizar documentação
10. **Monitorar e modularizar arquivos grandes durante todo o processo**

---

**Este documento pode (e deve) ser atualizado conforme a execução da refatoração.**

**Observação:**
- Todas as etapas devem seguir rigorosamente as regras de modularização, tamanho de arquivos e funções, e padronização do projeto, conforme `.project-rules/cursorrules.md`, `.project-rules/software-architecture.md` e demais arquivos de regras. 
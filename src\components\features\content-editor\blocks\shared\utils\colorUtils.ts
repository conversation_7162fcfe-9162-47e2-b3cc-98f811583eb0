// colorUtils.ts
// Funções utilitárias de cor centralizadas para todos os blocos do editor

/**
 * Converte hex para RGB
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Converte RGB para hex
 */
function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

/**
 * Escurece uma cor hex
 */
export function darkenColor(hex: string, amount: number): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;

  const factor = 1 - amount;
  return rgbToHex(
    Math.round(rgb.r * factor),
    Math.round(rgb.g * factor),
    Math.round(rgb.b * factor)
  );
}

/**
 * Clareia uma cor hex
 */
export function lightenColor(hex: string, amount: number): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;

  return rgbToHex(
    Math.round(rgb.r + (255 - rgb.r) * amount),
    Math.round(rgb.g + (255 - rgb.g) * amount),
    Math.round(rgb.b + (255 - rgb.b) * amount)
  );
}

/**
 * Retorna cor de contraste (preto ou branco) baseada na luminância
 */
export function getPureContrastColor(hex: string): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return '#000000';

  // Calcula luminância usando fórmula padrão
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  return luminance > 0.5 ? '#000000' : '#ffffff';
}

/**
 * Ajusta cor (positivo clareia, negativo escurece)
 */
export function adjustColor(hex: string, amount: number): string {
  return amount > 0 ? lightenColor(hex, amount) : darkenColor(hex, Math.abs(amount));
}

/**
 * Valida se é uma cor hex válida
 */
export function isValidHexColor(hex: string): boolean {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
}

/**
 * Normaliza cor hex (garante formato #RRGGBB)
 */
export function normalizeHexColor(hex: string): string {
  if (!hex) return '#000000';

  // Remove # se existir
  hex = hex.replace('#', '');

  // Se for formato curto (#RGB), expande para #RRGGBB
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }

  // Valida e retorna
  const normalized = '#' + hex.toUpperCase();
  return isValidHexColor(normalized) ? normalized : '#000000';
}
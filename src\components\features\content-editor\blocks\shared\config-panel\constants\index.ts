/**
 * Constantes para o painel de configuração de blocos
 * Exportações centralizadas de todas as configurações
 */

// Exportar configurações base
export * from './base-config';

// Exportar todos os presets de tipos de bloco
export * from './block-types';

// Componentes de ícones do Lucide React
import {
  AlertCircle, AlertTriangle, Bell, Flame, ShieldAlert, Flag, Eye, Clock, Megaphone, Zap,
  Info, BookOpen, Lightbulb, MessageSquare, Bookmark, ClipboardList, Search, List, Globe,
  XCircle, AlertOctagon, Ban, Slash, Skull, Bug, ShieldOff, StopCircle, Trash2, Radiation,
  CheckCircle, Award, Star, ThumbsUp, Trophy, Smile, Heart, Rocket, Medal, Feather,
  FileText, File, Image, Images, Play, HelpCircle, User, Camera, Monitor, Layout,
  Frame, Crop, Aperture, Focus, Palette, ArrowRight
} from 'lucide-react';

export const ICON_COMPONENTS: Record<string, React.FC<{ className?: string }>> = {
  // Text icons
  FileText,
  
  // File icons
  File,
  
  // Image icons
  Image,
  Images,
  Camera,
  Monitor,
  User,
  Layout,
  Frame,
  Crop,
  Aperture,
  Focus,
  Palette,
  
  // Video icons
  Play,
  
  // Quiz icons
  HelpCircle,
  
  // Warning icons
  AlertTriangle,
  AlertCircle,
  Bell,
  Flame,
  ShieldAlert,
  Flag,
  Eye,
  Clock,
  Megaphone,
  Zap,
  
  // Info icons
  Info,
  BookOpen,
  Lightbulb,
  MessageSquare,
  Bookmark,
  ClipboardList,
  Search,
  List,
  Globe,
  
  // Error icons
  XCircle,
  AlertOctagon,
  Ban,
  Slash,
  Skull,
  Bug,
  ShieldOff,
  StopCircle,
  Trash2,
  Radiation,
  
  // Success icons
  CheckCircle,
  Award,
  Star,
  ThumbsUp,
  Trophy,
  Smile,
  Heart,
  Rocket,
  Medal,
  Feather,
  
  // Action icons
  ArrowRight,
};

// Presets de estilo para compatibilidade com código existente
export const stylePresets = {
  card: {
    colors: [
      { background: "#ffffff", color: "#1f2937" },
      { background: "#f9fafb", color: "#374151" },
      { background: "#f3f4f6", color: "#4b5563" },
      { background: "#e5e7eb", color: "#6b7280" },
      { background: "#d1d5db", color: "#9ca3af" },
      { background: "#9ca3af", color: "#ffffff" },
      { background: "#6b7280", color: "#ffffff" },
      { background: "#4b5563", color: "#ffffff" },
      { background: "#374151", color: "#ffffff" },
      { background: "#1f2937", color: "#ffffff" },
      { background: "#111827", color: "#ffffff" }
    ],
    borders: [
      { color: "#e5e7eb", width: 1 },
      { color: "#d1d5db", width: 1 },
      { color: "#9ca3af", width: 1 },
      { color: "#6b7280", width: 1 },
      { color: "#4b5563", width: 1 },
      { color: "#374151", width: 1 },
      { color: "#1f2937", width: 1 },
      { color: "#111827", width: 1 },
      { color: "#000000", width: 1 },
      { color: "#ffffff", width: 1 },
      { color: "transparent", width: 0 }
    ],
    shadows: [
      "none",
      "0 1px 2px rgba(0, 0, 0, 0.05)",
      "0 1px 3px rgba(0, 0, 0, 0.1)",
      "0 4px 6px rgba(0, 0, 0, 0.1)",
      "0 10px 15px rgba(0, 0, 0, 0.1)",
      "0 20px 25px rgba(0, 0, 0, 0.1)",
      "0 25px 50px rgba(0, 0, 0, 0.25)"
    ],
    radius: [0, 4, 8, 12, 16, 20, 24, 9999]
  },
  icon: {
    colors: [
      { background: "#ffffff", color: "#1f2937" },
      { background: "#f9fafb", color: "#374151" },
      { background: "#f3f4f6", color: "#4b5563" },
      { background: "#e5e7eb", color: "#6b7280" },
      { background: "#d1d5db", color: "#9ca3af" },
      { background: "#9ca3af", color: "#ffffff" },
      { background: "#6b7280", color: "#ffffff" },
      { background: "#4b5563", color: "#ffffff" },
      { background: "#374151", color: "#ffffff" },
      { background: "#1f2937", color: "#ffffff" },
      { background: "#111827", color: "#ffffff" }
    ],
    borders: [
      { color: "#e5e7eb", width: 1 },
      { color: "#d1d5db", width: 1 },
      { color: "#9ca3af", width: 1 },
      { color: "#6b7280", width: 1 },
      { color: "#4b5563", width: 1 },
      { color: "#374151", width: 1 },
      { color: "#1f2937", width: 1 },
      { color: "#111827", width: 1 },
      { color: "#000000", width: 1 },
      { color: "#ffffff", width: 1 },
      { color: "transparent", width: 0 }
    ],
    shadows: [
      "none",
      "0 1px 2px rgba(0, 0, 0, 0.05)",
      "0 1px 3px rgba(0, 0, 0, 0.1)",
      "0 4px 6px rgba(0, 0, 0, 0.1)",
      "0 10px 15px rgba(0, 0, 0, 0.1)",
      "0 20px 25px rgba(0, 0, 0, 0.1)",
      "0 25px 50px rgba(0, 0, 0, 0.25)"
    ],
    radius: [0, 4, 8, 12, 16, 20, 24, 9999]
  }
};
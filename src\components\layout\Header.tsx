import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { ProjectForm } from '@/components/forms/ProjectForm';
import { Bell, Search, Plus, User, Settings, LogOut, Menu } from 'lucide-react';
import { useAuth } from '@/auth/useAuth';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  onNewProject?: () => void;
  showSidebarButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({ onNewProject, showSidebarButton = false }) => {
  const [notificationCount] = React.useState(3);
  const [showProjectForm, setShowProjectForm] = React.useState(false);
  const { profile, logout, loading } = useAuth();
  const navigate = useNavigate();

  const handleNewProject = () => {
    setShowProjectForm(true);
  };

  // Função utilitária para pegar as iniciais do nome
  function getInitials(name?: string) {
    if (!name) return '';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="max-w-none flex h-16 items-center justify-between px-4">
          {/* Esquerda: menu mobile */}
          <div className="flex items-center flex-shrink-0">
            {showSidebarButton && (
              <Button
                variant="ghost"
                size="icon"
                className="block lg:hidden mr-2"
                onClick={() => window.dispatchEvent(new CustomEvent('openSidebarMobile'))}
                aria-label="Abrir menu"
              >
                <Menu className="w-6 h-6" />
              </Button>
            )}
          </div>
          {/* Centro: logo/título */}
          <div className="flex-1 flex items-center justify-center lg:justify-start">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-project to-project-dark rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">PM</span>
              </div>
              <h1 className="text-xl font-bold text-gray-900">ProjectFlow</h1>
            </div>
          </div>
          {/* Direita: ações */}
          <div className="flex items-center space-x-3 flex-shrink-0 ml-2">
            {/* Notificações */}
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-5 h-5" />
              {notificationCount > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center text-xs"
                >
                  {notificationCount}
                </Badge>
              )}
            </Button>
            {/* Menu do Usuário */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                {React.cloneElement(
                  <Button variant="ghost" className="p-0">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={profile?.avatar_url || "/placeholder.svg"} alt={profile?.name || "Usuário"} />
                      <AvatarFallback className="bg-project-bg text-project">
                        {getInitials(profile?.name) || 'US'}
                      </AvatarFallback>
                    </Avatar>
                  </Button>,
                  { ref: React.useRef() }
                )}
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    <p className="font-medium">{profile?.name || 'Usuário'}</p>
                    <p className="text-sm text-muted-foreground">
                      {profile?.email || ''}
                    </p>
                  </div>
                </div>
                <DropdownMenuItem onClick={() => navigate('/perfil')}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Perfil</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Configurações</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={logout} disabled={loading}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sair</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <ProjectForm
        open={showProjectForm}
        onOpenChange={setShowProjectForm}
        mode="create"
      />
    </>
  );
};

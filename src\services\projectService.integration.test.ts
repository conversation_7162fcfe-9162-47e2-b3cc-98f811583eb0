import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { supabase } from '@/lib/supabaseClient';

let createdProjectId: string | null = null;

// ATENÇÃO: Este teste roda contra o Supabase real (dev/staging). Nunca rode em produção!
describe('projectService (integração Supabase real)', () => {
  beforeAll(async () => {
    // Limpeza prévia se necessário
  });

  afterAll(async () => {
    // Limpeza: remove o projeto criado
    if (createdProjectId) {
      await supabase.from('projects').delete().eq('id', createdProjectId);
    }
  });

  it('deve criar, buscar e remover um projeto real', async () => {
    // Criação
    const { data: created, error: createError } = await supabase.from('projects').insert([{ name: 'Projeto Integração', description: 'Teste integração', status: 'active' }]).single();
    expect(createError).toBeNull();
    expect(created).toBeDefined();
    createdProjectId = created.id;

    // Busca
    const { data: fetched, error: fetchError } = await supabase.from('projects').select('*').eq('id', createdProjectId).single();
    expect(fetchError).toBeNull();
    expect(fetched).toBeDefined();
    expect(fetched.name).toBe('Projeto Integração');

    // Remoção
    const { error: deleteError } = await supabase.from('projects').delete().eq('id', createdProjectId);
    expect(deleteError).toBeNull();
    createdProjectId = null;
  });
}); 
import React from 'react';
import { BlockConfig } from '@/types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ColorPicker } from '../../ColorPicker';
import { normalizeHexColor } from '../utils/colorUtils';
import { presetToBlockConfig, defaultBlockConfig, getDefaultConfigForBlockType } from '../constants/migration';
import { coloredBlockPresets } from '../constants/block-types';
import './CardAppearanceConfig.css';

interface CardAppearanceConfigProps {
  config: BlockConfig;
  onChange: (c: BlockConfig) => void;
  onReset?: () => void;
  blockType?: string;
  coloredVariant?: string;
  setFeedback?: (message: string | null) => void;
}

export const CardAppearanceConfig: React.FC<CardAppearanceConfigProps> = ({
  config,
  onChange,
  onReset,
  blockType,
  coloredVariant,
  setFeedback = () => {},
}) => {

  const handleBgColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...config,
      card: {
        ...config.card,
        backgroundColor: normalizeHexColor(e.target.value)
      }
    });
  };

  const handleResetCard = () => {
    // Usar configuração padrão específica para cada tipo de bloco
    if (blockType === 'colored-block') {
      // Para colored-blocks, usar preset baseado no coloredVariant
      const preset = coloredBlockPresets[coloredVariant || 'info'];
      const cardConfig = presetToBlockConfig(preset);

      onChange({
        ...config, // Manter configurações existentes de ícone e botão
        card: cardConfig.card, // Restaurar apenas configurações do card
      });
    } else if (blockType) {
      // Para outros tipos de bloco, usar configuração padrão específica do tipo
      const typeConfig = getDefaultConfigForBlockType(blockType);

      onChange({
        ...config, // Manter configurações existentes de ícone e botão
        card: typeConfig.card, // Restaurar configurações específicas do tipo
      });
    } else {
      // Fallback para configuração padrão genérica
      onChange({
        ...config,
        card: defaultBlockConfig.card,
      });
    }
  };

  return (
    <div className="appearance-card-panel custom-video-appearance-panel">
      <div className="appearance-header custom-appearance-header">
        <span className="appearance-title custom-appearance-title standard-title">Configurações de Aparência</span>
        <button className="restore-btn custom-restore-btn" type="button" onClick={handleResetCard}>Restaurar Padrão</button>
      </div>
      <hr className="appearance-divider custom-appearance-divider" />
      <div className="appearance-body custom-appearance-body">
        <div className="custom-appearance-grid-v3">
          {/* Linha 1: Cor do Fundo | (vazio) | Formato */}
          <div className="appearance-row">
            <div className="color-picker-group">
              <ColorPicker
                label=""
                value={config.card?.backgroundColor || '#f0fdf4'}
                onChange={color => onChange({
                  ...config,
                  card: { ...config.card, backgroundColor: color }
                })}
              />
              <span className="color-label-inline standard-label">Cor do Fundo</span>
            </div>
            <div></div>
            <div className="format-dropdown-group">
              <span className="format-label custom-format-label standard-label">Formato</span>
              <select className="format-dropdown custom-format-dropdown standard-field" value={config.card?.format || 'rounded'} onChange={e => onChange({ ...config, card: { ...config.card, format: e.target.value as 'rounded' | 'square' | 'pill' } })}>
                <option value="rounded">Arredondado</option>
                <option value="square">Quadrado</option>
                <option value="pill">Pílula</option>
              </select>
            </div>
          </div>
          {/* Linha 2: Cor da Fonte | (vazio) | (vazio) */}
          <div className="appearance-row">
            <div className="color-picker-group">
              <ColorPicker
                label=""
                value={config.card?.font?.color || '#374151'}
                onChange={color => onChange({
                  ...config,
                  card: { ...config.card, font: { ...config.card?.font, color } }
                })}
              />
              <span className="color-label-inline standard-label">Cor da Fonte</span>
            </div>
            <div></div>
            <div></div>
          </div>
          {/* Linha 3: Toggle Borda | Cor da Borda | Largura da Borda */}
          <div className="appearance-row">
            <label className="switch-label custom-switch-label standard-label">
              <input type="checkbox" className="switch-input custom-switch standard-checkbox" checked={!!config.card?.border?.enabled} onChange={e => onChange({ ...config, card: { ...config.card, border: { ...config.card?.border, enabled: e.target.checked } } })} />
              <span>Borda</span>
            </label>
            {config.card?.border?.enabled ? (
              <div className="color-picker-group">
                <ColorPicker
                  label=""
                  value={config.card?.border?.color || '#e5e5e5'}
                  onChange={color => onChange({ ...config, card: { ...config.card, border: { ...config.card?.border, color } } })}
                />
                <span className="color-label-inline standard-label">Cor da Borda</span>
              </div>
            ) : <div></div>}
            {config.card?.border?.enabled ? (
              <div className="slider-group custom-slider-group slider-vertical-label">
                <span className="slider-label custom-slider-label standard-label">Largura da Borda</span>
                <input type="range" min={1} max={8} value={config.card?.border?.width || 1} className="slider custom-slider" onChange={e => onChange({ ...config, card: { ...config.card, border: { ...config.card?.border, width: Number(e.target.value) } } })} />
              </div>
            ) : <div></div>}
          </div>
          {/* Linha 4: Toggle Sombra | (vazio) | Profundidade da Sombra */}
          <div className="appearance-row">
            <label className="switch-label custom-switch-label standard-label">
              <input type="checkbox" className="switch-input custom-switch standard-checkbox" checked={!!config.card?.shadow?.enabled} onChange={e => onChange({ ...config, card: { ...config.card, shadow: { ...config.card?.shadow, enabled: e.target.checked } } })} />
              <span>Sombra</span>
            </label>
            <div></div>
            {config.card?.shadow?.enabled ? (
              <div className="slider-group custom-slider-group slider-vertical-label">
                <span className="slider-label custom-slider-label standard-label">Profundidade da Sombra</span>
                <input type="range" min={1} max={10} value={config.card?.shadow?.depth || 2} className="slider custom-slider" onChange={e => onChange({ ...config, card: { ...config.card, shadow: { ...config.card?.shadow, depth: Number(e.target.value) } } })} />
              </div>
            ) : <div></div>}
          </div>
          {/* Linha 5: Toggle Hover | (vazio) | Profundidade do Hover */}
          <div className="appearance-row">
            <label className="switch-label custom-switch-label standard-label">
              <input type="checkbox" className="switch-input custom-switch standard-checkbox" checked={!!config.card?.hover?.enabled} onChange={e => onChange({ ...config, card: { ...config.card, hover: { ...config.card?.hover, enabled: e.target.checked } } })} />
              <span>Hover</span>
            </label>
            <div></div>
            {config.card?.hover?.enabled ? (
              <div className="slider-group custom-slider-group slider-vertical-label">
                <span className="slider-label custom-slider-label standard-label">Profundidade do Hover</span>
                <input type="range" min={1} max={10} value={config.card?.hover?.shadowDepth || 2} className="slider custom-slider" onChange={e => onChange({ ...config, card: { ...config.card, hover: { ...config.card?.hover, shadowDepth: Number(e.target.value), enabled: true } } })} />
              </div>
            ) : <div></div>}
          </div>
        </div>
      </div>
    </div>
  );
};
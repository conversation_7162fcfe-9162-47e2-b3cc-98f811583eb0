-- TESTE PARA VERIFICAR FORMATO DE UUID EM TASK_CONTENT_BLOCKS
-- Execute este script após corrigir o código

-- 1. Verificar estrutura da tabela task_content_blocks
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'task_content_blocks' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Verificar se existem IDs inválidos na tabela
SELECT 
    id,
    task_id,
    type,
    CASE 
        WHEN id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN '✅ UUID VÁLIDO'
        ELSE '❌ UUID INVÁLIDO'
    END as id_status,
    created_at
FROM task_content_blocks 
WHERE type = 'evidence'
ORDER BY created_at DESC
LIMIT 10;

-- 3. Teste de inserção com UUID válido
SELECT 
    gen_random_uuid() as uuid_valido,
    'evidence' as type,
    'Teste de UUID válido' as status;

-- 4. Verificar se a tarefa de teste existe
SELECT 
    id,
    title,
    'Tarefa encontrada para teste' as status
FROM tasks 
WHERE id = 'd6fb6099-a751-4bc0-acdc-50e1716bc8e8';

-- INTERPRETAÇÃO:
-- ✅ Teste 1: Deve mostrar colunas da tabela, especialmente se 'id' é UUID
-- ✅ Teste 2: Deve mostrar todos UUIDs como válidos após correção
-- ✅ Teste 3: Mostra como gerar UUID válido
-- ✅ Teste 4: Verifica se a tarefa existe para upload de evidência

-- PRÓXIMOS PASSOS APÓS CORREÇÃO:
-- 1. Reiniciar a aplicação (npm run dev)
-- 2. Testar upload de arquivo novamente
-- 3. Verificar se erro 22P02 foi resolvido

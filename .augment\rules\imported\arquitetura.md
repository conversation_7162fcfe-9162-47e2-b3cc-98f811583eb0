---
type: "manual"
---

# Arquitetura da pasta `/src`

Este documento lista todos os arquivos e pastas do diretório `src/`, descrevendo sua função e, quando aplic<PERSON>vel, a qual tela ou funcionalidade do sistema pertencem.

---

## Arquivos principais

- **App.tsx**: Componente raiz da aplicação React. Faz o roteamento e renderiza o layout principal.
- **App.css**: Estilos globais da aplicação.
- **index.css**: Estilos globais, incluindo resets e utilidades.
- **main.tsx**: Ponto de entrada da aplicação React.
- **vite-env.d.ts**: Tipagens globais para o Vite.

---

## auth/
Autenticação e controle de acesso.
- **AuthProvider.tsx**: Provider de contexto de autenticação.
- **AuthRoutes.tsx**: Rotas protegidas por autenticação.
- **LoginForm.tsx**: Tela de login.
- **RegisterForm.tsx**: Tela de cadastro.
- **RequireAuth.tsx**: HOC para proteger rotas.
- **ResetPasswordForm.tsx**: Tela de redefinição de senha.
- **useAuth.ts**: Hook para acessar o contexto de autenticação.
- **UserMenu.tsx**: Menu do usuário logado (avatar, logout, etc).

---

## components/
Componentes reutilizáveis e de interface.

### features/
Funcionalidades principais e blocos do editor:
- **Dashboard.tsx**: Tela principal do dashboard.
- **ProjectFilters.tsx**: Filtros de projetos.
- **RichContentEditor.tsx**: Editor de conteúdo em blocos (texto, vídeo, quiz, etc).
- **toolbar-animations.css**: Animações para toolbars do editor.

#### blocks/
Blocos de conteúdo do editor:
- **BlockCardIcon.tsx**: Ícone e layout dos cards de bloco.
- **BlockConfigPanel.tsx**: Painel de configuração visual dos blocos.
- **ColoredBlockEditor.tsx**: Editor de bloco colorido.
- **ColorPicker.tsx**: Componente de seleção de cor.
- **ContentSection.tsx**: Seção de conteúdo (usada em projetos/etapas).
- **DragDropPlugin.tsx**: Plugin de drag-and-drop para blocos.
- **FileBlockEditor.tsx**: Editor de bloco de arquivo.
- **IconPicker.tsx**: Seletor de ícones.
- **ImageBlockEditor.tsx**: Editor de bloco de imagem.
- **QuizBlockEditor.tsx**: Editor de bloco de quiz.
- **TableActionMenuPlugin.tsx**: Plugin de menu de ações em tabelas.
- **TableContextMenu.tsx**: Menu de contexto para tabelas.
- **VideoBlockCard.tsx**: Card de preview de vídeo.
- **VideoBlockEditor.tsx**: Editor de bloco de vídeo.
- **editor-styles.css**: Estilos específicos do editor.

##### blocks/text/
Plugins e componentes de texto do editor:
- **TextBlockEditor.tsx**: Editor de bloco de texto.
- **TextToolbar.tsx**: Toolbar de formatação de texto.
- **CodeBlockComponent.tsx**: Bloco de código.
- **CodeHighlightPlugin.tsx**: Plugin de destaque de sintaxe.
- **AutoFocusPlugin.tsx**: Plugin de foco automático.
- **AutoLinkPlugin.tsx**: Plugin de autolink.
- **EmojiPickerButton.tsx**: Botão de emojis.
- **FloatingTextFormatToolbarPlugin.tsx**: Toolbar flutuante de formatação.
- **FloatingToolbar.tsx**: Toolbar flutuante.
- **HashtagPlugin.tsx**: Plugin de hashtags.
- **HighlightPlugin.tsx**: Plugin de destaque de texto.
- **LineBreakPlugin.tsx**: Plugin de quebra de linha.
- **README.md**: Documentação dos plugins de texto.
- **index.ts**: Exporta plugins de texto.

### forms/
Formulários reutilizáveis:
- **FileUploadDialog.tsx**: Diálogo de upload de arquivos.
- **ProjectForm.tsx**: Formulário de criação/edição de projeto.
- **StageForm.tsx**: Formulário de criação/edição de etapa.
- **TaskForm.tsx**: Formulário de criação/edição de tarefa.
- **UserAutocomplete.tsx**: Autocomplete de usuários.
- **UserForm.tsx**: Formulário de criação/edição de usuário.

### layout/
Componentes de layout e navegação:
- **AppLayout.tsx**: Layout principal da aplicação.
- **Header.tsx**: Cabeçalho fixo.
- **SidebarStandalone.tsx**: Menu lateral (sidebar).

### ui/
Componentes de interface (UI) reutilizáveis: botões, cards, inputs, tabelas, tooltips, etc. (cada arquivo implementa um componente visual genérico usado em várias telas).

---

## hooks/
Hooks customizados:
- **use-mobile.tsx**: Detecta se o dispositivo é mobile.
- **use-toast.ts**: Hook para toasts/avisos.

---

## lib/
Bibliotecas utilitárias:
- **supabaseClient.ts**: Instância/configuração do Supabase.
- **utils.ts**: Funções utilitárias diversas.

---

## pages/
Páginas principais do sistema:
- **Index.tsx**: Tela inicial (dashboard).
- **NotFound.tsx**: Tela de 404.
- **ProjectDetails.tsx**: Detalhes de um projeto (layout 1).
- **ProjectDetails2.tsx**: Detalhes de um projeto (layout 2).
- **ProjectsList.tsx**: Listagem de projetos.
- **StageDetails.tsx**: Detalhes de uma etapa.
- **TaskDetails.tsx**: Detalhes de uma tarefa.
- **UserManagement.tsx**: Gestão de usuários.
- **UserProfile.tsx**: Perfil do usuário logado.

#### pages/StageDetails/
Componentes auxiliares para detalhes de etapa:
- **Breadcrumb.tsx**: Breadcrumb de navegação.
- **StageContent.tsx**: Conteúdo da etapa.
- **StageHeader.tsx**: Header da etapa.
- **TaskList.tsx**: Lista de tarefas da etapa.

#### pages/TaskDetails/
Componentes auxiliares para detalhes de tarefa:
- **TaskBreadcrumb.tsx**: Breadcrumb da tarefa.
- **TaskComments.tsx**: Comentários da tarefa.
- **TaskContentTabs.tsx**: Abas de conteúdo da tarefa.
- **TaskControlPanel.tsx**: Painel de controle da tarefa.
- **TaskEvidenceList.tsx**: Lista de evidências da tarefa.
- **TaskHeader.tsx**: Header da tarefa.
- **TaskQuickActions.tsx**: Ações rápidas da tarefa.
- **TaskTeamPanel.tsx**: Painel da equipe da tarefa.

---

## services/
Serviços de acesso a dados e integrações:
- **authService.ts**: Serviço de autenticação.
- **projectService.ts**: Serviço de projetos.
- **stageService.ts**: Serviço de etapas.
- **taskService.ts**: Serviço de tarefas.
- **userService.ts**: Serviço de usuários.
- **supabaseConnectionCheck.ts**: Verificação de conexão com Supabase.
- **projectService.integration.test.ts**, **projectService.test.ts**, **stageService.test.ts**, **taskService.test.ts**: Testes automatizados dos serviços.

---

## store/
Gerenciamento de estado global:
- **useProjectStore.ts**: Zustand store para projetos.

---

## test-utils/
Utilitários para testes:
- **README.md**: Documentação dos mocks de teste.
- **supabaseMock.ts**: Mock do Supabase para testes.

---

## types/
Definições de tipos TypeScript:
- **index.ts**: Tipos globais do sistema (projetos, tarefas, usuários, etc).

</rewritten_file> 
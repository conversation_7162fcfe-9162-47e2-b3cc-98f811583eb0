import React, { ForwardRefExoticComponent } from 'react';
import { AlertPreset, ALERT_PRESETS, AlertLayout } from '../shared/config-panel/constants/block-types';
import * as LucideIcons from 'lucide-react';
import { adjustColor } from '../shared/utils/colorUtils';
import { isLucideIcon } from '../shared/utils/iconUtils';

interface AlertBlockProps {
  presetId: string;
  title: string;
  message: string;
  icon: string;
  actionLabel?: string;
  onAction?: () => void;
  layout?: AlertLayout;
  showPresetId?: boolean;
  actionUrl?: string;
}

export const AlertBlock: React.FC<AlertBlockProps> = ({
  presetId,
  title,
  message,
  icon,
  actionLabel,
  onAction,
  layout,
  showPresetId,
  actionUrl,
}) => {
  const preset = ALERT_PRESETS.find(p => p.id === presetId);
  if (!preset) return null;
  let IconComp = LucideIcons[icon as keyof typeof LucideIcons];
  if (!isLucideIcon(IconComp)) {
    IconComp = LucideIcons['Info'];
  }
  const isOutline = preset.style === 'outline';
  const isSolid = preset.style === 'solid';
  const isSoft = preset.style === 'soft';
  const border = isOutline ? `1.5px solid ${preset.borderColor || preset.color}` : 'none';
  const bg = isSolid ? preset.color : isOutline ? '#fff' : preset.color;
  const textColor = isSolid ? '#fff' : preset.borderColor || preset.color;
  const iconBg = isSolid ? 'rgba(255,255,255,0.15)' : 'transparent';
  const iconColor = isSolid ? '#fff' : preset.borderColor || preset.color;
  const borderRadius = '10px';
  const padding = layout === 'compact' ? '8px 16px' : '16px 24px';
  const iconSize = layout === 'compact' ? 20 : 28;

  // Novo layout: icon-boxed
  const isIconBoxed = (preset.layout === 'icon-boxed' || layout === 'icon-boxed');
  const borderBoxed = isIconBoxed ? `1.5px solid ${preset.borderColor || preset.color}` : border;
  const bgBoxed = isIconBoxed ? '#fff' : bg;
  const textColorBoxed = isIconBoxed ? '#222' : textColor;
  const iconBgBoxed = isIconBoxed ? (preset.borderColor || preset.color) : iconBg;
  const iconColorBoxed = isIconBoxed ? '#fff' : iconColor;
  const iconBoxedSize = isIconBoxed ? 28 : iconSize;
  const iconBoxedRadius = isIconBoxed ? '8px' : '50%';
  const paddingBoxed = isIconBoxed ? '14px 20px' : padding;

  // Novo layout: icon-corner
  const isIconCorner = (preset.layout === 'icon-corner' || layout === 'icon-corner');
  const borderCorner = isIconCorner ? `1.5px solid ${preset.borderColor || preset.color}` : borderBoxed;
  const bgCorner = isIconCorner ? preset.color : bgBoxed;
  const textColorCorner = isIconCorner ? '#222' : textColorBoxed;
  const paddingCorner = isIconCorner ? '0' : paddingBoxed;
  const iconCornerBg = isIconCorner ? (preset.borderColor || preset.color) : iconBgBoxed;
  const iconCornerColor = isIconCorner ? '#fff' : iconColorBoxed;
  const iconCornerSize = isIconCorner ? 28 : iconBoxedSize;
  const iconCornerRadius = isIconCorner ? '0' : iconBoxedRadius;
  const iconCornerWidth = isIconCorner ? 48 : iconBoxedSize + 12;

  // Novo layout: icon-bubble
  const isIconBubble = (preset.layout === 'icon-bubble' || layout === 'icon-bubble');
  const borderBubble = isIconBubble ? `1.5px solid ${preset.borderColor || preset.color}` : borderCorner;
  const iconBubbleBg = isIconBubble ? (preset.borderColor || preset.color) : iconCornerBg;
  const iconBubbleColor = isIconBubble ? '#fff' : iconCornerColor;
  const iconBubbleSize = isIconBubble ? 28 : iconCornerSize;
  const iconBubbleWidth = isIconBubble ? 48 : iconCornerWidth;
  const textColorBubble = isIconBubble ? '#222' : textColorCorner;

  // Novo layout: bottom-bar
  const isBottomBar = (preset.layout === 'bottom-bar' || layout === 'bottom-bar');
  const borderBottomBar = isBottomBar ? `0 0 6px 0` : undefined;
  const borderColorBottomBar = isBottomBar ? (preset.borderColor || preset.color) : undefined;
  const bgBottomBar = isBottomBar ? preset.color : bg;
  const textColorBottomBar = isBottomBar ? '#222' : textColor;
  const iconBottomBarColor = isBottomBar ? (preset.borderColor || preset.color) : iconColor;
  const iconBottomBarSize = isBottomBar ? 24 : iconSize;
  const paddingBottomBar = isBottomBar ? '14px 20px' : padding;

  // Novo layout: left-bar
  const isLeftBar = (preset.layout === 'left-bar' || layout === 'left-bar');
  const borderLeftBar = isLeftBar ? `6px solid ${preset.borderColor || preset.color}` : border;
  const bgLeftBar = isLeftBar ? preset.color : bg;
  const textColorLeftBar = isLeftBar ? '#222' : textColor;
  const iconLeftBarColor = isLeftBar ? (preset.borderColor || preset.color) : iconColor;
  const iconLeftBarSize = isLeftBar ? 24 : iconSize;
  const paddingLeftBar = isLeftBar ? '16px 24px' : padding;

  // Badge do presetId no modo edição (para todos os layouts)
  const renderPresetIdBadge = () => onAction && (
    <span style={{
      position: 'absolute',
      top: 6,
      right: 12,
      background: '#f3f4f6',
      color: '#6b7280',
      fontSize: 11,
      padding: '2px 8px',
      borderRadius: 8,
      fontWeight: 500,
      letterSpacing: 0.2,
      zIndex: 10,
      pointerEvents: 'none',
      opacity: 0.85,
    }}>
      {presetId}
    </span>
  );

  // Definir cor base do botão igual à cor do ícone
  const buttonBgColor = preset.borderColor || preset.color;
  const buttonTextColor = '#fff';
  // Se o fundo do alerta for muito claro, inverter para texto colorido e fundo branco
  const isBgLight = (bgLeftBar || bg || bgBoxed || bgCorner || bgBottomBar || '#fff').toLowerCase() === '#fff' || (preset.style === 'outline' || preset.style === 'soft');
  const buttonFinalBg = isBgLight ? '#fff' : buttonBgColor;
  const buttonFinalText = isBgLight ? buttonBgColor : buttonTextColor;

  // Função utilitária para escurecer ou clarear cor no hover
  function adjustColor(hex: string, amount: number) {
    let col = hex.replace('#', '');
    if (col.length === 3) col = col.split('').map(x => x + x).join('');
    const num = parseInt(col, 16);
    let r = (num >> 16) + amount;
    let g = ((num >> 8) & 0x00FF) + amount;
    let b = (num & 0x0000FF) + amount;
    r = Math.max(Math.min(255, r), 0);
    g = Math.max(Math.min(255, g), 0);
    b = Math.max(Math.min(255, b), 0);
    return `#${(r << 16 | g << 8 | b).toString(16).padStart(6, '0')}`;
  }
  const buttonHoverBg = isBgLight ? adjustColor(buttonBgColor, 32) : adjustColor(buttonBgColor, -32);
  const buttonHoverText = isBgLight ? buttonHoverBg : '#fff';
  const buttonBoxShadow = '0 2px 8px rgba(0,0,0,0.10)';

  if (isBottomBar) {
    return (
      <div
        role="alert"
        aria-live="polite"
        style={{
          background: bgBottomBar,
          border: `1.5px solid ${preset.color}00`, // borda invisível para manter radius
          borderRadius,
          boxShadow: isSoft ? `0 2px 8px ${preset.color}22` : 'none',
          minHeight: 48,
          width: '100%',
          fontFamily: 'inherit',
          padding: paddingBottomBar,
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
        }}
        tabIndex={0}
      >
        {/* Borda inferior grossa */}
        <div style={{
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 0,
          height: 6,
          background: borderColorBottomBar,
          borderBottomLeftRadius: borderRadius,
          borderBottomRightRadius: borderRadius,
          zIndex: 1,
        }} />
        <span
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'transparent',
            borderRadius: '50%',
            width: iconBottomBarSize + 12,
            height: iconBottomBarSize + 12,
            marginRight: 12,
            zIndex: 2,
          }}
          aria-hidden="true"
        >
          {isLucideIcon(IconComp)
            ? React.createElement(IconComp, { size: iconBottomBarSize, color: iconBottomBarColor })
            : <span style={{ width: iconBottomBarSize, height: iconBottomBarSize }} />}
        </span>
        <div style={{ flex: 1, minWidth: 0, zIndex: 2 }}>
          <div style={{ fontWeight: preset.titleBold ? 700 : 500, fontSize: 17, marginBottom: 2, color: textColorBottomBar, textTransform: 'uppercase', letterSpacing: 0.5 }}>
            {title}
          </div>
          <div style={{ fontSize: 15, color: textColorBottomBar, opacity: 0.95, whiteSpace: 'pre-line', wordBreak: 'break-word' }}>{message}</div>
        </div>
        {/* Botão de ação: <a> se actionUrl, <button> se não */}
        {actionLabel && (actionUrl ? (
          <a
            href={actionUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              marginLeft: 16,
              background: buttonFinalBg,
              color: buttonFinalText,
              border: 'none',
              borderRadius: 6,
              padding: '6px 16px',
              fontWeight: 600,
              fontSize: 14,
              cursor: 'pointer',
              transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
              zIndex: 2,
              borderWidth: isBgLight ? 1.5 : 0,
              borderStyle: isBgLight ? 'solid' : 'none',
              borderColor: isBgLight ? buttonBgColor : undefined,
              boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
              display: 'inline-block',
              textDecoration: 'none',
            }}
            onMouseOver={e => {
              (e.currentTarget as HTMLAnchorElement).style.background = buttonHoverBg;
              (e.currentTarget as HTMLAnchorElement).style.setProperty('color', '#fff', 'important');
              (e.currentTarget as HTMLAnchorElement).style.boxShadow = buttonBoxShadow;
            }}
            onMouseOut={e => {
              (e.currentTarget as HTMLAnchorElement).style.background = buttonFinalBg;
              (e.currentTarget as HTMLAnchorElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
              (e.currentTarget as HTMLAnchorElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
            }}
            tabIndex={0}
          >
            {actionLabel}
          </a>
        ) : (
          <button
            onClick={onAction}
            style={{
              marginLeft: 16,
              background: buttonFinalBg,
              color: buttonFinalText,
              border: 'none',
              borderRadius: 6,
              padding: '6px 16px',
              fontWeight: 600,
              fontSize: 14,
              cursor: 'pointer',
              transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
              zIndex: 2,
              borderWidth: isBgLight ? 1.5 : 0,
              borderStyle: isBgLight ? 'solid' : 'none',
              borderColor: isBgLight ? buttonBgColor : undefined,
              boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
            }}
            onMouseOver={e => {
              (e.currentTarget as HTMLButtonElement).style.background = buttonHoverBg;
              (e.currentTarget as HTMLButtonElement).style.setProperty('color', '#fff', 'important');
              (e.currentTarget as HTMLButtonElement).style.boxShadow = buttonBoxShadow;
            }}
            onMouseOut={e => {
              (e.currentTarget as HTMLButtonElement).style.background = buttonFinalBg;
              (e.currentTarget as HTMLButtonElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
              (e.currentTarget as HTMLButtonElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
            }}
            tabIndex={0}
          >
            {actionLabel}
          </button>
        ))}
      </div>
    );
  }

  if (isIconBubble) {
    return (
      <div
        role="alert"
        aria-live="polite"
        style={{
          background: '#fff',
          border: borderBubble,
          borderRadius,
          display: 'flex',
          alignItems: 'stretch',
          minHeight: 56,
          width: '100%',
          fontFamily: 'inherit',
          boxShadow: isSoft ? `0 2px 8px ${preset.color}22` : 'none',
          overflow: 'hidden',
        }}
        tabIndex={0}
      >
        <div style={{ position: 'relative', width: iconBubbleWidth, minWidth: iconBubbleWidth, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', background: iconBubbleBg, borderTopLeftRadius: borderRadius, borderBottomLeftRadius: borderRadius }}>
          {isLucideIcon(IconComp)
            ? React.createElement(IconComp, { size: iconBubbleSize, color: iconBubbleColor })
            : <span style={{ width: iconBubbleSize, height: iconBubbleSize }} />}
          {/* Setinha (triângulo) */}
          <div style={{
            position: 'absolute',
            right: -12,
            top: '50%',
            transform: 'translateY(-50%)',
            width: 0,
            height: 0,
            borderTop: '12px solid transparent',
            borderBottom: '12px solid transparent',
            borderLeft: `12px solid ${iconBubbleBg}`,
            zIndex: 2,
          }} />
        </div>
        <div style={{ flex: 1, minWidth: 0, display: 'flex', flexDirection: 'column', justifyContent: 'center', height: '100%', padding: '16px 20px', borderTopRightRadius: borderRadius, borderBottomRightRadius: borderRadius }}>
          <div style={{ fontWeight: preset.titleBold ? 700 : 500, fontSize: 17, marginBottom: 2, color: textColorBubble, textTransform: 'uppercase', letterSpacing: 0.5 }}>
            {title}
          </div>
          <div style={{ fontSize: 15, color: textColorBubble, opacity: 0.95, whiteSpace: 'pre-line', wordBreak: 'break-word' }}>{message}</div>
        </div>
        {/* Botão de ação: <a> se actionUrl, <button> se não */}
        {actionLabel && (actionUrl ? (
          <a
            href={actionUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              marginLeft: 16,
              background: buttonFinalBg,
              color: buttonFinalText,
              border: 'none',
              borderRadius: 6,
              padding: '6px 16px',
              fontWeight: 600,
              fontSize: 14,
              cursor: 'pointer',
              transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
              zIndex: 2,
              borderWidth: isBgLight ? 1.5 : 0,
              borderStyle: isBgLight ? 'solid' : 'none',
              borderColor: isBgLight ? buttonBgColor : undefined,
              boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
              display: 'inline-block',
              textDecoration: 'none',
            }}
            onMouseOver={e => {
              (e.currentTarget as HTMLAnchorElement).style.background = buttonHoverBg;
              (e.currentTarget as HTMLAnchorElement).style.setProperty('color', '#fff', 'important');
              (e.currentTarget as HTMLAnchorElement).style.boxShadow = buttonBoxShadow;
            }}
            onMouseOut={e => {
              (e.currentTarget as HTMLAnchorElement).style.background = buttonFinalBg;
              (e.currentTarget as HTMLAnchorElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
              (e.currentTarget as HTMLAnchorElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
            }}
            tabIndex={0}
          >
            {actionLabel}
          </a>
        ) : (
          <button
            onClick={onAction}
            style={{
              marginLeft: 16,
              background: buttonFinalBg,
              color: buttonFinalText,
              border: 'none',
              borderRadius: 6,
              padding: '6px 16px',
              fontWeight: 600,
              fontSize: 14,
              cursor: 'pointer',
              transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
              zIndex: 2,
              borderWidth: isBgLight ? 1.5 : 0,
              borderStyle: isBgLight ? 'solid' : 'none',
              borderColor: isBgLight ? buttonBgColor : undefined,
              boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
            }}
            onMouseOver={e => {
              (e.currentTarget as HTMLButtonElement).style.background = buttonHoverBg;
              (e.currentTarget as HTMLButtonElement).style.setProperty('color', '#fff', 'important');
              (e.currentTarget as HTMLButtonElement).style.boxShadow = buttonBoxShadow;
            }}
            onMouseOut={e => {
              (e.currentTarget as HTMLButtonElement).style.background = buttonFinalBg;
              (e.currentTarget as HTMLButtonElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
              (e.currentTarget as HTMLButtonElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
            }}
            tabIndex={0}
          >
            {actionLabel}
          </button>
        ))}
      </div>
    );
  }

  if (isIconCorner) {
    return (
      <div
        role="alert"
        aria-live="polite"
        style={{
          background: '#fff',
          border: borderCorner,
          borderRadius,
          display: 'flex',
          alignItems: 'stretch',
          minHeight: 56,
          width: '100%',
          fontFamily: 'inherit',
          boxShadow: isSoft ? `0 2px 8px ${preset.color}22` : 'none',
          overflow: 'hidden',
        }}
        tabIndex={0}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            background: iconCornerBg,
            width: iconCornerWidth,
            minWidth: iconCornerWidth,
            borderTopLeftRadius: borderRadius,
            borderBottomLeftRadius: borderRadius,
          }}
          aria-hidden="true"
        >
          {isLucideIcon(IconComp)
            ? React.createElement(IconComp, { size: iconCornerSize, color: iconCornerColor })
            : <span style={{ width: iconCornerSize, height: iconCornerSize }} />}
        </div>
        <div style={{ flex: 1, minWidth: 0, display: 'flex', flexDirection: 'column', justifyContent: 'center', height: '100%', padding: '16px 20px', borderTopRightRadius: borderRadius, borderBottomRightRadius: borderRadius }}>
          <div style={{ fontWeight: preset.titleBold ? 700 : 500, fontSize: 17, marginBottom: 2, color: textColorCorner, textTransform: 'uppercase', letterSpacing: 0.5 }}>
            {title}
          </div>
          <div style={{ fontSize: 15, color: textColorCorner, opacity: 0.95, whiteSpace: 'pre-line', wordBreak: 'break-word' }}>{message}</div>
        </div>
        {/* Botão de ação: <a> se actionUrl, <button> se não */}
        {actionLabel && (actionUrl ? (
          <a
            href={actionUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              marginLeft: 16,
              background: buttonFinalBg,
              color: buttonFinalText,
              border: 'none',
              borderRadius: 6,
              padding: '6px 16px',
              fontWeight: 600,
              fontSize: 14,
              cursor: 'pointer',
              transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
              zIndex: 2,
              borderWidth: isBgLight ? 1.5 : 0,
              borderStyle: isBgLight ? 'solid' : 'none',
              borderColor: isBgLight ? buttonBgColor : undefined,
              boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
              display: 'inline-block',
              textDecoration: 'none',
            }}
            onMouseOver={e => {
              (e.currentTarget as HTMLAnchorElement).style.background = buttonHoverBg;
              (e.currentTarget as HTMLAnchorElement).style.setProperty('color', '#fff', 'important');
              (e.currentTarget as HTMLAnchorElement).style.boxShadow = buttonBoxShadow;
            }}
            onMouseOut={e => {
              (e.currentTarget as HTMLAnchorElement).style.background = buttonFinalBg;
              (e.currentTarget as HTMLAnchorElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
              (e.currentTarget as HTMLAnchorElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
            }}
            tabIndex={0}
          >
            {actionLabel}
          </a>
        ) : (
          <button
            onClick={onAction}
            style={{
              marginLeft: 16,
              background: buttonFinalBg,
              color: buttonFinalText,
              border: 'none',
              borderRadius: 6,
              padding: '6px 16px',
              fontWeight: 600,
              fontSize: 14,
              cursor: 'pointer',
              transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
              zIndex: 2,
              borderWidth: isBgLight ? 1.5 : 0,
              borderStyle: isBgLight ? 'solid' : 'none',
              borderColor: isBgLight ? buttonBgColor : undefined,
              boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
            }}
            onMouseOver={e => {
              (e.currentTarget as HTMLButtonElement).style.background = buttonHoverBg;
              (e.currentTarget as HTMLButtonElement).style.setProperty('color', '#fff', 'important');
              (e.currentTarget as HTMLButtonElement).style.boxShadow = buttonBoxShadow;
            }}
            onMouseOut={e => {
              (e.currentTarget as HTMLButtonElement).style.background = buttonFinalBg;
              (e.currentTarget as HTMLButtonElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
              (e.currentTarget as HTMLButtonElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
            }}
            tabIndex={0}
          >
            {actionLabel}
          </button>
        ))}
      </div>
    );
  }

  if (isLeftBar) {
    return (
      <>
        <div
          role="alert"
          aria-live="polite"
          style={{
            background: bgLeftBar,
            borderLeft: borderLeftBar,
            borderRadius,
            boxShadow: isSoft ? `0 2px 8px ${preset.color}22` : 'none',
            minHeight: 48,
            width: '100%',
            fontFamily: 'inherit',
            padding: paddingLeftBar,
            display: 'flex',
            alignItems: 'center',
            position: 'relative',
          }}
          tabIndex={0}
        >
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'transparent',
              borderRadius: '50%',
              width: iconLeftBarSize + 12,
              height: iconLeftBarSize + 12,
              marginRight: 12,
              zIndex: 2,
            }}
            aria-hidden="true"
          >
            {isLucideIcon(IconComp)
              ? React.createElement(IconComp, { size: iconLeftBarSize, color: iconLeftBarColor })
              : <span style={{ width: iconLeftBarSize, height: iconLeftBarSize }} />}
          </span>
          <div style={{ flex: 1, minWidth: 0, zIndex: 2 }}>
            <div style={{ fontWeight: preset.titleBold ? 700 : 500, fontSize: 17, marginBottom: 2, color: textColorLeftBar, textTransform: 'capitalize', letterSpacing: 0.5 }}>
              {title}
            </div>
            <div style={{ fontSize: 15, color: textColorLeftBar, opacity: 0.95, whiteSpace: 'pre-line', wordBreak: 'break-word' }}>{message}</div>
          </div>
          {/* Botão de ação: <a> se actionUrl, <button> se não */}
          {actionLabel && (actionUrl ? (
            <a
              href={actionUrl}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                marginLeft: 16,
                background: buttonFinalBg,
                color: buttonFinalText,
                border: 'none',
                borderRadius: 6,
                padding: '6px 16px',
                fontWeight: 600,
                fontSize: 14,
                cursor: 'pointer',
                transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
                zIndex: 2,
                borderWidth: isBgLight ? 1.5 : 0,
                borderStyle: isBgLight ? 'solid' : 'none',
                borderColor: isBgLight ? buttonBgColor : undefined,
                boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
                display: 'inline-block',
                textDecoration: 'none',
              }}
              onMouseOver={e => {
                (e.currentTarget as HTMLAnchorElement).style.background = buttonHoverBg;
                (e.currentTarget as HTMLAnchorElement).style.setProperty('color', '#fff', 'important');
                (e.currentTarget as HTMLAnchorElement).style.boxShadow = buttonBoxShadow;
              }}
              onMouseOut={e => {
                (e.currentTarget as HTMLAnchorElement).style.background = buttonFinalBg;
                (e.currentTarget as HTMLAnchorElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
                (e.currentTarget as HTMLAnchorElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
              }}
              tabIndex={0}
            >
              {actionLabel}
            </a>
          ) : (
            <button
              onClick={onAction}
              style={{
                marginLeft: 16,
                background: buttonFinalBg,
                color: buttonFinalText,
                border: 'none',
                borderRadius: 6,
                padding: '6px 16px',
                fontWeight: 600,
                fontSize: 14,
                cursor: 'pointer',
                transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
                zIndex: 2,
                borderWidth: isBgLight ? 1.5 : 0,
                borderStyle: isBgLight ? 'solid' : 'none',
                borderColor: isBgLight ? buttonBgColor : undefined,
                boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
              }}
              onMouseOver={e => {
                (e.currentTarget as HTMLButtonElement).style.background = buttonHoverBg;
                (e.currentTarget as HTMLButtonElement).style.setProperty('color', '#fff', 'important');
                (e.currentTarget as HTMLButtonElement).style.boxShadow = buttonBoxShadow;
              }}
              onMouseOut={e => {
                (e.currentTarget as HTMLButtonElement).style.background = buttonFinalBg;
                (e.currentTarget as HTMLButtonElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
                (e.currentTarget as HTMLButtonElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
              }}
              tabIndex={0}
            >
              {actionLabel}
            </button>
          ))}
        </div>
        {showPresetId && (
          <div style={{ marginTop: 8, fontSize: 12, color: '#6b7280', fontStyle: 'italic' }}>
            Modelo selecionado: <b>{presetId}</b>
          </div>
        )}
      </>
    );
  }

  return (
    <div
      role="alert"
      aria-live="polite"
      style={{
        background: bgBoxed,
        border: borderBoxed,
        borderRadius,
        padding: paddingBoxed,
        display: 'flex',
        alignItems: 'center',
        gap: 16,
        boxShadow: isSoft ? `0 2px 8px ${preset.color}22` : 'none',
        color: textColorBoxed,
        minHeight: 48,
        width: '100%',
        fontFamily: 'inherit',
      }}
      tabIndex={0}
    >
      {renderPresetIdBadge()}
      <span
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: iconBgBoxed,
          borderRadius: iconBoxedRadius,
          width: iconBoxedSize + 12,
          height: iconBoxedSize + 12,
          minWidth: iconBoxedSize + 12,
        }}
        aria-hidden="true"
      >
        {isLucideIcon(IconComp)
          ? React.createElement(IconComp, { size: iconBoxedSize, color: iconColorBoxed })
          : <span style={{ width: iconBoxedSize, height: iconBoxedSize }} />}
      </span>
      <div style={{ flex: 1, minWidth: 0 }}>
        <div style={{ fontWeight: preset.titleBold ? 700 : 500, fontSize: layout === 'compact' ? 15 : 17, marginBottom: 2, color: textColorBoxed, textTransform: 'uppercase', letterSpacing: 0.5 }}>
          {title}
        </div>
        <div style={{ fontSize: layout === 'compact' ? 13 : 15, color: textColorBoxed, opacity: 0.95, whiteSpace: 'pre-line', wordBreak: 'break-word' }}>{message}</div>
      </div>
      {/* Botão de ação: <a> se actionUrl, <button> se não */}
      {actionLabel && (actionUrl ? (
        <a
          href={actionUrl}
          target="_blank"
          rel="noopener noreferrer"
          style={{
            marginLeft: 16,
            background: buttonFinalBg,
            color: buttonFinalText,
            border: 'none',
            borderRadius: 6,
            padding: '6px 16px',
            fontWeight: 600,
            fontSize: 14,
            cursor: 'pointer',
            transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
            zIndex: 2,
            borderWidth: isBgLight ? 1.5 : 0,
            borderStyle: isBgLight ? 'solid' : 'none',
            borderColor: isBgLight ? buttonBgColor : undefined,
            boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
            display: 'inline-block',
            textDecoration: 'none',
          }}
          onMouseOver={e => {
            (e.currentTarget as HTMLAnchorElement).style.background = buttonHoverBg;
            (e.currentTarget as HTMLAnchorElement).style.setProperty('color', '#fff', 'important');
            (e.currentTarget as HTMLAnchorElement).style.boxShadow = buttonBoxShadow;
          }}
          onMouseOut={e => {
            (e.currentTarget as HTMLAnchorElement).style.background = buttonFinalBg;
            (e.currentTarget as HTMLAnchorElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
            (e.currentTarget as HTMLAnchorElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
          }}
          tabIndex={0}
        >
          {actionLabel}
        </a>
      ) : (
        <button
          onClick={onAction}
          style={{
            marginLeft: 16,
            background: buttonFinalBg,
            color: buttonFinalText,
            border: 'none',
            borderRadius: 6,
            padding: '6px 16px',
            fontWeight: 600,
            fontSize: 14,
            cursor: 'pointer',
            transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
            zIndex: 2,
            borderWidth: isBgLight ? 1.5 : 0,
            borderStyle: isBgLight ? 'solid' : 'none',
            borderColor: isBgLight ? buttonBgColor : undefined,
            boxShadow: '0 1px 3px rgba(0,0,0,0.07)',
          }}
          onMouseOver={e => {
            (e.currentTarget as HTMLButtonElement).style.background = buttonHoverBg;
            (e.currentTarget as HTMLButtonElement).style.setProperty('color', '#fff', 'important');
            (e.currentTarget as HTMLButtonElement).style.boxShadow = buttonBoxShadow;
          }}
          onMouseOut={e => {
            (e.currentTarget as HTMLButtonElement).style.background = buttonFinalBg;
            (e.currentTarget as HTMLButtonElement).style.setProperty('color', buttonFinalText || (isBgLight ? buttonBgColor : '#fff'), 'important');
            (e.currentTarget as HTMLButtonElement).style.boxShadow = '0 1px 3px rgba(0,0,0,0.07)';
          }}
          tabIndex={0}
        >
          {actionLabel}
        </button>
      ))}
      {showPresetId && (
        <div style={{ marginTop: 8, fontSize: 12, color: '#6b7280', fontStyle: 'italic' }}>
          Modelo selecionado: <b>{presetId}</b>
        </div>
      )}
    </div>
  );
};
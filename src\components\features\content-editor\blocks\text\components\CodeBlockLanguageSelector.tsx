import React, { useState } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Code, Search, ChevronDown } from 'lucide-react';
import { SUPPORTED_LANGUAGES } from '../extensions/CodeBlockExtension';

interface CodeBlockLanguageSelectorProps {
  editor: Editor;
}

export const CodeBlockLanguageSelector: React.FC<CodeBlockLanguageSelectorProps> = ({ editor }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  // Filtrar linguagens baseado na busca
  const filteredLanguages = SUPPORTED_LANGUAGES.filter(lang =>
    lang.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lang.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Obter linguagem atual
  const getCurrentLanguage = () => {
    if (!editor.isActive('codeBlock')) {
      return 'text';
    }

    const { selection } = editor.state;
    const { $from } = selection;

    // Procurar por code block atual ou pai
    for (let i = $from.depth; i >= 0; i--) {
      const node = $from.node(i);
      if (node.type.name === 'codeBlock') {
        return node.attrs.language || 'text';
      }
    }

    return 'text';
  };

  const currentLanguage = getCurrentLanguage();
  const currentLangInfo = SUPPORTED_LANGUAGES.find(lang => lang.value === currentLanguage);

  const setLanguage = (language: string) => {
    editor.chain().focus().updateAttributes('codeBlock', { language }).run();
    setIsOpen(false);
    setSearchTerm('');
  };

  const insertCodeBlock = (language: string = 'text') => {
    // Usar comando oficial do Tiptap para inserir code block
    editor.chain()
      .focus()
      .toggleCodeBlock()
      .updateAttributes('codeBlock', { language })
      .run();
    setIsOpen(false);
  };

  // Se não está em um code block, mostrar botão de inserir
  if (!editor.isActive('codeBlock')) {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 px-2">
            <Code className="h-4 w-4 mr-1" />
            Código
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64">
          <div className="p-2">
            <div className="relative mb-2">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar linguagem..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 h-8"
              />
            </div>
            <div className="max-h-48 overflow-y-auto">
              {filteredLanguages.map((lang) => (
                <DropdownMenuItem
                  key={lang.value}
                  onClick={() => insertCodeBlock(lang.value)}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <span className="text-lg">{lang.icon}</span>
                  <span>{lang.label}</span>
                </DropdownMenuItem>
              ))}
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Se está em um code block, mostrar seletor de linguagem
  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 px-2 bg-gray-50">
          <span className="text-lg mr-1">{currentLangInfo?.icon || '📄'}</span>
          {currentLangInfo?.label || 'Texto Simples'}
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64">
        <div className="p-2">
          <div className="relative mb-2">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar linguagem..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-8"
            />
          </div>
          <div className="max-h-48 overflow-y-auto">
            {filteredLanguages.map((lang) => (
              <DropdownMenuItem
                key={lang.value}
                onClick={() => setLanguage(lang.value)}
                className={`flex items-center gap-2 cursor-pointer ${
                  lang.value === currentLanguage ? 'bg-blue-50 text-blue-700' : ''
                }`}
              >
                <span className="text-lg">{lang.icon}</span>
                <span>{lang.label}</span>
                {lang.value === currentLanguage && (
                  <span className="ml-auto text-blue-600">✓</span>
                )}
              </DropdownMenuItem>
            ))}
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

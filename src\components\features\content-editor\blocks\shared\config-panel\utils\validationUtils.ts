/**
 * Utilitários para validação
 */

/**
 * Valida se uma URL de imagem é válida
 */
export function isValidImageUrl(url: string): boolean {
  try {
    new URL(url);
    return /\.(jpg|jpeg|png|gif|svg|webp)$/i.test(url);
  } catch {
    return false;
  }
}

/**
 * Valida se um valor numérico está dentro de um range
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max;
}
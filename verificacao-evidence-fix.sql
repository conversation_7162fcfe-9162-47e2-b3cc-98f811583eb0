-- SCRIPT PARA VERIFICAR CORREÇÃO DO BLOCO DE EVIDÊNCIA
-- Execute este script APÓS testar o upload de evidência

-- 1. Verificar se há blocos duplicados (evidence + file para mesma tarefa)
SELECT 
    'VERIFICAÇÃO DUPLICAÇÃO' as teste,
    task_id,
    count(*) as total_blocos,
    array_agg(DISTINCT type) as tipos_blocos,
    CASE 
        WHEN 'evidence' = ANY(array_agg(DISTINCT type)) AND 'file' = ANY(array_agg(DISTINCT type)) 
        THEN '❌ DUPLICAÇÃO DETECTADA: evidence + file'
        WHEN 'evidence' = ANY(array_agg(DISTINCT type)) AND count(*) = 1 
        THEN '✅ APENAS BLOCO EVIDENCE (CORRETO)'
        ELSE '⚠️ VERIFICAR MANUALMENTE'
    END as status
FROM task_content_blocks 
WHERE created_at > NOW() - INTERVAL '1 hour'  -- Apenas blocos recentes
GROUP BY task_id
ORDER BY created_at DESC;

-- 2. Verificar detalhes dos blocos de evidência
SELECT 
    'DETALHES EVIDENCE' as teste,
    id,
    task_id,
    type,
    content->'evidences' as evidencias_no_bloco,
    created_at,
    '✅ BLOCO EVIDENCE DETALHADO' as status
FROM task_content_blocks 
WHERE type = 'evidence' 
  AND created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC;

-- 3. Verificar registros na tabela evidence correspondentes
SELECT 
    'DETALHES EVIDENCE TABLE' as teste,
    e.id,
    e.task_id,
    e.block_id,
    e.file_name,
    e.type,
    e.status,
    e.created_at,
    '✅ REGISTRO NA TABELA EVIDENCE' as status
FROM evidence e
WHERE e.created_at > NOW() - INTERVAL '1 hour'
ORDER BY e.created_at DESC;

-- 4. Verificar se há blocos orphãos do tipo 'file'
SELECT 
    'VERIFICAÇÃO ORPHÃOS' as teste,
    id,
    task_id,
    type,
    content->'fileName' as nome_arquivo,
    created_at,
    '⚠️ POSSÍVEL BLOCO FILE ÓRFÃO' as status
FROM task_content_blocks 
WHERE type = 'file' 
  AND created_at > NOW() - INTERVAL '1 hour'
  AND task_id NOT IN (
    SELECT DISTINCT task_id 
    FROM task_content_blocks 
    WHERE type = 'evidence' 
      AND created_at > NOW() - INTERVAL '1 hour'
  )
ORDER BY created_at DESC;

-- 5. Resumo da correção
SELECT 
    'RESUMO' as teste,
    count(CASE WHEN type = 'evidence' THEN 1 END) as blocos_evidence,
    count(CASE WHEN type = 'file' THEN 1 END) as blocos_file,
    count(*) as total_blocos_recentes,
    CASE 
        WHEN count(CASE WHEN type = 'evidence' THEN 1 END) > 0 
         AND count(CASE WHEN type = 'file' THEN 1 END) = 0 
        THEN '✅ CORREÇÃO APLICADA: SÓ EVIDENCE, SEM FILE'
        WHEN count(CASE WHEN type = 'evidence' THEN 1 END) > 0 
         AND count(CASE WHEN type = 'file' THEN 1 END) > 0 
        THEN '❌ AINDA HÁ DUPLICAÇÃO: EVIDENCE + FILE'
        ELSE '⚠️ AGUARDANDO TESTE DE UPLOAD'
    END as status_correcao
FROM task_content_blocks 
WHERE created_at > NOW() - INTERVAL '1 hour';

-- INTERPRETAÇÃO:
-- ✅ Teste 1: Não deve mostrar duplicação (evidence + file)
-- ✅ Teste 2: Deve mostrar blocos do tipo 'evidence' com evidencias_no_bloco
-- ✅ Teste 3: Deve mostrar registros correspondentes na tabela evidence
-- ✅ Teste 4: Não deve mostrar blocos 'file' órfãos recentes
-- ✅ Teste 5: Deve mostrar "CORREÇÃO APLICADA"

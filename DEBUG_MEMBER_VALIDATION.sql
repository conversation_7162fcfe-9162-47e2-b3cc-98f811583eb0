-- =====================================================
-- DEBUG DA VALIDAÇÃO verificarAntesDeInserirMembro
-- =====================================================

-- Simular exatamente o que a função TypeScript faz

-- 1. Pegar IDs para teste
SELECT 'PREPARAÇÃO PARA TESTE' as etapa;

SELECT 
    'PROJECT_ID PARA USAR' as tipo,
    id as project_id
FROM projects 
ORDER BY created_at DESC 
LIMIT 1;

SELECT 
    'USER_IDS DISPONÍVEIS' as tipo,
    id as user_id,
    name,
    email,
    is_active
FROM profiles 
WHERE is_active = true
ORDER BY name;

-- =====================================================
-- 2. SIMULAR CADA PASSO DA VALIDAÇÃO
-- =====================================================

-- Passo 1: Verificar se user_id existe em profiles
SELECT 'PASSO 1: VERIFICAR USER EXISTS' as teste;

SELECT 
    'USER EXISTS CHECK' as tipo,
    p.id,
    p.name,
    p.is_active,
    CASE 
        WHEN p.id IS NOT NULL THEN 'OK - Usuário existe'
        ELSE 'ERRO - Usuário não existe'
    END as resultado
FROM (SELECT id FROM profiles WHERE is_active = true LIMIT 1) u
LEFT JOIN profiles p ON p.id = u.id;

-- Passo 2: Verificar se project_id existe em projects  
SELECT 'PASSO 2: VERIFICAR PROJECT EXISTS' as teste;

SELECT 
    'PROJECT EXISTS CHECK' as tipo,
    proj.id,
    proj.name,
    proj.status,
    CASE 
        WHEN proj.id IS NOT NULL THEN 'OK - Projeto existe'
        ELSE 'ERRO - Projeto não existe'
    END as resultado
FROM (SELECT id FROM projects ORDER BY created_at DESC LIMIT 1) p
LEFT JOIN projects proj ON proj.id = p.id;

-- Passo 3: Verificar se já existe membro com mesmo papel
SELECT 'PASSO 3: VERIFICAR MEMBRO EXISTENTE' as teste;

WITH test_data AS (
    SELECT 
        (SELECT id FROM projects ORDER BY created_at DESC LIMIT 1) as project_id,
        (SELECT id FROM profiles WHERE is_active = true LIMIT 1) as user_id,
        'member'::project_member_role as role
)
SELECT 
    'MEMBER EXISTS CHECK' as tipo,
    td.project_id,
    td.user_id,
    td.role,
    pm.id as existing_member_id,
    CASE 
        WHEN pm.id IS NOT NULL THEN 'ERRO - Membro já existe com esse papel'
        ELSE 'OK - Pode adicionar'
    END as resultado
FROM test_data td
LEFT JOIN project_members pm ON pm.project_id = td.project_id 
    AND pm.user_id = td.user_id 
    AND pm.role = td.role;

-- =====================================================
-- 3. TESTE COMPLETO COM TODOS OS PROFILES
-- =====================================================

SELECT 'TESTE COMPLETO PARA TODOS OS PROFILES' as etapa;

WITH project_target AS (
    SELECT id as project_id FROM projects ORDER BY created_at DESC LIMIT 1
)
SELECT 
    'VALIDAÇÃO COMPLETA' as tipo,
    p.id as user_id,
    p.name as user_name,
    p.email,
    pt.project_id,
    -- Verificar se profile existe (sempre true aqui)
    'OK' as profile_exists,
    -- Verificar se projeto existe
    CASE WHEN pt.project_id IS NOT NULL THEN 'OK' ELSE 'ERRO' END as project_exists,
    -- Verificar se já é membro
    CASE 
        WHEN pm.id IS NOT NULL THEN 'JÁ É MEMBRO (' || pm.role || ')'
        ELSE 'PODE ADICIONAR'
    END as member_status,
    -- Role que seria usado
    CASE 
        WHEN p.role = 'admin' THEN 'admin'::project_member_role
        ELSE 'member'::project_member_role
    END as role_to_use
FROM profiles p
CROSS JOIN project_target pt
LEFT JOIN project_members pm ON pm.project_id = pt.project_id AND pm.user_id = p.id
WHERE p.is_active = true
ORDER BY p.name;

-- =====================================================
-- 4. VERIFICAR CONSTRAINTS DA TABELA
-- =====================================================

SELECT 'VERIFICAR CONSTRAINTS' as etapa;

-- Ver valores válidos do enum project_member_role
SELECT 
    'ENUM VALUES' as tipo,
    enumlabel as valor_valido
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'project_member_role'
)
ORDER BY enumsortorder;

-- Ver constraints da tabela project_members
SELECT 
    'TABLE CONSTRAINTS' as tipo,
    constraint_name,
    constraint_type,
    table_name
FROM information_schema.table_constraints 
WHERE table_name = 'project_members'
AND table_schema = 'public';

-- Ver colunas e tipos
SELECT 
    'COLUMN INFO' as tipo,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'project_members'
AND table_schema = 'public'
ORDER BY ordinal_position;

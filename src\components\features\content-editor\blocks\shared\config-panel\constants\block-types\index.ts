/**
 * Exportações centralizadas de todos os presets de tipos de bloco
 */

// Centralização dos presets de blocos
// TODO: Dividir em múltiplos arquivos se crescer

export * from './alert/presets';
export * from './colored/presets';
export * from './evidence/presets';
export * from './file/presets';
export * from './image/presets';
export * from './quiz/presets';
export * from './text/presets';
export * from './video/presets';

// Text Block
import {
  textBlockPresets,
  textBlockVariants,
  textBlockIcons,
  type TextBlockVariant
} from './text/presets';

export {
  textBlockPresets,
  textBlockVariants,
  textBlockIcons,
  type TextBlockVariant
};

// Video Block
import {
  videoBlockPresets,
  videoBlockVariants,
  videoBlockIcons,
  type VideoBlockVariant
} from './video/presets';

// File Block
import {
  fileBlockPresets,
  fileBlockVariants,
  fileBlockIcons,
  type FileBlockVariant
} from './file/presets';

// Quiz Block
import {
  quizBlockPresets,
  quizBlockVariants,
  quizBlockIcons,
  type QuizBlockVariant
} from './quiz/presets';

// Colored Block
import {
  coloredBlockPresets,
  COLORED_BLOCK_PALETTES,
  ICONS_BY_VARIANT,
  COLORS_BY_VARIANT,
  type ColoredCardVariant
} from './colored/presets';

// Image Block
import {
  imageBlockPresets,
  imageBlockVariants,
  imageBlockIcons,
  type ImageBlockVariant
} from './image/presets';

// Evidence Block
import {
  evidenceBlockPresets
} from './evidence/presets';

// Alert Block
import {
  alertBlockPresets,
  alertBlockVariants,
  alertBlockIcons,
  ALERT_PRESETS,
  ALERT_ICONS,
  type AlertBlockVariant,
  type AlertType,
  type AlertStyle,
  type AlertLayout,
  type AlertPreset
} from './alert/presets';

// Re-export all imports
export {
  videoBlockPresets,
  videoBlockVariants,
  videoBlockIcons,
  type VideoBlockVariant,
  fileBlockPresets,
  fileBlockVariants,
  fileBlockIcons,
  type FileBlockVariant,
  quizBlockPresets,
  quizBlockVariants,
  quizBlockIcons,
  type QuizBlockVariant,
  coloredBlockPresets,
  COLORED_BLOCK_PALETTES,
  ICONS_BY_VARIANT,
  COLORS_BY_VARIANT,
  type ColoredCardVariant,
  imageBlockPresets,
  imageBlockVariants,
  imageBlockIcons,
  type ImageBlockVariant,
  evidenceBlockPresets,
  alertBlockPresets,
  alertBlockVariants,
  alertBlockIcons,
  ALERT_PRESETS,
  ALERT_ICONS,
  type AlertBlockVariant,
  type AlertType,
  type AlertStyle,
  type AlertLayout,
  type AlertPreset
};

// Tipo união de todas as variantes
export type BlockVariant = 
  | TextBlockVariant
  | VideoBlockVariant
  | FileBlockVariant
  | QuizBlockVariant
  | ColoredCardVariant
  | ImageBlockVariant
  | AlertBlockVariant;

// Mapeamento de tipos de bloco para seus presets
export const blockTypePresetsMap = {
  text: textBlockPresets,
  video: videoBlockPresets,
  file: fileBlockPresets,
  quiz: quizBlockPresets,
  'colored-block': coloredBlockPresets,
  image: imageBlockPresets,
  evidence: evidenceBlockPresets,
  alert: alertBlockPresets,
} as const;

// Mapeamento de tipos de bloco para suas variantes
export const blockTypeVariantsMap = {
  text: textBlockVariants,
  video: videoBlockVariants,
  file: fileBlockVariants,
  quiz: quizBlockVariants,
  'colored-block': Object.keys(coloredBlockPresets) as ColoredCardVariant[],
  image: imageBlockVariants,
  alert: alertBlockVariants,
} as const;

// Mapeamento de tipos de bloco para seus ícones
export const blockTypeIconsMap = {
  text: textBlockIcons,
  video: videoBlockIcons,
  file: fileBlockIcons,
  quiz: quizBlockIcons,
  'colored-block': Object.values(ICONS_BY_VARIANT).flat(),
  image: imageBlockIcons,
  alert: Object.values(alertBlockIcons).flat(),
} as const;

// Função utilitária para obter presets por tipo de bloco
export function getPresetsByBlockType(blockType: keyof typeof blockTypePresetsMap) {
  return blockTypePresetsMap[blockType];
}

// Função utilitária para obter variantes por tipo de bloco
export function getVariantsByBlockType(blockType: keyof typeof blockTypeVariantsMap) {
  return blockTypeVariantsMap[blockType];
}

// Função utilitária para obter ícones por tipo de bloco
export function getIconsByBlockType(blockType: keyof typeof blockTypeIconsMap) {
  return blockTypeIconsMap[blockType];
}
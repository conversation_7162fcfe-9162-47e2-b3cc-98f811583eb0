/**
 * Configurações base e padrões compartilhados entre todos os tipos de bloco
 */
import { BlockConfig } from '../../../../../../../types';

// Configuração padrão base para todos os blocos
export const defaultBlockConfig: BlockConfig = {
  card: {
    backgroundColor: '#ffffff',
    format: 'rounded',
    border: {
      enabled: true,
      color: '#e5e7eb',
      width: 1,
    },
    shadow: {
      enabled: true,
      depth: 1,
    },
    font: {
      size: 14,
      color: '#1f2937', // gray-800 - mais escuro e contrastante
      style: 'normal',
    },
    hover: {
      enabled: true,
      shadowDepth: 2,
    }
  },
  icon: {
    enabled: true,
    position: 'left-title',
    type: 'predefined',
    iconName: 'FileText',
    appearance: {
      background: '#6b7280',
      color: '#ffffff',
      format: 'rounded',
      size: 20,
      border: {
        enabled: false,
        color: '#6b7280',
        width: 1,
      },
      shadow: {
        enabled: true,
        depth: 1,
      },
      hover: {
        enabled: true,
        shadowDepth: 2,
      }
    }
  },
  button: {
    backgroundColor: '#7c3aed',
    color: '#ffffff',
    style: 'rounded',
    size: 'medium',
    position: 'bottom-center',
    border: {
      enabled: false,
      color: '#e5e5e5',
      width: 1,
    },
    shadow: {
      enabled: false,
      depth: 2,
    },
    hover: {
      enabled: false,
      shadowDepth: 3,
    },
    text: 'Clique aqui',
    url: '',
    newTab: false,
    icon: 'ArrowRight',
    iconPosition: 'left',
  },
};

// Ícones padrão por tipo de bloco
export const blockTypeDefaultIcons = {
  text: 'FileText',
  video: 'Play',
  file: 'File',
  quiz: 'HelpCircle',
  'colored-block': 'Info',
  image: 'Image',
} as const;

// Cores padrão por tipo de bloco - versões mais vivas e destacadas
export const blockTypeDefaultColors = {
  text: {
    primary: '#3b82f6', // blue-500 - mais vivo que o cinza anterior
    secondary: '#60a5fa', // blue-400
    background: '#f8fafc', // blue-50 muito sutil
    border: '#3b82f6', // blue-500 para destaque
  },
  video: {
    primary: '#a78bfa', // purple-400 - alinhado com preset
    secondary: '#c4b5fd', // purple-300
    background: '#f3f0ff', // purple-50
    border: '#a78bfa', // purple-400
  },
  file: {
    primary: '#f59e42', // orange-500 - mais vivo e quente
    secondary: '#fb923c', // orange-400
    background: '#fff7ed', // orange-50
    border: '#f59e42', // orange-500
  },
  quiz: {
    primary: '#ec4899', // pink-500 - mais vibrante
    secondary: '#f472b6', // pink-400
    background: '#fdf2f8', // pink-50
    border: '#ec4899', // pink-500
  },
  'colored-block': {
    primary: '#6366f1', // indigo-500 - mais saturado
    secondary: '#818cf8', // indigo-400
    background: '#eef2ff', // indigo-50
    border: '#6366f1', // indigo-500
  },
  image: {
    primary: '#22c55e', // green-500 - mais vivo que cinza
    secondary: '#4ade80', // green-400
    background: '#f0fdf4', // green-50
    border: '#22c55e', // green-500
  },
} as const;

// Formatos disponíveis para cards
export const cardFormats = [
  { value: 'square', label: 'Quadrado' },
  { value: 'rounded', label: 'Arredondado' },
  { value: 'circle', label: 'Circular' },
] as const;

// Formatos disponíveis para ícones
export const iconFormats = [
  { value: 'square', label: 'Quadrado' },
  { value: 'rounded', label: 'Arredondado' },
  { value: 'circle', label: 'Circular' },
] as const;

// Posições disponíveis para ícones
export const iconPositions = [
  { value: 'left-title', label: 'Esquerda do título' },
  { value: 'right-title', label: 'Direita do título' },
  { value: 'top', label: 'Acima do conteúdo' },
  { value: 'center', label: 'Centro' },
  { value: 'bottom', label: 'Abaixo do conteúdo' },
] as const;

// Estilos de fonte disponíveis
export const fontStyles = [
  { value: 'Inter', label: 'Inter' },
  { value: 'Roboto', label: 'Roboto' },
  { value: 'Open Sans', label: 'Open Sans' },
  { value: 'Lato', label: 'Lato' },
  { value: 'Montserrat', label: 'Montserrat' },
] as const;

// Tamanhos de botão disponíveis
export const buttonSizes = [
  { value: 'small', label: 'Pequeno' },
  { value: 'medium', label: 'Médio' },
  { value: 'large', label: 'Grande' },
] as const;

// Estilos de botão disponíveis
export const buttonStyles = [
  { value: 'filled', label: 'Preenchido' },
  { value: 'outlined', label: 'Contornado' },
  { value: 'text', label: 'Texto' },
] as const;

// Posições de botão disponíveis
export const buttonPositions = [
  { value: 'top-left', label: 'Superior esquerda' },
  { value: 'top-right', label: 'Superior direita' },
  { value: 'bottom-left', label: 'Inferior esquerda' },
  { value: 'bottom-right', label: 'Inferior direita' },
  { value: 'center', label: 'Centro' },
] as const;

// Sombras predefinidas
export const shadowPresets = [
  { value: 'none', label: 'Nenhuma', shadow: 'none' },
  { value: 'sm', label: 'Pequena', shadow: '0 1px 2px rgba(0, 0, 0, 0.05)' },
  { value: 'md', label: 'Média', shadow: '0 4px 6px rgba(0, 0, 0, 0.1)' },
  { value: 'lg', label: 'Grande', shadow: '0 10px 15px rgba(0, 0, 0, 0.1)' },
  { value: 'xl', label: 'Extra grande', shadow: '0 20px 25px rgba(0, 0, 0, 0.1)' },
] as const;

// Larguras de borda disponíveis
export const borderWidths = [
  { value: 0, label: 'Nenhuma' },
  { value: 1, label: '1px' },
  { value: 2, label: '2px' },
  { value: 3, label: '3px' },
  { value: 4, label: '4px' },
] as const;
/**
 * Presets específicos para blocos coloridos (alerts/notifications)
 */

// Definição local do tipo para evitar dependência circular
interface BlockTypePreset {
  icon: {
    name: string;
    position: string;
    type: string;
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  card: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
  button?: {
    backgroundColor: string;
    color: string;
    format: string;
    border: {
      enabled: boolean;
      color: string;
      width: number;
    };
    shadow?: string;
    hover?: {
      backgroundColor?: string;
      color?: string;
      shadow?: string;
      borderColor?: string;
    };
  };
}

export type ColoredCardVariant = 'warning' | 'info' | 'error' | 'success';

export const coloredBlockPresets: Record<ColoredCardVariant, BlockTypePreset> = {
  info: {
    icon: {
      name: 'Info',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#6366f1',
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#6366f1', width: 1 },
      shadow: '0 2px 8px #6366f140',
      hover: {
        backgroundColor: '#818cf8',
        color: '#fff',
        shadow: '0 4px 16px #6366f140',
        borderColor: '#6366f1',
      },
    },
    card: {
      backgroundColor: '#eef2ff',
      color: '#6366f1',
      format: 'rounded',
      border: { enabled: true, color: '#6366f1', width: 1 },
      shadow: '0 2px 8px #6366f140',
      hover: {
        backgroundColor: '#818cf8',
        color: '#fff',
        shadow: '0 4px 16px #6366f140',
        borderColor: '#6366f1',
      },
    },
    button: {
      backgroundColor: '#6366f1',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#6366f1', width: 1 },
      shadow: '0 2px 8px #6366f140',
      hover: {
        backgroundColor: '#818cf8',
        color: '#fff',
        shadow: '0 4px 16px #6366f140',
        borderColor: '#6366f1',
      },
    },
  },
  warning: {
    icon: {
      name: 'AlertTriangle',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#f59e42',
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fbbf24',
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
    card: {
      backgroundColor: '#fff7ed',
      color: '#f59e42',
      format: 'rounded',
      border: { enabled: true, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fbbf24',
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
    button: {
      backgroundColor: '#f59e42',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#f59e42', width: 1 },
      shadow: '0 2px 8px #fbbf2440',
      hover: {
        backgroundColor: '#fbbf24',
        color: '#fff',
        shadow: '0 4px 16px #fbbf2440',
        borderColor: '#f59e42',
      },
    },
  },
  error: {
    icon: {
      name: 'XCircle',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#ef4444',
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#ef4444', width: 1 },
      shadow: '0 2px 8px #ef444440',
      hover: {
        backgroundColor: '#f87171',
        color: '#fff',
        shadow: '0 4px 16px #ef444440',
        borderColor: '#ef4444',
      },
    },
    card: {
      backgroundColor: '#fef2f2',
      color: '#ef4444',
      format: 'rounded',
      border: { enabled: true, color: '#ef4444', width: 1 },
      shadow: '0 2px 8px #ef444440',
      hover: {
        backgroundColor: '#f87171',
        color: '#fff',
        shadow: '0 4px 16px #ef444440',
        borderColor: '#ef4444',
      },
    },
    button: {
      backgroundColor: '#ef4444',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#ef4444', width: 1 },
      shadow: '0 2px 8px #ef444440',
      hover: {
        backgroundColor: '#f87171',
        color: '#fff',
        shadow: '0 4px 16px #ef444440',
        borderColor: '#ef4444',
      },
    },
  },
  success: {
    icon: {
      name: 'CheckCircle',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#22c55e',
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#22c55e', width: 1 },
      shadow: '0 2px 8px #22c55e40',
      hover: {
        backgroundColor: '#4ade80',
        color: '#fff',
        shadow: '0 4px 16px #22c55e40',
        borderColor: '#22c55e',
      },
    },
    card: {
      backgroundColor: '#f0fdf4',
      color: '#22c55e',
      format: 'rounded',
      border: { enabled: true, color: '#22c55e', width: 1 },
      shadow: '0 2px 8px #22c55e40',
      hover: {
        backgroundColor: '#4ade80',
        color: '#fff',
        shadow: '0 4px 16px #22c55e40',
        borderColor: '#22c55e',
      },
    },
    button: {
      backgroundColor: '#22c55e',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#22c55e', width: 1 },
      shadow: '0 2px 8px #22c55e40',
      hover: {
        backgroundColor: '#4ade80',
        color: '#fff',
        shadow: '0 4px 16px #22c55e40',
        borderColor: '#22c55e',
      },
    },
  },
};

export type ColoredBlockVariant = 'info' | 'warning' | 'error' | 'success';

// Paletas de variações para cada tipo de colored-block
export const COLORED_BLOCK_PALETTES = {
  info: [
    { background: "#e0f2fe", color: "#1570ef" },
    { background: "#ffffff", color: "#1570ef" },
    { background: "#f0f9ff", color: "#1570ef" },
    { background: "#bae6fd", color: "#1570ef" },
    { background: "#7dd3fc", color: "#1570ef" },
    { background: "#38bdf8", color: "#fff" },
    { background: "#0ea5e9", color: "#fff" },
    { background: "#0369a1", color: "#fff" },
    { background: "#075985", color: "#fff" },
    { background: "#0c4a6e", color: "#fff" },
    { background: "#082f49", color: "#fff" }
  ],
  warning: [
    { background: "#fef7c3", color: "#f79009" },
    { background: "#ffffff", color: "#f79009" },
    { background: "#fffbe8", color: "#f79009" },
    { background: "#ffe9a7", color: "#f79009" },
    { background: "#ffd36a", color: "#f79009" },
    { background: "#fec84b", color: "#fff" },
    { background: "#fbbf24", color: "#fff" },
    { background: "#f59e42", color: "#fff" },
    { background: "#b45309", color: "#fff" },
    { background: "#92400e", color: "#fff" },
    { background: "#78350f", color: "#fff" }
  ],
  error: [
    { background: "#fee4e2", color: "#f04438" },
    { background: "#ffffff", color: "#f04438" },
    { background: "#fff0f0", color: "#f04438" },
    { background: "#ffd6d6", color: "#f04438" },
    { background: "#ffb4b4", color: "#f04438" },
    { background: "#ff7b7b", color: "#fff" },
    { background: "#f87171", color: "#fff" },
    { background: "#ef4444", color: "#fff" },
    { background: "#b91c1c", color: "#fff" },
    { background: "#991b1b", color: "#fff" },
    { background: "#7f1d1d", color: "#fff" }
  ],
  success: [
    { background: "#d1fadf", color: "#12b76a" },
    { background: "#ffffff", color: "#12b76a" },
    { background: "#e9fbe9", color: "#12b76a" },
    { background: "#b6f3ce", color: "#12b76a" },
    { background: "#7be2b8", color: "#12b76a" },
    { background: "#4fd1a1", color: "#12b76a" },
    { background: "#22c55e", color: "#fff" },
    { background: "#16a34a", color: "#fff" },
    { background: "#15803d", color: "#fff" },
    { background: "#166534", color: "#fff" },
    { background: "#14532d", color: "#fff" }
  ]
};

// Ícones permitidos por variante
export const ICONS_BY_VARIANT = {
  info: ['Info', 'AlertCircle', 'Bell', 'BookOpen', 'Lightbulb', 'MessageSquare', 'Eye'],
  warning: ['AlertTriangle', 'AlertCircle', 'Flag', 'Clock', 'Megaphone', 'Zap'],
  error: ['XCircle', 'AlertOctagon', 'Ban', 'Slash', 'Bug', 'ShieldOff', 'StopCircle'],
  success: ['CheckCircle', 'Award', 'Star', 'ThumbsUp', 'Trophy', 'Smile', 'Heart']
};

// Cores permitidas por variante
export const COLORS_BY_VARIANT = {
  info: ['#6366f1', '#3b82f6', '#0ea5e9', '#06b6d4'],
  warning: ['#f59e42', '#fbbf24', '#eab308', '#facc15'],
  error: ['#ef4444', '#f87171', '#dc2626', '#b91c1c'],
  success: ['#22c55e', '#4ade80', '#16a34a', '#15803d']
};

// Variantes de card colorido (para compatibilidade)
export const coloredCardVariants = {
  info: {
    backgroundColor: '#eef2ff',
    borderColor: '#6366f1',
    iconColor: '#6366f1'
  },
  warning: {
    backgroundColor: '#fff7ed',
    borderColor: '#f59e42',
    iconColor: '#f59e42'
  },
  error: {
    backgroundColor: '#fef2f2',
    borderColor: '#ef4444',
    iconColor: '#ef4444'
  },
  success: {
    backgroundColor: '#f0fdf4',
    borderColor: '#22c55e',
    iconColor: '#22c55e'
  }
};

// Cores de botão por variante (para compatibilidade)
export const buttonColorsByVariant = {
  info: '#6366f1',
  warning: '#f59e42',
  error: '#ef4444',
  success: '#22c55e'
};
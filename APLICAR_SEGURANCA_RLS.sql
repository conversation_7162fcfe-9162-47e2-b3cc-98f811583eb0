-- IMPLEMENTAÇÃO SEGURA DAS POLÍTICAS RLS
-- Execute este script para aplicar as políticas de segurança

-- 1. Habilitar RLS em todas as tabelas críticas
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_executors ENABLE ROW LEVEL SECURITY;
ALTER TABLE stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- 2. Política para TASKS - Usuários só podem ver tarefas onde são executores ou responsáveis
CREATE POLICY "users_can_view_assigned_tasks" ON tasks
FOR SELECT
USING (
  assigned_to = auth.uid() OR
  id IN (
    SELECT task_id 
    FROM task_executors 
    WHERE user_id = auth.uid()
  )
);

-- 3. Política para TASK_EXECUTORS - Usuá<PERSON>s só podem ver suas próprias designações
CREATE POLICY "users_can_view_own_executor_assignments" ON task_executors
FOR SELECT
USING (user_id = auth.uid());

-- 4. Política para STAGES - Usu<PERSON>rios só podem ver estágios de projetos onde participam
CREATE POLICY "users_can_view_project_stages" ON stages
FOR SELECT
USING (
  project_id IN (
    SELECT DISTINCT p.id 
    FROM projects p
    JOIN stages s ON s.project_id = p.id
    JOIN tasks t ON t.stage_id = s.id
    WHERE t.assigned_to = auth.uid() OR
          t.id IN (
            SELECT task_id 
            FROM task_executors 
            WHERE user_id = auth.uid()
          )
  )
);

-- 5. Política para PROJECTS - Usuários só podem ver projetos onde têm tarefas
CREATE POLICY "users_can_view_projects_with_tasks" ON projects
FOR SELECT
USING (
  id IN (
    SELECT DISTINCT p.id 
    FROM projects p
    JOIN stages s ON s.project_id = p.id
    JOIN tasks t ON t.stage_id = s.id
    WHERE t.assigned_to = auth.uid() OR
          t.id IN (
            SELECT task_id 
            FROM task_executors 
            WHERE user_id = auth.uid()
          )
  )
);

-- 6. Política para PROFILES - Usuários podem ver perfis de pessoas com quem trabalham
CREATE POLICY "users_can_view_work_related_profiles" ON profiles
FOR SELECT
USING (
  id = auth.uid() OR
  id IN (
    -- Responsáveis por tarefas onde o usuário é executor
    SELECT DISTINCT t.assigned_to
    FROM tasks t
    JOIN task_executors te ON te.task_id = t.id
    WHERE te.user_id = auth.uid()
    
    UNION
    
    -- Executores de tarefas onde o usuário é responsável
    SELECT DISTINCT te.user_id
    FROM task_executors te
    JOIN tasks t ON t.id = te.task_id
    WHERE t.assigned_to = auth.uid()
  )
);

-- 7. Validação das políticas
DO $$
BEGIN
  -- Verificar se as políticas foram criadas
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'tasks' AND policyname = 'users_can_view_assigned_tasks'
  ) THEN
    RAISE EXCEPTION 'Política para tasks não foi criada corretamente';
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'task_executors' AND policyname = 'users_can_view_own_executor_assignments'
  ) THEN
    RAISE EXCEPTION 'Política para task_executors não foi criada corretamente';
  END IF;
  
  RAISE NOTICE 'Todas as políticas RLS foram aplicadas com sucesso!';
END $$;

-- 8. Teste das políticas (opcional - para desenvolvimento)
/*
-- Execute como um usuário específico para testar
SELECT set_config('request.jwt.claims', '{"sub": "4b09be1f-5187-44c0-9b53-87b7c57e45b4"}', false);

-- Deve retornar apenas as tarefas onde o usuário é executor ou responsável
SELECT 
  t.id,
  t.title,
  t.status,
  CASE 
    WHEN t.assigned_to = auth.uid() THEN 'Responsável'
    ELSE 'Executor'
  END as role
FROM tasks t
LEFT JOIN task_executors te ON te.task_id = t.id
WHERE t.assigned_to = auth.uid() OR te.user_id = auth.uid();
*/

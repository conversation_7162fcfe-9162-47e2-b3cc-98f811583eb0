/**
 * Utilitários para converter dados entre Lexical e Tiptap
 * Garante compatibilidade com conteúdo existente
 */

export interface LexicalNode {
  type: string;
  version?: number;
  children?: LexicalNode[];
  text?: string;
  format?: number;
  style?: string;
  indent?: number;
  direction?: string;
  tag?: string;
  url?: string;
  rel?: string;
  target?: string;
  title?: string;
}

export interface LexicalDocument {
  root: {
    children: LexicalNode[];
    direction: string;
    format: string;
    indent: number;
    type: string;
    version: number;
  };
}

/**
 * Converte documento Lexical para HTML compatível com Tiptap
 */
export function convertLexicalToTiptap(lexicalData: string | LexicalDocument): string {
  try {
    let document: LexicalDocument;
    
    if (typeof lexicalData === 'string') {
      // Se for string, pode ser HTML ou JSON
      if (lexicalData.startsWith('<') || lexicalData.includes('<p>')) {
        // Já <PERSON>L, retorna como está
        return lexicalData;
      }
      document = JSON.parse(lexicalData);
    } else {
      document = lexicalData;
    }

    if (!document.root || !document.root.children) {
      return '<p></p>';
    }

    return convertNodesToHTML(document.root.children);
  } catch (error) {
    console.warn('Erro ao converter Lexical para Tiptap:', error);
    // Se falhar, tenta retornar como HTML simples
    if (typeof lexicalData === 'string') {
      return lexicalData.startsWith('<') ? lexicalData : `<p>${lexicalData}</p>`;
    }
    return '<p></p>';
  }
}

/**
 * Converte HTML do Tiptap para formato compatível com Lexical
 */
export function convertTiptapToLexical(html: string): string {
  // Para compatibilidade, mantemos como HTML
  // O Lexical pode importar HTML diretamente
  return html;
}

/**
 * Converte array de nós Lexical para HTML
 */
function convertNodesToHTML(nodes: LexicalNode[]): string {
  return nodes.map(node => convertNodeToHTML(node)).join('');
}

/**
 * Converte um nó Lexical individual para HTML
 */
function convertNodeToHTML(node: LexicalNode): string {
  switch (node.type) {
    case 'paragraph':
      const content = node.children ? convertNodesToHTML(node.children) : '';
      return `<p>${content}</p>`;

    case 'heading':
      const headingContent = node.children ? convertNodesToHTML(node.children) : '';
      const level = node.tag?.replace('h', '') || '1';
      return `<h${level}>${headingContent}</h${level}>`;

    case 'text':
      let text = node.text || '';
      
      // Aplicar formatação baseada no formato bitmask do Lexical
      if (node.format) {
        if (node.format & 1) text = `<strong>${text}</strong>`; // Bold
        if (node.format & 2) text = `<em>${text}</em>`; // Italic
        if (node.format & 8) text = `<s>${text}</s>`; // Strikethrough
        if (node.format & 16) text = `<u>${text}</u>`; // Underline
        if (node.format & 32) text = `<code>${text}</code>`; // Code
      }
      
      return text;

    case 'link':
      const linkContent = node.children ? convertNodesToHTML(node.children) : '';
      const href = node.url || '#';
      const target = node.target ? ` target="${node.target}"` : '';
      const rel = node.rel ? ` rel="${node.rel}"` : '';
      const title = node.title ? ` title="${node.title}"` : '';
      return `<a href="${href}"${target}${rel}${title}>${linkContent}</a>`;

    case 'list':
      const listContent = node.children ? convertNodesToHTML(node.children) : '';
      const listTag = node.tag === 'ol' ? 'ol' : 'ul';
      return `<${listTag}>${listContent}</${listTag}>`;

    case 'listitem':
      const itemContent = node.children ? convertNodesToHTML(node.children) : '';
      return `<li>${itemContent}</li>`;

    case 'quote':
      const quoteContent = node.children ? convertNodesToHTML(node.children) : '';
      return `<blockquote>${quoteContent}</blockquote>`;

    case 'code':
      const codeContent = node.children ? convertNodesToHTML(node.children) : '';
      return `<pre><code>${codeContent}</code></pre>`;

    case 'linebreak':
      return '<br>';

    case 'table':
      const tableContent = node.children ? convertNodesToHTML(node.children) : '';
      return `<table>${tableContent}</table>`;

    case 'tablerow':
      const rowContent = node.children ? convertNodesToHTML(node.children) : '';
      return `<tr>${rowContent}</tr>`;

    case 'tablecell':
      const cellContent = node.children ? convertNodesToHTML(node.children) : '';
      return `<td>${cellContent}</td>`;

    case 'tableheader':
      const headerContent = node.children ? convertNodesToHTML(node.children) : '';
      return `<th>${headerContent}</th>`;

    default:
      // Para tipos desconhecidos, tenta processar os filhos
      if (node.children) {
        return convertNodesToHTML(node.children);
      }
      return node.text || '';
  }
}

/**
 * Detecta se o conteúdo é do formato Lexical
 */
export function isLexicalFormat(content: string): boolean {
  try {
    if (typeof content !== 'string') return false;
    
    // Se começa com HTML tags, provavelmente não é Lexical JSON
    if (content.trim().startsWith('<')) return false;
    
    const parsed = JSON.parse(content);
    return parsed.root && parsed.root.type === 'root' && Array.isArray(parsed.root.children);
  } catch {
    return false;
  }
}

/**
 * Sanitiza HTML para garantir segurança
 */
export function sanitizeHTML(html: string): string {
  // Lista básica de tags permitidas
  const allowedTags = [
    'p', 'br', 'strong', 'b', 'em', 'i', 'u', 's', 'code', 'pre',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'blockquote',
    'a', 'table', 'tr', 'td', 'th', 'thead', 'tbody'
  ];

  // Remove scripts e outros elementos perigosos
  let sanitized = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  sanitized = sanitized.replace(/on\w+="[^"]*"/gi, ''); // Remove event handlers
  sanitized = sanitized.replace(/javascript:/gi, ''); // Remove javascript: URLs
  
  return sanitized;
}

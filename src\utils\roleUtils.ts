import { ProjectRole } from '@/hooks/usePermissions';

// Definição dos roles com metadata
export const PROJECT_ROLES = [
  { value: 'admin', label: 'Administrador', color: 'bg-red-100 text-red-800' },
  { value: 'manager', label: '<PERSON><PERSON><PERSON>', color: 'bg-purple-100 text-purple-800' },
  { value: 'editor', label: 'Editor', color: 'bg-blue-100 text-blue-800' },
  { value: 'executor', label: 'Executor', color: 'bg-green-100 text-green-800' },
  { value: 'approver', label: 'Aprovador', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'member', label: 'Membro', color: 'bg-gray-100 text-gray-800' },
] as const;

/**
 * Obtém informações de um role específico
 */
export const getRoleInfo = (role: string) => {
  return PROJECT_ROLES.find(r => r.value === role) || PROJECT_ROLES[5]; // default to 'member'
};

/**
 * Verifica se um role é válido
 */
export const isValidProjectRole = (role: string): role is ProjectRole => {
  return PROJECT_ROLES.some(r => r.value === role);
};

/**
 * Obtém todos os valores de roles válidos
 */
export const getValidProjectRoles = (): ProjectRole[] => {
  return PROJECT_ROLES.map(r => r.value as ProjectRole);
};

/**
 * Ordena roles por hierarquia (admin primeiro, member por último)
 */
export const sortRolesByHierarchy = (roles: ProjectRole[]): ProjectRole[] => {
  const hierarchy: ProjectRole[] = ['admin', 'manager', 'editor', 'executor', 'approver', 'member'];
  return roles.sort((a, b) => hierarchy.indexOf(a) - hierarchy.indexOf(b));
};

/**
 * Verifica se um role tem precedência sobre outro
 */
export const hasRolePrecedence = (role1: ProjectRole, role2: ProjectRole): boolean => {
  const hierarchy: ProjectRole[] = ['admin', 'manager', 'editor', 'executor', 'approver', 'member'];
  return hierarchy.indexOf(role1) < hierarchy.indexOf(role2);
};

/**
 * Obtém o role mais alto de uma lista
 */
export const getHighestRole = (roles: ProjectRole[]): ProjectRole | null => {
  if (roles.length === 0) return null;
  const sorted = sortRolesByHierarchy(roles);
  return sorted[0];
};

/**
 * Formata uma lista de roles para exibição
 */
export const formatRolesForDisplay = (roles: ProjectRole[]): string => {
  if (roles.length === 0) return 'Nenhum';
  if (roles.length === 1) return getRoleInfo(roles[0]).label;
  
  const sorted = sortRolesByHierarchy(roles);
  const labels = sorted.map(role => getRoleInfo(role).label);
  
  if (labels.length === 2) {
    return labels.join(' e ');
  }
  
  return labels.slice(0, -1).join(', ') + ' e ' + labels[labels.length - 1];
};

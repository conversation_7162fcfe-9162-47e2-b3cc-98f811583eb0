import { supabase } from '@/lib/supabaseClient';
import { Project, ProjectStatus, DashboardMetrics, ProjectMemberProfile } from '@/types';

// Mock data - Em produção seria substituído por chamadas reais à API
const mockProjects: Project[] = [
  {
    id: '1',
    name: 'Redesign do Site Corporativo',
    description: 'Modernização completa da interface e experiência do usuário do site principal da empresa.',
    status: 'active',
    progress: 65,
    start_date: '2024-01-15',
    end_date: '2024-04-30',
    team: [
      { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'manager', isActive: true },
      { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'member', isActive: true },
      { id: '3', name: '<PERSON>', email: '<EMAIL>', role: 'member', isActive: true }
    ],
    owner: { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'manager', isActive: true },
    stages: [],
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z'
  },
  {
    id: '2',
    name: 'Campanha de Marketing Digital Q1',
    description: 'Estratégia completa de marketing digital para o primeiro trimestre, incluindo redes sociais, email marketing e campanhas pagas.',
    status: 'active',
    progress: 30,
    start_date: '2024-01-01',
    end_date: '2024-03-31',
    team: [
      { id: '4', name: 'Pedro Lima', email: '<EMAIL>', role: 'manager', isActive: true },
      { id: '5', name: 'Carla Mendes', email: '<EMAIL>', role: 'member', isActive: true }
    ],
    owner: { id: '4', name: 'Pedro Lima', email: '<EMAIL>', role: 'manager', isActive: true },
    stages: [],
    createdAt: '2023-12-15T09:00:00Z',
    updatedAt: '2024-01-18T11:20:00Z'
  },
  {
    id: '3',
    name: 'Implementação do Sistema ERP',
    description: 'Configuração e implementação do novo sistema ERP para otimizar os processos internos da empresa.',
    status: 'planning',
    progress: 10,
    start_date: '2024-02-01',
    end_date: '2024-08-30',
    team: [
      { id: '6', name: 'Roberto Oliveira', email: '<EMAIL>', role: 'manager', isActive: true },
      { id: '7', name: 'Fernanda Rocha', email: '<EMAIL>', role: 'member', isActive: true }
    ],
    owner: { id: '6', name: 'Roberto Oliveira', email: '<EMAIL>', role: 'manager', isActive: true },
    stages: [],
    createdAt: '2024-01-05T14:30:00Z',
    updatedAt: '2024-01-15T16:45:00Z'
  }
];

import { requireAuth } from '@/lib/authUtils';

export const projectService = {
  async list() {
    await requireAuth(); // Verificar autenticação
    const { data, error } = await supabase.from('projects').select('*');
    if (error) throw error;
    return data;
  },

  async getMyProjects(): Promise<Project[]> {
    console.log('Buscando projetos do usuário com RLS implementado...');
    
    try {
      await requireAuth(); // Verificar autenticação

      // Buscar usuário atual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      console.log('Buscando projetos para usuário:', user.id, user.email);

      // Com as políticas RLS implementadas, não precisamos mais de filtros complexos
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erro ao buscar projetos:', error);
        throw error;
      }

      console.log('Projetos encontrados para usuário:', data?.length || 0);
      return (data as Project[]) || [];
      
    } catch (error) {
      console.error('Erro inesperado na busca de projetos do usuário:', error);
      throw error;
    }
  },

  async getById(id: string) {
    await requireAuth(); // Verificar autenticação

    try {
      let { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Erro ao buscar projeto por ID:', error.message);

        // Se for erro de múltiplas linhas ou nenhuma linha, tentar sem .single()
        if (error.message.includes('multiple') || error.message.includes('rows returned') || error.code === 'PGRST116') {
          console.warn('Erro 406/PGRST116 detectado, tentando busca sem .single() para projeto:', id);
          const { data: multipleData, error: multipleError } = await supabase
            .from('projects')
            .select('*')
            .eq('id', id)
            .limit(1);

          if (multipleError) {
            console.error('Erro na busca alternativa:', multipleError);
            throw multipleError;
          }

          console.log('Resultado da busca alternativa:', multipleData?.length || 0, 'projetos encontrados');
          data = multipleData?.[0] || null;

          if (!data) {
            console.warn('Projeto não encontrado no banco:', id);
            // Listar projetos disponíveis para debug
            const { data: allProjects } = await supabase
              .from('projects')
              .select('id, name')
              .limit(5);
            console.log('Projetos disponíveis (primeiros 5):', allProjects);
            throw new Error(`Projeto não encontrado: ${id}`);
          }

          console.log('Projeto encontrado via busca alternativa:', data.name);
        } else {
          throw error;
        }
      }

      return data;
    } catch (error) {
      console.error('Erro inesperado ao buscar projeto:', error);
      throw error;
    }
  },

  async create(project: any): Promise<Project> {
    await requireAuth(); // Verificar autenticação
    // Log do payload enviado
    console.log('Payload enviado para criação de projeto:', project);
    // Tentar logar o usuário autenticado
    try {
      const { data: { user } } = await supabase.auth.getUser();
      console.log('ID do usuário autenticado (supabase.auth.getUser()):', user?.id);
    } catch (e) {
      console.warn('Não foi possível obter o usuário autenticado para log:', e);
    }
    // Faz o insert, mas ignora o retorno
    const { error } = await supabase.from('projects').insert([project]);
    if (error) throw error;

    // Busca o projeto recém-criado
    const { data, error: selectError } = await supabase
      .from('projects')
      .select('*')
      .eq('name', project.name)
      .eq('owner_id', project.owner_id)
      .order('created_at', { ascending: false })
      .limit(1);
    console.log('Retorno do select após insert:', { data, selectError });
    if (selectError) throw selectError;
    if (!data || !data[0]) {
      throw new Error('Projeto criado, mas não foi possível recuperar o registro.');
    }
    return data[0] as Project;
  },

  async update(id: string, updates: any) {
    const { data, error } = await supabase.from('projects').update(updates).eq('id', id).single();
    if (error) throw error;
    return data;
  },

  async remove(id: string) {
    const { error } = await supabase.from('projects').delete().eq('id', id);
    if (error) throw error;
  },

  async getProjects(filters?: {
    status?: ProjectStatus[];
    responsibleId?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
  }): Promise<Project[]> {
    console.log('Buscando projetos com políticas RLS implementadas, filtros:', filters);
    
    try {
      await requireAuth(); // Verificar autenticação
      
      // Com as políticas RLS implementadas, uma consulta simples já retorna os projetos corretos
      let query = supabase
        .from('projects')
        .select('*');

      // As políticas RLS já filtram automaticamente os projetos acessíveis
      // Não precisamos mais da lógica complexa de busca manual

      // Filtro de status múltiplo
      if (filters?.status && filters.status.length > 0) {
        query = query.in('status', filters.status);
      }

      // Filtro de datas
      if (filters?.startDate) {
        query = query.gte('start_date', filters.startDate);
      }
      if (filters?.endDate) {
        query = query.lte('end_date', filters.endDate);
      }

      // Filtro de busca textual
      if (filters?.search) {
        query = query.ilike('name', `%${filters.search}%`);
      }

      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) {
        console.error('Erro ao buscar projetos:', error);
        throw error;
      }

      console.log('Projetos encontrados (com RLS):', data?.length || 0);
      return (data as Project[]) || [];
      
    } catch (error) {
      console.error('Erro inesperado na busca de projetos:', error);
      throw error;
    }
  },
  
  async getProjectMembers(projectId: string): Promise<ProjectMemberProfile[] | null> {
    const { data, error } = await supabase
      .from('project_members')
      .select(`
        id,
        role,
        profile:profiles!project_members_user_id_fkey (
          id,
          name,
          email,
          avatar_url
        )
      `)
      .eq('project_id', projectId);

    if (error) {
      console.error('Erro ao buscar membros do projeto:', error);
      throw error;
    }

    return data;
  },

  async getDashboardMetrics(): Promise<DashboardMetrics> {
    console.log('Calculando métricas reais do dashboard');
    const { data: projects, error } = await supabase.from('projects').select('*');
    if (error) throw error;
    const totalProjects = projects?.length || 0;
    const activeProjects = projects?.filter((p: any) => p.status === 'active').length || 0;
    const completedTasks = projects?.reduce((acc: number, p: any) => acc + (p.completedTasks || 0), 0) || 0;
    const pendingTasks = projects?.reduce((acc: number, p: any) => acc + (p.pendingTasks || 0), 0) || 0;
    const teamMembers = projects?.reduce((acc: number, p: any) => acc + (Array.isArray(p.team) ? p.team.length : 0), 0) || 0;
    const avgProgress = projects && projects.length > 0 ? Math.round(projects.reduce((acc: number, p: any) => acc + (p.progress || 0), 0) / projects.length) : 0;
    const metrics: DashboardMetrics = {
      totalProjects,
      activeProjects,
      completedTasks,
      pendingTasks,
      teamMembers,
      avgProgress
    };
    console.log('Métricas reais calculadas:', metrics);
    return metrics;
  },

  async saveContentBlocks(projectId: string, blocks: any[]) {
    // Salva os blocos de conteúdo no campo 'content' do projeto
    const { data, error } = await supabase
      .from('projects')
      .update({ content: blocks })
      .eq('id', projectId)
      .single();
    if (error) throw error;
    return data;
  }
};

/**
 * Verifica se é possível inserir um membro em project_members sem violar constraints ou integridade referencial.
 * Retorna { ok: true } se pode inserir, ou { ok: false, motivo, detalhe } se não pode.
 */
export async function verificarAntesDeInserirMembro({ project_id, user_id, role = 'member' }: { project_id: string, user_id: string, role?: string }) {
  // 1. Verifica se o user_id existe em profiles
  const { data: user, error: userError } = await supabase
    .from('profiles')
    .select('id')
    .eq('id', user_id)
    .single();
  if (userError || !user) {
    return { ok: false, motivo: 'Usuário não existe em profiles', detalhe: userError };
  }

  // 2. Verifica se o project_id existe em projects
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('id')
    .eq('id', project_id)
    .single();
  if (projectError || !project) {
    return { ok: false, motivo: 'Projeto não existe em projects', detalhe: projectError };
  }

  // 3. Verifica se já existe esse membro no projeto com o mesmo papel
  const { data: membroExistente, error: membroError } = await supabase
    .from('project_members')
    .select('id')
    .eq('project_id', project_id)
    .eq('user_id', user_id)
    .eq('role', role)
    .maybeSingle();
  if (membroError) {
    return { ok: false, motivo: 'Erro ao buscar membro existente', detalhe: membroError };
  }
  if (membroExistente) {
    return { ok: false, motivo: 'Membro já existe no projeto com esse papel' };
  }

  // 4. (Opcional) Verifica se o role é válido
  const rolesValidos = ['admin', 'manager', 'editor', 'executor', 'approver', 'member'];
  if (!rolesValidos.includes(role)) {
    return { ok: false, motivo: 'Papel (role) inválido' };
  }

  // Tudo certo!
  return { ok: true };
}

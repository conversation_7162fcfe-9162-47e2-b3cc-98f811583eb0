name: Validate Cursor Rules

on:
  push:
    paths:
      - '.project-rules/**'
  pull_request:
    paths:
      - '.project-rules/**'

jobs:
  check-files:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Check required files
        run: |
          required=(
            ".project-rules/cursorrules.md"
            ".project-rules/best-practices-cursor-ai.md"
            ".project-rules/arquitetura.md"
            ".project-rules/rbac.md"
            ".project-rules/rbac-por-papeis.md"
            ".project-rules/ux-ui.md"
            ".project-rules/padronizacao-blocos-editor.md"
            ".project-rules/sugestoes-implementacao.md"
            ".project-rules/estrutura-src.md"
            ".project-rules/plano-projeto-unificado.md"
            ".project-rules/software-architecture.md"
          )
          for f in "${required[@]}"; do
            if [ ! -f "$f" ]; then
              echo "Arquivo obrigatório ausente: $f" >&2
              exit 1
            fi
          done 
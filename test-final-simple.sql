-- TESTE FINAL SIMPLES - EXECUTE APÓS fix-emergency-rls.sql
-- Este teste verifica se todos os problemas foram resolvidos

-- 1. Status das tabelas (deve mostrar todas com RLS desabilitado)
SELECT 
    tablename,
    rowsecurity,
    CASE 
        WHEN rowsecurity = false THEN '✅ OK'
        ELSE '❌ PROBLEMA'
    END as status
FROM pg_tables 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
  AND schemaname = 'public'
ORDER BY tablename;

-- 2. Políticas restantes (deve retornar vazio)
SELECT 
    tablename,
    count(*) as policies_count,
    CASE 
        WHEN count(*) = 0 THEN '✅ OK'
        ELSE '❌ AINDA HÁ POLÍTICAS'
    END as status
FROM pg_policies 
WHERE tablename IN ('tasks', 'evidence', 'task_content_blocks') 
  AND schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;

-- 3. Usuário existe (deve retornar 1 linha)
SELECT 
    CASE 
        WHEN count(*) = 1 THEN '✅ USUÁRIO EXISTE'
        ELSE '❌ USUÁRIO NÃO ENCONTRADO'
    END as user_status
FROM public.profiles 
WHERE id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 4. Teste de acesso às tabelas (deve funcionar sem erro)
SELECT 
    'TASKS' as tabela,
    count(*) as total,
    '✅ ACESSO OK' as status
FROM public.tasks

UNION ALL

SELECT 
    'EVIDENCE' as tabela,
    count(*) as total,
    '✅ ACESSO OK' as status
FROM public.evidence

UNION ALL

SELECT 
    'TASK_CONTENT_BLOCKS' as tabela,
    count(*) as total,
    '✅ ACESSO OK' as status
FROM public.task_content_blocks

ORDER BY tabela;

-- SE TODOS OS TESTES MOSTRAREM ✅, REINICIE A APLICAÇÃO E TESTE O UPLOAD!

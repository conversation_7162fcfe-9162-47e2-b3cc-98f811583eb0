import React from 'react';
import { SidebarProvider, Sidebar, SidebarTrigger, useSidebarMenu, useSidebar } from '@/components/ui/sidebar';
import { Header } from '@/components/layout/Header';
import { useNavigate, useLocation } from 'react-router-dom';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface AppLayoutProps {
  children: React.ReactNode;
  onNewProject?: () => void;
}

const SidebarMenuButton: React.FC<{
  icon: React.ReactNode;
  label: string;
  active: boolean;
  onClick: () => void;
  collapsed: boolean;
}> = ({ icon, label, active, onClick, collapsed }) => {
  if (collapsed) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {React.cloneElement(
            <button
              className={`flex items-center justify-center w-10 h-10 rounded text-gray-800 text-sm font-medium transition-colors hover:bg-gray-100 ${active ? 'bg-gray-200 font-bold' : ''}`}
              onClick={onClick}
            >
              {icon}
            </button>,
            { ref: React.useRef() }
          )}
        </TooltipTrigger>
        <TooltipContent side="right">{label}</TooltipContent>
      </Tooltip>
    );
  }
  return (
    <button
      className={`flex items-center gap-3 px-3 py-2 rounded text-gray-800 text-sm font-medium transition-colors hover:bg-gray-100 ${active ? 'bg-gray-200 font-bold' : ''}`}
      onClick={onClick}
    >
      {icon}
      <span>{label}</span>
    </button>
  );
};

const SidebarMenuArea: React.FC = () => {
  const menuItems = useSidebarMenu();
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = useSidebar();
  const collapsed = state === 'collapsed';

  return (
    <div className={`flex flex-col h-full ${collapsed ? 'p-1 items-center w-full' : 'p-4'} gap-2 transition-all duration-200`}>
      <SidebarTrigger className={`mb-2 ${collapsed ? 'self-center' : 'self-end'}`} />
      {menuItems.map((item) => (
        <SidebarMenuButton
          key={item.label}
          icon={item.icon}
          label={item.label}
          active={item.path && (location.pathname.startsWith(item.path) && item.path !== '/' || item.path === '/' && location.pathname === '/')}
          onClick={item.onClick || (() => item.path && navigate(item.path))}
          collapsed={collapsed}
        />
      ))}
    </div>
  );
};

export const AppLayout: React.FC<AppLayoutProps> = ({ children, onNewProject }) => {
  return (
    <SidebarProvider>
      <div className="min-h-screen bg-gray-50">
        <div className="fixed top-0 left-0 w-full z-50">
          <Header onNewProject={onNewProject} />
        </div>
        <div className="flex pt-16 min-h-screen">
          <Sidebar className="bg-white border-r" collapsible="icon" />
          <div className="flex-1 flex flex-col min-w-0">
            <main className="flex-1">
              {children}
            </main>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
};
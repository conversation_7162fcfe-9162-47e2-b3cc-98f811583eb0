import { presetToBlockConfigButton, defaultBlockConfig } from '../config-panel/constants/migration';
import { blockTypePresetsMap } from '../config-panel/constants/block-types';

describe('Button Reset Functionality', () => {
  it('should return default button config when no preset is provided', () => {
    const result = presetToBlockConfigButton({});
    expect(result).toEqual(defaultBlockConfig.button);
  });

  it('should return default button config when preset has no button', () => {
    const result = presetToBlockConfigButton({ card: { backgroundColor: '#fff' } });
    expect(result).toEqual(defaultBlockConfig.button);
  });

  it('should convert video preset to button config correctly', () => {
    const videoPreset = blockTypePresetsMap.video?.default;
    if (videoPreset) {
      const result = presetToBlockConfigButton(videoPreset);
      
      expect(result).toHaveProperty('backgroundColor');
      expect(result).toHaveProperty('color');
      expect(result).toHaveProperty('style');
      expect(result).toHaveProperty('size');
      expect(result).toHaveProperty('position');
      expect(result).toHaveProperty('border');
      expect(result).toHaveProperty('shadow');
      expect(result).toHaveProperty('hover');
    }
  });

  it('should have all required properties in default button config', () => {
    const buttonConfig = defaultBlockConfig.button;
    
    expect(buttonConfig).toHaveProperty('backgroundColor');
    expect(buttonConfig).toHaveProperty('color');
    expect(buttonConfig).toHaveProperty('style');
    expect(buttonConfig).toHaveProperty('size');
    expect(buttonConfig).toHaveProperty('position');
    expect(buttonConfig).toHaveProperty('border');
    expect(buttonConfig).toHaveProperty('shadow');
    expect(buttonConfig).toHaveProperty('hover');
    expect(buttonConfig).toHaveProperty('text');
    expect(buttonConfig).toHaveProperty('url');
    expect(buttonConfig).toHaveProperty('newTab');
    expect(buttonConfig).toHaveProperty('icon');
    expect(buttonConfig).toHaveProperty('iconPosition');
  });

  it('should have correct default values', () => {
    const buttonConfig = defaultBlockConfig.button;

    expect(buttonConfig?.backgroundColor).toBe('#7c3aed');
    expect(buttonConfig?.color).toBe('#ffffff');
    expect(buttonConfig?.style).toBe('rounded');
    expect(buttonConfig?.size).toBe('medium');
    expect(buttonConfig?.position).toBe('bottom-center');
    expect(buttonConfig?.border?.enabled).toBe(false);
    expect(buttonConfig?.shadow?.enabled).toBe(false);
    expect(buttonConfig?.hover?.enabled).toBe(false);
  });

  it('should have correct size mappings', () => {
    // Teste conceitual das configurações de tamanho
    const sizeConfigs = {
      small: { width: 'auto', description: 'Tamanho automático (antigo médio)' },
      medium: { width: '50%', description: 'Metade da largura do card' },
      large: { width: '100%', description: 'Largura total do card' }
    };

    expect(sizeConfigs.small.width).toBe('auto');
    expect(sizeConfigs.medium.width).toBe('50%');
    expect(sizeConfigs.large.width).toBe('100%');
  });

  it('should have correct position mappings for absolute positioning', () => {
    // Teste conceitual das configurações de posição absoluta
    const positionConfigs = {
      'top-left': { top: '12px', left: '12px', description: 'Canto superior esquerdo do card' },
      'top-center': { top: '12px', left: '50%', transform: 'translateX(-50%)', description: 'Centro superior do card' },
      'top-right': { top: '12px', right: '12px', description: 'Canto superior direito do card' },
      'bottom-left': { bottom: '12px', left: '12px', description: 'Canto inferior esquerdo do card' },
      'bottom-center': { bottom: '12px', left: '50%', transform: 'translateX(-50%)', description: 'Centro inferior do card' },
      'bottom-right': { bottom: '12px', right: '12px', description: 'Canto inferior direito do card' }
    };

    expect(positionConfigs['top-left'].top).toBe('12px');
    expect(positionConfigs['top-left'].left).toBe('12px');
    expect(positionConfigs['top-center'].transform).toBe('translateX(-50%)');
    expect(positionConfigs['bottom-right'].bottom).toBe('12px');
    expect(positionConfigs['bottom-right'].right).toBe('12px');
  });

  it('should have correct padding for button spacing', () => {
    // Teste conceitual das configurações de padding para espaçamento
    const paddingConfigs = {
      videoCard: { buttonHeight: 60, description: 'Padding maior para evitar sobreposição - VideoBlockCard' },
      coloredBlock: { buttonHeight: 64, description: 'Padding maior para evitar sobreposição - ColoredBlockEditor' }
    };

    expect(paddingConfigs.videoCard.buttonHeight).toBe(60);
    expect(paddingConfigs.coloredBlock.buttonHeight).toBe(64);
  });

  it('should have responsive design considerations', () => {
    // Teste conceitual das configurações responsivas
    const responsiveConfigs = {
      mobile: {
        maxWidth: 'calc(100% - 24px)',
        padding: '8px 16px',
        fontSize: '14px',
        minHeight: '36px',
        description: 'Configurações para mobile - altura compacta'
      },
      tablet: {
        padding: '10px 20px',
        fontSize: '14px',
        minHeight: '40px',
        description: 'Configurações para tablet - altura compacta'
      },
      desktop: {
        padding: '10px 24px',
        fontSize: '15px',
        minHeight: '42px',
        description: 'Configurações para desktop - altura mais compacta'
      },
      overflow: {
        container: 'overflow-hidden',
        button: 'text-overflow: ellipsis',
        description: 'Controle de overflow'
      }
    };

    expect(responsiveConfigs.mobile.maxWidth).toBe('calc(100% - 24px)');
    expect(responsiveConfigs.mobile.minHeight).toBe('36px');
    expect(responsiveConfigs.tablet.minHeight).toBe('40px');
    expect(responsiveConfigs.desktop.minHeight).toBe('42px');
    expect(responsiveConfigs.overflow.container).toBe('overflow-hidden');
  });
});

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Filter, 
  Plus, 
  Star, 
  Clock, 
  Tag,
  BookOpen,
  Copy,
  Eye
} from 'lucide-react';

import { QuestionTemplate, QuizQuestion, QuizQuestionType } from '@/types/quiz';

interface QuestionBankProps {
  onSelectQuestion: (question: QuizQuestion) => void;
  onClose: () => void;
}

// Dados mockados para demonstração
const mockQuestionTemplates: QuestionTemplate[] = [
  {
    id: 'template_1',
    title: 'Qual é a capital do Brasil?',
    description: 'Pergunta básica sobre geografia brasileira',
    type: 'single-choice',
    category: 'Geografia',
    tags: ['brasil', 'capital', 'básico'],
    difficulty: 'easy',
    question: {
      type: 'single-choice',
      title: 'Qual é a capital do Brasil?',
      required: true,
      options: [
        { id: 'opt1', text: 'Brasília', isCorrect: true },
        { id: 'opt2', text: 'São Paulo', isCorrect: false },
        { id: 'opt3', text: 'Rio de Janeiro', isCorrect: false },
        { id: 'opt4', text: 'Belo Horizonte', isCorrect: false }
      ],
      correctFeedback: 'Correto! Brasília é a capital federal do Brasil.',
      incorrectFeedback: 'Incorreto. A capital do Brasil é Brasília.'
    },
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    createdBy: 'admin',
    isPublic: true
  },
  {
    id: 'template_2',
    title: 'Verdadeiro ou Falso: O Brasil tem 26 estados',
    description: 'Pergunta sobre divisão territorial brasileira',
    type: 'true-false',
    category: 'Geografia',
    tags: ['brasil', 'estados', 'território'],
    difficulty: 'medium',
    question: {
      type: 'true-false',
      title: 'O Brasil tem 26 estados',
      required: true,
      correctAnswer: true,
      correctFeedback: 'Correto! O Brasil tem 26 estados mais o Distrito Federal.',
      incorrectFeedback: 'Incorreto. O Brasil tem 26 estados mais o Distrito Federal.'
    },
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-01-16'),
    createdBy: 'admin',
    isPublic: true
  },
  {
    id: 'template_3',
    title: 'Explique o conceito de democracia',
    description: 'Pergunta dissertativa sobre ciência política',
    type: 'open-text',
    category: 'Ciência Política',
    tags: ['democracia', 'política', 'conceito'],
    difficulty: 'hard',
    question: {
      type: 'open-text',
      title: 'Explique o conceito de democracia e suas principais características',
      required: true,
      openTextKeywords: ['democracia', 'participação', 'eleições', 'representação', 'povo'],
      correctFeedback: 'Boa resposta! Você demonstrou compreensão do conceito.',
      incorrectFeedback: 'Sua resposta pode ser mais completa. Considere os aspectos de participação popular e representação.'
    },
    createdAt: new Date('2024-01-17'),
    updatedAt: new Date('2024-01-17'),
    createdBy: 'professor1',
    isPublic: true
  }
];

export const QuestionBank: React.FC<QuestionBankProps> = ({
  onSelectQuestion,
  onClose
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  const categories = Array.from(new Set(mockQuestionTemplates.map(q => q.category)));
  const difficulties = ['easy', 'medium', 'hard'];
  const questionTypes: QuizQuestionType[] = ['single-choice', 'multiple-choice', 'true-false', 'open-text', 'ordering', 'matching'];

  const filteredQuestions = mockQuestionTemplates.filter(question => {
    const matchesSearch = question.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || question.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || question.difficulty === selectedDifficulty;
    const matchesType = selectedType === 'all' || question.type === selectedType;

    return matchesSearch && matchesCategory && matchesDifficulty && matchesType;
  });

  const handleSelectQuestion = (template: QuestionTemplate) => {
    const question: QuizQuestion = {
      id: `question_${Date.now()}`,
      points: 1,
      ...template.question
    };
    onSelectQuestion(question);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeLabel = (type: QuizQuestionType) => {
    const labels = {
      'single-choice': 'Escolha Única',
      'multiple-choice': 'Múltipla Escolha',
      'true-false': 'V/F',
      'open-text': 'Aberta',
      'ordering': 'Ordenação',
      'matching': 'Correspondência'
    };
    return labels[type];
  };

  const getTypeColor = (type: QuizQuestionType) => {
    const colors = {
      'single-choice': 'bg-blue-100 text-blue-800',
      'multiple-choice': 'bg-purple-100 text-purple-800',
      'true-false': 'bg-indigo-100 text-indigo-800',
      'open-text': 'bg-orange-100 text-orange-800',
      'ordering': 'bg-pink-100 text-pink-800',
      'matching': 'bg-cyan-100 text-cyan-800'
    };
    return colors[type];
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="w-5 h-5" />
              Banco de Perguntas
            </CardTitle>
            <Button variant="outline" onClick={onClose}>
              Fechar
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Filtros */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Buscar perguntas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 standard-field"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="standard-field">
                <SelectValue placeholder="Categoria" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as categorias</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
              <SelectTrigger className="standard-field">
                <SelectValue placeholder="Dificuldade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as dificuldades</SelectItem>
                {difficulties.map(difficulty => (
                  <SelectItem key={difficulty} value={difficulty}>
                    {difficulty === 'easy' ? 'Fácil' : difficulty === 'medium' ? 'Médio' : 'Difícil'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="standard-field">
                <SelectValue placeholder="Tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os tipos</SelectItem>
                {questionTypes.map(type => (
                  <SelectItem key={type} value={type}>{getTypeLabel(type)}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Lista de Perguntas */}
          <div className="max-h-96 overflow-y-auto space-y-3">
            {filteredQuestions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-semibold mb-2">Nenhuma pergunta encontrada</h3>
                <p className="text-sm">
                  Tente ajustar os filtros ou criar uma nova pergunta.
                </p>
              </div>
            ) : (
              filteredQuestions.map((template) => (
                <Card key={template.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={getTypeColor(template.type)}>
                            {getTypeLabel(template.type)}
                          </Badge>
                          <Badge className={getDifficultyColor(template.difficulty)}>
                            {template.difficulty === 'easy' ? 'Fácil' : 
                             template.difficulty === 'medium' ? 'Médio' : 'Difícil'}
                          </Badge>
                          <Badge variant="outline">
                            {template.category}
                          </Badge>
                        </div>
                        
                        <h4 className="font-medium text-sm mb-1 truncate">
                          {template.title}
                        </h4>
                        
                        {template.description && (
                          <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                            {template.description}
                          </p>
                        )}
                        
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Tag className="w-3 h-3" />
                          <span>{template.tags.join(', ')}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          title="Visualizar pergunta"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSelectQuestion(template)}
                          className="h-8 w-8 p-0"
                          title="Usar esta pergunta"
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Estatísticas */}
          <div className="border-t pt-4">
            <div className="flex justify-between items-center text-sm text-gray-600">
              <span>
                {filteredQuestions.length} de {mockQuestionTemplates.length} perguntas
              </span>
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <Star className="w-4 h-4" />
                  {mockQuestionTemplates.filter(q => q.isPublic).length} públicas
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  Atualizado hoje
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

-- =====================================================
-- CONFIGURAÇÃO DE STORAGE E BUCKETS
-- =====================================================
-- Contém: Buckets de storage, políticas de storage
-- Dependências: Supabase Storage
-- Versão: 2.0 - Julho 2025

-- Verificar se o storage está habilitado
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'storage') THEN
    RAISE NOTICE '⚠️ Storage não está habilitado. Pule este script se não usar storage.';
  ELSE
    RAISE NOTICE '✅ Storage verificado';
  END IF;
END $$;

-- =====================================================
-- CRIAÇÃO DE BUCKETS
-- =====================================================

-- Bucket para arquivos de tarefas
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'task-files',
    'task-files',
    false,
    52428800, -- 50MB
    ARRAY[
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'video/mp4',
      'video/avi',
      'video/quicktime',
      'audio/mpeg',
      'audio/wav',
      'application/zip',
      'application/x-zip-compressed'
    ]
  )
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Bucket para evidências
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'evidence-files',
    'evidence-files',
    false,
    104857600, -- 100MB
    ARRAY[
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'video/mp4',
      'video/avi',
      'video/quicktime',
      'audio/mpeg',
      'audio/wav'
    ]
  )
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Bucket para avatares (público)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'avatars',
    'avatars',
    true,
    5242880, -- 5MB
    ARRAY[
      'image/jpeg',
      'image/png',
      'image/webp'
    ]
  )
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Bucket para arquivos temporários
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'temp-files',
    'temp-files',
    false,
    26214400, -- 25MB
    NULL -- Todos os tipos permitidos
  )
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- =====================================================
-- POLÍTICAS DE STORAGE
-- =====================================================

-- Remover políticas existentes
DROP POLICY IF EXISTS "task_files_select" ON storage.objects;
DROP POLICY IF EXISTS "task_files_insert" ON storage.objects;
DROP POLICY IF EXISTS "task_files_update" ON storage.objects;
DROP POLICY IF EXISTS "task_files_delete" ON storage.objects;

DROP POLICY IF EXISTS "evidence_files_select" ON storage.objects;
DROP POLICY IF EXISTS "evidence_files_insert" ON storage.objects;
DROP POLICY IF EXISTS "evidence_files_update" ON storage.objects;
DROP POLICY IF EXISTS "evidence_files_delete" ON storage.objects;

DROP POLICY IF EXISTS "avatars_select" ON storage.objects;
DROP POLICY IF EXISTS "avatars_insert" ON storage.objects;
DROP POLICY IF EXISTS "avatars_update" ON storage.objects;
DROP POLICY IF EXISTS "avatars_delete" ON storage.objects;

DROP POLICY IF EXISTS "temp_files_select" ON storage.objects;
DROP POLICY IF EXISTS "temp_files_insert" ON storage.objects;
DROP POLICY IF EXISTS "temp_files_update" ON storage.objects;
DROP POLICY IF EXISTS "temp_files_delete" ON storage.objects;

-- =====================================================
-- POLÍTICAS PARA TASK-FILES
-- =====================================================

-- Usuários podem ver arquivos de tarefas acessíveis
CREATE POLICY "task_files_select"
  ON storage.objects
  FOR SELECT
  USING (
    bucket_id = 'task-files' AND
    (
      -- Arquivos próprios
      owner = auth.uid() OR
      -- Arquivos de tarefas onde o usuário participa
      name ~ '^task-[0-9a-f-]+/' AND
      EXISTS (
        SELECT 1 FROM public.tasks t
        WHERE t.id::text = split_part(name, '-', 2) AND
        (
          t.assigned_to = auth.uid() OR
          t.id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
          t.stage_id IN (
            SELECT s.id FROM public.stages s
            JOIN public.projects p ON s.project_id = p.id
            WHERE p.owner_id = auth.uid() OR
                  p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
          )
        )
      ) OR
      -- Admins podem ver tudo
      EXISTS (
        SELECT 1 FROM public.profiles p 
        WHERE p.id = auth.uid() AND p.role = 'admin'
      )
    )
  );

-- Usuários podem fazer upload de arquivos para tarefas onde participam
CREATE POLICY "task_files_insert"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'task-files' AND
    owner = auth.uid() AND
    (
      -- Arquivos de tarefas onde o usuário é executor ou membro do projeto
      name ~ '^task-[0-9a-f-]+/' AND
      EXISTS (
        SELECT 1 FROM public.tasks t
        WHERE t.id::text = split_part(name, '-', 2) AND
        (
          t.assigned_to = auth.uid() OR
          t.id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
          t.stage_id IN (
            SELECT s.id FROM public.stages s
            JOIN public.projects p ON s.project_id = p.id
            WHERE p.owner_id = auth.uid() OR
                  p.id IN (
                    SELECT project_id FROM public.project_members 
                    WHERE user_id = auth.uid() AND role IN ('manager', 'editor', 'admin')
                  )
          )
        )
      ) OR
      -- Admins podem fazer upload em qualquer lugar
      EXISTS (
        SELECT 1 FROM public.profiles p 
        WHERE p.id = auth.uid() AND p.role = 'admin'
      )
    )
  );

-- Usuários podem atualizar metadados dos próprios arquivos
CREATE POLICY "task_files_update"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'task-files' AND
    owner = auth.uid()
  );

-- Usuários podem deletar próprios arquivos
CREATE POLICY "task_files_delete"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'task-files' AND
    owner = auth.uid()
  );

-- =====================================================
-- POLÍTICAS PARA EVIDENCE-FILES
-- =====================================================

-- Usuários podem ver evidências de tarefas acessíveis
CREATE POLICY "evidence_files_select"
  ON storage.objects
  FOR SELECT
  USING (
    bucket_id = 'evidence-files' AND
    (
      -- Arquivos próprios
      owner = auth.uid() OR
      -- Evidências de tarefas onde o usuário participa
      name ~ '^task-[0-9a-f-]+/' AND
      EXISTS (
        SELECT 1 FROM public.tasks t
        WHERE t.id::text = split_part(name, '-', 2) AND
        (
          t.assigned_to = auth.uid() OR
          t.id IN (SELECT task_id FROM public.task_executors WHERE user_id = auth.uid()) OR
          t.id IN (SELECT task_id FROM public.task_approvers WHERE user_id = auth.uid()) OR
          t.stage_id IN (
            SELECT s.id FROM public.stages s
            JOIN public.projects p ON s.project_id = p.id
            WHERE p.owner_id = auth.uid() OR
                  p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
          )
        )
      ) OR
      -- Admins podem ver tudo
      EXISTS (
        SELECT 1 FROM public.profiles p 
        WHERE p.id = auth.uid() AND p.role = 'admin'
      )
    )
  );

-- Apenas executores podem fazer upload de evidências
CREATE POLICY "evidence_files_insert"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'evidence-files' AND
    owner = auth.uid() AND
    (
      -- Evidências de tarefas onde o usuário é executor
      name ~ '^task-[0-9a-f-]+/' AND
      EXISTS (
        SELECT 1 FROM public.task_executors te
        WHERE te.task_id::text = split_part(name, '-', 2) AND
              te.user_id = auth.uid()
      ) OR
      -- Admins podem fazer upload em qualquer lugar
      EXISTS (
        SELECT 1 FROM public.profiles p 
        WHERE p.id = auth.uid() AND p.role = 'admin'
      )
    )
  );

-- Usuários podem atualizar metadados das próprias evidências
CREATE POLICY "evidence_files_update"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'evidence-files' AND
    owner = auth.uid()
  );

-- Usuários podem deletar próprias evidências não aprovadas
CREATE POLICY "evidence_files_delete"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'evidence-files' AND
    owner = auth.uid() AND
    (
      -- Verificar se não está aprovada
      NOT EXISTS (
        SELECT 1 FROM public.evidence e
        WHERE e.content = name AND e.status = 'approved'
      ) AND
      NOT EXISTS (
        SELECT 1 FROM public.task_attachments ta
        WHERE ta.file_url LIKE '%' || name || '%' AND ta.status = 'approved'
      )
    )
  );

-- =====================================================
-- POLÍTICAS PARA AVATARS
-- =====================================================

-- Todos podem ver avatares (bucket público)
CREATE POLICY "avatars_select"
  ON storage.objects
  FOR SELECT
  USING (bucket_id = 'avatars');

-- Usuários podem fazer upload do próprio avatar
CREATE POLICY "avatars_insert"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'avatars' AND
    owner = auth.uid() AND
    name ~ '^[0-9a-f-]+/avatar\.(jpg|jpeg|png|webp)$'
  );

-- Usuários podem atualizar próprio avatar
CREATE POLICY "avatars_update"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'avatars' AND
    owner = auth.uid()
  );

-- Usuários podem deletar próprio avatar
CREATE POLICY "avatars_delete"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'avatars' AND
    owner = auth.uid()
  );

-- =====================================================
-- POLÍTICAS PARA TEMP-FILES
-- =====================================================

-- Usuários podem ver apenas próprios arquivos temporários
CREATE POLICY "temp_files_select"
  ON storage.objects
  FOR SELECT
  USING (
    bucket_id = 'temp-files' AND
    owner = auth.uid()
  );

-- Usuários podem fazer upload de arquivos temporários
CREATE POLICY "temp_files_insert"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'temp-files' AND
    owner = auth.uid()
  );

-- Usuários podem atualizar próprios arquivos temporários
CREATE POLICY "temp_files_update"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'temp-files' AND
    owner = auth.uid()
  );

-- Usuários podem deletar próprios arquivos temporários
CREATE POLICY "temp_files_delete"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'temp-files' AND
    owner = auth.uid()
  );

-- =====================================================
-- FUNCTION PARA LIMPEZA AUTOMÁTICA
-- =====================================================

-- Function para limpar arquivos temporários antigos
CREATE OR REPLACE FUNCTION public.cleanup_temp_files()
RETURNS void AS $$
BEGIN
  -- Deletar arquivos temporários com mais de 24 horas
  DELETE FROM storage.objects
  WHERE bucket_id = 'temp-files'
    AND created_at < NOW() - INTERVAL '24 hours';
  
  RAISE NOTICE 'Arquivos temporários antigos removidos';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- HELPER FUNCTIONS PARA STORAGE
-- =====================================================

-- Function para obter URL assinada para download
CREATE OR REPLACE FUNCTION public.get_signed_url(
  bucket_name text,
  object_path text,
  expires_in integer DEFAULT 3600
) RETURNS text AS $$
BEGIN
  -- Esta function deve ser implementada usando a API do Supabase
  -- Retorna uma URL temporária para download do arquivo
  RETURN format('https://your-project.supabase.co/storage/v1/object/sign/%s/%s?expires_in=%s', 
                bucket_name, object_path, expires_in);
END;
$$ LANGUAGE plpgsql;

-- Function para verificar se usuário pode acessar arquivo
CREATE OR REPLACE FUNCTION public.can_access_file(
  bucket_name text,
  object_path text,
  user_id uuid
) RETURNS boolean AS $$
BEGIN
  -- Verificar se o usuário tem permissão para acessar o arquivo
  CASE bucket_name
    WHEN 'task-files' THEN
      RETURN EXISTS (
        SELECT 1 FROM storage.objects o
        WHERE o.bucket_id = bucket_name 
          AND o.name = object_path
          AND (
            o.owner = user_id OR
            EXISTS (
              SELECT 1 FROM public.profiles p 
              WHERE p.id = user_id AND p.role = 'admin'
            )
          )
      );
    WHEN 'evidence-files' THEN
      RETURN EXISTS (
        SELECT 1 FROM storage.objects o
        WHERE o.bucket_id = bucket_name 
          AND o.name = object_path
          AND (
            o.owner = user_id OR
            EXISTS (
              SELECT 1 FROM public.profiles p 
              WHERE p.id = user_id AND p.role = 'admin'
            )
          )
      );
    WHEN 'avatars' THEN
      RETURN true; -- Avatares são públicos
    WHEN 'temp-files' THEN
      RETURN EXISTS (
        SELECT 1 FROM storage.objects o
        WHERE o.bucket_id = bucket_name 
          AND o.name = object_path
          AND o.owner = user_id
      );
    ELSE
      RETURN false;
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VALIDAÇÃO E LOGS
-- =====================================================

DO $$
DECLARE
  bucket_count INTEGER;
  policy_count INTEGER;
BEGIN
  -- Contar buckets criados
  SELECT COUNT(*) INTO bucket_count
  FROM storage.buckets
  WHERE id IN ('task-files', 'evidence-files', 'avatars', 'temp-files');
  
  -- Contar políticas de storage
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies
  WHERE schemaname = 'storage' AND tablename = 'objects';
  
  RAISE NOTICE '✅ Storage configurado com sucesso!';
  RAISE NOTICE '🗂️ Buckets criados: %', bucket_count;
  RAISE NOTICE '🔒 Políticas de storage: %', policy_count;
  RAISE NOTICE '🧹 Function de limpeza automática criada';
  RAISE NOTICE '🔧 Helper functions criadas';
END $$;

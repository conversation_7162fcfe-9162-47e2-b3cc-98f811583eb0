## **Sistema de Permissões Baseado em Papéis (RBAC)**
### **Gerenciamento de Projetos**

---

## **1. Pa<PERSON><PERSON>is Globais do Sistema**

### **🔹 Admin (Administrador)**
**Escopo:** Acesso total e irrestrito ao sistema

**Permissões Completas:**
- ✅ **Gestão de Usuários:** <PERSON><PERSON>r, editar, excluir e gerenciar todos os usuários
- ✅ **Gestão de Projetos:** Criar, editar, excluir e gerenciar qualquer projeto
- ✅ **Controle Total:** Acesso a todas as funcionalidades sem restrições
- ✅ **Visibilidade:** Todos os projetos, etapas, tarefas e dados do sistema
- ✅ **Status de Projeto:** Pode alterar qualquer projeto para "Concluído"
- ✅ **Membros:** Pode incluir/remover membros e definir papéis em qualquer projeto

---

### **🔹 Manager (Gerente)**
**Escopo:** Gestão completa de seus próprios projetos

**Permissões de Projeto:**
- ✅ **Criar Projetos:** Novos projetos sob sua responsabilidade
- ✅ **Editar/Excluir:** Apenas seus próprios projetos
- ✅ **Gestão de Membros:** Incluir/remover membros em seus projetos
- ✅ **Definir Papéis:** Atribuir papéis de projeto (Editor, Executor, Aprovador)
- ✅ **Conclusão:** Alterar status de seus projetos para "Concluído"
- ✅ **Visibilidade:** Apenas projetos criados por ele ou onde é gerente

**Restrições:**
- ❌ **Usuários:** Não pode criar, editar ou excluir usuários do sistema
- ❌ **Projetos Externos:** Não pode acessar projetos de outros gerentes

---

### **🔹 User (Usuário)**
**Escopo:** Participação limitada em projetos como membro

**Permissões Básicas:**
- ✅ **Acesso a Projetos:** Apenas onde é membro ativo
- ✅ **Execução de Tarefas:** Conforme papel atribuído no projeto
- ✅ **Visualização:** Limitada aos projetos onde possui papel definido

**Restrições Globais:**
- ❌ **Criar Projetos:** Não pode criar novos projetos
- ❌ **Gestão de Usuários:** Sem acesso ao gerenciamento de usuários
- ❌ **Conclusão de Projetos:** Não pode alterar status para "Concluído"
- ❌ **Gestão de Membros:** Não pode incluir/remover membros

---

## **2. Papéis Específicos de Projeto**

### **🔸 Editor de Projeto**
**Função:** Criação e edição de conteúdo estrutural

**Permissões Específicas:**
- ✅ **Etapas:** Cadastrar, editar e organizar etapas do projeto
- ✅ **Tarefas:** Cadastrar, editar e configurar tarefas
- ✅ **Conteúdo:** Modificar descrições, requisitos e especificações
- ✅ **Estrutura:** Organizar fluxo de trabalho e dependências

**Limitações:**
- ❌ **Execução:** Não pode executar ou marcar tarefas como concluídas
- ❌ **Aprovação:** Não pode aprovar tarefas executadas
- ❌ **Evidências:** Não pode incluir evidências de execução

---

### **🔸 Executor de Projeto**
**Função:** Execução operacional de tarefas

**Permissões Específicas:**
- ✅ **Execução:** Executar tarefas atribuídas especificamente a ele
- ✅ **Evidências:** Incluir documentos, fotos, relatórios como comprovação
- ✅ **Status:** Alterar suas próprias tarefas para "Concluído"
- ✅ **Progresso:** Atualizar percentual de conclusão e observações

**Limitações:**
- ❌ **Estrutura:** Não pode cadastrar ou editar etapas/tarefas
- ❌ **Conteúdo:** Não pode modificar descrições ou requisitos
- ❌ **Aprovação:** Não pode aprovar tarefas (próprias ou de outros)

---

### **🔸 Aprovador de Projeto**
**Função:** Validação e aprovação de entregas

**Permissões Específicas:**
- ✅ **Aprovação:** Aprovar tarefas com status "Concluído"
- ✅ **Revisão:** Analisar evidências e qualidade das entregas
- ✅ **Feedback:** Fornecer comentários e solicitar correções
- ✅ **Rejeição:** Reprovar e solicitar reexecução quando necessário

**Limitações:**
- ❌ **Execução:** Não pode executar tarefas ou incluir evidências
- ❌ **Edição:** Não pode modificar conteúdo de etapas/tarefas
- ❌ **Pré-aprovação:** Só pode aprovar após status "Concluído"

---

## **3. Regras de Negócio e Fluxos**

### **📋 Fluxo de Aprovação**
1. **Executor** marca tarefa como "Concluído" + evidências
2. **Aprovador** recebe notificação para revisão
3. **Aprovador** pode: Aprovar ✅ ou Reprovar ❌
4. Se reprovada: volta para **Executor** com feedback

### **👥 Gestão de Membros**
- **Admin:** Pode gerenciar membros em qualquer projeto
- **Manager:** Pode gerenciar membros apenas em seus projetos
- **User:** Não pode gerenciar membros

### **🔍 Visibilidade e Acesso**
- **Princípio:** Usuários só veem projetos onde têm papel definido
- **Exceção:** Admin vê tudo, Manager vê seus projetos
- **Segurança:** Dados isolados por projeto e papel

### **⚡ Múltiplos Papéis**
- Um usuário pode ter **múltiplos papéis** no mesmo projeto
- Permissões são **cumulativas** (soma dos papéis)
- Exemplo: Editor + Executor = pode criar tarefas E executá-las

---

## **4. Matriz de Permissões Consolidada**

<table class="data-table">
  <thead>
    <tr>
      <th scope="col">Funcionalidade</th>
      <th scope="col">Admin</th>
      <th scope="col">Manager</th>
      <th scope="col">Editor</th>
      <th scope="col">Executor</th>
      <th scope="col">Aprovador</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Criar usuários</td>
      <td>✅</td>
      <td>❌</td>
      <td>❌</td>
      <td>❌</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Criar projetos</td>
      <td>✅</td>
      <td>✅</td>
      <td>❌</td>
      <td>❌</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Gerenciar membros</td>
      <td>✅</td>
      <td>✅*</td>
      <td>❌</td>
      <td>❌</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Criar/editar etapas</td>
      <td>✅</td>
      <td>✅*</td>
      <td>✅*</td>
      <td>❌</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Executar tarefas</td>
      <td>✅</td>
      <td>✅*</td>
      <td>❌</td>
      <td>✅*</td>
      <td>❌</td>
    </tr>
    <tr>
      <td>Aprovar tarefas</td>
      <td>✅</td>
      <td>✅*</td>
      <td>❌</td>
      <td>❌</td>
      <td>✅*</td>
    </tr>
    <tr>
      <td>Concluir projeto</td>
      <td>✅</td>
      <td>✅*</td>
      <td>❌</td>
      <td>❌</td>
      <td>❌</td>
    </tr>
  </tbody>
</table>

**Legenda:** `*` = Apenas em projetos próprios/atribuídos

---

## **5. Considerações de Implementação**

### **🔐 Segurança**
- Validação de permissões em **todas** as operações
- Logs de auditoria para ações críticas
- Isolamento de dados por projeto e papel

### **🚀 Escalabilidade**
- Sistema preparado para novos papéis
- Permissões granulares e flexíveis
- Herança de permissões por hierarquia

### **📱 Experiência do Usuário**
- Interface adaptada ao papel do usuário
- Ocultação de funcionalidades não permitidas
- Feedback claro sobre restrições de acesso

Este modelo oferece **flexibilidade**, **segurança** e **clareza** na gestão de permissões. Precisa de algum ajuste específico?
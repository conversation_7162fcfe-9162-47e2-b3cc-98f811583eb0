-- =====================
-- VERIFICAÇÃO: STATUS ATUAL DA TABELA PROFILES
-- Data: 2024-12-19
-- Objetivo: Diagnosticar problemas antes de aplicar correções
-- =====================

-- VERIFICAÇÃO 1: Status da tabela profiles
-- =====================
select 
    'STATUS DA TABELA PROFILES:' as info,
    schemaname,
    tablename,
    tableowner,
    case 
        when rowsecurity then 'RLS HABILITADO'
        else 'RLS DESABILITADO'
    end as rls_status
from pg_tables 
where tablename = 'profiles';

-- VERIFICAÇÃO 2: Políticas atuais
-- =====================
select 
    'POLÍTICAS ATUAIS:' as info,
    policyname,
    cmd,
    permissive,
    roles,
    substring(qual, 1, 100) as condition_preview
from pg_policies 
where tablename = 'profiles' and schemaname = 'public'
order by cmd, policyname;

-- VERIFICAÇÃO 3: Constraints da tabela
-- =====================
select 
    'CONSTRAINTS:' as info,
    conname as constraint_name,
    contype as type,
    case contype
        when 'p' then 'PRIMARY KEY'
        when 'u' then 'UNIQUE'
        when 'f' then 'FOREIGN KEY'
        when 'c' then 'CHECK'
        else contype::text
    end as constraint_type,
    substring(pg_get_constraintdef(oid), 1, 100) as definition_preview
from pg_constraint 
where conrelid = 'public.profiles'::regclass
order by contype, conname;

-- VERIFICAÇÃO 4: Função is_admin
-- =====================
select 
    'FUNÇÃO IS_ADMIN:' as info,
    case 
        when exists(select 1 from pg_proc where proname = 'is_admin') 
        then 'EXISTE'
        else 'NÃO EXISTE'
    end as status,
    case 
        when exists(select 1 from pg_proc where proname = 'is_admin') 
        then (select pg_get_functiondef(oid) from pg_proc where proname = 'is_admin' limit 1)
        else 'N/A'
    end as definition;

-- VERIFICAÇÃO 5: Registros na tabela
-- =====================
select 
    'REGISTROS NA TABELA:' as info,
    count(*) as total_profiles,
    count(case when role = 'admin' then 1 end) as admin_count,
    count(case when role = 'member' then 1 end) as member_count,
    count(case when is_active = true then 1 end) as active_count,
    count(case when is_active = false then 1 end) as inactive_count
from public.profiles;

-- VERIFICAÇÃO 6: Duplicatas potenciais
-- =====================
select 'VERIFICAÇÃO DE DUPLICATAS:' as info;

-- Por email
select 
    'Emails duplicados:' as tipo,
    email,
    count(*) as count
from public.profiles 
group by email 
having count(*) > 1;

-- Por ID (não deveria haver)
select 
    'IDs duplicados:' as tipo,
    id,
    count(*) as count
from public.profiles 
group by id 
having count(*) > 1;

-- VERIFICAÇÃO 7: Usuário atual
-- =====================
select 
    'USUÁRIO ATUAL:' as info,
    case 
        when auth.uid() is not null then 'AUTENTICADO: ' || auth.uid()::text
        else 'NÃO AUTENTICADO'
    end as auth_status;

-- VERIFICAÇÃO 8: Teste de acesso básico
-- =====================
select 'TESTE DE ACESSO:' as info;

-- Tentar contar registros (teste básico de SELECT)
do $$
declare
    profile_count integer;
begin
    select count(*) into profile_count from public.profiles;
    raise notice 'Conseguiu contar % perfis', profile_count;
exception
    when others then
        raise notice 'ERRO ao contar perfis: %', SQLERRM;
end $$;

-- VERIFICAÇÃO 9: Recomendações
-- =====================
select 'RECOMENDAÇÕES:' as info;

select 
    case 
        when not exists(select 1 from pg_policies where tablename = 'profiles')
        then 'CRÍTICO: Nenhuma política RLS encontrada - execute fix_policies_clean.sql'
        when (select rowsecurity from pg_tables where tablename = 'profiles') = false
        then 'ATENÇÃO: RLS desabilitado - dados sem proteção'
        when exists(select 1 from pg_policies where tablename = 'profiles' and cmd = 'SELECT' and qual like '%auth.uid() is not null%')
        then 'OK: Política permissiva de SELECT encontrada'
        else 'VERIFICAR: Políticas podem estar muito restritivas'
    end as recomendacao;

-- =====================================================
-- VERIFICAÇÃO RÁPIDA: Validar dados antes da correção
-- =====================================================

-- 1. Verificar se o usuário É executor da tarefa específica
SELECT 
    'EXECUTOR CHECK' as tipo,
    te.task_id,
    te.user_id,
    CASE 
        WHEN te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4' THEN '✅ SIM - É EXECUTOR'
        ELSE '❌ NÃO - NÃO É EXECUTOR'
    END as status
FROM task_executors te
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
    AND te.user_id = '4b09be1f-5187-44c0-9b53-87b7c57e45b4';

-- 2. Verificar se EXISTEM blocos de conteúdo para esta tarefa
SELECT 
    'CONTENT BLOCKS CHECK' as tipo,
    COUNT(*) as total_blocos,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ SIM - HÁ BLOCOS'
        ELSE '❌ NÃO - SEM BLOCOS'
    END as status
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- 3. Verificar detalhes dos blocos (se existem)
SELECT 
    'BLOCK DETAILS' as tipo,
    tcb.id as block_id,
    tcb.type,
    LEFT(tcb.content::text, 50) as content_preview,
    tcb.created_at
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88'
ORDER BY tcb.order, tcb.created_at;

-- 4. Verificar política RLS atual (apenas a mais importante)
SELECT 
    'RLS POLICY CHECK' as tipo,
    policyname,
    cmd,
    'Política ativa' as status
FROM pg_policies
WHERE schemaname = 'public' 
    AND tablename = 'task_content_blocks'
    AND policyname = 'Project members can view task content blocks';

-- =====================================================
-- INTERPRETAÇÃO DOS RESULTADOS:
-- =====================================================
-- Se EXECUTOR CHECK retorna ✅ SIM E CONTENT BLOCKS CHECK retorna ✅ SIM:
--   → O problema É a política RLS → Aplicar EXECUTAR_CORRECAO_FINAL.sql
--
-- Se EXECUTOR CHECK retorna ❌ NÃO:
--   → Usuário não foi designado como executor → Verificar task_executors
--
-- Se CONTENT BLOCKS CHECK retorna ❌ NÃO:
--   → Não há conteúdo na tarefa → Problema não é RLS
-- =====================================================

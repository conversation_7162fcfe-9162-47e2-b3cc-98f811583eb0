---
type: "manual"
---

# <PERSON><PERSON> de Boas Práticas e Aprendizados

Este mural serve para registrar aprendizados, dicas, padrões evolutivos e boas práticas identificadas ao longo do desenvolvimento do projeto.

## Como usar
- Registre aqui aprendizados relevantes, soluções para problemas recorrentes, dicas de produtividade e padrões que surgirem.
- Use exemplos práticos e explique o contexto.
- Revise periodicamente e promova discussões no time para evolução contínua.

---

## Exemplos

### 1. Padronização de Mensagens de Commit
> Sempre use o padrão `[Data/hora] - [Título]` e obtenha a data/hora do PowerShell.

### 2. Uso de Scripts Utilitários
> Scripts em `/scripts/` devem ser documentados e revisados periodicamente para evitar obsolescência.

### 3. Onboarding de Novos Membros
> Oriente sempre a começar pelo onboarding e central de regras.

### 4. Aprendizado sobre Troubleshooting
> Use as mcp tools para logs e auditorias antes de depurar manualmente.

---

Adicione novos aprendizados abaixo! 
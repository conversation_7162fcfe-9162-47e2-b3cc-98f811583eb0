// =====================================================
// DEBUG ESPECÍFICO: TaskDetailsV2 e projectMembers
// =====================================================
// Execute no console quando estiver na tela de detalhes da tarefa

console.log('🔍 DEBUG ESPECÍFICO: TaskDetailsV2 projectMembers');

// Função para verificar estado do React
function debugReactState() {
  console.log('📊 VERIFICANDO ESTADO DO REACT...');
  
  // Tentar encontrar o componente TaskDetailsV2
  const taskDetailsElements = document.querySelectorAll('[class*="task"], [class*="detail"]');
  console.log('🎯 Elementos relacionados a task encontrados:', taskDetailsElements.length);
  
  // Verificar se há logs específicos do TaskDetailsV2
  console.log('📝 Procure por logs que começam com [TaskDetailsV2] no console');
  
  // Verificar inputs de autocomplete
  const autocompleteInputs = document.querySelectorAll('input[placeholder*="Buscar"]');
  console.log('🔍 Inputs de autocomplete:', autocompleteInputs.length);
  
  autocompleteInputs.forEach((input, index) => {
    const card = input.closest('[class*="card"]');
    const cardTitle = card?.querySelector('h3, [class*="title"], p');
    console.log(`📝 Input ${index + 1}:`, {
      placeholder: input.placeholder,
      context: cardTitle?.textContent || 'Não identificado',
      hasResults: !!input.parentElement?.querySelector('ul')
    });
  });
}

// Função para testar manualmente a API projectService
async function debugProjectService() {
  console.log('🌐 TESTANDO projectService...');
  
  try {
    // Verificar se conseguimos acessar o Supabase
    if (window.supabase) {
      console.log('✅ Supabase disponível');
      
      // 1. Buscar projects
      const { data: projects, error: projectsError } = await window.supabase
        .from('projects')
        .select('id, name')
        .limit(5);
        
      if (projectsError) {
        console.error('❌ Erro ao buscar projects:', projectsError);
        return;
      }
      
      console.log('✅ Projects encontrados:', projects);
      
      if (!projects || projects.length === 0) {
        console.log('⚠️ Nenhum projeto encontrado');
        return;
      }
      
      // 2. Usar o primeiro projeto para testar project_members
      const projectId = projects[0].id;
      console.log('🎯 Testando com projeto:', projectId);
      
      // 3. Testar a query exata do getProjectMembers
      const { data: members, error: membersError } = await window.supabase
        .from('project_members')
        .select(`
          id,
          role,
          profile:profiles!project_members_user_id_fkey (
            id,
            name,
            email,
            avatar_url
          )
        `)
        .eq('project_id', projectId);
        
      if (membersError) {
        console.error('❌ Erro ao buscar project_members:', membersError);
        
        // Tentar query simplificada
        console.log('🔄 Tentando query simplificada...');
        const { data: simpleMembers, error: simpleError } = await window.supabase
          .from('project_members')
          .select('id, project_id, user_id, role')
          .eq('project_id', projectId);
          
        if (simpleError) {
          console.error('❌ Erro na query simplificada:', simpleError);
        } else {
          console.log('✅ Query simplificada funcionou:', simpleMembers);
          
          // Buscar profiles separadamente
          if (simpleMembers && simpleMembers.length > 0) {
            const userIds = simpleMembers.map(m => m.user_id);
            const { data: profiles, error: profilesError } = await window.supabase
              .from('profiles')
              .select('id, name, email, avatar_url')
              .in('id', userIds);
              
            if (profilesError) {
              console.error('❌ Erro ao buscar profiles:', profilesError);
            } else {
              console.log('✅ Profiles dos membros:', profiles);
              
              // Simular o que o código TypeScript faz
              const mappedMembers = simpleMembers.map(member => {
                const profile = profiles?.find(p => p.id === member.user_id);
                return profile ? {
                  id: profile.id,
                  name: profile.name,
                  email: profile.email,
                  avatar_url: profile.avatar_url
                } : null;
              }).filter(Boolean);
              
              console.log('✅ Membros mapeados (simulando código TS):', mappedMembers);
            }
          }
        }
      } else {
        console.log('✅ Project members com JOIN:', members);
        
        // Simular o mapeamento do código
        const mappedMembers = (members || []).map((m) => {
          const profile = m.profile || m.profiles?.[0] || m;
          return profile && profile.id ? {
            id: profile.id,
            name: profile.name,
            email: profile.email,
            avatar_url: profile.avatar_url
          } : null;
        }).filter(Boolean);
        
        console.log('✅ Membros mapeados:', mappedMembers);
      }
      
      // 4. Testar busca de todos os profiles ativos (fallback)
      const { data: allProfiles, error: allProfilesError } = await window.supabase
        .from('profiles')
        .select('id, name, email, avatar_url')
        .eq('is_active', true);
        
      if (allProfilesError) {
        console.error('❌ Erro ao buscar todos os profiles:', allProfilesError);
      } else {
        console.log('✅ Todos os profiles ativos (fallback):', allProfiles);
      }
      
    } else {
      console.log('❌ Supabase não disponível no window');
    }
  } catch (error) {
    console.error('❌ Erro inesperado:', error);
  }
}

// Função para simular interação com autocomplete
function debugAutocompleteInteraction() {
  console.log('🎮 SIMULANDO INTERAÇÃO COM AUTOCOMPLETE...');
  
  const inputs = document.querySelectorAll('input[placeholder*="Buscar"]');
  
  if (inputs.length === 0) {
    console.log('❌ Nenhum input de autocomplete encontrado');
    return;
  }
  
  inputs.forEach((input, index) => {
    console.log(`🎯 Testando input ${index + 1}...`);
    
    // Limpar input
    input.value = '';
    input.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Focar no input
    input.focus();
    
    // Verificar se dropdown aparece automaticamente (quando projectMembers existe)
    setTimeout(() => {
      const dropdown = input.parentElement?.querySelector('ul');
      if (dropdown) {
        console.log(`✅ Dropdown apareceu automaticamente para input ${index + 1}:`, dropdown.children.length, 'itens');
        
        // Listar itens do dropdown
        Array.from(dropdown.children).forEach((item, itemIndex) => {
          const name = item.querySelector('span')?.textContent;
          const email = item.querySelector('.text-gray-500')?.textContent;
          console.log(`  Item ${itemIndex + 1}:`, { name, email });
        });
      } else {
        console.log(`❌ Dropdown não apareceu para input ${index + 1}`);
        console.log('   Isso indica que projectMembers pode estar vazio');
      }
    }, 200);
    
    // Testar digitação
    setTimeout(() => {
      input.value = 'a';
      input.dispatchEvent(new Event('input', { bubbles: true }));
      
      setTimeout(() => {
        const dropdown = input.parentElement?.querySelector('ul');
        if (dropdown) {
          console.log(`✅ Dropdown após digitação para input ${index + 1}:`, dropdown.children.length, 'itens');
        } else {
          console.log(`❌ Nenhum resultado após digitação para input ${index + 1}`);
        }
      }, 500);
    }, 1000);
  });
}

// Executar debug completo
async function runCompleteDebug() {
  console.log('🚀 EXECUTANDO DEBUG COMPLETO...');
  
  debugReactState();
  
  console.log('⏳ Aguardando 2 segundos antes dos testes de API...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await debugProjectService();
  
  console.log('⏳ Aguardando 2 segundos antes dos testes de interação...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  debugAutocompleteInteraction();
  
  console.log('✅ DEBUG COMPLETO FINALIZADO');
  console.log('💡 PRÓXIMOS PASSOS:');
  console.log('   1. Verifique os logs acima');
  console.log('   2. Se projectMembers está vazio, o problema é no carregamento');
  console.log('   3. Se projectMembers tem dados mas autocomplete não funciona, o problema é no componente');
  console.log('   4. Execute DEBUG_PROJECT_MEMBERS_DETAILED.sql no Supabase para mais detalhes');
}

// Disponibilizar funções individualmente
window.debugTaskDetails = {
  state: debugReactState,
  api: debugProjectService,
  interaction: debugAutocompleteInteraction,
  full: runCompleteDebug
};

console.log('🛠️ FUNÇÕES DISPONÍVEIS:');
console.log('  debugTaskDetails.state() - Estado do React');
console.log('  debugTaskDetails.api() - Testes de API');
console.log('  debugTaskDetails.interaction() - Interação com autocomplete');
console.log('  debugTaskDetails.full() - Debug completo');

// Executar automaticamente
runCompleteDebug();

-- =====================================================
-- TESTE DE FUNCIONALIDADES BÁSICAS SEM RLS
-- =====================================================
-- Valida que todas as operações funcionam após remoção do RLS
-- Execute este script APÓS executar REMOVE_ALL_RLS.sql

-- =====================================================
-- CONFIGURAÇÃO DO TESTE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🧪 ===== INICIANDO TESTES DE FUNCIONALIDADES =====';
    RAISE NOTICE '📅 Data/Hora: %', NOW();
    RAISE NOTICE '👤 Usuário: %', COALESCE(auth.uid()::TEXT, 'NOT_AUTHENTICATED');
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TESTE 1: VERIFICAR LIMPEZA RLS
-- =====================================================

DO $$
DECLARE
    policies_count INTEGER;
    rls_tables_count INTEGER;
    table_name TEXT;
BEGIN
    RAISE NOTICE '🔍 TESTE 1: Verificando limpeza RLS...';
    
    -- Contar políticas restantes
    SELECT COUNT(*) INTO policies_count 
    FROM pg_policies 
    WHERE schemaname = 'public';
    
    IF policies_count > 0 THEN
        RAISE NOTICE '❌ FALHOU: % políticas RLS ainda existem', policies_count;
        
        FOR table_name IN 
            SELECT DISTINCT tablename 
            FROM pg_policies 
            WHERE schemaname = 'public'
        LOOP
            RAISE NOTICE '  - Tabela com políticas: %', table_name;
        END LOOP;
    ELSE
        RAISE NOTICE '✅ PASSOU: Nenhuma política RLS encontrada';
    END IF;
    
    -- Contar tabelas com RLS habilitado
    SELECT COUNT(*) INTO rls_tables_count
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' 
    AND c.relkind = 'r' 
    AND c.relrowsecurity = true;
    
    IF rls_tables_count > 0 THEN
        RAISE NOTICE '❌ FALHOU: % tabelas ainda têm RLS habilitado', rls_tables_count;
    ELSE
        RAISE NOTICE '✅ PASSOU: RLS desabilitado em todas as tabelas';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TESTE 2: ACESSO BÁSICO A TABELAS
-- =====================================================

DO $$
DECLARE
    profiles_count INTEGER;
    projects_count INTEGER;
    stages_count INTEGER;
    tasks_count INTEGER;
    project_members_count INTEGER;
BEGIN
    RAISE NOTICE '📊 TESTE 2: Verificando acesso básico às tabelas...';
    
    -- Testar profiles
    BEGIN
        SELECT COUNT(*) INTO profiles_count FROM public.profiles;
        RAISE NOTICE '✅ profiles: % registros acessíveis', profiles_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ ERRO em profiles: %', SQLERRM;
    END;
    
    -- Testar projects
    BEGIN
        SELECT COUNT(*) INTO projects_count FROM public.projects;
        RAISE NOTICE '✅ projects: % registros acessíveis', projects_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ ERRO em projects: %', SQLERRM;
    END;
    
    -- Testar stages
    BEGIN
        SELECT COUNT(*) INTO stages_count FROM public.stages;
        RAISE NOTICE '✅ stages: % registros acessíveis', stages_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ ERRO em stages: %', SQLERRM;
    END;
    
    -- Testar tasks
    BEGIN
        SELECT COUNT(*) INTO tasks_count FROM public.tasks;
        RAISE NOTICE '✅ tasks: % registros acessíveis', tasks_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ ERRO em tasks: %', SQLERRM;
    END;
    
    -- Testar project_members
    BEGIN
        SELECT COUNT(*) INTO project_members_count FROM public.project_members;
        RAISE NOTICE '✅ project_members: % registros acessíveis', project_members_count;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ ERRO em project_members: %', SQLERRM;
    END;
    
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TESTE 3: CRUD DE PROJETOS
-- =====================================================

DO $$
DECLARE
    test_project_id UUID;
    created_project RECORD;
    updated_title TEXT;
    projects_before INTEGER;
    projects_after INTEGER;
BEGIN
    RAISE NOTICE '🏗️  TESTE 3: CRUD de projetos...';
    
    -- Contar projetos antes
    SELECT COUNT(*) INTO projects_before FROM projects;
    
    -- CREATE: Inserir projeto teste
    INSERT INTO projects (
        title, 
        description, 
        created_by, 
        status,
        created_at,
        updated_at
    ) VALUES (
        'Projeto Teste RLS - ' || NOW()::TEXT,
        'Projeto criado durante teste de funcionalidades',
        COALESCE(auth.uid(), (SELECT id FROM profiles LIMIT 1)),
        'planning',
        NOW(),
        NOW()
    ) RETURNING id INTO test_project_id;
    
    RAISE NOTICE '✅ CREATE: Projeto criado com ID %', test_project_id;
    
    -- READ: Ler projeto criado
    SELECT * INTO created_project 
    FROM projects 
    WHERE id = test_project_id;
    
    IF created_project.id IS NOT NULL THEN
        RAISE NOTICE '✅ READ: Projeto lido com sucesso - "%"', created_project.title;
    ELSE
        RAISE NOTICE '❌ READ: Falha ao ler projeto criado';
    END IF;
    
    -- UPDATE: Atualizar projeto
    updated_title := 'Projeto ATUALIZADO - ' || NOW()::TEXT;
    UPDATE projects 
    SET title = updated_title, updated_at = NOW()
    WHERE id = test_project_id;
    
    -- Verificar atualização
    SELECT title INTO updated_title FROM projects WHERE id = test_project_id;
    IF updated_title LIKE 'Projeto ATUALIZADO%' THEN
        RAISE NOTICE '✅ UPDATE: Projeto atualizado com sucesso';
    ELSE
        RAISE NOTICE '❌ UPDATE: Falha ao atualizar projeto';
    END IF;
    
    -- DELETE: Deletar projeto (comentado para preservar dados de teste)
    -- DELETE FROM projects WHERE id = test_project_id;
    -- RAISE NOTICE '✅ DELETE: Projeto deletado com sucesso';
    
    -- Contar projetos depois
    SELECT COUNT(*) INTO projects_after FROM projects;
    
    IF projects_after > projects_before THEN
        RAISE NOTICE '✅ CRUD COMPLETO: Projeto mantido para próximos testes';
        RAISE NOTICE '📝 ID do projeto teste: %', test_project_id;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TESTE 4: AUTOCOMPLETE DE USUÁRIOS
-- =====================================================

DO $$
DECLARE
    active_users_count INTEGER;
    sample_user RECORD;
BEGIN
    RAISE NOTICE '👥 TESTE 4: Autocomplete de usuários...';
    
    -- Contar usuários ativos
    SELECT COUNT(*) INTO active_users_count 
    FROM profiles 
    WHERE is_active = true;
    
    RAISE NOTICE '📊 Usuários ativos encontrados: %', active_users_count;
    
    -- Buscar usuário exemplo para autocomplete
    SELECT id, name, email INTO sample_user
    FROM profiles 
    WHERE is_active = true 
    AND name IS NOT NULL
    LIMIT 1;
    
    IF sample_user.id IS NOT NULL THEN
        RAISE NOTICE '✅ AUTOCOMPLETE: Usuário exemplo - % (%)', sample_user.name, sample_user.email;
        RAISE NOTICE '📋 Campos disponíveis: id, name, email acessíveis';
    ELSE
        RAISE NOTICE '⚠️  AUTOCOMPLETE: Nenhum usuário ativo com nome encontrado';
    END IF;
    
    -- Testar busca por termo (simulando autocomplete)
    IF active_users_count > 0 THEN
        RAISE NOTICE '✅ BUSCA: Autocomplete funcionará normalmente (sem RLS)';
    ELSE
        RAISE NOTICE '❌ BUSCA: Sem usuários ativos para autocomplete';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TESTE 5: RELACIONAMENTOS ENTRE TABELAS
-- =====================================================

DO $$
DECLARE
    sample_project_id UUID;
    sample_stage_id UUID;
    sample_task_id UUID;
    relationships_ok BOOLEAN := true;
BEGIN
    RAISE NOTICE '🔗 TESTE 5: Relacionamentos entre tabelas...';
    
    -- Buscar projeto existente
    SELECT id INTO sample_project_id 
    FROM projects 
    LIMIT 1;
    
    IF sample_project_id IS NOT NULL THEN
        RAISE NOTICE '✅ Projeto base encontrado: %', sample_project_id;
        
        -- Buscar etapa do projeto
        SELECT id INTO sample_stage_id 
        FROM stages 
        WHERE project_id = sample_project_id 
        LIMIT 1;
        
        IF sample_stage_id IS NOT NULL THEN
            RAISE NOTICE '✅ Etapa encontrada: %', sample_stage_id;
            
            -- Buscar tarefa da etapa
            SELECT id INTO sample_task_id 
            FROM tasks 
            WHERE stage_id = sample_stage_id 
            LIMIT 1;
            
            IF sample_task_id IS NOT NULL THEN
                RAISE NOTICE '✅ Tarefa encontrada: %', sample_task_id;
                
                -- Testar acesso a executores
                IF EXISTS (SELECT 1 FROM task_executors WHERE task_id = sample_task_id) THEN
                    RAISE NOTICE '✅ Executores acessíveis';
                END IF;
                
                -- Testar acesso a aprovadores  
                IF EXISTS (SELECT 1 FROM task_approvers WHERE task_id = sample_task_id) THEN
                    RAISE NOTICE '✅ Aprovadores acessíveis';
                END IF;
                
            ELSE
                RAISE NOTICE '⚠️  Nenhuma tarefa encontrada na etapa';
            END IF;
        ELSE
            RAISE NOTICE '⚠️  Nenhuma etapa encontrada no projeto';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  Nenhum projeto encontrado';
        relationships_ok := false;
    END IF;
    
    IF relationships_ok THEN
        RAISE NOTICE '✅ RELACIONAMENTOS: Hierarquia projeto->etapa->tarefa acessível';
    ELSE
        RAISE NOTICE '❌ RELACIONAMENTOS: Problemas na hierarquia de dados';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- =====================================================
-- TESTE 6: PERFORMANCE BÁSICA
-- =====================================================

DO $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
    query_count INTEGER;
BEGIN
    RAISE NOTICE '⚡ TESTE 6: Performance básica...';
    
    -- Teste 1: Query complexa de projetos com stages e tasks
    start_time := clock_timestamp();
    
    SELECT COUNT(*) INTO query_count
    FROM projects p
    LEFT JOIN stages s ON s.project_id = p.id
    LEFT JOIN tasks t ON t.stage_id = s.id;
    
    end_time := clock_timestamp();
    duration := end_time - start_time;
    
    RAISE NOTICE '✅ Query complexa: % registros em %ms', 
        query_count, 
        EXTRACT(MILLISECONDS FROM duration);
    
    -- Teste 2: Autocomplete simulation
    start_time := clock_timestamp();
    
    SELECT COUNT(*) INTO query_count
    FROM profiles 
    WHERE is_active = true 
    AND (name ILIKE '%test%' OR email ILIKE '%test%');
    
    end_time := clock_timestamp();
    duration := end_time - start_time;
    
    RAISE NOTICE '✅ Autocomplete: % resultados em %ms', 
        query_count, 
        EXTRACT(MILLISECONDS FROM duration);
    
    IF EXTRACT(MILLISECONDS FROM duration) < 100 THEN
        RAISE NOTICE '✅ PERFORMANCE: Queries rápidas (< 100ms)';
    ELSE
        RAISE NOTICE '⚠️  PERFORMANCE: Queries lentas (> 100ms)';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- =====================================================
-- RELATÓRIO FINAL
-- =====================================================

DO $$
DECLARE
    total_profiles INTEGER;
    total_projects INTEGER;
    total_stages INTEGER;
    total_tasks INTEGER;
    total_members INTEGER;
BEGIN
    RAISE NOTICE '📋 ===== RELATÓRIO FINAL DOS TESTES =====';
    RAISE NOTICE '';
    
    -- Contadores finais
    SELECT COUNT(*) INTO total_profiles FROM profiles;
    SELECT COUNT(*) INTO total_projects FROM projects;
    SELECT COUNT(*) INTO total_stages FROM stages;
    SELECT COUNT(*) INTO total_tasks FROM tasks;
    SELECT COUNT(*) INTO total_members FROM project_members;
    
    RAISE NOTICE '📊 DADOS ACESSÍVEIS SEM RLS:';
    RAISE NOTICE '  - Profiles: %', total_profiles;
    RAISE NOTICE '  - Projects: %', total_projects;
    RAISE NOTICE '  - Stages: %', total_stages;
    RAISE NOTICE '  - Tasks: %', total_tasks;
    RAISE NOTICE '  - Project Members: %', total_members;
    RAISE NOTICE '';
    
    RAISE NOTICE '✅ STATUS: Sistema funcionando sem RLS';
    RAISE NOTICE '✅ RESULTADO: Todas as funcionalidades básicas operacionais';
    RAISE NOTICE '✅ PRÓXIMO PASSO: Implementar nova arquitetura RLS';
    RAISE NOTICE '';
    
    RAISE NOTICE '🎯 IMPLEMENTAÇÃO SUGERIDA:';
    RAISE NOTICE '  1. Executar helper functions (IMPLEMENTATION_PLAN.md)';
    RAISE NOTICE '  2. Implementar RLS por fases (profiles -> projects -> stages -> tasks)';
    RAISE NOTICE '  3. Testar cada fase antes de continuar';
    RAISE NOTICE '  4. Monitorar performance durante implementação';
    RAISE NOTICE '';
    
    RAISE NOTICE '🏁 TESTE CONCLUÍDO EM: %', NOW();
    RAISE NOTICE '================================================';
END $$;

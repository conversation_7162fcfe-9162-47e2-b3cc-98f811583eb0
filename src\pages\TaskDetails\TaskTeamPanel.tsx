import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>ontent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Trash2 } from 'lucide-react';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';

interface User {
  id: string;
  name: string;
  avatar_url?: string;
}

interface TaskTeamPanelProps {
  executors: User[];
  approvers: User[];
  canEditExecutors: boolean;
  executorsLoading: boolean;
  approversLoading: boolean;
  onRemoveExecutor: (id: string) => void;
  onAddExecutor: (user: User) => void;
  onRemoveApprover: (id: string) => void;
  onAddApprover: (user: User) => void;
}

export const TaskTeamPanel: React.FC<TaskTeamPanelProps> = ({
  executors,
  approvers,
  canEditExecutors,
  executorsLoading,
  approversLoading,
  onRemoveExecutor,
  onAddExecutor,
  onRemoveApprover,
  onAddApprover
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="text-lg">Equipe</CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <div>
        <p className="text-sm font-medium mb-2">Executores</p>
        <div className="space-y-2">
          {executors.map((executor) => (
            <div key={String(executor?.id ?? '')} className="flex items-center gap-2">
              <Avatar className="w-6 h-6">
                <AvatarImage src={executor?.avatar_url || '/placeholder.svg'} />
                <AvatarFallback className="text-xs">{executor?.name?.[0] || '?'}</AvatarFallback>
              </Avatar>
              <span className="text-sm">{executor?.name || 'Desconhecido'}</span>
              {canEditExecutors && (
                <Button size="icon" variant="ghost" disabled={executorsLoading} onClick={() => onRemoveExecutor(executor.id)} aria-label="Remover executor">
                  <Trash2 className="w-4 h-4 text-red-500" />
                </Button>
              )}
            </div>
          ))}
        </div>
        {canEditExecutors && (
          <div className="mt-2">
            <UserAutocomplete onSelect={onAddExecutor} />
          </div>
        )}
      </div>
      <div>
        <p className="text-sm font-medium mb-2">Aprovadores</p>
        <div className="space-y-2">
          {approvers.map((approver) => (
            <div key={String(approver?.id ?? '')} className="flex items-center gap-2">
              <Avatar className="w-6 h-6">
                <AvatarImage src={approver?.avatar_url || '/placeholder.svg'} />
                <AvatarFallback className="text-xs">{approver?.name?.[0] || '?'}</AvatarFallback>
              </Avatar>
              <span className="text-sm">{approver?.name || 'Desconhecido'}</span>
              {canEditExecutors && (
                <Button size="icon" variant="ghost" disabled={approversLoading} onClick={() => onRemoveApprover(approver.id)} aria-label="Remover aprovador">
                  <Trash2 className="w-4 h-4 text-red-500" />
                </Button>
              )}
            </div>
          ))}
        </div>
        {canEditExecutors && (
          <div className="mt-2">
            <UserAutocomplete onSelect={onAddApprover} />
          </div>
        )}
      </div>
    </CardContent>
  </Card>
); 
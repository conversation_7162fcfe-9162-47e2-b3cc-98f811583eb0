import { supabase } from '@/lib/supabaseClient';
import { Evidence } from '@/types';
import { User } from '@supabase/supabase-js';

export interface FileValidationResult {
    isValid: boolean;
    error?: string;
}

export interface EvidenceUploadOptions {
    taskId: string;
    file: File;
    blockId?: string;
}

export interface EvidenceUploadResponse {
    success: boolean;
    data?: Evidence;
    error?: string;
}

export class EvidenceService {
    private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private static readonly ALLOWED_TYPES = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/csv'
    ];

    static validateFile(file: File): FileValidationResult {
        // Validar tamanho
        if (file.size > this.MAX_FILE_SIZE) {
            return {
                isValid: false,
                error: `Arquivo muito grande. O tamanho máximo permitido é ${this.MAX_FILE_SIZE / 1024 / 1024}MB`
            };
        }

        // Validar tipo
        if (!this.ALLOWED_TYPES.includes(file.type)) {
            return {
                isValid: false,
                error: 'Tipo de arquivo não permitido. Tipos aceitos: imagens, PDF, DOC, DOCX, TXT e CSV'
            };
        }

        return { isValid: true };
    }

    async uploadEvidence({ taskId, file, blockId }: EvidenceUploadOptions): Promise<EvidenceUploadResponse> {
        try {
            // Validar arquivo
            const validation = EvidenceService.validateFile(file);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error
                };
            }

            // Verificar autenticação
            const { data: { user }, error: authError } = await supabase.auth.getUser();
            if (authError || !user) {
                throw new Error('Usuário não autenticado');
            }

            // Verificar se a task existe e o usuário tem acesso
            const { data: task, error: taskError } = await supabase
                .from('tasks')
                .select('id, project_id')
                .eq('id', taskId)
                .single();

            if (taskError || !task) {
                throw new Error('Tarefa não encontrada ou sem permissão');
            }

            // Upload do arquivo
            const fileExt = file.name.split('.').pop();
            const filePath = `${taskId}/${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`;
            
            const { error: uploadError } = await supabase.storage
                .from('task-attachments')
                .upload(filePath, file);

            if (uploadError) {
                throw new Error(`Erro no upload: ${uploadError.message}`);
            }

            // Criar registro do anexo com retry
            let retryCount = 0;
            const maxRetries = 3;
            let attachment;
            let attachmentError;

            while (retryCount < maxRetries) {
                const { data, error } = await supabase
                    .from('task_attachments')
                    .insert({
                        task_id: taskId,
                        user_id: user.id,
                        file_name: file.name,
                        file_path: filePath,
                        file_type: file.type,
                        block_id: blockId,
                        status: 'pending'
                    })
                    .select()
                    .single();

                if (!error && data) {
                    attachment = data;
                    break;
                }

                attachmentError = error;
                retryCount++;
                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }

            if (!attachment) {
                // Limpar arquivo se registro falhou
                await supabase.storage
                    .from('task-attachments')
                    .remove([filePath])
                    .catch(console.error);

                throw attachmentError || new Error('Erro ao salvar registro após várias tentativas');
            }

            return {
                success: true,
                data: {
                    id: attachment.id,
                    taskId: attachment.task_id,
                    blockId: attachment.block_id,
                    type: 'file',
                    content: attachment.file_path,
                    fileName: attachment.file_name,
                    uploadedBy: {
                        id: user.id,
                        email: user.email || '',
                        name: user.user_metadata?.name || user.email || user.id,
                        role: 'member',
                        isActive: true
                    },
                    uploadedAt: attachment.created_at,
                    status: attachment.status,
                    approvedBy: null,
                    approvedAt: null,
                    rejectionReason: null
                }
            };

        } catch (error: any) {
            console.error('❌ Erro no serviço de evidências:', error);
            return {
                success: false,
                error: `Erro ao salvar evidência: ${error.message}`
            };
        }
    }

    async getTaskEvidences(taskId: string): Promise<Evidence[]> {
        try {
            const { data: attachments, error } = await supabase
                .from('task_attachments')
                .select(`
                    *,
                    profiles:user_id (*),
                    approved_by_profile:approved_by (*)
                `)
                .eq('task_id', taskId)
                .order('created_at', { ascending: false });

            if (error) throw error;

            return attachments.map(attachment => ({
                id: attachment.id,
                taskId: attachment.task_id,
                blockId: attachment.block_id,
                type: 'file',
                content: attachment.file_path,
                fileName: attachment.file_name,
                uploadedBy: {
                    id: attachment.profiles.id,
                    name: attachment.profiles.name || attachment.profiles.email,
                    email: attachment.profiles.email,
                    role: 'member',
                    isActive: true
                },
                uploadedAt: attachment.created_at,
                status: attachment.status,
                approvedBy: attachment.approved_by_profile ? {
                    id: attachment.approved_by_profile.id,
                    name: attachment.approved_by_profile.name || attachment.approved_by_profile.email,
                    email: attachment.approved_by_profile.email,
                    role: 'member',
                    isActive: true
                } : null,
                approvedAt: attachment.approved_at,
                rejectionReason: attachment.rejection_reason
            }));

        } catch (error: any) {
            console.error('❌ Erro ao buscar evidências:', error);
            throw new Error(`Erro ao buscar evidências: ${error.message}`);
        }
    }

    async deleteEvidence(evidenceId: string): Promise<void> {
        try {
            // Buscar informações do anexo
            const { data: attachment, error: fetchError } = await supabase
                .from('task_attachments')
                .select('file_path')
                .eq('id', evidenceId)
                .single();

            if (fetchError) {
                throw new Error(`Erro ao buscar anexo: ${fetchError.message}`);
            }

            // Deletar arquivo do storage
            if (attachment?.file_path) {
                const { error: storageError } = await supabase.storage
                    .from('task-attachments')
                    .remove([attachment.file_path]);

                if (storageError) {
                    console.error('⚠️ Erro ao deletar arquivo do storage:', storageError);
                }
            }

            // Deletar registro do banco
            const { error: deleteError } = await supabase
                .from('task_attachments')
                .delete()
                .eq('id', evidenceId);

            if (deleteError) {
                throw new Error(`Erro ao deletar registro: ${deleteError.message}`);
            }

        } catch (error: any) {
            console.error('❌ Erro ao deletar evidência:', error);
            throw new Error(`Erro ao deletar evidência: ${error.message}`);
        }
    }
}

export const evidenceService = new EvidenceService();

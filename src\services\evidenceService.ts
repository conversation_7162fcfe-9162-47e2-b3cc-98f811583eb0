import { supabase } from '@/lib/supabaseClient';
import { Evidence } from '@/types';
import { requireAuth } from '@/lib/authUtils';

export interface EvidenceUploadOptions {
  taskId: string;
  blockId: string;
  file: File;
  description?: string;
  uploadedBy: string;
}

export interface EvidenceUpdateOptions {
  taskId: string;
  blockId: string;
  evidences: Evidence[];
}

export class EvidenceService {
  /**
   * Sanitiza nome do arquivo removendo caracteres especiais
   */
  private static sanitizeFileName(fileName: string): string {
    // Separar nome e extensão
    const lastDotIndex = fileName.lastIndexOf('.');
    const name = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    const extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex) : '';

    // Sanitizar o nome:
    // - Remover acentos e caracteres especiais
    // - Substituir espaços por underscores
    // - <PERSON>ter apenas letras, números, hífens e underscores
    const sanitizedName = name
      .normalize('NFD') // Decompor caracteres acentuados
      .replace(/[\u0300-\u036f]/g, '') // Remover diacríticos
      .replace(/[^a-zA-Z0-9\-_\s]/g, '') // Remover caracteres especiais
      .replace(/\s+/g, '_') // Substituir espaços por underscores
      .replace(/_+/g, '_') // Remover underscores duplicados
      .replace(/^_|_$/g, ''); // Remover underscores no início/fim

    return sanitizedName + extension;
  }

  /**
   * Faz upload de um arquivo para o storage e cria registro de evidência
   */
  static async uploadEvidence(options: EvidenceUploadOptions): Promise<Evidence> {
    const { taskId, blockId, file, description, uploadedBy } = options;

    // Verificar autenticação antes de fazer upload
    await requireAuth();

    try {
      // Usar bucket 'arquivos' que já existe e funciona
      const bucketName = 'arquivos';
      console.log(`🔍 Usando bucket: ${bucketName}`);

      // 1. Upload do arquivo para o Supabase Storage
      const sanitizedFileName = this.sanitizeFileName(file.name);
      const fileName = `evidences/${taskId}/${blockId}/${Date.now()}_${sanitizedFileName}`;
      console.log('📤 Fazendo upload do arquivo:', fileName);
      console.log('📝 Nome original:', file.name, '→ Nome sanitizado:', sanitizedFileName);

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(fileName, file, {
          upsert: false,
          contentType: file.type
        });

      if (uploadError) {
        console.error('❌ Erro no upload:', uploadError);
        throw new Error(`Erro no upload: ${uploadError.message}`);
      }

      console.log('✅ Upload realizado com sucesso:', uploadData);

      // 2. Obter URL pública do arquivo
      const { data: urlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(fileName);

      // 3. Verificar usuário autenticado
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      console.log('👤 Usuário autenticado:', user?.id, user?.email);

      if (userError || !user) {
        throw new Error('Usuário não autenticado');
      }

      // 4. Verificar acesso ao projeto da tarefa
      const { data: taskProject, error: taskError } = await supabase
        .from('tasks')
        .select(`
          id,
          stages (
            id,
            projects (
              id,
              owner_id,
              project_members (user_id)
            )
          )
        `)
        .eq('id', taskId)
        .single();

      console.log('📋 Dados da tarefa e projeto:', taskProject);

      if (taskError || !taskProject) {
        throw new Error('Tarefa não encontrada ou sem acesso');
      }

      // 5. Criar registro na tabela task_attachments (compatibilidade)
      console.log('💾 Criando registro de anexo...');
      const { data: attachmentData, error: attachmentError } = await supabase
        .from('task_attachments')
        .insert({
          task_id: taskId,
          block_id: blockId, // Adicionar block_id para sistema de aprovação
          uploaded_by: uploadedBy,
          file_url: urlData.publicUrl,
          description: description || file.name, // Manter nome original na descrição
          status: 'pending' // Nova evidência sempre inicia como pendente
        })
        .select()
        .single();

      if (attachmentError) {
        console.error('❌ Erro ao criar registro de anexo:', attachmentError);
        // Se falhar ao criar registro, remover arquivo do storage
        await supabase.storage.from('evidences').remove([fileName]);
        throw new Error(`Erro ao salvar evidência: ${attachmentError.message}`);
      }

      console.log('✅ Registro de anexo criado:', attachmentData);

      // 4. Retornar objeto Evidence
      const evidence: Evidence = {
        id: attachmentData.id,
        type: 'file',
        name: file.name,
        url: urlData.publicUrl,
        uploadedBy: uploadedBy,
        uploadedAt: attachmentData.created_at,
        size: file.size,
        mimeType: file.type
      };

      return evidence;
    } catch (error) {
      console.error('Erro no upload de evidência:', error);
      throw error;
    }
  }

  /**
   * Remove uma evidência (arquivo e registro)
   */
  static async removeEvidence(evidenceId: string): Promise<void> {
    try {
      // 1. Buscar dados da evidência
      const { data: attachment, error: fetchError } = await supabase
        .from('task_attachments')
        .select('file_url')
        .eq('id', evidenceId)
        .single();

      if (fetchError) {
        throw new Error(`Erro ao buscar evidência: ${fetchError.message}`);
      }

      // 2. Extrair caminho do arquivo da URL
      const url = attachment.file_url;
      const urlParts = url.split('/');
      const storageIndex = urlParts.findIndex(part => part === 'arquivos');

      if (storageIndex !== -1 && storageIndex < urlParts.length - 1) {
        const filePath = urlParts.slice(storageIndex + 1).join('/');

        // 3. Remover arquivo do storage
        const { error: storageError } = await supabase.storage
          .from('arquivos')
          .remove([filePath]);

        if (storageError) {
          console.warn('Erro ao remover arquivo do storage:', storageError);
          // Continua mesmo se falhar ao remover do storage
        }
      }

      // 4. Remover registro da tabela
      const { error: deleteError } = await supabase
        .from('task_attachments')
        .delete()
        .eq('id', evidenceId);

      if (deleteError) {
        throw new Error(`Erro ao remover evidência: ${deleteError.message}`);
      }
    } catch (error) {
      console.error('Erro ao remover evidência:', error);
      throw error;
    }
  }

  /**
   * Busca evidências de uma tarefa
   */
  static async getTaskEvidences(taskId: string): Promise<Evidence[]> {
    // Verificar autenticação antes de buscar evidências
    await requireAuth();

    try {
      const { data: attachments, error } = await supabase
        .from('task_attachments')
        .select(`
          id,
          task_id,
          block_id,
          file_url,
          description,
          created_at,
          uploaded_by,
          status,
          approved_by,
          approved_at,
          rejection_reason,
          profiles!uploaded_by (
            id,
            name,
            email
          ),
          approved_by_profile:profiles!approved_by (
            id,
            name,
            email
          )
        `)
        .eq('task_id', taskId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Erro ao buscar evidências: ${error.message}`);
      }

      // Debug: Log dos dados retornados
      console.log('Dados retornados do Supabase:', attachments);

      // Converter para formato Evidence
      const evidences: Evidence[] = attachments.map(attachment => {
        console.log('Processando attachment:', attachment);
        return {
          id: attachment.id,
          taskId: attachment.task_id,
          blockId: attachment.block_id,
          type: 'file',
          content: attachment.file_url,
          fileName: attachment.description || 'Arquivo',
          uploadedBy: attachment.profiles ? {
            id: attachment.profiles.id,
            name: attachment.profiles.name || attachment.profiles.email,
            email: attachment.profiles.email
          } : attachment.uploaded_by,
          uploadedAt: attachment.created_at,
          // Campos de aprovação
          status: attachment.status || 'pending',
          approvedBy: attachment.approved_by_profile ? {
            id: attachment.approved_by_profile.id,
            name: attachment.approved_by_profile.name || attachment.approved_by_profile.email,
            email: attachment.approved_by_profile.email
          } : undefined,
          approvedAt: attachment.approved_at,
          rejectionReason: attachment.rejection_reason
        };
      });

      return evidences;
    } catch (error) {
      console.error('Erro ao buscar evidências:', error);
      throw error;
    }
  }

  /**
   * Atualiza evidências de um bloco específico
   * (Para compatibilidade com o sistema de blocos)
   */
  static async updateBlockEvidences(options: EvidenceUpdateOptions): Promise<void> {
    const { taskId, blockId, evidences } = options;

    try {
      // Por enquanto, apenas log das evidências
      // Em uma implementação futura, poderia salvar as evidências específicas do bloco
      console.log('Evidências do bloco atualizadas:', {
        taskId,
        blockId,
        evidences: evidences.length
      });

      // TODO: Implementar salvamento específico por bloco se necessário
      // Isso permitiria ter múltiplos blocos de evidência com evidências separadas
    } catch (error) {
      console.error('Erro ao atualizar evidências do bloco:', error);
      throw error;
    }
  }

  /**
   * Valida se um arquivo pode ser enviado
   */
  static validateFile(file: File, options: {
    maxSize?: number; // em MB
    allowedTypes?: string[];
  }): string | null {
    const { maxSize = 10, allowedTypes = [] } = options;

    // Verificar tamanho
    const maxSizeBytes = maxSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `Arquivo muito grande. Tamanho máximo: ${maxSize}MB`;
    }

    // Verificar tipo se especificado
    if (allowedTypes.length > 0) {
      const isAllowed = allowedTypes.some(type => {
        if (type.includes('*')) {
          // Tipo genérico como 'image/*'
          const baseType = type.split('/')[0];
          return file.type.startsWith(baseType + '/');
        } else if (type.startsWith('.')) {
          // Extensão como '.pdf'
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        } else {
          // Tipo MIME específico
          return file.type === type;
        }
      });

      if (!isAllowed) {
        return `Tipo de arquivo não permitido. Tipos aceitos: ${allowedTypes.join(', ')}`;
      }
    }

    return null; // Arquivo válido
  }

  /**
   * Gera URL de download para uma evidência
   */
  static getDownloadUrl(evidence: Evidence): string {
    // Se já é uma URL pública, retorna diretamente
    if (evidence.url.startsWith('http')) {
      return evidence.url;
    }

    // Caso contrário, gera URL do Supabase Storage
    const { data } = supabase.storage
      .from('arquivos')
      .getPublicUrl(evidence.url);

    return data.publicUrl;
  }
}

export default EvidenceService;

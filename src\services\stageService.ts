import { supabase } from '@/lib/supabaseClient';
import { requireAuth } from '@/lib/authUtils';

interface Stage {
  id: string;
  name: string;
  description?: string;
  project_id: string;
  created_at: string;
  updated_at: string;
}

export const stageService = {
  async getByProjectId(projectId: string) {
    const { data, error } = await supabase.from('stages').select('*').eq('project_id', projectId);
    if (error) throw error;
    return data;
  },

  async getMyStagesByProjectId(projectId: string): Promise<Stage[]> {
    await requireAuth(); // Verificar autenticação

    // Buscar usuário atual
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Usuário não autenticado');

    console.log('Buscando etapas para usuário:', user.id, 'no projeto:', projectId);

    // Com RLS atualizado, a consulta simples já retorna apenas etapas acessíveis
    const { data, error } = await supabase
      .from('stages')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Erro ao buscar etapas:', error);
      throw error;
    }

    console.log('Etapas encontradas para usuário:', data?.length || 0);
    return data || [];
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('stages')
      .select('*')
      .eq('id', id)
      .single();
    if (error) throw error;
    // Buscar responsáveis usando a view
    const responsibles = await this.getResponsiblesByStageId(id);
    return { ...data, responsibles };
  },

  async create(stage: any) {
    const { data, error } = await supabase.from('stages').insert([stage]).select().single();
    if (error) {
      console.error('Erro ao inserir etapa:', error, 'Payload:', stage);
      throw error;
    }
    if (!data) {
      console.error('Insert de etapa não retornou data! Payload:', stage);
      throw new Error('Falha ao criar etapa: insert não retornou dados.');
    }
    return data;
  },

  async update(id: string, updates: any) {
    const { data, error } = await supabase.from('stages').update(updates).eq('id', id).single();
    if (error) throw error;
    return data;
  },

  async remove(id: string) {
    const { error } = await supabase.from('stages').delete().eq('id', id);
    if (error) throw error;
  },

  async getResponsiblesByStageId(stageId: string) {
    const { data, error } = await supabase
      .from('stage_responsibles_with_profile')
      .select('*')
      .eq('stage_id', stageId);
    if (error) throw error;
    return (data || []).map((r: any) => ({
      id: r.user_id,
      name: r.profile_name,
      email: r.profile_email,
      avatar_url: r.profile_avatar_url,
      assigned_at: r.assigned_at,
    }));
  },

  async saveContentBlocks(stageId: string, blocks: any[]) {
    // Salva os blocos de conteúdo no campo 'content' da etapa
    const { data, error } = await supabase
      .from('stages')
      .update({ content: blocks })
      .eq('id', stageId)
      .single();
    if (error) throw error;
    return data;
  }
}; 
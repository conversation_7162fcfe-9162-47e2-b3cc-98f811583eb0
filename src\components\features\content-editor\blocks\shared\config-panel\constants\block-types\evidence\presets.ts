import { BlockTypePreset } from '../../types';

/**
 * Presets específicos para blocos de Evidências
 * Cada preset define configurações completas de ícone, card e botão
 */

export const evidenceBlockPresets: Record<string, BlockTypePreset> = {
  default: {
    icon: {
      name: 'Paperclip',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#14b8a6', // teal-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#14b8a6', width: 1 },
      shadow: '0 2px 8px #14b8a640',
      hover: {
        backgroundColor: '#0d9488', // teal-600
        color: '#fff',
        shadow: '0 4px 16px #14b8a640',
        borderColor: '#14b8a6',
      },
    },
    button: {
      backgroundColor: '#14b8a6',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#14b8a6', width: 1 },
      shadow: '0 2px 8px #14b8a640',
      hover: {
        backgroundColor: '#0d9488',
        color: '#fff',
        shadow: '0 4px 16px #14b8a640',
        borderColor: '#14b8a6',
      },
    },
    card: {
      backgroundColor: '#f0fdfa', // teal-50
      color: '#134e4a', // teal-900
      format: 'rounded',
      border: { enabled: true, color: '#14b8a6', width: 1 },
      shadow: '0 1px 3px #0000001a',
      hover: {
        backgroundColor: '#ccfbf1', // teal-100
        color: '#134e4a',
        shadow: '0 4px 12px #0000001a',
        borderColor: '#14b8a6',
      },
    },
  },

  professional: {
    icon: {
      name: 'FileText',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#1f2937', // gray-800
      color: '#fff',
      format: 'square',
      border: { enabled: true, color: '#1f2937', width: 1 },
      shadow: '0 2px 8px #1f293740',
      hover: {
        backgroundColor: '#374151', // gray-700
        color: '#fff',
        shadow: '0 4px 16px #1f293740',
        borderColor: '#1f2937',
      },
    },
    button: {
      backgroundColor: '#1f2937',
      color: '#fff',
      format: 'square',
      border: { enabled: false, color: '#1f2937', width: 1 },
      shadow: '0 2px 8px #1f293740',
      hover: {
        backgroundColor: '#374151',
        color: '#fff',
        shadow: '0 4px 16px #1f293740',
        borderColor: '#1f2937',
      },
    },
    card: {
      backgroundColor: '#f9fafb', // gray-50
      color: '#1f2937', // gray-800
      format: 'square',
      border: { enabled: true, color: '#374151', width: 1 },
      shadow: '0 1px 3px #0000001a',
      hover: {
        backgroundColor: '#f3f4f6', // gray-100
        color: '#1f2937',
        shadow: '0 4px 12px #0000001a',
        borderColor: '#374151',
      },
    },
  },

  modern: {
    icon: {
      name: 'Upload',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#8b5cf6', // violet-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#8b5cf6', width: 1 },
      shadow: '0 2px 8px #8b5cf640',
      hover: {
        backgroundColor: '#7c3aed', // violet-600
        color: '#fff',
        shadow: '0 4px 16px #8b5cf640',
        borderColor: '#8b5cf6',
      },
    },
    button: {
      backgroundColor: '#8b5cf6',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#8b5cf6', width: 1 },
      shadow: '0 2px 8px #8b5cf640',
      hover: {
        backgroundColor: '#7c3aed',
        color: '#fff',
        shadow: '0 4px 16px #8b5cf640',
        borderColor: '#8b5cf6',
      },
    },
    card: {
      backgroundColor: '#f5f3ff', // violet-50
      color: '#5b21b6', // violet-800
      format: 'rounded',
      border: { enabled: true, color: '#8b5cf6', width: 1 },
      shadow: '0 1px 3px #0000001a',
      hover: {
        backgroundColor: '#ede9fe', // violet-100
        color: '#5b21b6',
        shadow: '0 4px 12px #0000001a',
        borderColor: '#8b5cf6',
      },
    },
  },

  secure: {
    icon: {
      name: 'Shield',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#dc2626', // red-600
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#dc2626', width: 1 },
      shadow: '0 2px 8px #dc262640',
      hover: {
        backgroundColor: '#b91c1c', // red-700
        color: '#fff',
        shadow: '0 4px 16px #dc262640',
        borderColor: '#dc2626',
      },
    },
    button: {
      backgroundColor: '#dc2626',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#dc2626', width: 1 },
      shadow: '0 2px 8px #dc262640',
      hover: {
        backgroundColor: '#b91c1c',
        color: '#fff',
        shadow: '0 4px 16px #dc262640',
        borderColor: '#dc2626',
      },
    },
    card: {
      backgroundColor: '#fef2f2', // red-50
      color: '#7f1d1d', // red-900
      format: 'rounded',
      border: { enabled: true, color: '#dc2626', width: 1 },
      shadow: '0 1px 3px #0000001a',
      hover: {
        backgroundColor: '#fee2e2', // red-100
        color: '#7f1d1d',
        shadow: '0 4px 12px #0000001a',
        borderColor: '#dc2626',
      },
    },
  },

  creative: {
    icon: {
      name: 'Palette',
      position: 'left-title',
      type: 'predefined',
      backgroundColor: '#f59e0b', // amber-500
      color: '#fff',
      format: 'circle',
      border: { enabled: true, color: '#f59e0b', width: 1 },
      shadow: '0 2px 8px #f59e0b40',
      hover: {
        backgroundColor: '#d97706', // amber-600
        color: '#fff',
        shadow: '0 4px 16px #f59e0b40',
        borderColor: '#f59e0b',
      },
    },
    button: {
      backgroundColor: '#f59e0b',
      color: '#fff',
      format: 'rounded',
      border: { enabled: false, color: '#f59e0b', width: 1 },
      shadow: '0 2px 8px #f59e0b40',
      hover: {
        backgroundColor: '#d97706',
        color: '#fff',
        shadow: '0 4px 16px #f59e0b40',
        borderColor: '#f59e0b',
      },
    },
    card: {
      backgroundColor: '#fffbeb', // amber-50
      color: '#78350f', // amber-900
      format: 'rounded',
      border: { enabled: true, color: '#f59e0b', width: 1 },
      shadow: '0 1px 3px #0000001a',
      hover: {
        backgroundColor: '#fef3c7', // amber-100
        color: '#78350f',
        shadow: '0 4px 12px #0000001a',
        borderColor: '#f59e0b',
      },
    },
  },
};

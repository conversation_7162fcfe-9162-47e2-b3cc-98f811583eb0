import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";

interface StageContentProps {
  objetivos?: string;
  requisitos?: string;
}

export const StageContent: React.FC<StageContentProps> = ({ objetivos, requisitos }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Conteúdo da Etapa</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="prose max-w-none">
          <h3>Objetivos desta Etapa</h3>
          <p>{objetivos || 'Objetivos não informados.'}</p>
          <h3>Requisitos Técnicos</h3>
          <p>{requisitos || 'Requisitos não informados.'}</p>
        </div>
      </CardContent>
    </Card>
  );
}; 
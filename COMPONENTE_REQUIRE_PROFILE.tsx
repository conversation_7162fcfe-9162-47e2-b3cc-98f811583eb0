import React from 'react';
import { useAuthContext } from '@/auth/AuthProvider';
import { Navigate } from 'react-router-dom';

interface RequireProfileProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

// **COMPONENTE DE CONTROLE DE ACESSO**
// Garante que apenas usuários com profiles válidos acessem o sistema
export const RequireProfile: React.FC<RequireProfileProps> = ({ 
  children, 
  fallback 
}) => {
  const { 
    user, 
    profile, 
    profileExists, 
    requiresProfileSetup, 
    loading 
  } = useAuthContext();

  // Ainda carregando
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Usuário não autenticado
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // **CONTROLE RIGOROSO**: Usuário autenticado mas sem profile
  if (user && !profileExists) {
    if (requiresProfileSetup) {
      return fallback || <ProfileSetupRequired />;
    }
    
    // Profile ainda sendo verificado
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Verificando permissões...</p>
        </div>
      </div>
    );
  }

  // **CONTROLE RIGOROSO**: Profile existe mas está inativo
  if (profile && profile.is_active === false) {
    return <ProfileInactive />;
  }

  // ✅ Usuário autenticado E profile válido - permitir acesso
  return <>{children}</>;
};

// Componente para quando o usuário precisa de setup de profile
const ProfileSetupRequired: React.FC = () => {
  const { logout } = useAuthContext();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 text-red-500">
            <svg className="h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Acesso Requer Aprovação
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Sua conta foi criada mas seu perfil ainda não foi configurado.
          </p>
        </div>
        
        <div className="rounded-md bg-yellow-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Próximos Passos:
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>Entre em contato com o administrador do sistema</li>
                  <li>Solicite a criação do seu perfil de usuário</li>
                  <li>Aguarde a aprovação para acessar o sistema</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            onClick={logout}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Fazer Logout
          </button>
        </div>
      </div>
    </div>
  );
};

// Componente para quando o profile está inativo
const ProfileInactive: React.FC = () => {
  const { logout } = useAuthContext();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 text-red-500">
            <svg className="h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Acesso Bloqueado
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Seu acesso ao sistema foi desativado.
          </p>
        </div>
        
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Conta Desativada
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  Entre em contato com o administrador do sistema para reativar sua conta.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            onClick={logout}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Fazer Logout
          </button>
        </div>
      </div>
    </div>
  );
};

import 'dotenv/config';
import { supabase } from '@/lib/supabaseClient';

async function checkSupabaseConnection() {
  try {
    const { data, error } = await supabase.from('projects').select('*').limit(1);
    if (error) {
      console.error('Erro ao conectar ao Supabase:', error.message);
      process.exit(1);
    }
    console.log('Conexão com Supabase bem-sucedida! Exemplo de dado:', data);
    process.exit(0);
  } catch (err) {
    console.error('Erro inesperado:', err);
    process.exit(1);
  }
}

async function checkSupabaseTasks() {
  console.log('Iniciando teste isolado de busca de tarefas...');
  try {
    const { data, error } = await supabase.from('tasks').select('*').limit(5);
    if (error) {
      console.error('Erro ao buscar tarefas:', error.message);
    } else {
      console.log('Tarefas encontradas:', data);
    }
  } catch (err) {
    console.error('Erro inesperado:', err);
  }
}

checkSupabaseConnection();
checkSupabaseTasks(); 
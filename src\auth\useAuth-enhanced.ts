// Hook customizado para ter acesso consistente ao user com role
import { useAuthContext } from "./AuthProvider";

export const useAuth = () => {
  const { user, profile, ...rest } = useAuthContext();
  
  // Criar um user enriquecido com dados do profile
  const enhancedUser = user ? {
    ...user,
    ...profile,
    // Garantir que os campos essenciais do user não sejam sobrescritos
    id: user.id,
    email: user.email,
    // Adicionar campos do profile
    role: profile?.role || 'member',
    name: profile?.name || user.email?.split('@')[0] || 'Usuário',
    is_active: profile?.is_active ?? true
  } : null;
  
  return {
    user: enhancedUser,
    profile,
    ...rest
  };
};

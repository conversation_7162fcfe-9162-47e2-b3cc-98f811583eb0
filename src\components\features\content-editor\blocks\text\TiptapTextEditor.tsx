import React, { useEffect, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
// NOVAS EXTENSÕES OFICIAIS DISPONÍVEIS
import Color from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import Placeholder from '@tiptap/extension-placeholder';
// EXTENSÕES CUSTOMIZADAS
import LineHeightExtension from './extensions/LineHeightExtension';
import CodeBlockExtension from './extensions/CodeBlockExtension';
import TabExtension from './extensions/TabExtension';
import CodeBlockHighlightExtension from './extensions/CodeBlockHighlightExtension';
import { TiptapToolbar } from './TiptapToolbar';
import { convertLexicalToTiptap, isLexicalFormat } from './utils/lexical-tiptap-converter';
import './styles/tiptap-official.css';
import './styles/code-block-syntax.css';
// Syntax highlighter customizado
import { applySyntaxHighlighting } from './utils/syntax-highlighter';

interface TiptapTextEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  editable?: boolean;
  className?: string;
  showToolbar?: boolean;
}

export function TiptapTextEditor({
  content = '',
  onChange,
  placeholder = 'Digite seu texto aqui...',
  editable = true,
  className = '',
  showToolbar = true
}: TiptapTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);

  // Converter conteúdo Lexical para Tiptap se necessário
  const processedContent = React.useMemo(() => {
    if (!content) return '';
    return isLexicalFormat(content) ? convertLexicalToTiptap(content) : content;
  }, [content]);
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configurar extensões do StarterKit
        history: {
          depth: 100,
        },
        codeBlock: false, // Desabilitar para usar extensão customizada
        taskList: false, // Desabilitar para usar nossa própria extensão
        // horizontalRule já incluído no StarterKit
      }),

      // EXTENSÕES DE FORMATAÇÃO BÁSICA
      Underline,
      TextStyle, // Necessário para Color e outras extensões de estilo

      // EXTENSÕES DE COR - OFICIAL TIPTAP
      Color.configure({
        types: ['textStyle'],
      }),

      // FontFamily removido devido a conflitos de versão

      // EXTENSÃO DE ALTURA DE LINHA - CUSTOMIZADA
      LineHeightExtension.configure({
        types: ['heading', 'paragraph'],
        defaultLineHeight: '1.5',
      }),

      // EXTENSÃO DE PLACEHOLDER - OFICIAL TIPTAP
      Placeholder.configure({
        placeholder: ({ node }) => {
          if (node.type.name === 'heading') {
            return 'Digite um título...';
          }
          return 'Digite seu texto aqui...';
        },
        includeChildren: true,
      }),

      // EXTENSÃO DE REGRA HORIZONTAL - JÁ INCLUÍDA NO STARTERKIT

      // EXTENSÕES DE LISTA E TAREFAS
      TaskList,
      TaskItem.configure({
        nested: true,
      }),

      // EXTENSÃO DE ALINHAMENTO - OFICIAL TIPTAP
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
        defaultAlignment: 'left',
      }),

      // EXTENSÃO DE DESTAQUE - OFICIAL TIPTAP
      Highlight.configure({
        multicolor: true,
        HTMLAttributes: {
          class: 'highlight',
        },
      }),

      // EXTENSÃO DE LINK - OFICIAL TIPTAP
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800',
        },
        protocols: ['ftp', 'mailto'],
        autolink: true,
        linkOnPaste: true,
      }),

      // EXTENSÃO DE BLOCO DE CÓDIGO - CUSTOMIZADA COM SYNTAX HIGHLIGHTING
      CodeBlockExtension,

      // EXTENSÃO DE TAB - CUSTOMIZADA
      TabExtension,

      // EXTENSÃO DE SYNTAX HIGHLIGHTING - CUSTOMIZADA
      CodeBlockHighlightExtension,
    ],
    content: processedContent,
    editable,
    onUpdate: ({ editor }) => {
      if (onChange) {
        onChange(editor.getHTML());
      }
    },
    editorProps: {
      attributes: {
        class: `tiptap-editor-content w-full focus:outline-none ${className}`,
        'data-placeholder': placeholder,
      },
    },
    // CORREÇÃO CRÍTICA: Remover dependências que causam remount
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
  }, []);

  // CORREÇÃO CRÍTICA: Atualizar conteúdo de forma segura
  React.useEffect(() => {
    if (editor && processedContent !== editor.getHTML()) {
      // Usar setTimeout para evitar conflitos DOM
      const timeoutId = setTimeout(() => {
        try {
          editor.commands.setContent(processedContent, false);
        } catch (error) {
          console.warn('Erro ao atualizar conteúdo do editor:', error);
        }
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [processedContent, editor]);

  // CORREÇÃO CRÍTICA: Atualizar editabilidade de forma segura
  React.useEffect(() => {
    if (editor) {
      // Usar setTimeout para evitar conflitos DOM
      const timeoutId = setTimeout(() => {
        try {
          editor.setEditable(editable);
        } catch (error) {
          console.warn('Erro ao atualizar editabilidade:', error);
        }
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [editable, editor]);

  // Syntax highlighting desabilitado temporariamente para não interferir na edição
  // TODO: Implementar syntax highlighting que não interfira na digitação

  // CORREÇÃO CRÍTICA: Mover useEffect ANTES do return condicional
  // Adicionar handles de redimensionamento quando há tabelas
  React.useEffect(() => {
    if (editor && editorRef.current) {
      const tables = editorRef.current.querySelectorAll('table');
      tables.forEach((table) => {
        // TableResizeHandles será aplicado via componente
      });
    }
  }, [editor]);

  // CORREÇÃO CRÍTICA: Return condicional APÓS todos os hooks
  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    );
  }

  return (
    <div className="tiptap-editor" ref={editorRef}>
      {showToolbar && <TiptapToolbar editor={editor} />}
      <div className="relative">
        <EditorContent
          editor={editor}
          className={`min-h-[200px] p-4 ${showToolbar ? 'rounded-b-md' : 'rounded-md'} focus-within:ring-1 focus-within:ring-blue-500 focus-within:ring-opacity-50`}
        />
      </div>
    </div>
  );
}

// Hook para acessar o editor externamente
export function useTiptapEditor(content: string, onChange?: (content: string) => void) {
  return useEditor({
    extensions: [
      StarterKit.configure({
        history: {
          depth: 100,
        },
        codeBlock: false,
        // horizontalRule incluído no StarterKit
      }),
      TextStyle,
      Color.configure({
        types: ['textStyle'],
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      // FontFamily removido devido a conflitos
      LineHeightExtension.configure({
        types: ['heading', 'paragraph'],
        defaultLineHeight: '1.5',
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800',
        },
        autolink: true,
        linkOnPaste: true,
      }),
      CodeBlockExtension,
      TabExtension,
      CodeBlockHighlightExtension,
    ],
    content,
    onUpdate: ({ editor }) => {
      if (onChange) {
        onChange(editor.getHTML());
      }
    },
  });
}

---
type: "manual"
---

# Plano de Implementação do RBAC (Regras por Papéis)

Este documento detalha todas as etapas, decisões e recomendações para implementar o modelo de RBAC (Role-Based Access Control) no sistema de gestão de projetos, garantindo <PERSON>gu<PERSON>, escalabilidade e clareza para desenvolvedores e stakeholders.

---

## 1. **Modelagem e Ajustes no Banco de Dados (Supabase/Postgres)**

### 1.1. Estrutura de Tabelas
- Garantir existência das tabelas: `profiles`, `projects`, `stages`, `tasks`, `project_members`, `task_executors`, `task_approvers`, etc.
- O campo `role` em `profiles` define o papel global (admin, manager, member).
- O campo `role` em `project_members` define o(s) papel(is) do usuário em cada projeto.

### 1.2. Enum para Papéis de Projeto
- Criar o ENUM `project_member_role` com valores: `admin`, `manager`, `editor`, `executor`, `approver`, `member`.
- Alterar a coluna `role` de `project_members` para usar o ENUM.
- Criar índice único para evitar duplicidade de papéis por usuário/projeto.

### 1.3. Policies RLS Granulares
- Implementar policies detalhadas para:
  - Só executores podem concluir tarefas.
  - Só aprovadores podem aprovar tarefas.
  - Só editores podem criar/editar etapas/tarefas.
  - Managers só podem gerenciar projetos próprios.
  - Admin tem acesso total.
- Exemplo de policy:
  ```sql
  create policy "Executor can complete tasks"
    on public.tasks
    for update
    using (
      id in (
        select t.id from public.tasks t
        join public.task_executors te on te.task_id = t.id
        where te.user_id = auth.uid()
      )
      AND new.status = 'completed'
    );
  ```
- Garantir policies para SELECT, INSERT, UPDATE, DELETE em todas as tabelas sensíveis.

### 1.4. Triggers e Auditoria
- Criar triggers para registrar ações críticas em `project_history` (aprovação, conclusão, exclusão, etc.).

### 1.5. Scripts de Migração
- Scripts para migrar dados antigos de papéis para o novo ENUM, se necessário.

---

## 2. **Frontend (React/TypeScript)**

### 2.1. Contexto e Hooks de Permissão
- Criar hook `useProjectRoles(projectId)` para buscar e armazenar os papéis do usuário em cada projeto.
- Exemplo:
  ```ts
  const roles = useProjectRoles(projectId); // ['editor', 'executor']
  ```

### 2.2. Wrappers e Componentes de Permissão
- Criar componentes como `<RequireProjectRole role="editor">...</RequireProjectRole>` para proteger rotas, botões e ações.
- Padronizar componente de acesso negado (`AccessDeniedCard`).

### 2.3. Ajustes de UI/UX
- Esconder/mostrar botões e ações conforme o papel do usuário.
- Exibir feedback claro quando uma ação for bloqueada por permissão.
- Permitir atribuição de múltiplos papéis por membro nos formulários de projeto.

### 2.4. Integração com Backend
- Tratar erros de permissão vindos do backend e exibir mensagens amigáveis.
- Atualizar queries para sempre filtrar dados conforme as policies.

---

## 3. **Serviços e Store**
- Atualizar services (`projectService`, `userService`, etc.) para considerar papéis de projeto nas queries e mutações.
- Garantir que o estado dos papéis do usuário esteja disponível globalmente (Context/Zustand).

---

## 4. **Testes**
- Testes unitários e de integração para todos os fluxos de permissão.
- Testes de UI para garantir que botões/ações não aparecem para quem não tem permissão.
- Testes de soma de permissões (usuário com mais de um papel).

---

## 5. **Documentação e Treinamento**
- Documentar todos os papéis, permissões e fluxos de acesso para o time de devs e para o usuário final.
- Incluir exemplos de uso dos hooks e wrappers de permissão.
- Treinar o time para uso correto do modelo RBAC.

---

## 6. **Manutenção e Evolução Futura**
- Padronizar nomes de roles e manter enums sincronizados entre backend, banco e frontend.
- Revisar e atualizar policies periodicamente conforme surgirem novos papéis ou fluxos.
- Monitorar logs de auditoria para identificar tentativas de acesso negado e possíveis melhorias.

---

## 7. **Checklist de Implementação**
- [ ] Enum e policies criados e aplicados no banco
- [ ] Hooks e wrappers de permissão implementados no frontend
- [ ] UI/UX adaptada para múltiplos papéis
- [ ] Services e stores atualizados
- [ ] Testes de permissão realizados
- [ ] Documentação entregue

---

**Este documento deve ser atualizado conforme o projeto evoluir e novas necessidades de permissão surgirem.** 
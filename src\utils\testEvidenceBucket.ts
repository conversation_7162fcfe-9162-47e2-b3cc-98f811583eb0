import { supabase } from '@/lib/supabaseClient';

/**
 * Função de teste para verificar se o bucket 'evidences' está funcionando
 */
export async function testEvidenceBucket(): Promise<void> {
  try {
    console.log('🧪 Testando bucket evidences...');

    // 1. Verificar se o bucket existe
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Erro ao listar buckets:', bucketsError);
      return;
    }

    const evidencesBucket = buckets.find(bucket => bucket.id === 'evidences');
    
    if (!evidencesBucket) {
      console.error('❌ Bucket evidences não encontrado');
      return;
    }

    console.log('✅ Bucket evidences encontrado:', evidencesBucket);

    // 2. Testar upload de um arquivo de teste
    const testContent = new Blob(['Teste de upload'], { type: 'text/plain' });
    const testFileName = `test/${Date.now()}_test.txt`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('evidences')
      .upload(testFileName, testContent, {
        upsert: false,
        contentType: 'text/plain'
      });

    if (uploadError) {
      console.error('❌ Erro no upload de teste:', uploadError);
      return;
    }

    console.log('✅ Upload de teste realizado com sucesso:', uploadData);

    // 3. Testar obtenção de URL pública
    const { data: urlData } = supabase.storage
      .from('evidences')
      .getPublicUrl(testFileName);

    console.log('✅ URL pública gerada:', urlData.publicUrl);

    // 4. Testar remoção do arquivo de teste
    const { error: deleteError } = await supabase.storage
      .from('evidences')
      .remove([testFileName]);

    if (deleteError) {
      console.error('⚠️ Erro ao remover arquivo de teste:', deleteError);
    } else {
      console.log('✅ Arquivo de teste removido com sucesso');
    }

    console.log('🎉 Teste do bucket evidences concluído com sucesso!');

  } catch (error) {
    console.error('❌ Erro geral no teste:', error);
  }
}

// Executar teste automaticamente se este arquivo for importado
if (typeof window !== 'undefined') {
  // Disponibilizar função globalmente para teste manual
  (window as any).testEvidenceBucket = testEvidenceBucket;
}

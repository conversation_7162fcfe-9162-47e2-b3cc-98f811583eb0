import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/ui/use-toast';
import { generateUUID } from '@/lib/utils';
import {
  Paperclip,
  Upload,
  Eye,
  Download,
  Trash2,
  FileIcon,
  ImageIcon,
  FileText,
  Video,
  Archive,
  Plus,
  CheckCircle,
  AlertCircle,
  Clock,
  User,
  Calendar,
  HardDrive
} from 'lucide-react';

import { ContentBlock, EvidenceBlockContent, BlockConfig, defaultBlockConfig, Evidence } from '@/types';
import { taskService } from '@/services/taskService';

// Tipo local para evidências de execução (compatível com EvidenceService)
interface ExecutionEvidence {
  id: string;
  type: 'file' | 'image' | 'text' | 'url';
  name: string;
  url: string;
  size?: number;
  mimeType?: string;
  uploadedBy?: string | { id: string; name: string; email: string };
  uploadedAt?: string;
}

import { BlockCardIcon } from '../shared/BlockCardIcon';
import { BlockCard } from '@/components/ui/BlockCard';
import { FileUploadDialog } from '@/components/forms/FileUploadDialog';
import { EvidenceService } from '@/services/evidenceService';
import { EvidenceApprovalService, EvidenceWithApproval } from '@/services/evidenceApprovalService';
import { EvidenceStatusBadge, useEvidenceStatus } from './EvidenceStatusBadge';
import { EvidenceApprovalActions } from './EvidenceApprovalActions';
import { useAuth } from '@/auth/useAuth';

export interface EvidenceExecutionBlockProps {
  content: EvidenceBlockContent;
  config?: BlockConfig;
  taskId?: string;
  blockId?: string;
  onEvidenceChange?: (evidences: Evidence[]) => void;
  className?: string;
  onContentChange?: (block: ContentBlock) => void;
}

const DEFAULT_EVIDENCE_CONTENT: EvidenceBlockContent = {
  title: 'Evidências/Anexos',
  description: 'Envie arquivos como evidência ou anexo para esta tarefa.',
  allowUpload: true,
  allowedFileTypes: ['image/*', 'video/*', '.pdf', '.doc', '.docx', '.txt'],
  maxFileSize: 10,
  maxFiles: 10,
  showUploadedBy: true,
  showUploadDate: true,
  evidences: []
};

const getFileIcon = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp':
      return ImageIcon;
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      return Video;
    case 'pdf':
    case 'doc':
    case 'docx':
    case 'txt':
      return FileText;
    case 'zip':
    case 'rar':
    case '7z':
      return Archive;
    default:
      return FileIcon;
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);

  if (diffHours < 1) return 'Agora há pouco';
  if (diffHours < 24) return `${diffHours}h atrás`;
  if (diffDays < 7) return `${diffDays}d atrás`;

  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

const getFileTypeInfo = (fileName?: string, mimeType?: string) => {
  if (!fileName) {
    return { icon: FileIcon, color: 'text-gray-600', bgColor: 'bg-gray-50', category: 'Arquivo' };
  }

  const extension = fileName.split('.').pop()?.toLowerCase();

  if (mimeType?.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
    return { icon: ImageIcon, color: 'text-green-600', bgColor: 'bg-green-50', category: 'Imagem' };
  }
  if (mimeType?.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'mkv'].includes(extension || '')) {
    return { icon: Video, color: 'text-purple-600', bgColor: 'bg-purple-50', category: 'Vídeo' };
  }
  if (['pdf'].includes(extension || '')) {
    return { icon: FileText, color: 'text-red-600', bgColor: 'bg-red-50', category: 'PDF' };
  }
  if (['doc', 'docx', 'txt', 'rtf'].includes(extension || '')) {
    return { icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-50', category: 'Documento' };
  }
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension || '')) {
    return { icon: Archive, color: 'text-orange-600', bgColor: 'bg-orange-50', category: 'Arquivo' };
  }

  return { icon: FileIcon, color: 'text-gray-600', bgColor: 'bg-gray-50', category: 'Arquivo' };
};

export const EvidenceExecutionBlock: React.FC<EvidenceExecutionBlockProps> = ({
  content,
  config,
  taskId,
  blockId,
  onEvidenceChange,
  onContentChange,
  className
}) => {
  const safeConfig = config || defaultBlockConfig;
  const safeContent = { ...DEFAULT_EVIDENCE_CONTENT, ...content };
  const { toast } = useToast();
  const { user } = useAuth();

  const [showFileUpload, setShowFileUpload] = useState(false);
  const [evidences, setEvidences] = useState<EvidenceWithApproval[]>([]);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [canUpload, setCanUpload] = useState(false);

  // Carregar evidências do banco de dados quando o componente for montado
  useEffect(() => {
    const loadEvidences = async () => {
      if (!taskId) {
        setLoading(false);
        return;
      }

      try {
        console.log('🔍 Carregando evidências para tarefa:', taskId, 'bloco:', blockId);

        if (!user?.id) {
          console.warn('Usuário não autenticado');
          return;
        }

        // Carregar evidências com informações de aprovação e permissões
        const evidencesWithApproval = await EvidenceApprovalService.getEvidencesWithApproval(
          taskId,
          blockId || 'default',
          user.id
        );

        console.log('✅ Evidências com aprovação carregadas:', evidencesWithApproval);
        setEvidences(evidencesWithApproval);

        // Verificar se o usuário pode fazer upload
        const uploadPermission = await EvidenceApprovalService.canUploadEvidence(taskId, user.id);
        setCanUpload(uploadPermission);
      } catch (error) {
        console.error('❌ Erro ao carregar evidências:', error);
        toast({
          title: 'Erro ao carregar evidências',
          description: 'Não foi possível carregar as evidências da tarefa.',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    loadEvidences();
  }, [taskId, blockId, user?.id, toast]);

  // Handler para mudanças de aprovação
  const handleApprovalChange = useCallback((updatedEvidence: Evidence) => {
    setEvidences(prev => prev.map(evidence =>
      evidence.id === updatedEvidence.id
        ? { ...evidence, ...updatedEvidence } as EvidenceWithApproval
        : evidence
    ));
  }, []);

  // Handler para exclusão de evidência
  const handleDeleteEvidence = useCallback(async (evidenceId: string) => {
    if (!taskId || !blockId || !user?.id) return;

    try {
      await EvidenceApprovalService.deleteEvidence(evidenceId, taskId, blockId, user.id);
      setEvidences(prev => prev.filter(evidence => evidence.id !== evidenceId));

      toast({
        title: 'Evidência excluída',
        description: 'A evidência foi excluída com sucesso.',
        variant: 'default'
      });
    } catch (error) {
      console.error('Erro ao excluir evidência:', error);
      toast({
        title: 'Erro ao excluir',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    }
  }, [taskId, blockId, user?.id, toast]);

  // Configurações de estilo do card
  const cardBgColor = safeConfig.card?.backgroundColor || '#ffffff';
  const cardTextColor = safeConfig.card?.font?.color || '#000000';
  const cardFormat = safeConfig.card?.format || 'rounded';
  const cardBorder = safeConfig.card?.border?.enabled;
  const cardBorderColor = safeConfig.card?.border?.color || '#e5e5e5';
  const cardBorderWidth = safeConfig.card?.border?.width || 1;
  const cardShadow = safeConfig.card?.shadow?.enabled;
  const cardShadowDepth = safeConfig.card?.shadow?.depth || 1;
  const cardHover = safeConfig.card?.hover?.enabled;
  const cardHoverShadowDepth = safeConfig.card?.hover?.shadowDepth || 2;

  // Função para gerar box-shadow
  const getBoxShadow = (depth: number) => `0 ${depth * 2}px ${depth * 4}px rgba(0,0,0,${depth * 0.1})`;

  // Estilos do card
  const cardStyles: React.CSSProperties = {
    background: cardBgColor,
    color: cardTextColor,
    borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
    border: cardBorder ? `${cardBorderWidth}px solid ${cardBorderColor}` : 'none',
    boxShadow: cardShadow ? getBoxShadow(cardShadowDepth) : 'none',
    transition: cardHover ? 'box-shadow 0.3s ease, transform 0.2s ease' : 'none',
    cursor: cardHover ? 'pointer' : 'default'
  };

  // Handlers de hover
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = getBoxShadow(cardHoverShadowDepth);
      e.currentTarget.style.transform = 'translateY(-2px)';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = cardShadow ? getBoxShadow(cardShadowDepth) : 'none';
      e.currentTarget.style.transform = 'translateY(0)';
    }
  };

  const handleFileUploaded = useCallback(async (fileData: { name: string; size: number; type: string; file?: File }) => {
    if (!taskId || !blockId || !user?.id || !fileData.file) {
      toast({
        title: 'Erro',
        description: 'Informações necessárias não disponíveis para upload.',
        variant: 'destructive'
      });
      return;
    }

    // Validar arquivo
    const validationResult = EvidenceService.validateFile(fileData.file);
    if (!validationResult.isValid) {
      toast({
        title: 'Arquivo inválido',
        description: validationResult.error,
        variant: 'destructive'
      });
      return;
    }

    setUploading(true);
    try {
      const uploadResult = await EvidenceService.uploadEvidence({
        taskId,
        blockId,
        file: fileData.file,
        userId: user.id
      });

      if (!uploadResult.success || !uploadResult.data) {
        throw new Error(uploadResult.error || 'Falha ao enviar arquivo');
      }

      const uploadedEvidence = uploadResult.data;

      // Create evidence block
      const newContentBlock: ContentBlock = {
        id: generateUUID(), // Use our UUID generator function
        type: 'evidence',
        content: {
          ...safeContent,
          evidences: [
            ...(safeContent.evidences || []),
            {
              id: uploadedEvidence.id,
              taskId: uploadedEvidence.taskId,
              type: uploadedEvidence.type,
              content: uploadedEvidence.content,
              fileName: uploadedEvidence.fileName,
              fileSize: uploadedEvidence.fileSize,
              uploadedAt: uploadedEvidence.uploadedAt,
              uploadedBy: {
                id: user.id,
                name: user.name || '',
                email: user.email || '',
                role: 'member',
                isActive: true
              },
              status: 'pending'
            }
          ]
        },
        order: 0
      };

      // Create file block
      const fileBlock: ContentBlock = {
        id: uploadedEvidence.id,
        type: 'file',
        content: {
          name: uploadedEvidence.fileName,
          fileName: uploadedEvidence.fileName,
          url: uploadedEvidence.content,
          type: fileData.type,
          size: uploadedEvidence.fileSize,
          uploadedAt: uploadedEvidence.uploadedAt,
          uploadedBy: user.name || user.email || '',
          allowDownload: true,
          allowInlineView: true
        },
        order: 1
      };

      // Save both blocks
      if (taskId) {
        await taskService.saveContentBlocks(taskId, [newContentBlock, fileBlock]);
      }

      // Notify parent components about the change
      if (onContentChange) {
        onContentChange(newContentBlock);
      }

      // Update evidence list
      const updatedEvidences = await EvidenceApprovalService.getEvidencesWithApproval(
        taskId,
        blockId,
        user.id
      );
      setEvidences(updatedEvidences);

      // Show success message
      toast({
        title: 'Arquivo enviado',
        description: `${fileData.name} foi adicionado às evidências e ao conteúdo da tarefa.`,
      });

      // Close upload dialog
      setShowFileUpload(false);
    } catch (error) {
      console.error('Erro no upload:', error);
      toast({
        title: 'Erro no upload',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    } finally {
      setUploading(false);
    }
  }, [evidences, onEvidenceChange, onContentChange, toast, taskId, blockId, user, safeContent]);

  const handleRemoveEvidence = useCallback(async (evidenceId: string) => {
    try {
      await EvidenceService.removeEvidence(evidenceId);

      const updatedEvidences = evidences.filter(e => e.id !== evidenceId);
      setEvidences(updatedEvidences);

      toast({
        title: 'Arquivo removido',
        description: 'O arquivo foi removido das evidências.',
      });
    } catch (error) {
      console.error('Erro ao remover evidência:', error);
      toast({
        title: 'Erro ao remover',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    }
  }, [evidences, toast]);

  const handleDownloadEvidence = useCallback(async (evidence: EvidenceWithApproval) => {
    try {
      const downloadUrl = evidence.content.startsWith('http') 
        ? evidence.content 
        : await EvidenceService.getDownloadUrl(evidence as any);

      // Criar link temporário para download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = evidence.fileName || 'arquivo';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Download iniciado',
        description: `Baixando ${evidence.fileName || 'arquivo'}...`,
      });
    } catch (error) {
      console.error('Erro no download:', error);
      toast({
        title: 'Erro no download',
        description: 'Não foi possível baixar o arquivo.',
        variant: 'destructive'
      });
    }
  }, [toast]);

  const handleViewEvidence = useCallback(async (evidence: EvidenceWithApproval) => {
    try {
      const viewUrl = evidence.content.startsWith('http') 
        ? evidence.content 
        : await EvidenceService.getDownloadUrl(evidence as any);
        
      window.open(viewUrl, '_blank');

      toast({
        title: 'Arquivo aberto',
        description: `Abrindo ${evidence.fileName || 'arquivo'} em nova aba...`,
      });
    } catch (error) {
      console.error('Erro ao visualizar:', error);
      toast({
        title: 'Erro ao visualizar',
        description: 'Não foi possível abrir o arquivo.',
        variant: 'destructive'
      });
    }
  }, [toast]);

  const canUploadMore = evidences.length < (safeContent.maxFiles || 10);
  const usagePercentage = safeContent.maxFiles ? (evidences.length / safeContent.maxFiles) * 100 : 0;

  return (
    <>
      <BlockCard
        className={`${className || ''}`}
        style={cardStyles}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <BlockCardIcon
          config={safeConfig.icon}
          title={safeContent.title || 'Evidências/Anexos'}
          textColor={cardTextColor}
          content={
            <div className="space-y-6">
              {/* Descrição */}
              {safeContent.description && (
                <p className="text-sm text-gray-600 leading-relaxed">
                  {safeContent.description}
                </p>
              )}

              {/* Header com estatísticas */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-gray-50 rounded-xl border border-gray-100">
                <div className="flex items-center gap-4">
                  <Badge variant="secondary" className="text-xs font-medium px-3 py-1.5">
                    {evidences.length} {evidences.length === 1 ? 'arquivo' : 'arquivos'}
                  </Badge>
                  {safeContent.maxFiles && (
                    <div className="flex items-center gap-3">
                      <Progress value={usagePercentage} className="w-24 h-2" />
                      <span className="text-xs text-gray-500 font-medium whitespace-nowrap">
                        {safeContent.maxFiles} máx
                      </span>
                    </div>
                  )}
                </div>

                {safeContent.allowUpload && canUploadMore && (
                  <Button
                    size="sm"
                    onClick={() => setShowFileUpload(true)}
                    disabled={uploading}
                    className="gap-2 px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all duration-200 flex-shrink-0"
                    style={{
                      backgroundColor: cardTextColor || '#14b8a6',
                      color: cardBgColor || '#ffffff',
                      borderColor: cardTextColor || '#14b8a6'
                    }}
                  >
                    <Plus className="w-4 h-4" />
                    Adicionar
                  </Button>
                )}
              </div>

              {/* Indicador de carregamento */}
              {loading ? (
                <div className="text-center py-12">
                  <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-sm text-gray-500">Carregando evidências...</p>
                </div>
              ) : (
                <>
                  {/* Lista de evidências */}
                  {evidences.length > 0 ? (
                <div className="space-y-4">
                  {evidences.map((evidence) => {
                    const fileInfo = getFileTypeInfo(evidence.fileName, evidence.fileSize ? 'file' : undefined);
                    const FileIconComponent = fileInfo.icon;

                    return (
                      <div
                        key={evidence.id}
                        className="group relative bg-white border border-gray-200 rounded-xl p-4 hover:shadow-lg hover:border-gray-300 transition-all duration-200"
                      >
                        <div className="flex flex-col sm:flex-row sm:items-start gap-4">
                          {/* Ícone do arquivo */}
                          <div className={`w-12 h-12 rounded-lg ${fileInfo.bgColor} flex items-center justify-center flex-shrink-0 shadow-sm`}>
                            <FileIconComponent className={`w-6 h-6 ${fileInfo.color}`} />
                          </div>

                          {/* Informações do arquivo */}
                          <div className="flex-1 min-w-0">
                            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                              <div className="min-w-0 flex-1">
                                <h4 className="font-semibold text-gray-900 text-sm mb-2 break-words">
                                  {evidence.fileName || 'Arquivo sem nome'}
                                </h4>
                                <div className="flex flex-wrap items-center gap-3 mb-3">
                                  <Badge variant="outline" className="text-xs font-medium px-2 py-1">
                                    {fileInfo.category}
                                  </Badge>
                                  {evidence.fileSize && (
                                    <span className="text-xs text-gray-500 flex items-center gap-1">
                                      <HardDrive className="w-3 h-3" />
                                      {formatFileSize(evidence.fileSize)}
                                    </span>
                                  )}
                                  {/* Badge de status de aprovação */}
                                  <EvidenceStatusBadge evidence={evidence} />
                                </div>

                                {/* Metadados */}
                                {(safeContent.showUploadedBy || safeContent.showUploadDate) && (
                                  <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500">
                                    {safeContent.showUploadedBy && evidence.uploadedBy && (
                                      <span className="flex items-center gap-1">
                                        <User className="w-3 h-3" />
                                        <span className="truncate max-w-[120px]">
                                          {typeof evidence.uploadedBy === 'string'
                                            ? evidence.uploadedBy
                                            : evidence.uploadedBy.name || 'Desconhecido'}
                                        </span>
                                      </span>
                                    )}
                                    {safeContent.showUploadDate && evidence.uploadedAt && (
                                      <span className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        {formatDate(evidence.uploadedAt)}
                                      </span>
                                    )}
                                  </div>
                                )}
                              </div>

                              {/* Ações */}
                              <div className="flex items-center gap-1 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleViewEvidence(evidence)}
                                  className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600 rounded-lg"
                                  title="Visualizar"
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleDownloadEvidence(evidence)}
                                  className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 rounded-lg"
                                  title="Download"
                                >
                                  <Download className="w-4 h-4" />
                                </Button>
                                {evidence.canDelete && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleDeleteEvidence(evidence.id)}
                                    className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 rounded-lg"
                                    title="Remover"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Seção de aprovação expandida */}
                          <div className="mt-4 pt-4 border-t border-gray-100">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                              {/* Status detalhado */}
                              <div className="flex-1">
                                <EvidenceStatusBadge evidence={evidence} showDetails={true} />
                              </div>

                              {/* Ações de aprovação */}
                              {user && taskId && blockId && (
                                <div className="flex-shrink-0">
                                  <EvidenceApprovalActions
                                    evidence={evidence}
                                    taskId={taskId}
                                    blockId={blockId}
                                    userId={user.id}
                                    canApprove={evidence.canApprove}
                                    onApprovalChange={handleApprovalChange}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12 px-4">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-xl flex items-center justify-center">
                    <Paperclip className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-base font-semibold text-gray-900 mb-2">
                    Nenhuma evidência adicionada
                  </h3>
                  <p className="text-sm text-gray-500 mb-6 max-w-sm mx-auto leading-relaxed">
                    {safeContent.allowUpload
                      ? 'Adicione arquivos como evidência ou anexo para esta tarefa.'
                      : 'Não há evidências disponíveis para esta tarefa.'
                    }
                  </p>
                  {safeContent.allowUpload && canUploadMore && (
                    <Button
                      onClick={() => setShowFileUpload(true)}
                      disabled={uploading}
                      className="gap-2 px-4 py-2 font-medium shadow-sm hover:shadow-md transition-all duration-200"
                      style={{
                        backgroundColor: cardTextColor || '#14b8a6',
                        color: cardBgColor || '#ffffff'
                      }}
                    >
                      <Plus className="w-4 h-4" />
                      Adicionar Arquivo
                    </Button>
                  )}
                </div>
              )}

              {/* Informações sobre limites */}
              {safeContent.allowUpload && (evidences.length > 0 || !canUploadMore) && (
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-gray-600 space-y-1">
                      <p className="font-medium text-gray-700">Informações sobre upload:</p>
                      <div className="space-y-1 text-xs">
                        {safeContent.maxFileSize && (
                          <p>• Tamanho máximo: <span className="font-medium">{safeContent.maxFileSize}MB</span> por arquivo</p>
                        )}
                        {safeContent.maxFiles && (
                          <p>• Limite: <span className="font-medium">{safeContent.maxFiles} arquivos</span> no total</p>
                        )}
                        {safeContent.allowedFileTypes && safeContent.allowedFileTypes.length > 0 && (
                          <p>• Tipos aceitos: <span className="font-medium">{safeContent.allowedFileTypes.join(', ')}</span></p>
                        )}
                        {!canUploadMore && (
                          <p className="text-orange-600 font-medium">
                            • Limite de arquivos atingido
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Indicador de upload */}
              {uploading && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm text-blue-700 font-medium">
                      Enviando arquivo...
                    </span>
                  </div>
                </div>
              )}
                </>
              )}
            </div>
          }
        />
      </BlockCard>

      {/* Dialog de upload */}
      <FileUploadDialog
        open={showFileUpload}
        onOpenChange={setShowFileUpload}
        onFileUploaded={handleFileUploaded}
      />
    </>
  );
};

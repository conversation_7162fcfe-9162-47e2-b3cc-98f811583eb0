# 🗄️ Executar Schema Completo do Supabase

## 🎯 **SCHEMA ATUALIZADO COM QUIZ COMPLETO!**

### ✅ **O que foi atualizado:**
- **Arquivo único:** `data/supabase_schema.sql` contém TUDO
- **Estrutura do Quiz** integrada ao schema principal
- **Tipos corrigidos** para compatibilidade com modo híbrido
- **Relacionamentos otimizados** para performance

---

## 🚀 **EXECUTAR SCHEMA COMPLETO:**

### **1. 📊 Acesse o Supabase:**
```
1. Vá para: https://supabase.com/dashboard
2. Selecione projeto: "Gestão de Projeto"
3. Clique em "SQL Editor"
```

### **2. 📝 Execute o Schema Completo:**
```
1. Clique em "New Query"
2. Copie TODO o conteúdo do arquivo: data/supabase_schema.sql
3. Cole no editor SQL
4. Clique em "Run" (ou Ctrl+Enter)
5. Aguarde execução completa (pode demorar alguns minutos)
```

### **3. ✅ Verificar Criação:**
```
1. Vá para "Table Editor"
2. Deve ver TODAS as tabelas:
   - Tabelas principais: projects, stages, tasks, profiles
   - Tabelas auxiliares: project_members, task_executors, etc.
   - Tabelas do Quiz: quizzes, quiz_attempts, quiz_answers, user_quiz_progress, quiz_statistics
```

---

## 📊 **ESTRUTURA DO QUIZ INCLUÍDA:**

### **🧩 Tabela `quizzes`:**
```sql
- id (UUID, PK) - ID único do quiz
- task_id (TEXT) - ID da tarefa (compatível com frontend)
- block_id (TEXT) - ID do bloco de conteúdo
- content (JSONB) - QuizContent completo
- is_active (BOOLEAN) - Se o quiz está ativo
- created_at, updated_at (TIMESTAMP)
- UNIQUE(task_id, block_id)
```

### **🎯 Tabela `quiz_attempts`:**
```sql
- id (UUID, PK) - ID único da tentativa
- quiz_id (UUID, FK) - Referência para quizzes.id
- user_id (UUID, FK) - Referência para profiles.id
- started_at, submitted_at (TIMESTAMP)
- score, max_score (INTEGER) - Compatível com LocalQuizService
- percentage (DECIMAL) - Porcentagem de acerto
- passed (BOOLEAN) - Se foi aprovado
- status (TEXT) - 'in_progress', 'submitted', 'graded'
- time_spent (INTEGER) - Tempo em segundos
```

### **📝 Tabela `quiz_answers`:**
```sql
- id (UUID, PK) - ID único da resposta
- attempt_id (UUID, FK) - Referência para quiz_attempts.id
- question_id (TEXT) - ID da pergunta
- question_type (TEXT) - Tipo da pergunta
- selected_options (TEXT[]) - Opções selecionadas
- text_answer (TEXT) - Resposta de texto
- boolean_answer (BOOLEAN) - Verdadeiro/falso
- ordering_answer (JSONB) - Resposta de ordenação
- matching_answer (JSONB) - Resposta de correspondência
- is_correct (BOOLEAN) - Se está correto
- points_earned (INTEGER) - Pontos ganhos
- time_spent (INTEGER) - Tempo gasto
```

### **📈 Tabela `user_quiz_progress`:**
```sql
- id (UUID, PK) - ID único do progresso
- user_id (UUID, FK) - Referência para profiles.id
- quiz_id (UUID, FK) - Referência para quizzes.id
- total_attempts (INTEGER) - Total de tentativas
- best_score (INTEGER) - Melhor pontuação
- best_percentage (DECIMAL) - Melhor porcentagem
- passed (BOOLEAN) - Se foi aprovado
- first_attempt_at, last_attempt_at (TIMESTAMP)
- UNIQUE(user_id, quiz_id)
```

### **📊 Tabela `quiz_statistics`:**
```sql
- id (UUID, PK) - ID único das estatísticas
- quiz_id (UUID, FK) - Referência para quizzes.id
- total_attempts (INTEGER) - Total de tentativas
- unique_users (INTEGER) - Usuários únicos
- average_score (DECIMAL) - Pontuação média
- pass_rate (DECIMAL) - Taxa de aprovação
- average_time_spent (INTEGER) - Tempo médio
- question_stats (JSONB) - Estatísticas por pergunta
- last_calculated_at (TIMESTAMP) - Última atualização
```

---

## 🔧 **RECURSOS INCLUÍDOS:**

### **✅ Índices para Performance:**
```sql
- Busca rápida por task_id e block_id
- Consultas eficientes por usuário
- Relacionamentos otimizados
- Queries de estatísticas rápidas
```

### **✅ Triggers Automáticos:**
```sql
- updated_at atualizado automaticamente
- Estatísticas recalculadas ao finalizar tentativa
- Timestamps precisos em todas as operações
```

### **✅ RLS Policies:**
```sql
- Segurança por usuário e projeto
- Acesso controlado a dados do quiz
- Proteção de dados pessoais
- Permissões granulares
```

### **✅ Constraints e Validações:**
```sql
- Relacionamentos garantidos por Foreign Keys
- Valores únicos onde necessário
- Tipos de dados apropriados
- Status válidos enforçados
```

---

## 🧪 **TESTE APÓS EXECUÇÃO:**

### **1. 🗄️ Verificar Tabelas:**
```sql
-- Execute esta query para verificar:
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename LIKE '%quiz%'
ORDER BY tablename;
```

### **2. 🚀 Teste o Quiz:**
```
1. Vá para uma tarefa com quiz
2. Complete o quiz normalmente
3. Finalize o quiz
4. Observe logs no console
```

### **3. 📊 Logs Esperados:**
```
✅ "📝 Criando quiz no Supabase..."
✅ "✅ Quiz criado no Supabase com ID: [uuid]"
✅ "✅ Resultados salvos no Supabase com sucesso"
```

### **4. 🗄️ Verificar Dados Salvos:**
```sql
-- Verificar quiz criado:
SELECT * FROM quizzes ORDER BY created_at DESC LIMIT 1;

-- Verificar tentativa salva:
SELECT * FROM quiz_attempts ORDER BY created_at DESC LIMIT 1;

-- Verificar respostas salvas:
SELECT * FROM quiz_answers ORDER BY created_at DESC LIMIT 5;
```

---

## 🚨 **TROUBLESHOOTING:**

### **Se Schema Falhar:**
```
1. Verifique se está no projeto correto
2. Confirme permissões de administrador
3. Execute em partes se necessário
4. Verifique logs de erro no Supabase
```

### **Se Tabelas Não Aparecem:**
```
1. Recarregue página do Supabase
2. Verifique aba "Table Editor"
3. Execute query de verificação
4. Confirme que script executou sem erros
```

### **Se Ainda Há Erro 400:**
```
1. Confirme que todas as tabelas foram criadas
2. Verifique estrutura das colunas
3. Teste com dados simples primeiro
4. Verifique logs detalhados no console
```

---

## 🎯 **VANTAGENS DO SCHEMA UNIFICADO:**

### **✅ Simplicidade:**
- **Um arquivo único** para todo o banco
- **Execução única** cria tudo
- **Manutenção centralizada**
- **Versionamento simplificado**

### **✅ Consistência:**
- **Relacionamentos garantidos**
- **Tipos de dados padronizados**
- **Nomenclatura consistente**
- **Estrutura otimizada**

### **✅ Performance:**
- **Índices otimizados**
- **Queries eficientes**
- **Triggers automáticos**
- **Estatísticas em tempo real**

---

## 📞 **PRÓXIMOS PASSOS:**

### **1. 🗄️ Execute o Schema:**
- Copie conteúdo de `data/supabase_schema.sql`
- Execute no SQL Editor do Supabase
- Verifique criação de todas as tabelas

### **2. 🧪 Teste o Sistema:**
- Execute um quiz completo
- Verifique logs no console
- Confirme dados no Supabase

### **3. ✅ Confirme Funcionamento:**
- Toast de sucesso aparece
- Dados salvos corretamente
- Sem mais erros 400

---

## 🎉 **RESULTADO ESPERADO:**

### **✅ Após Execução:**
```
✅ Todas as tabelas criadas no Supabase
✅ Relacionamentos funcionando
✅ Índices e triggers ativos
✅ Quiz salva dados automaticamente
✅ Sem mais erros 400
✅ Toast de sucesso aparece
✅ Dados visíveis no painel
✅ Sistema completo funcionando
```

### **🔄 Fluxo Garantido:**
```
1. Usuário executa quiz (modo local)
2. Quiz finaliza localmente (garantido)
3. Sistema cria quiz no Supabase (automático)
4. Sistema salva tentativa no Supabase
5. Sistema salva respostas no Supabase
6. Usuário recebe confirmação de sucesso
7. Dados ficam disponíveis no painel
8. Estatísticas são calculadas automaticamente
```

---

## 🚀 **CONCLUSÃO:**

**🎯 O schema unificado oferece:**
- **Estrutura completa** em um arquivo
- **Compatibilidade total** com modo híbrido
- **Performance otimizada** para produção
- **Manutenção simplificada**

**🗄️ Execute agora o schema completo e tenha todo o sistema funcionando!** ✅📊🚀

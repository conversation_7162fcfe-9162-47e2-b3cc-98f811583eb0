-- =====================================================
-- TESTE RÁPIDO DE FUNCIONALIDADES (VERSÃO MÍNIMA)
-- =====================================================
-- Execute uma seção por vez no Supabase SQL Editor

-- =====================================================
-- SEÇÃO 1: VERIFICAR LIMPEZA RLS
-- =====================================================

-- Verificar se ainda há políticas RLS
SELECT 
    COUNT(*) as policies_restantes,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ LIMPEZA COMPLETA'
        ELSE '❌ AINDA HÁ POLÍTICAS'
    END as status_limpeza
FROM pg_policies 
WHERE schemaname = 'public';

-- =====================================================
-- SEÇÃO 2: TESTAR ACESSO ÀS TABELAS PRINCIPAIS
-- =====================================================

-- Profiles
SELECT 'profiles' as tabela, COUNT(*) as total FROM profiles;

-- Projects  
SELECT 'projects' as tabela, COUNT(*) as total FROM projects;

-- Stages
SELECT 'stages' as tabela, COUNT(*) as total FROM stages;

-- Tasks
SELECT 'tasks' as tabela, COUNT(*) as total FROM tasks;

-- Project Members
SELECT 'project_members' as tabela, COUNT(*) as total FROM project_members;

-- =====================================================
-- SEÇÃO 3: TESTAR DADOS PARA AUTOCOMPLETE
-- =====================================================

-- Usuários ativos para autocomplete
SELECT 
    COUNT(*) as usuarios_ativos,
    '✅ DISPONÍVEL PARA AUTOCOMPLETE' as status
FROM profiles 
WHERE is_active = true;

-- Amostra de usuários
SELECT 
    id,
    name,
    email,
    role
FROM profiles 
WHERE is_active = true 
AND name IS NOT NULL
LIMIT 3;

-- =====================================================
-- SEÇÃO 4: VERIFICAR RELACIONAMENTOS
-- =====================================================

-- Projetos com etapas
SELECT 
    p.name as projeto,
    COUNT(s.id) as total_etapas
FROM projects p
LEFT JOIN stages s ON s.project_id = p.id
GROUP BY p.id, p.name
LIMIT 3;

-- Etapas com tarefas
SELECT 
    s.name as etapa,
    COUNT(t.id) as total_tarefas
FROM stages s
LEFT JOIN tasks t ON t.stage_id = s.id
GROUP BY s.id, s.name
LIMIT 3;

-- =====================================================
-- SEÇÃO 5: STATUS FINAL
-- =====================================================

-- Resumo completo
SELECT 
    'VERIFICAÇÃO FINAL' as categoria,
    'Total de políticas RLS' as item,
    COUNT(*)::TEXT as valor
FROM pg_policies 
WHERE schemaname = 'public'

UNION ALL

SELECT 
    '',
    'Status do sistema',
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public')
        THEN '✅ SISTEMA LIMPO E FUNCIONANDO'
        ELSE '❌ AINDA HÁ POLÍTICAS RLS'
    END

UNION ALL

SELECT 
    '',
    'Auth disponível',
    CASE 
        WHEN auth.uid() IS NULL THEN '✅ Função auth.uid() OK (NULL sem login)'
        ELSE '✅ Usuário logado: ' || auth.uid()::TEXT
    END;

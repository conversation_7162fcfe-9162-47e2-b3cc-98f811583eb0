import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Timer,
  ArrowLeft,
  ArrowRight,
  Save,
  Send,
  Play,
  RotateCcw,
  Trophy,
  Target,
  X,
  BarChart3,
  GripVertical
} from 'lucide-react';

// Imports para drag and drop
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { QuizBlockContent, BlockConfig, defaultBlockConfig } from '@/types';
import { QuizContent, QuizQuestion, QuizAnswer, QuizAttempt, UserQuizProgress, QuizUtils, SurveyResults } from '@/types/quiz';
import { BlockCardIcon } from '../shared/BlockCardIcon';
import { BlockCard } from '@/components/ui/BlockCard';
import { QuizService } from '@/services/quizService';
import { LocalQuizService } from '@/services/localQuizService';
import { useToast } from '@/hooks/ui/use-toast';
import { useAuth } from '@/auth/useAuth';
import { DetailedResultsModal } from './components/DetailedResultsModal';
import { SurveyResultsModal } from './components/SurveyResultsModal';

// Componente para item arrastável de ordenação
function SortableOrderingItem({ item, index }: { item: any, index: number }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="p-3 border rounded-lg bg-gray-50 cursor-move hover:bg-gray-100 transition-colors"
    >
      <div className="flex items-center gap-2">
        <GripVertical className="w-4 h-4 text-gray-400" />
        <span className="text-sm font-medium">{index + 1}.</span>
        <span className="text-sm">{item.text}</span>
      </div>
    </div>
  );
}

// Componente para item de correspondência
function MatchingItem({
  id,
  text,
  side,
  isSelected,
  isMatched,
  matchedWith,
  onSelect,
  question
}: {
  id: string;
  text: string;
  side: 'left' | 'right';
  isSelected: boolean;
  isMatched: boolean;
  matchedWith?: string;
  onSelect: () => void;
  question: any;
}) {
  const getMatchedText = () => {
    if (!matchedWith) return null;
    if (side === 'left') {
      return question.matchingPairs?.find((p: any) => p.id === matchedWith)?.right;
    } else {
      return question.matchingPairs?.find((p: any) => p.id === matchedWith)?.left;
    }
  };

  return (
    <div
      onClick={onSelect}
      className={`p-3 border rounded-lg cursor-pointer transition-all ${
        isSelected
          ? 'border-blue-500 bg-blue-100'
          : isMatched
            ? 'border-green-500 bg-green-50'
            : side === 'left'
              ? 'bg-blue-50 hover:bg-blue-100'
              : 'bg-green-50 hover:bg-green-100'
      }`}
    >
      <div className="text-sm">{text}</div>
      {isMatched && matchedWith && (
        <div className="text-xs text-gray-600 mt-1">
          ↔ {getMatchedText()}
        </div>
      )}
    </div>
  );
}

// Função auxiliar para garantir que o quiz existe no Supabase
const ensureQuizExistsInSupabase = async (taskId: string, blockId: string, quizContent: QuizContent): Promise<string | null> => {
  try {
    // Verificar se quiz já existe
    const existingQuiz = await QuizService.loadQuiz(taskId, blockId);

    if (!existingQuiz) {
      console.log('📝 Criando quiz no Supabase...');
      // Criar quiz no Supabase
      const quizId = await QuizService.createQuiz(taskId, blockId, quizContent);
      if (quizId) {
        console.log('✅ Quiz criado no Supabase com ID:', quizId);
        return quizId;
      } else {
        console.warn('⚠️ Falha ao criar quiz no Supabase');
        return null;
      }
    } else {
      console.log('✅ Quiz já existe no Supabase com ID:', existingQuiz.id);
      return existingQuiz.id;
    }
  } catch (error) {
    console.warn('⚠️ Erro ao verificar/criar quiz no Supabase:', error);
    // Não falhar - continuar com salvamento local
    return null;
  }
};

interface QuizExecutionBlockProps {
  content: QuizBlockContent;
  config?: BlockConfig;
  taskId: string;
  blockId: string;
  onComplete?: (passed: boolean, score: number) => void;
  className?: string;
}

export const QuizExecutionBlock: React.FC<QuizExecutionBlockProps> = ({
  content,
  config,
  taskId,
  blockId,
  onComplete,
  className
}) => {
  const safeConfig = config || defaultBlockConfig;

  // Configurações de estilo do card
  const cardBgColor = safeConfig.card?.backgroundColor || '#ffffff';
  const cardTextColor = safeConfig.card?.color || '#000000';
  const cardFormat = safeConfig.card?.format || 'rounded';
  const cardBorder = safeConfig.card?.border?.enabled;
  const cardBorderColor = safeConfig.card?.border?.color || '#e5e5e5';
  const cardBorderWidth = safeConfig.card?.border?.width || 1;
  const cardShadow = safeConfig.card?.shadow?.enabled;
  const cardShadowDepth = safeConfig.card?.shadow?.depth || 1;
  const cardHover = safeConfig.card?.hover?.enabled;
  const cardHoverShadowDepth = safeConfig.card?.hover?.shadowDepth || 2;

  // Função para gerar box-shadow
  const getBoxShadow = (depth: number) => `0 ${depth * 2}px ${depth * 4}px rgba(0,0,0,${depth * 0.1})`;

  // Estilos do card
  const cardStyles: React.CSSProperties = {
    background: cardBgColor,
    color: cardTextColor,
    borderRadius: cardFormat === 'pill' ? 9999 : cardFormat === 'square' ? 0 : 12,
    border: cardBorder ? `${cardBorderWidth}px solid ${cardBorderColor}` : 'none',
    boxShadow: cardShadow ? getBoxShadow(cardShadowDepth) : 'none',
    transition: cardHover ? 'box-shadow 0.3s ease, transform 0.2s ease' : 'none',
    cursor: cardHover ? 'pointer' : 'default'
  };

  // Handlers de hover
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = getBoxShadow(cardHoverShadowDepth);
      e.currentTarget.style.transform = 'translateY(-2px)';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardHover) {
      e.currentTarget.style.boxShadow = cardShadow ? getBoxShadow(cardShadowDepth) : 'none';
      e.currentTarget.style.transform = 'translateY(0)';
    }
  };
  const [quizContent, setQuizContent] = useState<QuizContent | null>(null);
  const [currentAttempt, setCurrentAttempt] = useState<QuizAttempt | null>(null);
  const [userProgress, setUserProgress] = useState<UserQuizProgress | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, QuizAnswer>>({});
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [questionTimeRemaining, setQuestionTimeRemaining] = useState<number | null>(null);
  const [questionTimeSpent, setQuestionTimeSpent] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quizState, setQuizState] = useState<'loading' | 'ready' | 'executing' | 'completed' | 'blocked'>('loading');
  const [isLocalMode, setIsLocalMode] = useState(false);
  const [showDetailedResults, setShowDetailedResults] = useState(false);
  const [showSurveyResults, setShowSurveyResults] = useState(false);
  const [surveyResults, setSurveyResults] = useState<SurveyResults | null>(null);
  const [allAttempts, setAllAttempts] = useState<QuizAttempt[]>([]);

  // Estados para funcionalidades de ordenação e correspondência
  const [selectedMatchingItem, setSelectedMatchingItem] = useState<{id: string, side: 'left' | 'right'} | null>(null);

  const { user } = useAuth();
  const { toast } = useToast();

  // Sensores para drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Memoizar itens embaralhados para correspondência
  const shuffledRightItems = useMemo(() => {
    if (!quizContent) return [];
    const currentQuestion = quizContent.questions[currentQuestionIndex];
    if (currentQuestion?.type !== 'matching') return [];

    const items = currentQuestion.matchingPairs?.map(pair => ({ id: pair.id, text: pair.right })) || [];
    return [...items].sort(() => Math.random() - 0.5);
  }, [quizContent, currentQuestionIndex]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const questionStartTimeRef = useRef<number>(Date.now());

  // Função para embaralhar array
  const shuffleArray = useCallback(<T,>(array: T[]): T[] => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }, []);

  // Converter formato antigo para novo se necessário
  const convertToQuizContent = useCallback((): QuizContent => {
    if (content.quiz) {
      return content.quiz;
    }

    // Converter formato antigo
    const defaultConfig = {
      title: content.question || 'Quiz',
      maxAttempts: 3,
      allowRetry: true,
      passingScore: 70,
      showScore: true,
      showCorrectAnswers: true,
      showFeedback: true,
      showDetailedResults: true,
      showTimer: false,
      isRequired: false,
      blockProgressUntilPassed: false,
      shuffleQuestions: false,
      shuffleOptions: false,
      showProgressBar: true,
      allowSaveDraft: true,
      enableAnalytics: true,
      showResultsToUser: true
    };

    const questions = content.question ? [{
      id: 'question_1',
      type: 'single-choice' as const,
      title: content.question,
      points: 1,
      required: true,
      options: (content.options || []).map((opt, idx) => ({
        id: `opt_${idx}`,
        text: opt,
        isCorrect: idx === 0 // Primeira opção como correta por padrão
      }))
    }] : [];

    return {
      config: defaultConfig,
      questions
    };
  }, [content]);

  // Aplicar embaralhamento baseado nas configurações
  const applyShuffling = useCallback((quizContent: QuizContent): QuizContent => {
    if (!quizContent) return quizContent;

    let processedQuestions = [...quizContent.questions];

    // Embaralhar perguntas se configurado
    if (quizContent.config.shuffleQuestions) {
      processedQuestions = shuffleArray(processedQuestions);
    }

    // Embaralhar opções se configurado
    if (quizContent.config.shuffleOptions) {
      processedQuestions = processedQuestions.map(question => {
        if (question.options && question.options.length > 0) {
          return {
            ...question,
            options: shuffleArray(question.options)
          };
        }
        return question;
      });
    }

    return {
      ...quizContent,
      questions: processedQuestions
    };
  }, [shuffleArray]);

  // Determinar qual tentativa é considerada "oficial" para exibição
  const getOfficialAttempt = useCallback((attempts: QuizAttempt[], userProgress: UserQuizProgress | null): QuizAttempt | null => {
    if (!attempts || attempts.length === 0) return null;

    // Sempre encontrar a tentativa com o melhor resultado, independentemente do userProgress
    const bestAttempt = attempts.reduce((best, current) => {
      if (!best) return current;

      // Priorizar tentativas aprovadas
      if (current.passed && !best.passed) return current;
      if (best.passed && !current.passed) return best;

      // Se ambas têm o mesmo status de aprovação, usar a com melhor porcentagem
      if (current.percentage > best.percentage) return current;

      // Se têm a mesma porcentagem, usar a mais recente
      if (current.percentage === best.percentage) {
        return current.attemptNumber > best.attemptNumber ? current : best;
      }

      return best;
    }, null as QuizAttempt | null);

    if (bestAttempt) {
      console.log('🎯 Attempt oficial determinado (melhor resultado):', {
        attemptNumber: bestAttempt.attemptNumber,
        percentage: bestAttempt.percentage,
        passed: bestAttempt.passed,
        totalAttempts: attempts.length,
        reason: 'Melhor resultado entre todas as tentativas'
      });
      return bestAttempt;
    }

    // Fallback: usar a última tentativa (caso não haja tentativas válidas)
    const lastAttempt = attempts[attempts.length - 1];
    console.log('🎯 Usando última tentativa como oficial (fallback):', {
      attemptNumber: lastAttempt.attemptNumber,
      percentage: lastAttempt.percentage,
      passed: lastAttempt.passed,
      reason: 'Fallback - última tentativa'
    });
    return lastAttempt;
  }, []);

  // Carregar dados iniciais
  useEffect(() => {
    const loadQuizData = async () => {
      if (!user?.id) return;

      try {
        setIsLoading(true);
        setError(null);

        // Converter conteúdo
        const convertedContent = convertToQuizContent();
        console.log('📊 Quiz convertido:', convertedContent);
        console.log('📋 Conteúdo original:', content);
        console.log('🔢 Número de perguntas:', convertedContent?.questions?.length || 0);

        // Aplicar embaralhamento se configurado
        const shuffledContent = applyShuffling(convertedContent);
        console.log('🔀 Embaralhamento aplicado:', {
          shuffleQuestions: shuffledContent?.config?.shuffleQuestions,
          shuffleOptions: shuffledContent?.config?.shuffleOptions
        });
        setQuizContent(shuffledContent);

        // VERIFICAÇÃO CRÍTICA: Se não há perguntas válidas, não continuar
        if (!convertedContent || !convertedContent.questions || convertedContent.questions.length === 0) {
          console.error('❌ ERRO CRÍTICO: Nenhuma pergunta válida encontrada');
          setError('Nenhuma pergunta encontrada no quiz');
          setQuizState('blocked');
          setIsLoading(false);
          return;
        }

        // DECISÃO: SEMPRE usar modo local para evitar erros 406
        console.log('🎯 DECISÃO: Usando modo local para evitar erros 406 do Supabase');
        const useLocalMode = true; // Usar variável local para lógica imediata
        setIsLocalMode(useLocalMode);

        // Marcar para sempre usar modo local
        localStorage.setItem('quiz_force_local_mode', 'true');

        // Carregar progresso local se disponível
        const localProgress = LocalQuizService.getUserProgress(taskId, blockId, user.id);
        console.log('📊 Progresso local carregado:', localProgress);
        setUserProgress(localProgress);

        // Verificar se há tentativa em andamento
        const ongoingAttemptKey = `quiz_local_attempt_${taskId}_${blockId}_${user.id}`;
        const ongoingAttempt = localStorage.getItem(ongoingAttemptKey);

        if (ongoingAttempt) {
          try {
            const attemptData = JSON.parse(ongoingAttempt);
            console.log('🔄 Tentativa em andamento encontrada:', attemptData);
            setCurrentAttempt(attemptData);
            setQuizState('executing');
            setCurrentQuestionIndex(attemptData.currentQuestionIndex || 0);
            setAnswers(attemptData.answers || {});
            return;
          } catch (err) {
            console.warn('Erro ao carregar tentativa em andamento:', err);
            localStorage.removeItem(ongoingAttemptKey);
          }
        }

        // Verificar se deve ir para estado 'completed'
        const shouldBeCompleted = localProgress && (
          // Caso 1: Passou e não permite retry
          (localProgress.passed && !convertedContent.config.allowRetry) ||
          // Caso 2: Tentativas esgotadas
          (convertedContent.config.maxAttempts !== -1 && localProgress.totalAttempts >= convertedContent.config.maxAttempts) ||
          // Caso 3: maxAttempts = 1 e já tentou
          (convertedContent.config.maxAttempts === 1 && localProgress.totalAttempts >= 1)
        );

        if (shouldBeCompleted) {
          console.log('✅ Quiz deve ir para estado completed:', {
            passed: localProgress?.passed,
            allowRetry: convertedContent.config.allowRetry,
            totalAttempts: localProgress?.totalAttempts,
            maxAttempts: convertedContent.config.maxAttempts
          });

          // Carregar todas as tentativas e o attempt oficial para exibir no estado completed
          try {
            let attempts: QuizAttempt[] = [];
            let officialAttempt: QuizAttempt | null = null;

            if (useLocalMode) {
              attempts = LocalQuizService.getUserAttempts(taskId, blockId, user.id);
              officialAttempt = getOfficialAttempt(attempts, localProgress);
            } else {
              attempts = await QuizService.getUserAttempts(taskId, blockId, user.id);
              officialAttempt = getOfficialAttempt(attempts, localProgress);
            }

            // Atualizar allAttempts para uso no modal e renderização
            setAllAttempts(attempts);
            console.log('📊 AllAttempts carregado na inicialização:', {
              totalAttempts: attempts.length,
              attempts: attempts.map(a => ({
                attemptNumber: a.attemptNumber,
                percentage: a.percentage,
                passed: a.passed
              }))
            });

            if (officialAttempt) {
              setCurrentAttempt(officialAttempt);
              console.log('✅ Attempt oficial carregado para estado completed:', {
                attemptNumber: officialAttempt.attemptNumber,
                percentage: officialAttempt.percentage,
                passed: officialAttempt.passed,
                isOfficial: true
              });
            }
          } catch (err) {
            console.warn('⚠️ Erro ao carregar attempt oficial:', err);
          }

          setQuizState('completed');
        } else {
          console.log('✅ Quiz configurado em modo local com sucesso');
          setQuizState('ready');
        }

      } catch (err) {
        console.error('❌ ERRO GERAL ao carregar quiz:', err);

        // Em caso de qualquer erro, usar modo local
        setIsLocalMode(true);
        localStorage.setItem('quiz_force_local_mode', 'true');

        // Se há conteúdo válido, continuar em modo local
        if (convertedContent && convertedContent.questions && convertedContent.questions.length > 0) {
          console.log('✅ Erro capturado, mas quiz válido - Continuando em modo local');
          setQuizState('ready');
        } else {
          console.error('❌ Erro e nenhuma pergunta válida encontrada');
          setError('Erro ao carregar quiz');
          setQuizState('blocked');
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadQuizData();
  }, [user?.id, taskId, blockId, convertToQuizContent, applyShuffling]);

  // Debug: Log do estado atual
  useEffect(() => {
    console.log('🔍 DEBUG Estado atual:', {
      quizState,
      isLocalMode,
      hasQuizContent: !!quizContent,
      questionsCount: quizContent?.questions?.length || 0,
      isLoading,
      error
    });
  }, [quizState, isLocalMode, quizContent, isLoading, error]);

  // Timer do quiz
  useEffect(() => {
    if (quizState === 'executing' && quizContent?.config.timeLimit && timeRemaining !== null) {
      timerRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev === null || prev <= 1) {
            handleTimeUp();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }
  }, [quizState, quizContent?.config.timeLimit, timeRemaining]);

  // Rastrear tempo por pergunta
  useEffect(() => {
    if (quizState === 'executing' && quizContent?.questions[currentQuestionIndex]) {
      questionStartTimeRef.current = Date.now();

      // Configurar timer da pergunta se ela tem limite de tempo
      const currentQuestion = quizContent.questions[currentQuestionIndex];
      if (currentQuestion.timeLimit) {
        setQuestionTimeRemaining(currentQuestion.timeLimit);
      } else {
        setQuestionTimeRemaining(null);
      }

      return () => {
        const questionId = quizContent.questions[currentQuestionIndex]?.id;
        if (questionId) {
          const timeSpent = Math.floor((Date.now() - questionStartTimeRef.current) / 1000);
          setQuestionTimeSpent(prev => ({
            ...prev,
            [questionId]: (prev[questionId] || 0) + timeSpent
          }));
        }
      };
    }
  }, [currentQuestionIndex, quizState, quizContent]);

  // Timer da pergunta individual
  useEffect(() => {
    if (quizState === 'executing' && questionTimeRemaining !== null && questionTimeRemaining > 0) {
      const questionTimer = setInterval(() => {
        setQuestionTimeRemaining(prev => {
          if (prev === null || prev <= 1) {
            // Tempo da pergunta esgotado - avançar automaticamente
            handleNextQuestion();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(questionTimer);
    }
  }, [quizState, questionTimeRemaining]);



  // Abrir modal de resultado detalhado
  const handleOpenDetailedResults = async () => {
    if (!user?.id) return;

    try {
      // Carregar todos os attempts do usuário para este quiz
      let attempts: QuizAttempt[] = [];

      if (isLocalMode) {
        attempts = LocalQuizService.getUserAttempts(taskId, blockId, user.id);
      } else {
        attempts = await QuizService.getUserAttempts(taskId, blockId, user.id);
      }

      console.log('📊 Tentativas carregadas para modal detalhado:', {
        totalAttempts: attempts.length,
        attempts: attempts.map(a => ({
          id: a.id,
          attemptNumber: a.attemptNumber,
          percentage: a.percentage,
          passed: a.passed,
          hasAnswers: !!a.answers,
          answersCount: a.answers?.length || 0
        }))
      });

      setAllAttempts(attempts);
      setShowDetailedResults(true);
    } catch (err) {
      console.error('Erro ao carregar histórico de tentativas:', err);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar o histórico completo',
        variant: 'destructive',
      });
      // Abrir modal mesmo com erro, usando apenas dados disponíveis
      setShowDetailedResults(true);
    }
  };

  // Função para determinar o estado do botão
  const getButtonState = () => {
    if (!quizContent || !userProgress) {
      return {
        canStart: true,
        buttonText: 'Iniciar Quiz',
        disabled: false,
        reason: null
      };
    }

    const currentAttempts = userProgress.totalAttempts || 0;
    const maxAttempts = quizContent.config.maxAttempts;
    const hasCompleted = userProgress.passed || currentAttempts > 0;
    const allowRetry = quizContent.config.allowRetry;

    // Regra 1: maxAttempts = 1 (uma tentativa única) E já respondeu
    if (maxAttempts === 1 && currentAttempts >= 1) {
      return {
        canStart: false,
        buttonText: 'Quiz Finalizado',
        disabled: true,
        reason: 'Tentativa única já utilizada'
      };
    }

    // Regra 2: Esgotou todas as tentativas disponíveis
    if (maxAttempts !== -1 && currentAttempts >= maxAttempts) {
      return {
        canStart: false,
        buttonText: 'Tentativas Esgotadas',
        disabled: true,
        reason: `Todas as ${maxAttempts} tentativas foram utilizadas`
      };
    }

    // Regra 3: allowRetry = false E já completou o quiz (independente da nota)
    if (!allowRetry && hasCompleted) {
      return {
        canStart: false,
        buttonText: 'Quiz Finalizado',
        disabled: true,
        reason: 'Não é permitido refazer este quiz'
      };
    }

    // Pode iniciar/tentar novamente
    const isRetry = currentAttempts > 0;
    return {
      canStart: true,
      buttonText: isRetry ? 'Tentar Novamente' : 'Iniciar Quiz',
      disabled: false,
      reason: null
    };
  };

  // Iniciar quiz
  const handleStartQuiz = async () => {
    if (!user?.id || !quizContent) return;

    const buttonState = getButtonState();
    if (!buttonState.canStart) {
      toast({
        title: 'Não é possível iniciar',
        description: buttonState.reason || 'Quiz não disponível',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsLoading(true);

      let attempt: QuizAttempt;

      if (isLocalMode) {
        console.log('Iniciando quiz em modo local');
        attempt = LocalQuizService.startAttempt(taskId, blockId, user.id, quizContent);
      } else {
        console.log('Iniciando quiz em modo Supabase');
        attempt = await QuizService.startAttempt(taskId, blockId, user.id);
      }

      setCurrentAttempt(attempt);
      setQuizState('executing');
      setCurrentQuestionIndex(0);
      setAnswers({});
      setQuestionTimeSpent({});

      // Salvar tentativa em andamento no localStorage
      const ongoingAttemptKey = `quiz_local_attempt_${taskId}_${blockId}_${user.id}`;
      const attemptData = {
        ...attempt,
        currentQuestionIndex: 0,
        answers: {},
        questionTimeSpent: {}
      };
      localStorage.setItem(ongoingAttemptKey, JSON.stringify(attemptData));
      console.log('💾 Tentativa em andamento salva:', attemptData);

      // Configurar timer se necessário
      if (quizContent.config.timeLimit) {
        setTimeRemaining(quizContent.config.timeLimit);
      }

      toast({
        title: 'Quiz iniciado',
        description: isLocalMode ? 'Modo local - Boa sorte!' : 'Boa sorte!',
      });

    } catch (err) {
      console.error('Erro ao iniciar quiz:', err);
      setError(err instanceof Error ? err.message : 'Erro ao iniciar quiz');
      toast({
        title: 'Erro',
        description: 'Não foi possível iniciar o quiz',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Salvar resposta
  const handleAnswerChange = useCallback(async (questionId: string, answerData: Partial<QuizAnswer>) => {
    if (!currentAttempt) return;

    const question = quizContent?.questions.find(q => q.id === questionId);
    if (!question) return;

    const answer: QuizAnswer = {
      questionId,
      questionType: question.type,
      timeSpent: questionTimeSpent[questionId] || 0,
      // Não definir isCorrect e pointsEarned aqui - será calculado na finalização
      ...answers[questionId],
      ...answerData
    };

    setAnswers(prev => {
      const newAnswers = {
        ...prev,
        [questionId]: answer
      };

      // Salvar tentativa em andamento atualizada
      if (user?.id) {
        const ongoingAttemptKey = `quiz_local_attempt_${taskId}_${blockId}_${user.id}`;
        const attemptData = {
          ...currentAttempt,
          currentQuestionIndex,
          answers: newAnswers,
          questionTimeSpent
        };
        localStorage.setItem(ongoingAttemptKey, JSON.stringify(attemptData));
        console.log('💾 Progresso salvo automaticamente');
      }

      return newAnswers;
    });

    // Salvar automaticamente se configurado
    if (quizContent?.config.allowSaveDraft) {
      try {
        if (isLocalMode) {
          LocalQuizService.saveAnswer(currentAttempt.id, answer);
        } else {
          await QuizService.saveAnswer(currentAttempt.id, answer);
        }
      } catch (err) {
        console.error('Erro ao salvar resposta:', err);
        // Não mostrar erro para o usuário, apenas log
      }
    }
  }, [currentAttempt, quizContent, answers, questionTimeSpent, isLocalMode]);

  // Navegar entre perguntas
  const handleNextQuestion = () => {
    if (quizContent && currentQuestionIndex < quizContent.questions.length - 1) {
      setCurrentQuestionIndex(prev => {
        const newIndex = prev + 1;
        // Salvar progresso ao navegar
        saveOngoingAttempt(newIndex);
        return newIndex;
      });
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => {
        const newIndex = prev - 1;
        // Salvar progresso ao navegar
        saveOngoingAttempt(newIndex);
        return newIndex;
      });
    }
  };

  // Função auxiliar para salvar tentativa em andamento
  const saveOngoingAttempt = (questionIndex?: number) => {
    if (user?.id && currentAttempt) {
      const ongoingAttemptKey = `quiz_local_attempt_${taskId}_${blockId}_${user.id}`;
      const attemptData = {
        ...currentAttempt,
        currentQuestionIndex: questionIndex ?? currentQuestionIndex,
        answers,
        questionTimeSpent
      };
      localStorage.setItem(ongoingAttemptKey, JSON.stringify(attemptData));
      console.log('💾 Progresso salvo na navegação');
    }
  };

  // Finalizar quiz
  const handleSubmitQuiz = async () => {
    if (!currentAttempt || !quizContent) return;

    try {
      setIsSubmitting(true);

      // Calcular tempo total gasto
      const totalTimeSpent = Object.values(questionTimeSpent).reduce((sum, time) => sum + time, 0);

      // Processar respostas com correção (apenas para modo assessment)
      const isAssessmentMode = QuizUtils.isAssessmentMode(quizContent.config);
      const processedAnswers = Object.values(answers).map((answer) => {
        const question = quizContent.questions.find(q => q.id === answer.questionId);
        if (!question) return answer;

        let isCorrect = false;
        let pointsEarned = 0;

        // Aplicar correção automática apenas em modo assessment
        if (isAssessmentMode) {
          // Lógica de correção baseada no tipo
          switch (question.type) {
            case 'single-choice':
              const correctOption = question.options?.find(opt => opt.isCorrect);
              isCorrect = answer.selectedOptions?.[0] === correctOption?.id;
              break;

            case 'multiple-choice':
              const correctOptions = question.options?.filter(opt => opt.isCorrect).map(opt => opt.id) || [];
              const selectedOptions = answer.selectedOptions || [];
              isCorrect = correctOptions.length === selectedOptions.length &&
                         correctOptions.every(id => selectedOptions.includes(id));
              break;

            case 'true-false':
              isCorrect = answer.booleanAnswer === question.correctAnswer;
              break;

            case 'open-text':
              if (question.openTextKeywords && answer.textAnswer) {
                const text = answer.textAnswer.toLowerCase();
                const keywordMatches = question.openTextKeywords.filter(keyword =>
                  text.includes(keyword.toLowerCase())
                );
                isCorrect = keywordMatches.length >= Math.ceil(question.openTextKeywords.length * 0.6);
              }
              break;

            case 'ordering':
              if (question.orderingItems && answer.orderedItems) {
                // Verificar se a ordem está correta
                const correctOrder = question.orderingItems
                  .sort((a, b) => a.correctOrder - b.correctOrder)
                  .map(item => item.id);
                isCorrect = JSON.stringify(answer.orderedItems) === JSON.stringify(correctOrder);
              }
              break;

            case 'matching':
              if (question.matchingPairs && answer.matchedPairs) {
                // Verificar se todas as correspondências estão corretas
                const correctMatches = question.matchingPairs.reduce((acc: any, pair) => {
                  acc[pair.id] = pair.id; // Em matching, o ID do par é a correspondência correta
                  return acc;
                }, {});
                isCorrect = JSON.stringify(answer.matchedPairs) === JSON.stringify(correctMatches);
              }
              break;
          }

          if (isCorrect) {
            pointsEarned = question.points;
          }
        } else {
          // Modo survey: não há correção, apenas coleta de dados
          // Todas as respostas são "válidas" mas não corretas/incorretas
          isCorrect = undefined; // Não aplicável em pesquisas
          pointsEarned = question.points; // Dar pontos por participação
        }

        return {
          ...answer,
          isCorrect,
          pointsEarned,
          feedback: isAssessmentMode
            ? (isCorrect ? question.correctFeedback : question.incorrectFeedback)
            : 'Obrigado pela sua resposta!', // Feedback neutro para pesquisas
          timeSpent: questionTimeSpent[answer.questionId] || 0
        };
      });

      // Calcular resultado final
      const totalScore = processedAnswers.reduce((sum, answer) => sum + answer.pointsEarned, 0);
      const maxScore = quizContent.questions.reduce((sum, q) => sum + q.points, 0);
      const percentage = maxScore > 0 ? (totalScore / maxScore) * 100 : 0;

      // Lógica de aprovação diferente para cada modo
      const passed = isAssessmentMode
        ? percentage >= quizContent.config.passingScore  // Assessment: baseado na nota
        : true; // Survey: sempre "aprovado" (participação completa)

      const finalAttempt = {
        ...currentAttempt,
        submittedAt: new Date(),
        answers: processedAnswers,
        score: totalScore,
        maxScore,
        percentage,
        passed,
        status: 'graded' as const,
        timeSpent: totalTimeSpent
      };

      // MODO HÍBRIDO: Sempre finalizar localmente primeiro, depois tentar salvar no Supabase
      console.log('🔄 Finalizando quiz em modo híbrido (local + Supabase)');

      // 1. Finalizar localmente primeiro (garantido)
      const localFinalAttempt = LocalQuizService.submitAttempt(
        currentAttempt.id,
        processedAnswers,
        totalTimeSpent,
        quizContent
      );
      setCurrentAttempt(localFinalAttempt);
      console.log('✅ Quiz finalizado localmente');

      // 2. Tentar salvar no Supabase (melhor esforço)
      try {
        console.log('💾 Tentando salvar resultados no Supabase...');

        // Criar quiz no Supabase se não existir
        const quizId = await ensureQuizExistsInSupabase(taskId, blockId, quizContent);

        if (!quizId) {
          throw new Error('Não foi possível criar/encontrar quiz no Supabase');
        }

        // Salvar tentativa no Supabase
        const supabaseAttempt = await QuizService.createAttempt(
          quizId,
          user.id,
          localFinalAttempt
        );

        if (supabaseAttempt) {
          // Salvar respostas no Supabase
          for (const processedAnswer of processedAnswers) {
            await QuizService.saveAnswer(supabaseAttempt.id, processedAnswer);
          }

          // Finalizar tentativa no Supabase
          await QuizService.submitAttempt(
            supabaseAttempt.id,
            processedAnswers,
            totalTimeSpent
          );
        }

        console.log('✅ Resultados salvos no Supabase com sucesso');

        toast({
          title: 'Dados salvos',
          description: 'Resultados salvos no servidor com sucesso',
          variant: 'default',
        });

      } catch (supabaseError) {
        console.warn('⚠️ Erro ao salvar no Supabase (dados mantidos localmente):', supabaseError);

        toast({
          title: 'Aviso',
          description: 'Quiz finalizado. Dados salvos localmente (servidor indisponível)',
          variant: 'default',
        });
      }

      setQuizState('completed');

      // Atualizar userProgress no estado local para refletir a nova tentativa
      try {
        let updatedProgress: UserQuizProgress | null = null;

        if (isLocalMode) {
          updatedProgress = LocalQuizService.getUserProgress(taskId, blockId, user.id);
        } else {
          updatedProgress = await QuizService.getUserProgress(taskId, blockId, user.id);
        }

        if (updatedProgress) {
          setUserProgress(updatedProgress);
          console.log('✅ UserProgress atualizado após finalizar quiz:', updatedProgress);
        }

        // Atualizar currentAttempt com a tentativa oficial (melhor resultado)
        try {
          let allAttempts: QuizAttempt[] = [];

          if (isLocalMode) {
            allAttempts = LocalQuizService.getUserAttempts(taskId, blockId, user.id);
          } else {
            allAttempts = await QuizService.getUserAttempts(taskId, blockId, user.id);
          }

          const officialAttempt = getOfficialAttempt(allAttempts, updatedProgress);
          if (officialAttempt) {
            setCurrentAttempt(officialAttempt);
            console.log('✅ CurrentAttempt atualizado com tentativa oficial após finalizar:', {
              attemptNumber: officialAttempt.attemptNumber,
              percentage: officialAttempt.percentage,
              passed: officialAttempt.passed,
              isOfficial: true
            });
          }
        } catch (err) {
          console.warn('⚠️ Erro ao atualizar currentAttempt com tentativa oficial:', err);
        }

      } catch (err) {
        console.warn('⚠️ Erro ao atualizar userProgress:', err);
      }

      // Limpar tentativa em andamento
      const ongoingAttemptKey = `quiz_local_attempt_${taskId}_${blockId}_${user.id}`;
      localStorage.removeItem(ongoingAttemptKey);
      console.log('🗑️ Tentativa em andamento removida - Quiz finalizado');

      // Verificar se o quiz é obrigatório e se deve bloquear progresso
      // Pesquisas nunca bloqueiam progresso (sempre consideradas "completas")
      const shouldBlockProgress = isAssessmentMode &&
                                  quizContent.config.isRequired &&
                                  quizContent.config.blockProgressUntilPassed &&
                                  !passed;

      // Notificar componente pai
      if (onComplete) {
        onComplete(passed, totalScore);
      }

      // Se deve bloquear progresso, mostrar aviso (apenas para assessments)
      if (shouldBlockProgress && isAssessmentMode) {
        toast({
          title: 'Quiz Obrigatório',
          description: `Você precisa atingir pelo menos ${quizContent.config.passingScore}% para continuar.`,
          variant: 'destructive',
          duration: 5000
        });
      }

      // Mensagens diferentes para cada modo
      if (isAssessmentMode) {
        toast({
          title: passed ? 'Parabéns!' : 'Quiz finalizado',
          description: passed
            ? `Você foi aprovado com ${percentage.toFixed(1)}%!`
            : `Você obteve ${percentage.toFixed(1)}%. Nota mínima: ${quizContent.config.passingScore}%`,
          variant: passed ? 'default' : 'destructive',
        });
      } else {
        // Modo survey: mensagem de agradecimento
        toast({
          title: 'Pesquisa finalizada!',
          description: 'Obrigado por participar da nossa pesquisa. Suas respostas foram registradas com sucesso.',
          variant: 'default',
        });
      }

    } catch (err) {
      console.error('Erro ao finalizar quiz:', err);
      setError(err instanceof Error ? err.message : 'Erro ao finalizar quiz');
      toast({
        title: 'Erro',
        description: 'Não foi possível finalizar o quiz',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Tempo esgotado
  const handleTimeUp = () => {
    toast({
      title: 'Tempo esgotado',
      description: 'O quiz será finalizado automaticamente',
      variant: 'destructive',
    });
    handleSubmitQuiz();
  };

  // Renderizar pergunta atual
  const renderCurrentQuestion = () => {
    if (!quizContent || !quizContent.questions[currentQuestionIndex]) {
      console.log('Não há pergunta para renderizar:', { quizContent, currentQuestionIndex });
      return null;
    }

    const question = quizContent.questions[currentQuestionIndex];
    const currentAnswer = answers[question.id];

    console.log('Renderizando pergunta:', { question, currentAnswer, questionType: question.type });

    switch (question.type) {
      case 'single-choice':
        return (
          <RadioGroup
            value={currentAnswer?.selectedOptions?.[0] || ''}
            onValueChange={(value) => handleAnswerChange(question.id, { selectedOptions: [value] })}
          >
            {question.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <RadioGroupItem value={option.id} id={option.id} />
                <Label htmlFor={option.id} className="text-sm cursor-pointer">
                  {option.text}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'multiple-choice':
        return (
          <div className="space-y-2">
            {question.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={currentAnswer?.selectedOptions?.includes(option.id) || false}
                  onCheckedChange={(checked) => {
                    const currentSelected = currentAnswer?.selectedOptions || [];
                    const newSelected = checked
                      ? [...currentSelected, option.id]
                      : currentSelected.filter(id => id !== option.id);
                    handleAnswerChange(question.id, { selectedOptions: newSelected });
                  }}
                />
                <Label htmlFor={option.id} className="text-sm cursor-pointer">
                  {option.text}
                </Label>
              </div>
            ))}
          </div>
        );

      case 'true-false':
        return (
          <RadioGroup
            value={currentAnswer?.booleanAnswer?.toString() || ''}
            onValueChange={(value) => handleAnswerChange(question.id, { booleanAnswer: value === 'true' })}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="true" id="true" />
              <Label htmlFor="true" className="text-sm cursor-pointer">
                Verdadeiro
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="false" id="false" />
              <Label htmlFor="false" className="text-sm cursor-pointer">
                Falso
              </Label>
            </div>
          </RadioGroup>
        );

      case 'open-text':
        return (
          <Textarea
            value={currentAnswer?.textAnswer || ''}
            onChange={(e) => handleAnswerChange(question.id, { textAnswer: e.target.value })}
            placeholder="Digite sua resposta..."
            className="min-h-[100px]"
          />
        );

      case 'ordering':
        const currentOrderedItems = currentAnswer?.orderedItems ||
          question.orderingItems?.map(item => item.id) || [];

        const handleOrderingDragEnd = (event: any) => {
          const { active, over } = event;
          if (active.id !== over?.id) {
            const oldIndex = currentOrderedItems.indexOf(active.id);
            const newIndex = currentOrderedItems.indexOf(over.id);

            if (oldIndex !== -1 && newIndex !== -1) {
              const newOrder = [...currentOrderedItems];
              const [removed] = newOrder.splice(oldIndex, 1);
              newOrder.splice(newIndex, 0, removed);

              handleAnswerChange(question.id, { orderedItems: newOrder });
            }
          }
        };

        return (
          <div className="space-y-2">
            <p className="text-sm text-gray-600 mb-3">
              Arraste os itens para ordená-los corretamente:
            </p>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleOrderingDragEnd}
            >
              <SortableContext
                items={currentOrderedItems}
                strategy={verticalListSortingStrategy}
              >
                <div className="space-y-2">
                  {currentOrderedItems.map((itemId, index) => {
                    const item = question.orderingItems?.find(i => i.id === itemId);
                    if (!item) return null;

                    return (
                      <SortableOrderingItem
                        key={item.id}
                        item={item}
                        index={index}
                      />
                    );
                  })}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        );

      case 'matching':
        const currentMatches = currentAnswer?.matchedPairs || {};

        const handleMatchingSelect = (leftId: string, rightId: string) => {
          const newMatches = { ...currentMatches };

          // Remove qualquer correspondência anterior para este item da esquerda
          delete newMatches[leftId];

          // Remove qualquer correspondência anterior para este item da direita
          Object.keys(newMatches).forEach(key => {
            if (newMatches[key] === rightId) {
              delete newMatches[key];
            }
          });

          // Adiciona a nova correspondência
          newMatches[leftId] = rightId;

          handleAnswerChange(question.id, { matchedPairs: newMatches });
        };



        return (
          <div className="space-y-3">
            <p className="text-sm text-gray-600 mb-3">
              Clique em um item da Coluna A e depois em um item da Coluna B para conectá-los:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Coluna A</h4>
                {question.matchingPairs?.map((pair) => (
                  <MatchingItem
                    key={`left-${pair.id}`}
                    id={pair.id}
                    text={pair.left}
                    side="left"
                    isSelected={selectedMatchingItem?.id === pair.id && selectedMatchingItem?.side === 'left'}
                    isMatched={!!currentMatches[pair.id]}
                    matchedWith={currentMatches[pair.id]}
                    onSelect={() => setSelectedMatchingItem({ id: pair.id, side: 'left' })}
                    question={question}
                  />
                ))}
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Coluna B</h4>
                {shuffledRightItems.map((item) => (
                  <MatchingItem
                    key={`right-${item.id}`}
                    id={item.id}
                    text={item.text}
                    side="right"
                    isSelected={selectedMatchingItem?.id === item.id && selectedMatchingItem?.side === 'right'}
                    isMatched={Object.values(currentMatches).includes(item.id)}
                    matchedWith={Object.keys(currentMatches).find(key => currentMatches[key] === item.id)}
                    onSelect={() => {
                      if (selectedMatchingItem?.side === 'left') {
                        handleMatchingSelect(selectedMatchingItem.id, item.id);
                        setSelectedMatchingItem(null);
                      } else {
                        setSelectedMatchingItem({ id: item.id, side: 'right' });
                      }
                    }}
                    question={question}
                  />
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>Tipo de pergunta não suportado</p>
          </div>
        );
    }
  };









  // Estado: Pronto para iniciar
  if (quizState === 'ready') {
    console.log('🎯 Quiz pronto para iniciar:', {
      quizContent,
      questionsLength: quizContent?.questions?.length,
      isLocalMode,
      quizState
    });
    return (
      <BlockCard
        className={`flex flex-col gap-1 p-4 ${className || ''}`}
        style={cardStyles}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <BlockCardIcon
          config={safeConfig?.icon}
          title={quizContent?.config?.title || 'Quiz'}
          textColor={cardTextColor}
          content={
            <div className="space-y-4">
              {quizContent.config.description && (
                <p className="text-sm text-gray-600">{quizContent.config.description}</p>
              )}

              {quizContent.config.instructions && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <h4 className="font-medium text-blue-900 mb-1">Instruções</h4>
                  <p className="text-sm text-blue-800">{quizContent.config.instructions}</p>
                </div>
              )}

              {quizContent.config.isRequired && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                  <h4 className="font-medium text-orange-900 mb-1">⚠️ Quiz Obrigatório</h4>
                  <p className="text-sm text-orange-800">
                    {quizContent.config.blockProgressUntilPassed
                      ? `Você deve atingir pelo menos ${quizContent.config.passingScore}% para continuar.`
                      : 'Este quiz deve ser completado, mas você pode continuar independente da nota.'
                    }
                  </p>
                </div>
              )}

              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                <div className="text-center">
                  <div className="font-semibold">{quizContent.questions.length}</div>
                  <div className="text-gray-500">Perguntas</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold">{quizContent.questions.reduce((sum, q) => sum + q.points, 0)}</div>
                  <div className="text-gray-500">Pontos</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold">{quizContent.config.passingScore}%</div>
                  <div className="text-gray-500">Nota Mín.</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold">
                    {quizContent.config.maxAttempts === -1 ? '∞' : quizContent.config.maxAttempts}
                  </div>
                  <div className="text-gray-500">Tentativas</div>
                </div>
              </div>

              {userProgress && (
                <div className="bg-gray-50 rounded-lg p-3">
                  <h4 className="font-medium mb-2">Seu Progresso</h4>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-gray-600">
                        {QuizUtils.isSurveyMode(quizContent.config) ? 'Participações: ' : 'Tentativas: '}
                      </span>
                      <span className="font-medium">{userProgress.totalAttempts}</span>
                    </div>
                    {QuizUtils.isAssessmentMode(quizContent.config) && (
                      <div>
                        <span className="text-gray-600">Melhor nota: </span>
                        <span className="font-medium">{userProgress.bestPercentage.toFixed(1)}%</span>
                      </div>
                    )}
                    {QuizUtils.isSurveyMode(quizContent.config) && (
                      <div>
                        <span className="text-gray-600">Status: </span>
                        <span className="font-medium text-blue-600">Participou</span>
                      </div>
                    )}
                  </div>
                  {QuizUtils.isAssessmentMode(quizContent.config) && userProgress.passed && (
                    <div className="flex items-center gap-1 mt-2 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Aprovado</span>
                    </div>
                  )}
                  {QuizUtils.isSurveyMode(quizContent.config) && userProgress.totalAttempts > 0 && (
                    <div className="flex items-center gap-1 mt-2 text-blue-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Pesquisa Concluída</span>
                    </div>
                  )}
                </div>
              )}

              {(() => {
                const buttonState = getButtonState();
                const currentAttempts = userProgress?.totalAttempts || 0;
                const maxAttempts = quizContent.config.maxAttempts;
                const remainingAttempts = maxAttempts === -1 ? Infinity : maxAttempts - currentAttempts;

                return (
                  <>
                    {/* Mostrar informações de tentativas restantes (apenas quando pode tentar) */}
                    {buttonState.canStart && maxAttempts !== -1 && currentAttempts > 0 && remainingAttempts > 0 && (
                      <div className="text-sm text-gray-600 text-center mb-2">
                        {remainingAttempts} tentativa{remainingAttempts !== 1 ? 's' : ''} restante{remainingAttempts !== 1 ? 's' : ''}
                      </div>
                    )}

                    {/* Mostrar razão quando desabilitado */}
                    {buttonState.disabled && buttonState.reason && (
                      <div className="text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded p-2 text-center mb-2">
                        {buttonState.reason}
                      </div>
                    )}

                    <Button
                      onClick={handleStartQuiz}
                      className="w-full"
                      disabled={isLoading || buttonState.disabled}
                      variant={buttonState.disabled ? "secondary" : "default"}
                    >
                      {buttonState.disabled ? (
                        <X className="w-4 h-4 mr-2" />
                      ) : (
                        <Play className="w-4 h-4 mr-2" />
                      )}
                      {buttonState.buttonText}
                    </Button>
                  </>
                );
              })()}

              {isLocalMode && (
                <div className="mt-2 text-xs text-blue-600 bg-blue-50 border border-blue-200 rounded p-2">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Modo local ativo - Dados salvos no navegador</span>
                  </div>
                </div>
              )}
            </div>
          }
        />
      </BlockCard>
    );
  }

  // Estado: Executando quiz
  if (quizState === 'executing' && quizContent && currentAttempt) {
    console.log('Executando quiz:', {
      currentQuestionIndex,
      questionsLength: quizContent.questions.length,
      currentQuestion: quizContent.questions[currentQuestionIndex]
    });

    const currentQuestion = quizContent.questions[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / quizContent.questions.length) * 100;
    const canGoNext = currentQuestionIndex < quizContent.questions.length - 1;
    const canGoPrev = currentQuestionIndex > 0;
    const isLastQuestion = currentQuestionIndex === quizContent.questions.length - 1;

    return (
      <div className={`w-full space-y-4 ${className || ''}`}>
        {/* Header do Quiz */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">{quizContent.config.title}</CardTitle>
                <p className="text-sm text-gray-600">
                  Tentativa {currentAttempt.attemptNumber} de {quizContent.config.maxAttempts === -1 ? '∞' : quizContent.config.maxAttempts}
                </p>
              </div>
              {quizContent.config.showTimer && timeRemaining !== null && (
                <div className="flex items-center gap-2 text-sm">
                  <Timer className="w-4 h-4" />
                  <span className={timeRemaining < 60 ? 'text-red-600 font-bold' : ''}>
                    {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}
                  </span>
                </div>
              )}
            </div>
          </CardHeader>

          {quizContent.config.showProgressBar && (
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progresso</span>
                  <span>{currentQuestionIndex + 1} de {quizContent.questions.length}</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            </CardContent>
          )}
        </Card>

        {/* Pergunta Atual */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">
                    Pergunta {currentQuestionIndex + 1}
                  </Badge>
                  <Badge variant="secondary">
                    {currentQuestion.points} {currentQuestion.points === 1 ? 'ponto' : 'pontos'}
                  </Badge>
                  {currentQuestion.required && (
                    <Badge variant="destructive">
                      Obrigatória
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg">{currentQuestion.title}</CardTitle>
                {currentQuestion.description && (
                  <p className="text-gray-600 mt-2">{currentQuestion.description}</p>
                )}
              </div>
              {currentQuestion.timeLimit && (
                <div className="flex items-center gap-1 text-sm">
                  <Clock className="w-4 h-4" />
                  <span className={questionTimeRemaining !== null && questionTimeRemaining < 10 ? 'text-red-600 font-bold' : 'text-gray-500'}>
                    {questionTimeRemaining !== null
                      ? `${Math.floor(questionTimeRemaining / 60)}:${(questionTimeRemaining % 60).toString().padStart(2, '0')}`
                      : `${currentQuestion.timeLimit}s`
                    }
                  </span>
                </div>
              )}
            </div>
          </CardHeader>

          <CardContent>
            {renderCurrentQuestion()}
          </CardContent>
        </Card>

        {/* Navegação */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={handlePreviousQuestion}
                disabled={!canGoPrev}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Anterior
              </Button>

              <div className="flex gap-2">
                {quizContent.config.allowSaveDraft && !isLastQuestion && (
                  <Button variant="outline" disabled>
                    <Save className="w-4 h-4 mr-2" />
                    Salvo
                  </Button>
                )}

                {isLastQuestion ? (
                  <Button
                    onClick={handleSubmitQuiz}
                    disabled={isSubmitting}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Finalizando...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Finalizar Quiz
                      </>
                    )}
                  </Button>
                ) : (
                  <Button
                    onClick={handleNextQuestion}
                    disabled={!canGoNext}
                  >
                    Próxima
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Estado: Quiz completado (VERSÃO ANTIGA - COMENTADA)
  if (false && quizState === 'completed' && quizContent) {
    // Se não há currentAttempt mas há userProgress, criar um attempt fictício para exibição
    const displayAttempt = currentAttempt || (userProgress ? {
      id: 'display-attempt',
      userId: user?.id || '',
      taskId,
      blockId,
      attemptNumber: userProgress.totalAttempts || 1,
      startedAt: new Date(),
      completedAt: new Date(),
      answers: {},
      score: userProgress.bestScore || 0,
      totalScore: quizContent.questions.reduce((sum, q) => sum + (q.points || 1), 0),
      percentage: userProgress.bestPercentage || 0, // Usar bestPercentage em vez de bestScore
      passed: userProgress.passed || false,
      timeSpent: 0
    } as QuizAttempt : null);

    if (displayAttempt) {
    return (
      <BlockCard
        className={`flex flex-col gap-1 p-4 ${className || ''}`}
        style={cardStyles}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <BlockCardIcon
          config={safeConfig?.icon}
          title={quizContent.config.title}
          textColor={cardTextColor}
          content={
            <div className="space-y-4">
              <div className="text-center space-y-4">
                {displayAttempt.passed ? (
                  <Trophy className="w-12 h-12 text-yellow-500 mx-auto" />
                ) : (
                  <Target className="w-12 h-12 text-gray-400 mx-auto" />
                )}

                <div>
                  <h3 className="text-lg font-semibold">
                    {displayAttempt.passed ? 'Parabéns! Você foi aprovado!' : 'Quiz Finalizado'}
                  </h3>
                  <p className="text-gray-600">
                    {displayAttempt.passed
                      ? 'Você atingiu a nota mínima necessária.'
                      : 'Você não atingiu a nota mínima desta vez.'}
                  </p>
                </div>

                {quizContent.config.showScore && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold">{displayAttempt.percentage.toFixed(1)}%</div>
                        <div className="text-sm text-gray-600">Sua Nota</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{quizContent.config.passingScore}%</div>
                        <div className="text-sm text-gray-600">Nota Mínima</div>
                      </div>
                    </div>
                    <div className="mt-3">
                      <div className="text-sm text-gray-600 mb-1">
                        {displayAttempt.score} de {displayAttempt.totalScore} pontos
                      </div>
                      <Progress
                        value={displayAttempt.percentage}
                        className="w-full"
                      />
                    </div>
                  </div>
                )}

                {/* Mostrar progresso do usuário */}
                {userProgress && (
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-lg font-semibold">
                          {QuizUtils.isSurveyMode(quizContent.config) ? 'Participações' : 'Tentativas'}: {userProgress.totalAttempts}
                        </div>
                        {QuizUtils.isAssessmentMode(quizContent.config) && (
                          <div className="text-sm text-gray-600">
                            Melhor nota: {userProgress.bestScore?.toFixed(1) || 0}%
                          </div>
                        )}
                        {QuizUtils.isSurveyMode(quizContent.config) && (
                          <div className="text-sm text-gray-600">
                            Obrigado pela participação
                          </div>
                        )}
                      </div>
                      <div>
                        {QuizUtils.isAssessmentMode(quizContent.config) && (
                          <div className={`text-lg font-semibold ${userProgress.passed ? 'text-green-600' : 'text-gray-600'}`}>
                            {userProgress.passed ? '✅ Aprovado' : '❌ Não Aprovado'}
                          </div>
                        )}
                        {QuizUtils.isSurveyMode(quizContent.config) && (
                          <div className="text-lg font-semibold text-blue-600">
                            ✅ Concluída
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Botão Ver Resultado Detalhado */}
                {quizContent.config.showDetailedResults && (
                  <div className="text-center">
                    <Button
                      variant="outline"
                      onClick={handleOpenDetailedResults}
                      className="w-full"
                    >
                      <BarChart3 className="w-4 h-4 mr-2" />
                      Ver Resultado Detalhado
                    </Button>
                  </div>
                )}



                {(() => {
                  const buttonState = getButtonState();

                  // Se pode tentar novamente (passou todas as validações)
                  if (buttonState.canStart) {
                    return (
                      <Button onClick={handleStartQuiz} variant="outline" className="w-full">
                        <RotateCcw className="w-4 h-4 mr-2" />
                        {buttonState.buttonText}
                      </Button>
                    );
                  }

                  // Se não pode mais tentar, mostrar botão desabilitado
                  if (!buttonState.canStart) {
                    return (
                      <div className="space-y-2">
                        <div className="text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded p-2 text-center">
                          {buttonState.reason}
                        </div>
                        <Button variant="secondary" className="w-full" disabled>
                          <X className="w-4 h-4 mr-2" />
                          {buttonState.buttonText}
                        </Button>
                      </div>
                    );
                  }

                  return null;
                })()}
              </div>
            </div>
          }
        />
      </BlockCard>
    );
    }
  }

  return (
    <>
      {/* Renderização principal do quiz */}
      {(() => {
        // Estado: Usuário não autenticado
        if (!user?.id) {
          return (
            <BlockCard
              className={`flex flex-col gap-1 p-4 ${className || ''}`}
              style={cardStyles}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <BlockCardIcon
                config={safeConfig?.icon}
                title="Quiz"
                textColor={cardTextColor}
                content={
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Você precisa estar logado para acessar este quiz.
                      <Button
                        variant="link"
                        className="p-0 h-auto ml-1 text-red-600 underline"
                        onClick={() => {
                          // Salvar a URL atual para redirecionamento após login
                          localStorage.setItem('redirectAfterLogin', window.location.pathname);
                          window.location.href = '/login';
                        }}
                      >
                        Fazer login
                      </Button>
                    </AlertDescription>
                  </Alert>
                }
              />
            </BlockCard>
          );
        }

        // Estado: Carregando
        if (isLoading) {
          return (
            <BlockCard
              className={`flex flex-col gap-1 p-4 ${className || ''}`}
              style={cardStyles}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <BlockCardIcon
                config={safeConfig?.icon}
                title="Quiz"
                textColor={cardTextColor}
                content={
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Carregando quiz...</p>
                  </div>
                }
              />
            </BlockCard>
          );
        }

        // Estado: Erro ou bloqueado
        if (error || quizState === 'blocked') {
          return (
            <BlockCard
              className={`flex flex-col gap-1 p-4 ${className || ''}`}
              style={cardStyles}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <BlockCardIcon
                config={safeConfig?.icon}
                title={quizContent?.config.title || 'Quiz'}
                textColor={cardTextColor}
                content={
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                }
              />
            </BlockCard>
          );
        }

        // Preview mode (apenas se realmente não há conteúdo)
        if (!quizContent) {
          return (
            <BlockCard
              className={`flex flex-col gap-1 p-4 ${className || ''}`}
              style={cardStyles}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <BlockCardIcon
                config={safeConfig?.icon}
                title="Quiz"
                textColor={cardTextColor}
                content={
                  <div className="text-center py-4 text-gray-500">
                    <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                    <p>Carregando quiz...</p>
                  </div>
                }
              />
            </BlockCard>
          );
        }

        // Se não há perguntas válidas, mostrar erro
        if (!quizContent.questions || quizContent.questions.length === 0) {
          return (
            <BlockCard
              className={`flex flex-col gap-1 p-4 ${className || ''}`}
              style={cardStyles}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <BlockCardIcon
                config={safeConfig?.icon}
                title={quizContent.config?.title || "Quiz"}
                textColor={cardTextColor}
                content={
                  <div className="text-center py-4 text-red-500">
                    <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                    <p>Quiz não encontrado</p>
                    <p className="text-xs mt-1">Nenhuma pergunta válida encontrada</p>
                  </div>
                }
              />
            </BlockCard>
          );
        }

        // Estado: Pronto para iniciar
        if (quizState === 'ready') {
          console.log('🎯 Quiz pronto para iniciar:', {
            quizContent,
            questionsLength: quizContent?.questions?.length,
            isLocalMode,
            quizState
          });
          return (
            <BlockCard
              className={`flex flex-col gap-1 p-4 ${className || ''}`}
              style={cardStyles}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <BlockCardIcon
                config={safeConfig?.icon}
                title={quizContent.config.title}
                textColor={cardTextColor}
                content={
                  <div className="space-y-4">
                    {/* Instruções do Quiz */}
                    {quizContent.config.instructions && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-semibold text-blue-900 mb-2">Instruções</h4>
                        <p className="text-blue-800 text-sm">{quizContent.config.instructions}</p>
                      </div>
                    )}

                    {/* Informações do Quiz */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-blue-600">{quizContent.questions.length}</div>
                        <div className="text-sm text-gray-600">Perguntas</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">
                          {quizContent.questions.reduce((sum, q) => sum + (q.points || 1), 0)}
                        </div>
                        <div className="text-sm text-gray-600">Pontos</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-orange-600">{quizContent.config.passingScore}%</div>
                        <div className="text-sm text-gray-600">Nota Mín.</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-purple-600">
                          {quizContent.config.maxAttempts === -1 ? '∞' : quizContent.config.maxAttempts}
                        </div>
                        <div className="text-sm text-gray-600">Tentativas</div>
                      </div>
                    </div>

                    {/* Progresso do usuário se houver */}
                    {userProgress && userProgress.totalAttempts > 0 && (
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-900 mb-2">Seu Progresso</h4>
                        <div className="grid grid-cols-2 gap-4 text-center">
                          <div>
                            <div className="text-lg font-semibold">
                              {QuizUtils.isSurveyMode(quizContent.config) ? 'Participações' : 'Tentativas'}: {userProgress.totalAttempts}
                            </div>
                            {QuizUtils.isAssessmentMode(quizContent.config) && (
                              <div className="text-sm text-gray-600">
                                Melhor nota: {userProgress.bestPercentage?.toFixed(1) || 0}%
                              </div>
                            )}
                            {QuizUtils.isSurveyMode(quizContent.config) && (
                              <div className="text-sm text-gray-600">
                                Última participação
                              </div>
                            )}
                          </div>
                          <div>
                            {QuizUtils.isAssessmentMode(quizContent.config) && (
                              <div className={`text-lg font-semibold ${userProgress.passed ? 'text-green-600' : 'text-gray-600'}`}>
                                {userProgress.passed ? '✅ Aprovado' : '❌ Não Aprovado'}
                              </div>
                            )}
                            {QuizUtils.isSurveyMode(quizContent.config) && (
                              <div className="text-lg font-semibold text-blue-600">
                                ✅ Participou
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {(() => {
                      const buttonState = getButtonState();
                      const currentAttempts = userProgress?.totalAttempts || 0;
                      const maxAttempts = quizContent.config.maxAttempts;
                      const remainingAttempts = maxAttempts === -1 ? Infinity : maxAttempts - currentAttempts;

                      return (
                        <>
                          {/* Mostrar informações de tentativas restantes (apenas quando pode tentar) */}
                          {buttonState.canStart && maxAttempts !== -1 && currentAttempts > 0 && remainingAttempts > 0 && (
                            <div className="text-sm text-gray-600 text-center mb-2">
                              {remainingAttempts} tentativa{remainingAttempts !== 1 ? 's' : ''} restante{remainingAttempts !== 1 ? 's' : ''}
                            </div>
                          )}

                          {/* Mostrar razão quando desabilitado */}
                          {buttonState.disabled && buttonState.reason && (
                            <div className="text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded p-2 text-center mb-2">
                              {buttonState.reason}
                            </div>
                          )}

                          <Button
                            onClick={handleStartQuiz}
                            className="w-full"
                            disabled={buttonState.disabled}
                            size="lg"
                          >
                            <Play className="w-4 h-4 mr-2" />
                            {buttonState.buttonText}
                          </Button>

                          {/* Modo local ativo */}
                          {isLocalMode && (
                            <div className="text-center">
                              <div className="inline-flex items-center gap-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                                Modo local ativo - Dados salvos no navegador
                              </div>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                }
              />
            </BlockCard>
          );
        }

        // Estado: Quiz completado
        console.log('🔍 DEBUG - Estado do quiz:', {
          quizState,
          hasQuizContent: !!quizContent,
          currentAttempt: currentAttempt ? {
            id: currentAttempt.id,
            attemptNumber: currentAttempt.attemptNumber,
            percentage: currentAttempt.percentage,
            passed: currentAttempt.passed
          } : null,
          userProgress: userProgress ? {
            totalAttempts: userProgress.totalAttempts,
            bestPercentage: userProgress.bestPercentage,
            passed: userProgress.passed
          } : null,
          allAttemptsCount: allAttempts.length,
          allAttempts: allAttempts.map(a => ({
            id: a.id,
            attemptNumber: a.attemptNumber,
            percentage: a.percentage,
            passed: a.passed
          }))
        });

        if (quizState === 'completed' && quizContent) {
          // Função para determinar a melhor tentativa (resultado oficial)
          const getBestAttempt = (attempts: QuizAttempt[]): QuizAttempt | null => {
            if (!attempts || attempts.length === 0) return null;

            return attempts.reduce((best, current) => {
              if (!best) return current;

              // Priorizar por aprovação primeiro
              if (current.passed && !best.passed) return current;
              if (!current.passed && best.passed) return best;

              // Se ambos aprovados ou ambos reprovados, priorizar por porcentagem
              if (current.percentage > best.percentage) return current;
              if (current.percentage < best.percentage) return best;

              // Se porcentagens iguais, priorizar tentativa mais recente
              return current.attemptNumber > best.attemptNumber ? current : best;
            });
          };

          // Determinar qual attempt usar para exibição - SEMPRE usar a melhor tentativa
          let displayAttempt = allAttempts.length > 0 ? getBestAttempt(allAttempts) : currentAttempt;

          // Se não há currentAttempt mas há userProgress, criar um attempt baseado nos melhores dados
          if (!displayAttempt && userProgress) {
            const totalScore = quizContent.questions.reduce((sum, q) => sum + (q.points || 1), 0);

            displayAttempt = {
              id: 'display-attempt-best',
              userId: user?.id || '',
              quizId: `${taskId}:${blockId}`,
              attemptNumber: userProgress.totalAttempts || 1,
              startedAt: new Date(),
              completedAt: new Date(),
              submittedAt: new Date(),
              answers: [],
              score: userProgress.bestScore || 0,
              maxScore: totalScore,
              totalScore: totalScore,
              percentage: userProgress.bestPercentage || 0,
              passed: userProgress.passed || false,
              timeSpent: 0,
              status: 'graded' as const
            } as QuizAttempt;

            console.log('📊 DisplayAttempt criado baseado no userProgress:', {
              bestScore: userProgress.bestScore,
              bestPercentage: userProgress.bestPercentage,
              passed: userProgress.passed,
              totalAttempts: userProgress.totalAttempts
            });
          } else if (displayAttempt) {
            console.log('📊 DisplayAttempt usando melhor tentativa:', {
              attemptNumber: displayAttempt.attemptNumber,
              percentage: displayAttempt.percentage,
              passed: displayAttempt.passed,
              score: displayAttempt.score,
              totalAttempts: allAttempts.length
            });
          }

          if (displayAttempt) {
            return (
              <BlockCard
                className={`flex flex-col gap-1 p-4 ${className || ''}`}
                style={cardStyles}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <BlockCardIcon
                  config={safeConfig?.icon}
                  title={quizContent.config.title}
                  textColor={cardTextColor}
                  content={
                    <div className="space-y-4">
                      <div className="text-center space-y-4">
                        {QuizUtils.isSurveyMode(quizContent.config) ? (
                          <Trophy className="w-12 h-12 text-blue-500 mx-auto" />
                        ) : (
                          displayAttempt.passed ? (
                            <Trophy className="w-12 h-12 text-yellow-500 mx-auto" />
                          ) : (
                            <Target className="w-12 h-12 text-gray-400 mx-auto" />
                          )
                        )}

                        <div>
                          <h3 className="text-lg font-semibold">
                            {QuizUtils.isSurveyMode(quizContent.config)
                              ? 'Pesquisa Finalizada!'
                              : (displayAttempt.passed ? 'Parabéns! Você foi aprovado!' : 'Quiz Finalizado')
                            }
                          </h3>
                          <p className="text-gray-600">
                            {QuizUtils.isSurveyMode(quizContent.config)
                              ? 'Obrigado por participar da nossa pesquisa. Suas respostas foram registradas com sucesso.'
                              : (displayAttempt.passed
                                ? 'Você atingiu a nota mínima necessária.'
                                : 'Você não atingiu a nota mínima desta vez.')
                            }
                          </p>
                        </div>

                        {quizContent.config.showScore && QuizUtils.isAssessmentMode(quizContent.config) && (
                          <div className="bg-gray-50 rounded-lg p-4">
                            <div className="grid grid-cols-2 gap-4 text-center">
                              <div>
                                <div className="text-2xl font-bold">{displayAttempt.percentage.toFixed(1)}%</div>
                                <div className="text-sm text-gray-600">Sua Nota</div>
                              </div>
                              <div>
                                <div className="text-2xl font-bold">{quizContent.config.passingScore}%</div>
                                <div className="text-sm text-gray-600">Nota Mínima</div>
                              </div>
                            </div>
                            <div className="mt-3">
                              <div className="text-sm text-gray-600 mb-1">
                                {displayAttempt.score} de {displayAttempt.totalScore} pontos
                              </div>
                              <Progress
                                value={displayAttempt.percentage}
                                className="w-full"
                              />
                            </div>
                          </div>
                        )}

                        {/* Informações específicas para pesquisas */}
                        {QuizUtils.isSurveyMode(quizContent.config) && (
                          <div className="bg-blue-50 rounded-lg p-4">
                            <div className="grid grid-cols-2 gap-4 text-center">
                              <div>
                                <div className="text-2xl font-bold text-blue-600">
                                  {quizContent.questions.length}
                                </div>
                                <div className="text-sm text-gray-600">Perguntas Respondidas</div>
                              </div>
                              <div>
                                <div className="text-2xl font-bold text-green-600">
                                  ✓
                                </div>
                                <div className="text-sm text-gray-600">Participação Completa</div>
                              </div>
                            </div>
                            <div className="mt-3 text-center">
                              <div className="text-sm text-blue-700">
                                Suas respostas contribuem para nossa pesquisa
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Mostrar progresso do usuário */}
                        {userProgress && (
                          <div className={`${QuizUtils.isSurveyMode(quizContent.config) ? 'bg-blue-50' : 'bg-gray-50'} rounded-lg p-4`}>
                            <div className="grid grid-cols-2 gap-4 text-center">
                              <div>
                                <div className="text-lg font-semibold">
                                  {QuizUtils.isSurveyMode(quizContent.config) ? 'Participações' : 'Tentativas'}: {userProgress.totalAttempts}
                                </div>
                                {QuizUtils.isAssessmentMode(quizContent.config) && (
                                  <div className="text-sm text-gray-600">
                                    Melhor nota: {userProgress.bestPercentage?.toFixed(1) || 0}%
                                  </div>
                                )}
                                {QuizUtils.isSurveyMode(quizContent.config) && (
                                  <div className="text-sm text-gray-600">
                                    Obrigado pela participação
                                  </div>
                                )}
                              </div>
                              <div>
                                {QuizUtils.isAssessmentMode(quizContent.config) && (
                                  <div className={`text-lg font-semibold ${userProgress.passed ? 'text-green-600' : 'text-gray-600'}`}>
                                    {userProgress.passed ? '✅ Aprovado' : '❌ Não Aprovado'}
                                  </div>
                                )}
                                {QuizUtils.isSurveyMode(quizContent.config) && (
                                  <div className="text-lg font-semibold text-blue-600">
                                    ✅ Concluída
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Botões de Resultado */}
                        {quizContent.config.showDetailedResults && (
                          <div className="text-center space-y-2">
                            {QuizUtils.isAssessmentMode(quizContent.config) ? (
                              <Button
                                variant="outline"
                                onClick={handleOpenDetailedResults}
                                className="w-full"
                              >
                                <BarChart3 className="w-4 h-4 mr-2" />
                                Ver Resultado Detalhado
                              </Button>
                            ) : (
                              <Button
                                variant="outline"
                                onClick={async () => {
                                  try {
                                    console.log('🔍 Carregando dados reais da pesquisa...');
                                    const realSurveyData = await QuizService.getSurveyResults(taskId, blockId);
                                    console.log('📊 Dados da pesquisa carregados:', realSurveyData);
                                    setSurveyResults(realSurveyData);
                                    setShowSurveyResults(true);
                                  } catch (error) {
                                    console.error('❌ Erro ao carregar dados da pesquisa:', error);
                                    toast({
                                      title: 'Erro',
                                      description: 'Erro ao carregar dados da pesquisa. Mostrando dados de demonstração.',
                                      variant: 'destructive',
                                    });
                                    setSurveyResults(null); // Usar dados mockados
                                    setShowSurveyResults(true);
                                  }
                                }}
                                className="w-full"
                              >
                                <BarChart3 className="w-4 h-4 mr-2" />
                                Ver Resultados da Pesquisa
                              </Button>
                            )}
                          </div>
                        )}

                        {/* Botão de ação */}
                        {(() => {
                          const buttonState = getButtonState();

                          // Se pode tentar novamente (passou todas as validações)
                          if (buttonState.canStart) {
                            return (
                              <Button onClick={handleStartQuiz} variant="outline" className="w-full">
                                <RotateCcw className="w-4 h-4 mr-2" />
                                {buttonState.buttonText}
                              </Button>
                            );
                          }

                          // Se não pode mais tentar, mostrar botão desabilitado
                          if (!buttonState.canStart) {
                            return (
                              <div className="space-y-2">
                                <div className="text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded p-2 text-center">
                                  {buttonState.reason}
                                </div>
                                <Button variant="secondary" className="w-full" disabled>
                                  <X className="w-4 h-4 mr-2" />
                                  {buttonState.buttonText}
                                </Button>
                              </div>
                            );
                          }

                          return null;
                        })()}
                      </div>
                    </div>
                  }
                />
              </BlockCard>
            );
          }
        }

        return null; // Placeholder para os outros estados
      })()}

      {/* Modal de Resultado Detalhado (Assessment) */}
      {quizContent && QuizUtils.isAssessmentMode(quizContent.config) && (
        <DetailedResultsModal
          isOpen={showDetailedResults}
          onClose={() => setShowDetailedResults(false)}
          quizContent={quizContent}
          currentAttempt={currentAttempt}
          userProgress={userProgress}
          allAttempts={allAttempts}
        />
      )}

      {/* Modal de Resultados da Pesquisa (Survey) */}
      {quizContent && QuizUtils.isSurveyMode(quizContent.config) && (
        <SurveyResultsModal
          isOpen={showSurveyResults}
          onClose={() => {
            setShowSurveyResults(false);
            setSurveyResults(null); // Limpar dados ao fechar
          }}
          quizContent={quizContent}
          surveyResults={surveyResults}
          totalResponses={surveyResults?.totalResponses || userProgress?.totalAttempts || 1}
        />
      )}
    </>
  );
};

// Script para debugar a estrutura dos content blocks
const { createClient } = require('@supabase/supabase-js');

// Configuração do Supabase (você precisa adicionar suas credenciais)
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugContentBlocks() {
  try {
    // Buscar alguns content blocks para ver a estrutura
    const { data, error } = await supabase
      .from('task_content_blocks')
      .select('*')
      .eq('type', 'text')
      .limit(5);
    
    if (error) {
      console.error('Erro ao buscar content blocks:', error);
      return;
    }
    
    console.log('Content blocks encontrados:');
    data.forEach((block, index) => {
      console.log(`\nBloco ${index + 1}:`);
      console.log('ID:', block.id);
      console.log('Type:', block.type);
      console.log('Content:', JSON.stringify(block.content, null, 2));
      console.log('Content.text type:', typeof block.content?.text);
      console.log('Content.text value:', block.content?.text);
    });
    
  } catch (err) {
    console.error('Erro:', err);
  }
}

// debugContentBlocks();
console.log('Script criado. Configure as credenciais do Supabase e descomente a última linha para executar.');
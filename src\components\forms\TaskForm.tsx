import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/ui/use-toast';
import { taskService } from '@/services/taskService';
import { useAuth } from '@/auth/useAuth';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { supabase } from '@/lib/supabaseClient';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card';
import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from '@/components/ui/avatar';
import { Users } from 'lucide-react';

const taskSchema = z.object({
  title: z.string().min(1, 'Título é obrigatório'),
  description: z.string().min(1, 'Descrição é obrigatória'),
  estimatedHours: z.number().min(1, 'Horas estimadas são obrigatórias'),
  dueDate: z.string().min(1, 'Data de entrega é obrigatória'),
});

type TaskFormData = z.infer<typeof taskSchema>;

interface TaskFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: any;
  mode: 'create' | 'edit';
  stageId: string;
  onCreated?: () => void;
  projectMembers: { id: string; name: string; email: string; avatar_url?: string }[];
}

// Utilitário para converter datas para o formato YYYY-MM-DD
function toDateInputValue(dateString?: string) {
  if (!dateString) return '';
  return dateString.split('T')[0];
}

export const TaskForm: React.FC<TaskFormProps> = ({
  open,
  onOpenChange,
  task,
  mode,
  stageId,
  onCreated,
  projectMembers
}) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [executors, setExecutors] = React.useState<{ id: string; name: string; email: string; avatar_url?: string }[]>(
    Array.isArray(task?.executors)
      ? task.executors.filter((e): e is { id: string; name: string; email: string; avatar_url?: string } =>
          e && typeof e === 'object' && 'id' in e && 'name' in e && 'email' in e
        )
      : []
  );

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: task?.title || '',
      description: task?.description || '',
      estimatedHours: task?.estimatedHours || 8,
      dueDate: toDateInputValue(task?.dueDate) || '',
    },
  });

  // Atualiza o formulário quando a tarefa muda (edição)
  React.useEffect(() => {
    if (task && mode === 'edit') {
      form.reset({
        title: task.title || '',
        description: task.description || '',
        estimatedHours: task.estimatedHours || 8,
        dueDate: toDateInputValue(task.dueDate) || '',
      });
      setExecutors(
        Array.isArray(task.executors)
          ? task.executors.filter(
              (e): e is { id: string; name: string; email: string; avatar_url?: string } =>
                e && typeof e === 'object' && 'id' in e && 'name' in e && 'email' in e
            )
          : []
      );
    }
  }, [task, mode, form]);

  const onSubmit = async (data: TaskFormData) => {
    try {
      console.log('[TaskForm] onSubmit - Dados do formulário:', data);
      console.log('[TaskForm] onSubmit - Executores selecionados:', executors);
      let taskId = task?.id;
      if (mode === 'create') {
        if (!user?.id) {
          toast({
            title: 'Erro',
            description: 'Usuário não autenticado. Faça login novamente.',
            variant: 'destructive',
          });
          return;
        }
        const newTask = await taskService.create({
          title: data.title,
          description: data.description,
          estimated_hours: data.estimatedHours,
          due_date: data.dueDate,
          stage_id: stageId,
          created_by: user.id
        });
        console.log('[TaskForm] onSubmit - Resposta do insert da tarefa:', newTask);
        taskId = newTask.id;
        // Salvar executores na tabela task_executors
        if (executors.length > 0) {
          const results = await Promise.all(executors.map(e =>
            supabase.from('task_executors').insert({ task_id: taskId, user_id: e.id })
          ));
          results.forEach((res, idx) => {
            if (res.error) {
              console.error(`[TaskForm] Erro ao inserir executor ${executors[idx].id}:`, res.error);
            } else {
              console.log(`[TaskForm] Executor inserido com sucesso:`, executors[idx]);
            }
          });
          console.log('[TaskForm] onSubmit - Resultados do insert de executores:', results);
        }
        toast({
          title: 'Tarefa criada',
          description: 'A tarefa foi criada com sucesso.',
        });
        onCreated?.();
      } else {
        const updateResult = await taskService.update(task.id, {
          title: data.title,
          description: data.description,
          estimated_hours: data.estimatedHours,
          due_date: data.dueDate,
        });
        console.log('[TaskForm] onSubmit - Resposta do update da tarefa:', updateResult);
        // Atualizar executores (remover todos e inserir os atuais)
        const deleteResult = await supabase.from('task_executors').delete().eq('task_id', task.id);
        console.log('[TaskForm] onSubmit - Resultado do delete de executores:', deleteResult);
        if (executors.length > 0) {
          const results = await Promise.all(executors.map(e =>
            supabase.from('task_executors').insert({ task_id: task.id, user_id: e.id })
          ));
          results.forEach((res, idx) => {
            if (res.error) {
              console.error(`[TaskForm] Erro ao inserir executor ${executors[idx].id}:`, res.error);
            } else {
              console.log(`[TaskForm] Executor inserido com sucesso:`, executors[idx]);
            }
          });
          console.log('[TaskForm] onSubmit - Resultados do insert de executores (update):', results);
        }
        toast({
          title: 'Tarefa atualizada',
          description: 'A tarefa foi atualizada com sucesso.',
        });
        onCreated?.();
      }
      onOpenChange(false);
      form.reset();
      setExecutors([]);
    } catch (error) {
      console.error('[TaskForm] Erro geral ao salvar tarefa:', error, {
        data,
        executors,
        user,
        task,
        stageId
      });
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao salvar a tarefa.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md" aria-describedby="task-form-desc">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Nova Tarefa' : 'Editar Tarefa'}
          </DialogTitle>
          <DialogDescription id="task-form-desc">
            Preencha os campos obrigatórios para {mode === 'create' ? 'criar' : 'editar'} uma tarefa.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título da Tarefa</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o título da tarefa" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Descreva a tarefa" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="estimatedHours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Horas Estimadas</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="8"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Entrega</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Card className="mb-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-project" />
                  Executores da Tarefa
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 mb-2 min-h-[40px]">
                  {executors.length === 0 ? (
                    <span className="text-gray-400 text-sm">Nenhum executor adicionado</span>
                  ) : (
                    executors.map(executor => (
                      <span
                        key={executor.id}
                        className="inline-flex items-center gap-2 bg-gray-100 text-gray-800 px-3 py-1 rounded-full shadow-sm border border-gray-200 min-w-0 max-w-full"
                        style={{ maxWidth: '100%' }}
                      >
                        <Avatar className="w-7 h-7">
                          <AvatarImage src={executor.avatar_url || '/placeholder.svg'} alt={executor.name || ''} />
                          <AvatarFallback className="text-xs">{(executor.name || '?').charAt(0).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col min-w-0 max-w-[120px]">
                          <span className="truncate font-medium text-sm">{executor.name}</span>
                          <span className="truncate text-xs text-gray-500">{executor.email}</span>
                        </div>
                        <button
                          type="button"
                          className="ml-1 text-gray-400 hover:text-red-500 transition"
                          onClick={() => setExecutors(prev => prev.filter(e => e.id !== executor.id))}
                          aria-label="Remover executor"
                        >
                          ×
                        </button>
                      </span>
                    ))
                  )}
                </div>
                <UserAutocomplete
                  onSelect={user => {
                    setExecutors(prev => {
                      if (prev.some(e => e.id === user.id)) return prev;
                      return [...prev, user];
                    });
                  }}
                  excludeIds={executors.map(e => e.id)}
                  users={projectMembers}
                />
              </CardContent>
            </Card>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" className="bg-task hover:bg-task-dark">
                {mode === 'create' ? 'Criar Tarefa' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

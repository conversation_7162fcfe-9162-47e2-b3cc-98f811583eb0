import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/ui/use-toast';
import { taskService } from '@/services/taskService';
import { useAuth } from '@/auth/useAuth';
import { UserAutocomplete } from '@/components/forms/UserAutocomplete';
import { supabase } from '@/lib/supabaseClient';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card';
import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from '@/components/ui/avatar';
import { Users, X } from 'lucide-react';

const taskSchema = z.object({
  title: z.string().min(1, 'Título é obrigatório'),
  description: z.string().min(1, 'Descrição é obrigatória'),
  estimatedHours: z.number().min(1, 'Horas estimadas são obrigatórias'),
  dueDate: z.string().min(1, 'Data de entrega é obrigatória'),
});

type TaskFormData = z.infer<typeof taskSchema>;

interface TaskFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: any;
  mode: 'create' | 'edit';
  stageId: string;
  onCreated?: () => void;
  projectMembers: { id: string; name: string; email: string; avatar_url?: string }[];
}

// Utilitário para converter datas para o formato YYYY-MM-DD
function toDateInputValue(dateString?: string) {
  if (!dateString) return '';
  return dateString.split('T')[0];
}

export const TaskForm: React.FC<TaskFormProps> = ({
  open,
  onOpenChange,
  task,
  mode,
  stageId,
  onCreated,
  projectMembers
}) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [executors, setExecutors] = React.useState<{ id: string; name: string; email: string; avatar_url?: string }[]>(
    Array.isArray(task?.executors)
      ? task.executors.filter((e): e is { id: string; name: string; email: string; avatar_url?: string } =>
          e && typeof e === 'object' && 'id' in e && 'name' in e && 'email' in e
        )
      : []
  );

  const [approvers, setApprovers] = React.useState<{ id: string; name: string; email: string; avatar_url?: string }[]>(
    Array.isArray(task?.approvers)
      ? task.approvers.filter((a): a is { id: string; name: string; email: string; avatar_url?: string } =>
          a && typeof a === 'object' && 'id' in a && 'name' in a && 'email' in a
        )
      : []
  );

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: task?.title || '', // Campo correto do banco é title
      description: task?.description || '',
      estimatedHours: task?.estimated_hours || task?.estimatedHours || 8,
      dueDate: toDateInputValue(task?.due_date || task?.dueDate) || '',
    },
  });

  // Atualiza o formulário quando a tarefa muda (edição)
  React.useEffect(() => {
    if (task && mode === 'edit') {
      console.log('[TaskForm] Atualizando formulário com dados da tarefa:', task);
      form.reset({
        title: task.title || '', // Campo correto do banco é title
        description: task.description || '',
        estimatedHours: task.estimated_hours || task.estimatedHours || 8,
        dueDate: toDateInputValue(task.due_date || task.dueDate) || '',
      });
      
      // Corrigido: melhor tratamento dos executores
      let executorsList = [];
      if (Array.isArray(task.executors)) {
        executorsList = task.executors.filter(
          (e): e is { id: string; name: string; email: string; avatar_url?: string } =>
            e && typeof e === 'object' && 'id' in e && 'name' in e && 'email' in e
        );
      }
      console.log('[TaskForm] Executores carregados:', executorsList);
      setExecutors(executorsList);

      // Carregar aprovadores
      let approversList = [];
      if (Array.isArray(task.approvers)) {
        approversList = task.approvers.filter(
          (a): a is { id: string; name: string; email: string; avatar_url?: string } =>
            a && typeof a === 'object' && 'id' in a && 'name' in a && 'email' in a
        );
      }
      console.log('[TaskForm] Aprovadores carregados:', approversList);
      setApprovers(approversList);
    }
  }, [task, mode, form]);

  const onSubmit = async (data: TaskFormData) => {
    try {
      console.log('[TaskForm] onSubmit - Dados do formulário:', data);
      console.log('[TaskForm] onSubmit - Executores selecionados:', executors);
      let taskId = task?.id;
      if (mode === 'create') {
        if (!user?.id) {
          toast({
            title: 'Erro',
            description: 'Usuário não autenticado. Faça login novamente.',
            variant: 'destructive',
          });
          return;
        }
        const newTask = await taskService.create({
          title: data.title,
          description: data.description,
          estimated_hours: data.estimatedHours,
          due_date: data.dueDate,
          stage_id: stageId,
          created_by: user.id
        });
        console.log('[TaskForm] onSubmit - Resposta do insert da tarefa:', newTask);
        taskId = newTask.id;
        // Salvar executores na tabela task_executors
        if (executors.length > 0) {
          const results = await Promise.all(executors.map(e =>
            supabase.from('task_executors').insert({ task_id: taskId, user_id: e.id })
          ));
          results.forEach((res, idx) => {
            if (res.error) {
              console.error(`[TaskForm] Erro ao inserir executor ${executors[idx].id}:`, res.error);
            } else {
              console.log(`[TaskForm] Executor inserido com sucesso:`, executors[idx]);
            }
          });
          console.log('[TaskForm] onSubmit - Resultados do insert de executores:', results);
        }

        // Salvar aprovadores na tabela task_approvers
        if (approvers.length > 0) {
          const results = await Promise.all(approvers.map(a =>
            supabase.from('task_approvers').insert({ task_id: taskId, user_id: a.id })
          ));
          results.forEach((res, idx) => {
            if (res.error) {
              console.error(`[TaskForm] Erro ao inserir aprovador ${approvers[idx].id}:`, res.error);
            } else {
              console.log(`[TaskForm] Aprovador inserido com sucesso:`, approvers[idx]);
            }
          });
          console.log('[TaskForm] onSubmit - Resultados do insert de aprovadores:', results);
        }
        toast({
          title: 'Tarefa criada',
          description: 'A tarefa foi criada com sucesso.',
        });
        onCreated?.();
      } else {
        const updateResult = await taskService.update(task.id, {
          title: data.title,
          description: data.description,
          estimated_hours: data.estimatedHours,
          due_date: data.dueDate,
        });
        console.log('[TaskForm] onSubmit - Resposta do update da tarefa:', updateResult);
        // Atualizar executores (remover todos e inserir os atuais)
        const deleteResult = await supabase.from('task_executors').delete().eq('task_id', task.id);
        console.log('[TaskForm] onSubmit - Resultado do delete de executores:', deleteResult);
        if (executors.length > 0) {
          const results = await Promise.all(executors.map(e =>
            supabase.from('task_executors').insert({ task_id: task.id, user_id: e.id })
          ));
          results.forEach((res, idx) => {
            if (res.error) {
              console.error(`[TaskForm] Erro ao inserir executor ${executors[idx].id}:`, res.error);
            } else {
              console.log(`[TaskForm] Executor inserido com sucesso:`, executors[idx]);
            }
          });
          console.log('[TaskForm] onSubmit - Resultados do insert de executores (update):', results);
        }

        // Atualizar aprovadores (remover todos e inserir os atuais)
        const deleteApproversResult = await supabase.from('task_approvers').delete().eq('task_id', task.id);
        console.log('[TaskForm] onSubmit - Resultado do delete de aprovadores:', deleteApproversResult);
        if (approvers.length > 0) {
          const results = await Promise.all(approvers.map(a =>
            supabase.from('task_approvers').insert({ task_id: task.id, user_id: a.id })
          ));
          results.forEach((res, idx) => {
            if (res.error) {
              console.error(`[TaskForm] Erro ao inserir aprovador ${approvers[idx].id}:`, res.error);
            } else {
              console.log(`[TaskForm] Aprovador inserido com sucesso:`, approvers[idx]);
            }
          });
          console.log('[TaskForm] onSubmit - Resultados do insert de aprovadores (update):', results);
        }
        toast({
          title: 'Tarefa atualizada',
          description: 'A tarefa foi atualizada com sucesso.',
        });
        onCreated?.();
      }
      onOpenChange(false);
      form.reset();
      setExecutors([]);
      setApprovers([]);
    } catch (error) {
      console.error('[TaskForm] Erro geral ao salvar tarefa:', error, {
        data,
        executors,
        user,
        task,
        stageId
      });
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao salvar a tarefa.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md" aria-describedby="task-form-desc">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Nova Tarefa' : 'Editar Tarefa'}
          </DialogTitle>
          <DialogDescription id="task-form-desc">
            Preencha os campos obrigatórios para {mode === 'create' ? 'criar' : 'editar'} uma tarefa.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título da Tarefa</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o título da tarefa" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Descreva a tarefa" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="estimatedHours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Horas Estimadas</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="8"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Entrega</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Card className="mb-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-project" />
                  Executores da Tarefa
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Header da Grid (Desktop) */}
                <div className="hidden sm:grid grid-cols-[40px_1fr_1fr_40px] gap-3 py-2 px-1 border-b border-gray-200 bg-gray-50/50 rounded-t-lg mb-2">
                  <div></div>
                  <div className="text-sm font-medium text-gray-700">Nome</div>
                  <div className="text-sm font-medium text-gray-700">Email</div>
                  <div></div>
                </div>

                {/* Lista de Executores em Grid */}
                <div className="space-y-2">
                  {executors.length === 0 ? (
                    <div className="text-center py-8">
                      <Users className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                      <span className="text-gray-400 text-sm">Nenhum executor adicionado</span>
                      <p className="text-xs text-gray-300 mt-1">Use o campo abaixo para adicionar executores</p>
                    </div>
                  ) : (
                    executors.map(executor => (
                      <div key={executor.id}>
                        {/* Mobile Layout */}
                        <div className="flex sm:hidden items-start gap-3 py-3 px-2 border border-gray-100 rounded-lg hover:bg-gray-50/50 transition-colors group">
                          <Avatar className="w-10 h-10 border border-gray-200 flex-shrink-0">
                            <AvatarImage src={executor.avatar_url || '/placeholder.svg'} alt={executor.name || ''} />
                            <AvatarFallback className="text-xs bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-700 font-medium">
                              {(executor.name || executor.email || '?').charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div className="min-w-0 flex-1">
                                <p className="text-sm font-medium text-gray-900 truncate">{executor.name || 'Nome não informado'}</p>
                                <p className="text-xs text-gray-600 truncate">{executor.email || 'Email não informado'}</p>
                              </div>
                              <button
                                type="button"
                                onClick={() => setExecutors(prev => prev.filter(e => e.id !== executor.id))}
                                className="ml-2 w-6 h-6 rounded-md text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors flex-shrink-0"
                                title="Remover executor"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        {/* Desktop Grid Layout */}
                        <div className="hidden sm:grid grid-cols-[40px_1fr_1fr_40px] gap-3 py-3 px-1 border-b border-gray-50 hover:bg-gray-50/50 transition-colors group">
                          {/* Avatar */}
                          <div className="flex items-center">
                            <Avatar className="w-8 h-8 border border-gray-200">
                              <AvatarImage src={executor.avatar_url || '/placeholder.svg'} alt={executor.name || ''} />
                              <AvatarFallback className="text-xs bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-700 font-medium">
                                {(executor.name || executor.email || '?').charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                          </div>

                          {/* Nome */}
                          <div className="flex items-center min-w-0">
                            <span className="text-sm font-medium text-gray-900 truncate" title={executor.name}>
                              {executor.name || 'Nome não informado'}
                            </span>
                          </div>

                          {/* Email */}
                          <div className="flex items-center min-w-0">
                            <span className="text-sm text-gray-600 truncate" title={executor.email}>
                              {executor.email || 'Email não informado'}
                            </span>
                          </div>

                          {/* Botão Remover */}
                          <div className="flex items-center">
                            <button
                              type="button"
                              onClick={() => setExecutors(prev => prev.filter(e => e.id !== executor.id))}
                              className="w-6 h-6 rounded-md text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors opacity-0 group-hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
                              title="Remover executor"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Campo de Adicionar */}
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <UserAutocomplete
                    onSelect={user => {
                      setExecutors(prev => {
                        if (prev.some(e => e.id === user.id)) return prev;
                        return [...prev, user];
                      });
                    }}
                    excludeIds={executors.map(e => e.id)}
                    users={projectMembers}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="mb-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-project" />
                  Aprovadores da Tarefa
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Header da Grid (Desktop) */}
                <div className="hidden sm:grid grid-cols-[40px_1fr_1fr_40px] gap-3 py-2 px-1 border-b border-gray-200 bg-gray-50/50 rounded-t-lg mb-2">
                  <div></div>
                  <div className="text-sm font-medium text-gray-700">Nome</div>
                  <div className="text-sm font-medium text-gray-700">Email</div>
                  <div></div>
                </div>

                {/* Lista de Aprovadores em Grid */}
                <div className="space-y-2">
                  {approvers.length === 0 ? (
                    <div className="text-center py-8">
                      <Users className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                      <span className="text-gray-400 text-sm">Nenhum aprovador adicionado</span>
                      <p className="text-xs text-gray-300 mt-1">Use o campo abaixo para adicionar aprovadores</p>
                    </div>
                  ) : (
                    approvers.map(approver => (
                      <div key={approver.id}>
                        {/* Mobile Layout */}
                        <div className="flex sm:hidden items-start gap-3 py-3 px-2 border border-gray-100 rounded-lg hover:bg-gray-50/50 transition-colors group">
                          <Avatar className="w-10 h-10 border border-gray-200 flex-shrink-0">
                            <AvatarImage src={approver.avatar_url || '/placeholder.svg'} alt={approver.name || ''} />
                            <AvatarFallback className="text-xs bg-gradient-to-br from-green-100 to-emerald-100 text-green-700 font-medium">
                              {(approver.name || approver.email || '?').charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div className="min-w-0 flex-1">
                                <p className="text-sm font-medium text-gray-900 truncate">{approver.name || 'Nome não informado'}</p>
                                <p className="text-xs text-gray-600 truncate">{approver.email || 'Email não informado'}</p>
                              </div>
                              <button
                                type="button"
                                onClick={() => setApprovers(prev => prev.filter(a => a.id !== approver.id))}
                                className="ml-2 w-6 h-6 rounded-md text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors flex-shrink-0"
                                title="Remover aprovador"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        {/* Desktop Grid Layout */}
                        <div className="hidden sm:grid grid-cols-[40px_1fr_1fr_40px] gap-3 py-3 px-1 border-b border-gray-50 hover:bg-gray-50/50 transition-colors group">
                          {/* Avatar */}
                          <div className="flex items-center">
                            <Avatar className="w-8 h-8 border border-gray-200">
                              <AvatarImage src={approver.avatar_url || '/placeholder.svg'} alt={approver.name || ''} />
                              <AvatarFallback className="text-xs bg-gradient-to-br from-green-100 to-emerald-100 text-green-700 font-medium">
                                {(approver.name || approver.email || '?').charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                          </div>

                          {/* Nome */}
                          <div className="flex items-center min-w-0">
                            <span className="text-sm font-medium text-gray-900 truncate" title={approver.name}>
                              {approver.name || 'Nome não informado'}
                            </span>
                          </div>

                          {/* Email */}
                          <div className="flex items-center min-w-0">
                            <span className="text-sm text-gray-600 truncate" title={approver.email}>
                              {approver.email || 'Email não informado'}
                            </span>
                          </div>

                          {/* Botão Remover */}
                          <div className="flex items-center">
                            <button
                              type="button"
                              onClick={() => setApprovers(prev => prev.filter(a => a.id !== approver.id))}
                              className="w-6 h-6 rounded-md text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors opacity-0 group-hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
                              title="Remover aprovador"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Campo de Adicionar */}
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <UserAutocomplete
                    onSelect={user => {
                      setApprovers(prev => {
                        if (prev.some(a => a.id === user.id)) return prev;
                        return [...prev, user];
                      });
                    }}
                    excludeIds={[...executors.map(e => e.id), ...approvers.map(a => a.id)]}
                    users={projectMembers}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit" className="bg-task hover:bg-task-dark">
                {mode === 'create' ? 'Criar Tarefa' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

<!-- VIDEO BLOCK -->
<div class="block-config block-config-panel hidden">
  <div class="block-title">Configuração: Vídeo</div>
  <div class="preview"><b>Preview do bloco de vídeo</b></div>
  <div class="tabs">
    <button class="tab-btn">Aparência</button>
    <button class="tab-btn">Ícone</button>
    <button class="tab-btn">Botão</button>
    <button class="tab-btn">Presets</button>
  </div>
  <!-- Aparência -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Aparência do Card</div>
      <div class="fields">
        <div class="field"><label>Cor de fundo do card</label><input type="color" value="#f3f0ff"></div>
        <div class="field"><label>Cor do texto do card</label><input type="color" value="#222"></div>
        <div class="field"><label>Formato do card</label><select><option>Arredondado</option><option>Quadrado</option><option>Pílula</option></select></div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="video-border-fields"> Ativar borda</label></div>
        <div class="field" id="video-border-fields" style="display:none;">
          <label>Cor da borda</label><input type="color" value="#e5e5e5">
          <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="video-shadow-fields"> Ativar sombra</label></div>
        <div class="field" id="video-shadow-fields" style="display:none;">
          <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="video-hover-fields"> Ativar hover</label></div>
        <div class="field" id="video-hover-fields" style="display:none;">
          <label>Sombra extra no hover</label><input type="checkbox"></div>
      </div>
      <button class="preset-btn" style="margin-top:12px;">Restaurar padrão do card</button>
    </div>
  </div>
  <!-- Ícone -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Configuração do Ícone</div>
      <div class="fields">
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="video-icon-fields" checked> Ativar ícone</label></div>
        <div class="field" id="video-icon-fields">
          <label>Posição</label>
          <select>
            <option>Esquerda do título</option>
            <option>Direita do título</option>
            <option>Esquerda do título e descrição</option>
            <option>Direita do título e descrição</option>
            <option>Esquerda do conteúdo</option>
            <option>Direita do conteúdo</option>
            <option>Topo centralizado</option>
            <option>Rodapé centralizado</option>
          </select>
          <label>Tipo</label>
          <select class="toggle-btn" data-target="video-icon-type-predefinido-fields">
            <option value="predefinido">Predefinido</option>
            <option value="personalizado">Personalizado</option>
          </select>
          <div id="video-icon-type-predefinido-fields">
            <label>Ícone (Lucide)</label>
            <button class="preset-btn">Escolher ícone</button>
            <span style="font-size:0.95em;color:#64748b;">(Abrir modal com lista de ícones Lucide)</span>
          </div>
          <div id="video-icon-type-personalizado-fields" style="display:none;">
            <label>URL do ícone personalizado</label><input type="url" placeholder="https://exemplo.com/icone.svg">
          </div>
          <label>Cor de fundo do ícone</label><input type="color" value="#f3f4f6">
          <label>Cor do ícone</label><input type="color" value="#a78bfa">
          <label>Formato do ícone</label><select><option>Circular</option><option>Quadrado</option></select>
          <label><input type="checkbox" class="toggle-btn" data-target="video-icon-border-fields"> Ativar borda</label>
          <div id="video-icon-border-fields" style="display:none;">
            <label>Cor da borda</label><input type="color" value="#e5e5e5">
            <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
          </div>
          <label><input type="checkbox" class="toggle-btn" data-target="video-icon-shadow-fields"> Ativar sombra</label>
          <div id="video-icon-shadow-fields" style="display:none;">
            <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
          </div>
          <label><input type="checkbox" class="toggle-btn" data-target="video-icon-hover-fields"> Ativar hover</label>
          <div id="video-icon-hover-fields" style="display:none;">
            <label>Sombra extra no hover</label><input type="checkbox"></div>
        </div>
      </div>
      <button class="preset-btn" style="margin-top:12px;">Restaurar padrão do ícone</button>
    </div>
  </div>
  <!-- Botão -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Configuração do Botão</div>
      <div class="fields">
        <div class="field"><label>Cor de fundo do botão</label><input type="color" value="#a78bfa"></div>
        <div class="field"><label>Cor do texto do botão</label><input type="color" value="#fff"></div>
        <div class="field"><label>Estilo</label><select><option>Arredondado</option><option>Plano</option><option>Preenchido</option><option>Outlined</option><option>Text</option></select></div>
        <div class="field"><label>Tamanho</label><select><option>Pequeno</option><option>Médio</option><option>Grande</option></select></div>
        <div class="field"><label>Posição</label><select>
          <option>Superior esquerda</option>
          <option>Superior direita</option>
          <option>Superior centralizado</option>
          <option>Inferior esquerda</option>
          <option>Inferior direita</option>
          <option>Inferior centralizado</option>
        </select></div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="video-button-border-fields"> Ativar borda</label></div>
        <div class="field" id="video-button-border-fields" style="display:none;">
          <label>Cor da borda</label><input type="color" value="#e5e5e5">
          <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="video-button-shadow-fields"> Ativar sombra</label></div>
        <div class="field" id="video-button-shadow-fields" style="display:none;">
          <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="video-button-hover-fields"> Ativar hover</label></div>
        <div class="field" id="video-button-hover-fields" style="display:none;">
          <label>Sombra extra no hover</label><input type="checkbox"></div>
        <div class="field"><label>Ícone do botão (usar o mesmo do card)</label><input type="text" value="Play"></div>
      </div>
      <button class="preset-btn" style="margin-top:12px;">Restaurar padrão do botão</button>
    </div>
  </div>
  <!-- Presets -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Presets de Estilo</div>
      <div class="preset-list">
        <button class="preset-btn selected">Default</button>
        <button class="preset-btn">YouTube</button>
        <button class="preset-btn">Vimeo</button>
      </div>
      <div style="margin-top:12px; color:#64748b; font-size:0.95rem;">Selecionar um preset aplica automaticamente cor, ícone e layout.</div>
    </div>
  </div>
</div>

<!-- ALERT BLOCK -->
<div class="block-config block-config-panel">
  <div class="block-title">Configuração: Alerta</div>
  <div class="preview"><b>Preview do bloco de alerta</b></div>
  <div class="tabs">
    <button class="tab-btn">Aparência</button>
    <button class="tab-btn">Ícone</button>
    <button class="tab-btn">Botão</button>
    <button class="tab-btn">Presets</button>
  </div>
  <!-- Aparência -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Aparência do Card</div>
      <div class="fields">
        <div class="field"><label>Cor de fundo do card</label><input type="color" value="#fef2f2"></div>
        <div class="field"><label>Cor do texto do card</label><input type="color" value="#222"></div>
        <div class="field"><label>Formato do card</label><select><option>Arredondado</option><option>Quadrado</option><option>Pílula</option></select></div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-border-fields"> Ativar borda</label></div>
        <div class="field" id="alert-border-fields" style="display:none;">
          <label>Cor da borda</label><input type="color" value="#e5e5e5">
          <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-shadow-fields"> Ativar sombra</label></div>
        <div class="field" id="alert-shadow-fields" style="display:none;">
          <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-hover-fields"> Ativar hover</label></div>
        <div class="field" id="alert-hover-fields" style="display:none;">
          <label>Sombra extra no hover</label><input type="checkbox"></div>
      </div>
      <button class="preset-btn" style="margin-top:12px;">Restaurar padrão do card</button>
    </div>
  </div>
  <!-- Ícone -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Configuração do Ícone</div>
      <div class="fields">
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-icon-fields" checked> Ativar ícone</label></div>
        <div class="field" id="alert-icon-fields">
          <label>Posição</label>
          <select>
            <option>Esquerda do título</option>
            <option>Direita do título</option>
            <option>Esquerda do título e descrição</option>
            <option>Direita do título e descrição</option>
            <option>Esquerda do conteúdo</option>
            <option>Direita do conteúdo</option>
            <option>Topo centralizado</option>
            <option>Rodapé centralizado</option>
          </select>
          <label>Tipo</label>
          <select class="toggle-btn" data-target="alert-icon-type-predefinido-fields">
            <option value="predefinido">Predefinido</option>
            <option value="personalizado">Personalizado</option>
          </select>
          <div id="alert-icon-type-predefinido-fields">
            <label>Ícone (Lucide)</label>
            <button class="preset-btn">Escolher ícone</button>
            <span style="font-size:0.95em;color:#64748b;">(Abrir modal com lista de ícones Lucide)</span>
          </div>
          <div id="alert-icon-type-personalizado-fields" style="display:none;">
            <label>URL do ícone personalizado</label><input type="url" placeholder="https://exemplo.com/icone.svg">
          </div>
          <label>Cor de fundo do ícone</label><input type="color" value="#f3f4f6">
          <label>Cor do ícone</label><input type="color" value="#2563eb">
          <label>Formato do ícone</label><select><option>Circular</option><option>Quadrado</option></select>
          <label><input type="checkbox" class="toggle-btn" data-target="alert-icon-border-fields"> Ativar borda</label>
          <div id="alert-icon-border-fields" style="display:none;">
            <label>Cor da borda</label><input type="color" value="#e5e5e5">
            <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
          </div>
          <label><input type="checkbox" class="toggle-btn" data-target="alert-icon-shadow-fields"> Ativar sombra</label>
          <div id="alert-icon-shadow-fields" style="display:none;">
            <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
          </div>
          <label><input type="checkbox" class="toggle-btn" data-target="alert-icon-hover-fields"> Ativar hover</label>
          <div id="alert-icon-hover-fields" style="display:none;">
            <label>Sombra extra no hover</label><input type="checkbox"></div>
        </div>
      </div>
      <button class="preset-btn" style="margin-top:12px;">Restaurar padrão do ícone</button>
    </div>
  </div>
  <!-- Botão -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Configuração do Botão</div>
      <div class="fields">
        <div class="field"><label>Cor de fundo do botão</label><input type="color" value="#3b82f6"></div>
        <div class="field"><label>Cor do texto do botão</label><input type="color" value="#fff"></div>
        <div class="field"><label>Estilo</label><select><option>Arredondado</option><option>Plano</option><option>Preenchido</option><option>Outlined</option><option>Text</option></select></div>
        <div class="field"><label>Tamanho</label><select><option>Pequeno</option><option>Médio</option><option>Grande</option></select></div>
        <div class="field"><label>Posição</label><select>
          <option>Superior esquerda</option>
          <option>Superior direita</option>
          <option>Superior centralizado</option>
          <option>Inferior esquerda</option>
          <option>Inferior direita</option>
          <option>Inferior centralizado</option>
        </select></div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-button-border-fields"> Ativar borda</label></div>
        <div class="field" id="alert-button-border-fields" style="display:none;">
          <label>Cor da borda</label><input type="color" value="#e5e5e5">
          <label>Largura da borda</label><input type="number" min="1" max="5" value="1">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-button-shadow-fields"> Ativar sombra</label></div>
        <div class="field" id="alert-button-shadow-fields" style="display:none;">
          <label>Profundidade da sombra</label><input type="range" min="1" max="5" value="2">
        </div>
        <div class="field"><label><input type="checkbox" class="toggle-btn" data-target="alert-button-hover-fields"> Ativar hover</label></div>
        <div class="field" id="alert-button-hover-fields" style="display:none;">
          <label>Sombra extra no hover</label><input type="checkbox"></div>
        <div class="field"><label>Ícone do botão (usar o mesmo do card)</label><input type="text" value="Info"></div>
      </div>
      <button class="preset-btn" style="margin-top:12px;">Restaurar padrão do botão</button>
    </div>
  </div>
  <!-- Presets -->
  <div class="tab-content hidden">
    <div class="section">
      <div class="section-title">Presets de Estilo</div>
      <div class="preset-list">
        <button class="preset-btn selected">Success</button>
        <button class="preset-btn">Info</button>
        <button class="preset-btn">Warning</button>
        <button class="preset-btn">Error</button>
        <button class="preset-btn">Layout 1</button>
        <button class="preset-btn">Layout 2</button>
      </div>
      <div style="margin-top:12px; color:#64748b; font-size:0.95rem;">Selecionar um preset aplica automaticamente cor, ícone e layout.</div>
    </div>
  </div>
</div> 

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modelo Real do Configurador de Blocos</title>
  <style>
    body { font-family: 'Inter', Arial, sans-serif; background: #f3f4f6; margin: 0; padding: 32px; }
    .container { max-width: 900px; margin: 0 auto; }
    h1 { font-size: 2rem; margin-bottom: 24px; }
    .premissa { background: #eef2ff; border-left: 6px solid #6366f1; border-radius: 8px; padding: 20px 28px; margin-bottom: 32px; color: #3730a3; }
    .premissa-title { font-size: 1.15rem; font-weight: bold; margin-bottom: 8px; color: #3730a3; }
    .premissa-list { margin: 0 0 0 18px; padding: 0; font-size: 1rem; }
    .block-type-selector { display: flex; gap: 10px; margin-bottom: 32px; }
    .block-type-btn { padding: 8px 18px; border-radius: 8px; border: 1px solid #e5e7eb; background: #f3f4f6; cursor: pointer; font-size: 1rem; font-weight: 500; color: #6366f1; transition: background 0.15s, color 0.15s; }
    .block-type-btn.active { background: #6366f1; color: #fff; border-color: #6366f1; }
    .block-config { background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 32px; margin-bottom: 40px; }
    .block-title { font-size: 1.3rem; font-weight: bold; margin-bottom: 16px; color: #6366f1; }
    .preview { background: #f8fafc; border-radius: 8px; padding: 16px; margin-bottom: 24px; border: 1px solid #e5e7eb; }
    .tabs { display: flex; gap: 8px; margin-bottom: 24px; }
    .tab-btn { padding: 8px 20px; border-radius: 8px 8px 0 0; border: 1px solid #e5e7eb; background: #f3f4f6; cursor: pointer; font-size: 1rem; font-weight: 500; color: #6366f1; border-bottom: none; }
    .tab-btn.active { background: #fff; color: #1e293b; border-bottom: 1px solid #fff; }
    .tab-content { background: #fff; border-radius: 0 0 8px 8px; border: 1px solid #e5e7eb; border-top: none; padding: 24px; }
    .section { margin-bottom: 28px; }
    .section-title { font-size: 1.1rem; font-weight: 600; margin-bottom: 10px; color: #374151; }
    .fields { display: flex; flex-wrap: wrap; gap: 16px; }
    .field { flex: 1 1 220px; min-width: 180px; display: flex; flex-direction: column; margin-bottom: 8px; }
    .field label { font-size: 0.95rem; font-weight: 500; margin-bottom: 4px; color: #4b5563; }
    .field input, .field select { padding: 6px 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 1rem; background: #f9fafb; }
    .preset-list { display: flex; gap: 8px; flex-wrap: wrap; }
    .preset-btn { padding: 6px 16px; border-radius: 8px; border: 1px solid #d1d5db; background: #f3f4f6; cursor: pointer; font-size: 0.95rem; }
    .preset-btn.selected { background: #6366f1; color: #fff; border-color: #6366f1; }
    .divider { border-bottom: 1px solid #e5e7eb; margin: 24px 0; }
    .hidden { display: none; }
    @media (max-width: 700px) {
      .fields { flex-direction: column; }
      .block-config { padding: 16px; }
      .tab-content { padding: 12px; }
    }
  </style>
</head> 

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Alternância de blocos
    const typeBtns = document.querySelectorAll('.block-type-btn');
    const configPanels = document.querySelectorAll('.block-config-panel');
    typeBtns.forEach((btn, idx) => {
      btn.addEventListener('click', () => {
        typeBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        configPanels.forEach((panel, pidx) => {
          panel.classList.toggle('hidden', idx !== pidx);
        });
      });
    });
    // Alternância de abas para cada painel
    document.querySelectorAll('.block-config-panel').forEach(panel => {
      const tabs = panel.querySelectorAll('.tab-btn');
      const contents = panel.querySelectorAll('.tab-content');
      tabs.forEach((tab, idx) => {
        tab.addEventListener('click', () => {
          tabs.forEach(t => t.classList.remove('active'));
          contents.forEach(c => c.classList.add('hidden'));
          tab.classList.add('active');
          contents[idx].classList.remove('hidden');
        });
      });
      // Inicialização: primeira aba ativa
      if (tabs.length) {
        tabs[0].classList.add('active');
        contents[0].classList.remove('hidden');
      }
    });
    // Campos condicionais (IDs únicos por bloco)
    document.querySelectorAll('.block-config-panel').forEach(panel => {
      panel.querySelectorAll('.toggle-btn').forEach(input => {
        input.addEventListener('change', function() {
          const target = panel.querySelector('#' + this.dataset.target);
          if (target) target.style.display = this.checked ? '' : 'none';
        });
        // Estado inicial
        const target = panel.querySelector('#' + input.dataset.target);
        if (target) target.style.display = input.checked ? '' : 'none';
      });
      // Tipo de ícone: alternar entre predefinido/personalizado
      const iconTypeSelect = panel.querySelector('select.toggle-btn[data-target$="icon-type-predefinido-fields"]');
      if (iconTypeSelect) {
        iconTypeSelect.addEventListener('change', function() {
          const predef = panel.querySelector('#' + this.dataset.target);
          const pers = panel.querySelector('#' + this.dataset.target.replace('predefinido', 'personalizado'));
          if (this.value === 'predefinido') {
            if (predef) predef.style.display = '';
            if (pers) pers.style.display = 'none';
          } else {
            if (predef) predef.style.display = 'none';
            if (pers) pers.style.display = '';
          }
        });
        // Estado inicial
        if (iconTypeSelect.value === 'predefinido') {
          const predef = panel.querySelector('#' + iconTypeSelect.dataset.target);
          const pers = panel.querySelector('#' + iconTypeSelect.dataset.target.replace('predefinido', 'personalizado'));
          if (predef) predef.style.display = '';
          if (pers) pers.style.display = 'none';
        } else {
          const predef = panel.querySelector('#' + iconTypeSelect.dataset.target);
          const pers = panel.querySelector('#' + iconTypeSelect.dataset.target.replace('predefinido', 'personalizado'));
          if (predef) predef.style.display = 'none';
          if (pers) pers.style.display = '';
        }
      }
    });
    // Inicialização: mostrar apenas o painel do tipo padrão
    configPanels.forEach((panel, idx) => {
      panel.classList.toggle('hidden', idx !== 0);
    });
    typeBtns[0].classList.add('active');
  });
</script> 
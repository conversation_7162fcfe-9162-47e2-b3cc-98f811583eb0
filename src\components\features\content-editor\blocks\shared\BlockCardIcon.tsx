import React from 'react';
import * as LucideIcons from 'lucide-react';
import { BlockConfig } from '@/types';
import { getLucideIconComponent } from './utils/iconUtils';

/**
 * BlockCardIcon
 *
 * Componente utilitário para renderizar o ícone personalizável do card de bloco na tela de execução/tarefa.
 *
 * Principais responsabilidades:
 * - Exibir o ícone customizável do bloco, conforme configuração feita no painel visual (config.icon)
 * - Suportar todas as opções de posição (esquerda, direita, topo, etc), tipo (Lucide/custom), cor, formato, borda, sombra, tamanho, etc
 * - Garantir layout flexível e responsivo, centralizando a lógica de renderização do ícone para todos os tipos de bloco
 * - Não interfere no ícone de identidade do tipo de bloco (menus/toolbars)
 *
 * Props:
 * - config: BlockConfig['icon'] — configuração visual do ícone do card
 * - title: string | ReactNode — título do bloco
 * - description?: string | ReactNode — descrição do bloco
 * - content?: ReactNode — conteúdo principal do bloco (corpo, listas, etc)
 * - truncateText?: boolean — se o texto deve ser truncado ou permitido quebrar linha
 *
 * Exemplo de uso:
 *
 *   <BlockCardIcon config={blockConfig.icon} title={...} description={...} content={...} />
 *
 * Orientações para integração:
 * - Use este componente apenas no modo de visualização/execução do bloco (nunca no modo de edição)
 * - Passe title, description e content conforme o bloco
 * - O componente cuida automaticamente do layout conforme a posição escolhida no painel
 * - Para adicionar suporte a novos tipos de bloco, basta passar as props corretamente
 *
 * Manutenção:
 * - Para adicionar novas posições, ajuste o switch de layout no componente
 * - Para novos estilos, expanda a lógica de style conforme necessário
 * - Para garantir robustez, mantenha a validação do ícone Lucide e fallback seguro
 */

interface BlockCardIconProps {
  config?: BlockConfig['icon'];
  title?: React.ReactNode;
  description?: React.ReactNode;
  content?: React.ReactNode;
  truncateText?: boolean;
  textColor?: string; // Cor do texto herdada do card
}

export const BlockCardIcon: React.FC<BlockCardIconProps> = ({ config, title, description, content, truncateText = true, textColor }) => {
  // Estilos de texto que herdam a cor do card
  const titleStyle: React.CSSProperties = textColor ? { color: textColor } : {};
  const descriptionStyle: React.CSSProperties = textColor ? { color: textColor, opacity: 0.8 } : {};

  if (!config?.enabled) return (
    <div className="w-full">
      {title && <h4 className={truncateText ? 'font-bold text-lg truncate overflow-hidden' : 'font-bold text-lg'} style={titleStyle}>{title}</h4>}
      {description && <div className={(truncateText ? 'text-sm font-normal line-clamp-2' : 'text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
      {content}
    </div>
  );
  const {
    type = 'predefined',
    iconName = 'FileText',
    customIconUrl,
    appearance = {},
    position = 'left-title',
  } = config;
  const size = appearance.size || 28;
  const style: React.CSSProperties = {
    background: appearance.background || '#f3f4f6',
    color: appearance.color || '#374151',
    borderRadius: appearance.format === 'circle' ? '50%' : '8px',
    border: appearance.border?.enabled
      ? `${appearance.border?.width || 1}px solid ${appearance.border?.color || '#e5e5e5'}`
      : 'none',
    boxShadow: appearance.shadow?.enabled
      ? `0 2px ${2 * (appearance.shadow?.depth || 1)}px #0002`
      : 'none',
    width: size + 20,
    height: size + 20,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '0',
    fontSize: size,
    position: 'relative',
    flexShrink: 0,
  };
  // Renderização do ícone
  let iconNode: React.ReactNode = null;
  if (type === 'custom' && customIconUrl) {
    iconNode = (
      <img
        src={customIconUrl}
        alt="Ícone personalizado"
        style={{ width: size, height: size, objectFit: 'contain', borderRadius: 'inherit' }}
      />
    );
  } else {
    const IconLucide = (iconName && LucideIcons[iconName]) ? LucideIcons[iconName] : LucideIcons.FileText;
    iconNode = <IconLucide className="w-6 h-6" />;
  }

  // Layout flexível conforme posição
  switch (position) {
    case 'top-center':
      return (
        <div className="flex flex-col items-center gap-2 w-full">
          <div style={style}>{iconNode}</div>
          {title && <h4 className={truncateText ? 'font-bold text-lg truncate overflow-hidden' : 'font-bold text-lg'} style={titleStyle}>{title}</h4>}
          {description && <div className={(truncateText ? 'text-sm font-normal line-clamp-2' : 'text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
          {content}
        </div>
      );
    case 'left-title':
      return (
        <div className="w-full p-4">
          <div className="flex flex-row items-center gap-2">
            <div style={style}>{iconNode}</div>
            {title && <h4 className={truncateText ? 'font-bold text-lg truncate overflow-hidden' : 'font-bold text-lg'} style={titleStyle}>{title}</h4>}
          </div>
          {description && <div className={(truncateText ? 'text-sm font-normal line-clamp-2' : 'text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
          {content}
        </div>
      );
    case 'right-title':
      return (
        <div className="w-full">
          <div className="flex flex-row items-center gap-2 justify-between">
            {title && <h4 className={truncateText ? 'font-bold text-lg truncate overflow-hidden' : 'font-bold text-lg'} style={titleStyle}>{title}</h4>}
            <div style={style}>{iconNode}</div>
          </div>
          {description && <div className={(truncateText ? 'text-sm font-normal line-clamp-2' : 'text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
          {content}
        </div>
      );
    case 'left-title-desc':
      return (
        <div className="w-full">
          <div className="flex flex-row items-center gap-2">
            <div style={style}>{iconNode}</div>
            <div className="flex-1 min-w-0">
              {title && <h4 className={truncateText ? 'font-bold text-lg truncate overflow-hidden' : 'font-bold text-lg'} style={titleStyle}>{title}</h4>}
              {description && <div className={(truncateText ? 'text-sm font-normal line-clamp-2' : 'text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
            </div>
          </div>
          {content && <div className="mt-2">{content}</div>}
        </div>
      );
    case 'right-title-desc':
      return (
        <div className="w-full">
          <div className="flex flex-row items-center gap-2 justify-between">
            <div className="flex-1 min-w-0">
              {title && <h4 className={truncateText ? 'font-bold text-lg truncate overflow-hidden' : 'font-bold text-lg'} style={titleStyle}>{title}</h4>}
              {description && <div className={(truncateText ? 'text-sm font-normal line-clamp-2' : 'text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
            </div>
            <div style={style}>{iconNode}</div>
          </div>
          {content && <div className="mt-2">{content}</div>}
        </div>
      );
    case 'left-content':
      // Ícone ao lado de todo o conteúdo (título, descrição, corpo), centralizado verticalmente
      return (
        <div className="flex flex-row items-stretch gap-2 w-full">
          <div className="flex flex-col justify-center flex-shrink-0 h-full">
            <div className="flex items-center h-full" style={{height: '100%'}}>{iconNode}</div>
          </div>
          <div className="flex-1 min-w-0 flex flex-col gap-1 justify-center">
            <div className="min-w-0 max-w-full">
              {title && <div className={truncateText ? 'block font-bold text-lg truncate overflow-x-hidden min-w-0 max-w-full' : 'block font-bold text-lg'} style={titleStyle}>{title}</div>}
              {description && <div className={(truncateText ? 'block text-sm font-normal line-clamp-2 overflow-hidden min-w-0 max-w-full' : 'block text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
            </div>
            {content}
          </div>
        </div>
      );
    case 'right-content':
      // Ícone ao lado de todo o conteúdo (título, descrição, corpo), centralizado verticalmente
      return (
        <div className="flex flex-row items-stretch gap-2 w-full justify-between">
          <div className="flex-1 min-w-0 flex flex-col gap-1 justify-center">
            <div className="min-w-0 max-w-full">
              {title && <div className={truncateText ? 'block font-bold text-lg truncate overflow-x-hidden min-w-0 max-w-full' : 'block font-bold text-lg'} style={titleStyle}>{title}</div>}
              {description && <div className={(truncateText ? 'block text-sm font-normal line-clamp-2 overflow-hidden min-w-0 max-w-full' : 'block text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
            </div>
            {content}
          </div>
          <div className="flex flex-col justify-center flex-shrink-0 h-full">
            <div className="flex items-center h-full" style={{height: '100%'}}>{iconNode}</div>
          </div>
        </div>
      );
    default:
      // Fallback: ícone à esquerda de todo o conteúdo
      return (
        <div className="flex flex-row items-start gap-2 w-full">
          <div style={style}>{iconNode}</div>
          <div className="flex-1 min-w-0">
            {title && <h4 className={truncateText ? 'font-bold text-lg truncate overflow-hidden' : 'font-bold text-lg'} style={titleStyle}>{title}</h4>}
            {description && <div className={(truncateText ? 'text-sm font-normal line-clamp-2' : 'text-sm font-normal') + ' text-justify'} style={descriptionStyle}>{description}</div>}
            {content}
          </div>
        </div>
      );
  }
};
import React, { useState, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Trophy,
  Target,
  Clock,
  CheckCircle,
  XCircle,
  BarChart3,
  FileText,
  Calendar,
  TrendingUp,
  Award,
  X
} from 'lucide-react';

import { QuizContent, QuizAttempt, UserQuizProgress, QuizAnswer } from '@/types/quiz';

interface DetailedResultsModalProps {
  isOpen: boolean;
  onClose: () => void;
  quizContent: QuizContent;
  currentAttempt: QuizAttempt | null;
  userProgress: UserQuizProgress | null;
  allAttempts?: QuizAttempt[];
}

interface QuestionAnalysis {
  questionId: string;
  questionTitle: string;
  questionType: string;
  userAnswer: QuizAnswer | null;
  correctAnswer: string[];
  isCorrect: boolean;
  pointsEarned: number;
  pointsPossible: number;
  timeSpent?: number;
}

interface QuizStatistics {
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  accuracyRate: number;
  averageTimePerQuestion: number;
  totalTimeSpent: number;
  scoreByType: Record<string, { correct: number; total: number }>;
}

export const DetailedResultsModal: React.FC<DetailedResultsModalProps> = ({
  isOpen,
  onClose,
  quizContent,
  currentAttempt,
  userProgress,
  allAttempts = []
}) => {
  // Estado para controlar qual tentativa está sendo visualizada
  const [selectedAttempt, setSelectedAttempt] = useState<QuizAttempt | null>(null);
  const [isLoadingAttempt, setIsLoadingAttempt] = useState(false);

  // Função para determinar a tentativa oficial (melhor resultado)
  const getOfficialAttempt = (attempts: QuizAttempt[]): QuizAttempt | null => {
    if (!attempts || attempts.length === 0) return null;

    const bestAttempt = attempts.reduce((best, current) => {
      if (!best) return current;

      // Priorizar tentativas aprovadas
      if (current.passed && !best.passed) return current;
      if (best.passed && !current.passed) return best;

      // Se ambas têm o mesmo status de aprovação, usar a com melhor porcentagem
      if (current.percentage > best.percentage) return current;

      // Se têm a mesma porcentagem, usar a mais recente
      if (current.percentage === best.percentage) {
        return current.attemptNumber > best.attemptNumber ? current : best;
      }

      return best;
    }, null as QuizAttempt | null);

    return bestAttempt;
  };

  // Inicializar com a tentativa oficial quando o modal abrir
  useEffect(() => {
    if (isOpen && !selectedAttempt) {
      // Determinar a tentativa oficial baseada em todas as tentativas disponíveis
      let officialAttempt: QuizAttempt | null = null;

      if (allAttempts.length > 0) {
        officialAttempt = getOfficialAttempt(allAttempts);
        console.log('🎯 Tentativa oficial determinada a partir de allAttempts:', {
          attemptNumber: officialAttempt?.attemptNumber,
          percentage: officialAttempt?.percentage,
          passed: officialAttempt?.passed,
          totalAttempts: allAttempts.length
        });
      } else if (currentAttempt) {
        officialAttempt = currentAttempt;
        console.log('🎯 Usando currentAttempt como oficial (fallback):', {
          attemptNumber: currentAttempt.attemptNumber,
          percentage: currentAttempt.percentage,
          passed: currentAttempt.passed
        });
      }

      if (officialAttempt) {
        setSelectedAttempt(officialAttempt);
      }
    }
  }, [isOpen, allAttempts, currentAttempt, selectedAttempt]);

  // Log quando selectedAttempt mudar
  useEffect(() => {
    if (selectedAttempt) {
      console.log('🔄 selectedAttempt atualizado:', {
        attemptNumber: selectedAttempt.attemptNumber,
        percentage: selectedAttempt.percentage,
        passed: selectedAttempt.passed,
        hasAnswers: selectedAttempt.answers?.length || 0
      });
    }
  }, [selectedAttempt]);

  // Resetar estado quando modal fechar
  useEffect(() => {
    if (!isOpen) {
      setSelectedAttempt(null);
      setIsLoadingAttempt(false);
    }
  }, [isOpen]);

  // Função para selecionar uma tentativa específica
  const handleSelectAttempt = (attempt: QuizAttempt) => {
    if (attempt.id === selectedAttempt?.id) {
      console.log('🔄 Tentativa já selecionada:', attempt.attemptNumber);
      return; // Já selecionada
    }

    console.log('🎯 Selecionando tentativa:', {
      from: selectedAttempt?.attemptNumber || 'nenhuma',
      to: attempt.attemptNumber,
      fromId: selectedAttempt?.id,
      toId: attempt.id,
      hasAnswers: attempt.answers?.length || 0,
      answersData: attempt.answers
    });

    setIsLoadingAttempt(true);

    // Simular um pequeno delay para mostrar o loading (opcional)
    setTimeout(() => {
      console.log('🔄 Definindo selectedAttempt para:', {
        id: attempt.id,
        attemptNumber: attempt.attemptNumber,
        percentage: attempt.percentage,
        passed: attempt.passed,
        answersCount: attempt.answers?.length || 0,
        fullAttempt: attempt
      });

      setSelectedAttempt(attempt);
      setIsLoadingAttempt(false);

      console.log('✅ Tentativa selecionada com sucesso - estado atualizado');
    }, 100);
  };

  // Função para voltar ao resultado oficial
  const handleBackToOfficial = () => {
    const officialAttempt = allAttempts.length > 0 ? getOfficialAttempt(allAttempts) : currentAttempt;
    if (officialAttempt) {
      setSelectedAttempt(officialAttempt);
      console.log('🔄 Voltando ao resultado oficial:', {
        attemptNumber: officialAttempt.attemptNumber,
        percentage: officialAttempt.percentage,
        passed: officialAttempt.passed
      });
    }
  };
  // Tradução de tipos de pergunta
  const getQuestionTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      'single-choice': 'Escolha Única',
      'multiple-choice': 'Múltipla Escolha',
      'true-false': 'Verdadeiro/Falso',
      'open-text': 'Resposta Aberta',
      'ordering': 'Ordenação',
      'matching': 'Correspondência'
    };
    return labels[type] || type;
  };

  // Formatação de tempo
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Calcular análise por pergunta (baseado na tentativa selecionada)
  const calculateQuestionAnalysis = (): QuestionAnalysis[] => {
    console.log('🔍 calculateQuestionAnalysis - selectedAttempt:', {
      id: selectedAttempt?.id,
      attemptNumber: selectedAttempt?.attemptNumber,
      percentage: selectedAttempt?.percentage,
      hasAnswers: !!selectedAttempt?.answers,
      answersCount: selectedAttempt?.answers?.length,
      answersData: selectedAttempt?.answers
    });

    if (!selectedAttempt || !selectedAttempt.answers) {
      console.log('❌ Sem selectedAttempt ou answers');
      return [];
    }

    const analysis = quizContent.questions.map((question, index) => {
      const userAnswer = selectedAttempt.answers.find(a => a.questionId === question.id);

      console.log(`🔍 Pergunta ${index + 1} (${question.id}):`, {
        questionId: question.id,
        foundUserAnswer: !!userAnswer,
        userAnswerData: userAnswer,
        allAnswerIds: selectedAttempt.answers.map(a => a.questionId)
      });

      // Determinar resposta correta baseada no tipo de pergunta
      let correctAnswer: string[] = [];
      if (question.type === 'true-false') {
        correctAnswer = question.options?.filter(opt => opt.isCorrect).map(opt => opt.text) || [];
      } else if (question.type === 'open-text') {
        correctAnswer = question.correctAnswer ? [question.correctAnswer] : [];
      } else if (question.type === 'ordering') {
        // Para ordenação, mostrar a ordem correta
        const correctOrder = question.orderingItems
          ?.sort((a, b) => a.correctOrder - b.correctOrder)
          .map((item, index) => `${index + 1}. ${item.text}`) || [];
        correctAnswer = correctOrder;
      } else if (question.type === 'matching') {
        // Para correspondência, mostrar os pares corretos
        const correctPairs = question.matchingPairs?.map(pair =>
          `${pair.left} ↔ ${pair.right}`
        ) || [];
        correctAnswer = correctPairs;
      } else {
        correctAnswer = question.options?.filter(opt => opt.isCorrect).map(opt => opt.text) || [];
      }

      // 🔧 CORREÇÃO RETROATIVA: Recalcular isCorrect baseado nas respostas reais
      // Ignorar o valor salvo (que pode estar corrompido) e calcular novamente
      let isCorrect = false;

      if (userAnswer) {
        switch (question.type) {
          case 'single-choice':
            const correctOption = question.options?.find(opt => opt.isCorrect);
            isCorrect = userAnswer.selectedOptions?.[0] === correctOption?.id;
            break;

          case 'multiple-choice':
            const correctOptions = question.options?.filter(opt => opt.isCorrect).map(opt => opt.id) || [];
            const selectedOptions = userAnswer.selectedOptions || [];
            isCorrect = correctOptions.length === selectedOptions.length &&
                       correctOptions.every(id => selectedOptions.includes(id));
            break;

          case 'true-false':
            isCorrect = userAnswer.booleanAnswer === question.correctAnswer;
            break;

          case 'open-text':
            if (question.openTextKeywords && userAnswer.textAnswer) {
              const text = userAnswer.textAnswer.toLowerCase();
              const keywordMatches = question.openTextKeywords.filter(keyword =>
                text.includes(keyword.toLowerCase())
              );
              isCorrect = keywordMatches.length >= Math.ceil(question.openTextKeywords.length * 0.6);
            }
            break;

          case 'ordering':
            if (question.orderingItems && userAnswer.orderedItems) {
              const correctOrder = question.orderingItems
                .sort((a, b) => a.correctOrder - b.correctOrder)
                .map(item => item.id);
              isCorrect = JSON.stringify(userAnswer.orderedItems) === JSON.stringify(correctOrder);
            }
            break;

          case 'matching':
            if (question.matchingPairs && userAnswer.matchedPairs) {
              const correctMatches = question.matchingPairs.reduce((acc: any, pair) => {
                acc[pair.id] = pair.id;
                return acc;
              }, {});
              isCorrect = JSON.stringify(userAnswer.matchedPairs) === JSON.stringify(correctMatches);
            }
            break;
        }
      }

      console.log(`🔍 Análise pergunta ${question.title}:`, {
        questionId: question.id,
        selectedAttemptId: selectedAttempt?.id,
        selectedAttemptNumber: selectedAttempt?.attemptNumber,
        userAnswerIsCorrect: userAnswer?.isCorrect,
        recalculatedIsCorrect: isCorrect,
        pointsEarned: userAnswer?.pointsEarned ?? 0,
        userAnswerFull: userAnswer,
        questionType: question.type,
        correctOptions: question.options?.filter(opt => opt.isCorrect).map(opt => opt.id),
        selectedOptions: userAnswer?.selectedOptions
      });

      return {
        questionId: question.id,
        questionTitle: question.title,
        questionType: question.type,
        userAnswer: userAnswer || null,
        correctAnswer,
        isCorrect,
        pointsEarned: userAnswer?.pointsEarned ?? 0,
        pointsPossible: question.points || 1,
        timeSpent: userAnswer?.timeSpent
      };
    });

    return analysis;
  };

  // Calcular estatísticas do quiz
  const calculateStatistics = (): QuizStatistics => {
    const analysis = calculateQuestionAnalysis();
    const totalQuestions = analysis.length;
    const correctAnswers = analysis.filter(a => a.isCorrect).length;
    const incorrectAnswers = totalQuestions - correctAnswers;
    const accuracyRate = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;
    
    const totalTimeSpent = selectedAttempt?.timeSpent || 0;
    const averageTimePerQuestion = totalQuestions > 0 ? totalTimeSpent / totalQuestions : 0;

    // Estatísticas por tipo de pergunta
    const scoreByType: Record<string, { correct: number; total: number }> = {};
    analysis.forEach(a => {
      if (!scoreByType[a.questionType]) {
        scoreByType[a.questionType] = { correct: 0, total: 0 };
      }
      scoreByType[a.questionType].total++;
      if (a.isCorrect) {
        scoreByType[a.questionType].correct++;
      }
    });

    return {
      totalQuestions,
      correctAnswers,
      incorrectAnswers,
      accuracyRate,
      averageTimePerQuestion,
      totalTimeSpent,
      scoreByType
    };
  };

  // Gerar feedback personalizado
  const generatePersonalizedFeedback = (stats: QuizStatistics): string => {
    const { accuracyRate, scoreByType, totalQuestions, correctAnswers } = stats;
    const passed = selectedAttempt?.passed || false;
    const passingScore = quizContent.config.passingScore;

    if (passed) {
      if (accuracyRate >= 95) {
        return "🏆 Desempenho excepcional! Você demonstrou domínio completo do conteúdo.";
      } else if (accuracyRate >= 85) {
        return "🎉 Excelente trabalho! Você teve um desempenho muito sólido.";
      } else if (accuracyRate >= passingScore + 10) {
        return "👏 Muito bom! Você superou a nota mínima com folga.";
      } else {
        return "✅ Parabéns! Você foi aprovado. Continue estudando para melhorar ainda mais.";
      }
    } else {
      const pointsNeeded = Math.ceil((passingScore / 100) * totalQuestions) - correctAnswers;

      if (accuracyRate >= passingScore - 10) {
        return `📈 Você está quase lá! Precisa de apenas ${pointsNeeded} acerto${pointsNeeded !== 1 ? 's' : ''} a mais para ser aprovado.`;
      }

      // Identificar tipo de pergunta com menor desempenho
      const weakestType = Object.entries(scoreByType)
        .filter(([_, data]) => data.total > 0)
        .map(([type, data]) => ({
          type: getQuestionTypeLabel(type),
          rate: data.correct / data.total,
          total: data.total
        }))
        .sort((a, b) => a.rate - b.rate)[0];

      if (weakestType && weakestType.rate < 0.5) {
        return `📚 Foque em revisar questões de "${weakestType.type}". Este foi seu ponto mais fraco.`;
      } else {
        return `📖 Continue estudando! Você precisa de ${pointsNeeded} acerto${pointsNeeded !== 1 ? 's' : ''} a mais para atingir ${passingScore}%.`;
      }
    }
  };

  // Recalcular análises sempre que selectedAttempt mudar
  const questionAnalysis = useMemo(() => {
    return calculateQuestionAnalysis();
  }, [selectedAttempt, quizContent.questions]);

  const statistics = useMemo(() => {
    return calculateStatistics();
  }, [questionAnalysis, selectedAttempt]);

  const personalizedFeedback = useMemo(() => {
    const feedback = generatePersonalizedFeedback(statistics);
    console.log('💬 Recalculando feedback:', {
      selectedAttemptId: selectedAttempt?.id,
      attemptNumber: selectedAttempt?.attemptNumber,
      feedback: feedback.substring(0, 50) + '...'
    });
    return feedback;
  }, [statistics, selectedAttempt, quizContent.config.passingScore]);



  if (!currentAttempt) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Resultado Detalhado - {quizContent.config.title}
          </DialogTitle>
          <DialogDescription>
            Análise completa do seu desempenho no quiz
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Resumo Geral */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-4 h-4" />
                Resumo Geral
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {selectedAttempt?.percentage?.toFixed(1) || '0.0'}%
                  </div>
                  <div className="text-sm text-gray-600">Nota Final</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {statistics.correctAnswers}/{statistics.totalQuestions}
                  </div>
                  <div className="text-sm text-gray-600">Acertos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {formatTime(statistics.totalTimeSpent)}
                  </div>
                  <div className="text-sm text-gray-600">Tempo Total</div>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Progresso</span>
                  <span className="text-sm text-gray-600">
                    {statistics.accuracyRate.toFixed(1)}% de acertos
                  </span>
                </div>
                <Progress value={statistics.accuracyRate} className="w-full" />
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">{personalizedFeedback}</p>
              </div>
            </CardContent>
          </Card>

          {/* Estatísticas por Tipo */}
          {Object.keys(statistics.scoreByType).length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Desempenho por Tipo de Pergunta
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(statistics.scoreByType).map(([type, data]) => {
                    const percentage = data.total > 0 ? (data.correct / data.total) * 100 : 0;
                    return (
                      <div key={type} className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          {getQuestionTypeLabel(type)}
                        </span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">
                            {data.correct}/{data.total}
                          </span>
                          <div className="w-20">
                            <Progress value={percentage} className="h-2" />
                          </div>
                          <span className="text-sm font-medium w-12 text-right">
                            {percentage.toFixed(0)}%
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Análise por Pergunta */}
          {(quizContent.config.showCorrectAnswers || quizContent.config.showFeedback) && questionAnalysis.length > 0 && (
            <Card className="border-2 border-dashed border-blue-200">
              <CardHeader className="bg-blue-50">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Análise por Pergunta
                  {selectedAttempt && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-normal text-gray-600">-</span>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">
                        Tentativa {selectedAttempt.attemptNumber}
                      </Badge>
                      <Badge variant={selectedAttempt.passed ? "default" : "destructive"} className="text-xs">
                        {selectedAttempt.percentage.toFixed(1)}%
                      </Badge>
                    </div>
                  )}
                </CardTitle>
                {selectedAttempt && (
                  <div className="text-sm text-blue-700 bg-blue-100 px-3 py-2 rounded-md mt-2">
                    📊 Visualizando respostas da Tentativa {selectedAttempt.attemptNumber} •
                    {statistics.correctAnswers}/{statistics.totalQuestions} acertos •
                    {selectedAttempt.passed ? '✅ Aprovado' : '❌ Não aprovado'}
                  </div>
                )}
              </CardHeader>
              <CardContent>
                {isLoadingAttempt ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center gap-2 text-gray-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span>Carregando análise...</span>
                    </div>
                  </div>
                ) : (
                  <div className={`space-y-4 transition-all duration-300 ${isLoadingAttempt ? 'opacity-50' : 'opacity-100'}`}
                       key={selectedAttempt?.id} // Força re-render quando tentativa muda
                  >
                    {questionAnalysis.map((analysis, index) => (
                    <div key={`${selectedAttempt?.id}-${analysis.questionId}`}
                         className={`border rounded-lg p-4 transition-all duration-300 ${
                           analysis.isCorrect
                             ? 'border-green-200 bg-green-50'
                             : 'border-red-200 bg-red-50'
                         }`}>
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant={analysis.isCorrect ? "default" : "destructive"}>
                              {analysis.isCorrect ? <CheckCircle className="w-3 h-3" /> : <XCircle className="w-3 h-3" />}
                            </Badge>
                            <span className="text-sm font-medium">
                              Pergunta {index + 1} - {getQuestionTypeLabel(analysis.questionType)}
                            </span>
                            <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700">
                              T{selectedAttempt?.attemptNumber}
                            </Badge>
                          </div>
                          <h4 className="font-medium text-gray-900 mb-2">
                            {analysis.questionTitle}
                          </h4>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">
                            {analysis.pointsEarned}/{analysis.pointsPossible} pts
                          </div>
                          {analysis.timeSpent && (
                            <div className="text-xs text-gray-500">
                              {formatTime(analysis.timeSpent)}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Resposta do usuário */}
                      <div className="mb-3">
                        <div className="text-sm font-medium text-gray-700 mb-1 flex items-center gap-2">
                          Sua resposta:
                          <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700">
                            T{selectedAttempt?.attemptNumber}
                          </Badge>
                        </div>
                        <div className={`text-sm p-3 rounded border transition-all duration-300 ${
                          analysis.isCorrect
                            ? 'bg-green-50 border-green-200'
                            : 'bg-red-50 border-red-200'
                        }`}
                        key={`${selectedAttempt?.id}-${analysis.questionId}-answer`} // Força re-render
                        >
                          {(() => {
                            console.log(`🔍 Renderizando resposta da pergunta ${index + 1}:`, {
                              questionId: analysis.questionId,
                              selectedAttemptId: selectedAttempt?.id,
                              selectedAttemptNumber: selectedAttempt?.attemptNumber,
                              hasUserAnswer: !!analysis.userAnswer,
                              userAnswerData: analysis.userAnswer,
                              isCorrect: analysis.isCorrect
                            });

                            if (!analysis.userAnswer) {
                              return <span className="text-gray-500 italic">❌ Não respondida</span>;
                            }

                            if (analysis.userAnswer.textAnswer) {
                              return (
                                <div className="flex items-start gap-2">
                                  <span className={analysis.isCorrect ? 'text-green-700' : 'text-red-700'}>
                                    {analysis.isCorrect ? '✅' : '❌'}
                                  </span>
                                  <span className="font-medium">
                                    "{analysis.userAnswer.textAnswer}"
                                  </span>
                                </div>
                              );
                            }

                            if (analysis.userAnswer.orderedItems && analysis.userAnswer.orderedItems.length > 0) {
                              // Renderizar resposta de ordenação
                              const question = quizContent.questions.find(q => q.id === analysis.questionId);
                              const orderedTexts = analysis.userAnswer.orderedItems.map(itemId => {
                                const item = question?.orderingItems?.find(item => item.id === itemId);
                                return item?.text || itemId;
                              });

                              return (
                                <div className="flex items-start gap-2">
                                  <span className={analysis.isCorrect ? 'text-green-700' : 'text-red-700'}>
                                    {analysis.isCorrect ? '✅' : '❌'}
                                  </span>
                                  <div className="font-medium">
                                    <div className="text-sm text-gray-600 mb-2">Ordem escolhida:</div>
                                    {orderedTexts.map((text, idx) => (
                                      <div key={`${selectedAttempt?.id}-${idx}-${text}`}
                                           className="mb-1 p-2 rounded bg-white/50 border border-gray-200 flex items-center gap-2">
                                        <span className="font-bold text-blue-600">{idx + 1}.</span>
                                        <span>{text}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              );
                            }

                            if (analysis.userAnswer.matchedPairs && Object.keys(analysis.userAnswer.matchedPairs).length > 0) {
                              // Renderizar resposta de correspondência
                              const question = quizContent.questions.find(q => q.id === analysis.questionId);
                              const matches = Object.entries(analysis.userAnswer.matchedPairs);

                              return (
                                <div className="flex items-start gap-2">
                                  <span className={analysis.isCorrect ? 'text-green-700' : 'text-red-700'}>
                                    {analysis.isCorrect ? '✅' : '❌'}
                                  </span>
                                  <div className="font-medium">
                                    <div className="text-sm text-gray-600 mb-2">Correspondências escolhidas:</div>
                                    {matches.map(([leftId, rightId], idx) => {
                                      const leftPair = question?.matchingPairs?.find(p => p.id === leftId);
                                      const rightPair = question?.matchingPairs?.find(p => p.id === rightId);
                                      return (
                                        <div key={`${selectedAttempt?.id}-${idx}-${leftId}-${rightId}`}
                                             className="mb-1 p-2 rounded bg-white/50 border border-gray-200 flex items-center gap-2">
                                          <span className="text-blue-600">{leftPair?.left || leftId}</span>
                                          <span className="text-gray-400">↔</span>
                                          <span className="text-green-600">{rightPair?.right || rightId}</span>
                                        </div>
                                      );
                                    })}
                                  </div>
                                </div>
                              );
                            }

                            if (analysis.userAnswer.selectedOptions && analysis.userAnswer.selectedOptions.length > 0) {
                              // Buscar texto das opções selecionadas
                              const question = quizContent.questions.find(q => q.id === analysis.questionId);
                              const selectedTexts = analysis.userAnswer.selectedOptions.map(optionId => {
                                const option = question?.options?.find(opt => opt.id === optionId);
                                return option?.text || optionId;
                              });

                              console.log(`🔍 Opções selecionadas para pergunta ${index + 1}:`, {
                                selectedOptions: analysis.userAnswer.selectedOptions,
                                selectedTexts: selectedTexts,
                                selectedTextsDetailed: selectedTexts.map((text, idx) => `${idx + 1}. ${text}`),
                                questionOptions: question?.options?.map(opt => ({ id: opt.id, text: opt.text, isCorrect: opt.isCorrect }))
                              });

                              return (
                                <div className="flex items-start gap-2">
                                  <span className={analysis.isCorrect ? 'text-green-700' : 'text-red-700'}>
                                    {analysis.isCorrect ? '✅' : '❌'}
                                  </span>
                                  <div className="font-medium">
                                    {selectedTexts.map((text, idx) => (
                                      <div key={`${selectedAttempt?.id}-${idx}-${text}`}
                                           className="mb-1 p-1 rounded bg-white/50 border border-gray-200">
                                        • {text}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              );
                            }

                            return <span className="text-gray-500 italic">❓ Resposta não registrada</span>;
                          })()}
                        </div>
                      </div>

                      {/* Resposta(s) correta(s) */}
                      {quizContent.config.showCorrectAnswers && analysis.correctAnswer.length > 0 && (
                        <div className="mb-3">
                          <div className="text-sm font-medium text-gray-700 mb-1">
                            Resposta{analysis.correctAnswer.length > 1 ? 's' : ''} correta{analysis.correctAnswer.length > 1 ? 's' : ''}:
                          </div>
                          <div className="text-sm bg-green-50 border border-green-200 p-3 rounded">
                            <div className="flex items-start gap-2">
                              <span className="text-green-700">✅</span>
                              <div className="font-medium text-green-800">
                                {analysis.correctAnswer.map((answer, idx) => (
                                  <div key={idx} className="mb-1">• {answer}</div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}



                      {/* Feedback explicativo */}
                      {quizContent.config.showFeedback && (() => {
                        const question = quizContent.questions.find(q => q.id === analysis.questionId);
                        const feedback = analysis.userAnswer?.feedback || question?.explanation;

                        if (feedback) {
                          return (
                            <div className="mb-3">
                              <div className="text-sm font-medium text-gray-700 mb-1">
                                Feedback:
                              </div>
                              <div className="text-sm bg-blue-50 border border-blue-200 p-3 rounded">
                                <div className="flex items-start gap-2">
                                  <span className="text-blue-600">💡</span>
                                  <span className="text-blue-800">{feedback}</span>
                                </div>
                              </div>
                            </div>
                          );
                        }
                        return null;
                      })()}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Resultado Oficial vs Histórico de Tentativas */}
          {allAttempts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {allAttempts.length === 1 ? 'Tentativa Realizada' : 'Histórico de Tentativas'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Indicador da tentativa sendo visualizada */}
                  {(() => {
                    const officialAttempt = allAttempts.length > 0 ? getOfficialAttempt(allAttempts) : currentAttempt;
                    return selectedAttempt && selectedAttempt.id !== officialAttempt?.id;
                  })() && (
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-amber-700 text-sm">
                            👁️ Visualizando: Tentativa {selectedAttempt.attemptNumber}
                          </span>
                        </div>
                        <button
                          onClick={handleBackToOfficial}
                          className="text-xs text-amber-700 hover:text-amber-800 underline"
                        >
                          Voltar ao Resultado Oficial
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Resultado Oficial (destacado) */}
                  {(() => {
                    const officialAttempt = allAttempts.length > 0 ? getOfficialAttempt(allAttempts) : currentAttempt;

                    if (!officialAttempt) return null;

                    return (
                      <div className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        selectedAttempt?.id === officialAttempt.id
                          ? 'border-blue-300 bg-blue-50 shadow-md'
                          : 'border-blue-200 bg-blue-50 hover:border-blue-300 hover:shadow-sm'
                      }`}
                      onClick={() => handleSelectAttempt(officialAttempt)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge variant={officialAttempt.passed ? "default" : "destructive"} className="text-xs">
                              <Trophy className="w-3 h-3 mr-1" />
                              Resultado Oficial
                            </Badge>
                            <span className="text-sm font-medium text-blue-800">
                              Tentativa {officialAttempt.attemptNumber}
                            </span>
                            {selectedAttempt?.id === officialAttempt.id && (
                              <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                Visualizando
                              </span>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-blue-800">
                              {officialAttempt.percentage.toFixed(1)}% - {officialAttempt.passed ? 'Aprovado' : 'Não aprovado'}
                            </div>
                            <div className="text-xs text-blue-600">
                              {new Date(officialAttempt.completedAt || officialAttempt.startedAt).toLocaleString()}
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-between text-sm text-blue-700">
                          <span>{officialAttempt.score}/{officialAttempt.totalScore || officialAttempt.maxScore} pontos</span>
                          <span>{formatTime(officialAttempt.timeSpent || 0)}</span>
                        </div>
                      </div>
                    );
                  })()}

                  {/* Tentativas Anteriores */}
                  {allAttempts.length > 1 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Tentativas Anteriores</h4>
                      <div className="space-y-2">
                        {allAttempts
                          .filter(attempt => attempt.id !== currentAttempt?.id)
                          .map((attempt, index) => (
                            <div
                              key={attempt.id}
                              className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-all ${
                                selectedAttempt?.id === attempt.id
                                  ? 'bg-gray-100 border-gray-300 shadow-md'
                                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-gray-300 hover:shadow-sm'
                              }`}
                              onClick={() => handleSelectAttempt(attempt)}
                            >
                              <div className="flex items-center gap-3">
                                <Badge variant="outline" className="text-xs">
                                  Tentativa {attempt.attemptNumber}
                                </Badge>
                                {selectedAttempt?.id === attempt.id && (
                                  <span className="text-xs text-gray-600 bg-gray-200 px-2 py-1 rounded">
                                    Visualizando
                                  </span>
                                )}
                                <div>
                                  <div className="text-sm">
                                    {attempt.percentage.toFixed(1)}% - {attempt.passed ? 'Aprovado' : 'Não aprovado'}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {new Date(attempt.completedAt || attempt.startedAt).toLocaleString()}
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-sm">
                                  {attempt.score}/{attempt.totalScore || attempt.maxScore} pts
                                </div>
                                <div className="text-xs text-gray-500">
                                  {formatTime(attempt.timeSpent || 0)}
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Botões de Ação */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              <X className="w-4 h-4 mr-2" />
              Fechar
            </Button>
            {/* TODO: Implementar funcionalidade de impressão/exportação */}
            {/* <Button variant="outline">
              <FileText className="w-4 h-4 mr-2" />
              Exportar PDF
            </Button> */}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

-- =====================================================
-- SCRIPT DE ROLLBACK SEGURO
-- =====================================================
-- Remove todas as tabelas, políticas e dados criados
-- Use apenas em caso de necessidade de limpeza total
-- Versão: 2.1 - Julho 2025 (Versão Segura)

-- AVISO: Este script irá remover TODOS os dados!
DO $$
BEGIN
  RAISE NOTICE '⚠️ ATENÇÃO: Este script irá remover TODOS os dados!';
  RAISE NOTICE '📅 Iniciado em: %', NOW();
END $$;

-- =====================================================
-- FUNÇÃO AUXILIAR PARA ROLLBACK SEGURO
-- =====================================================

CREATE OR REPLACE FUNCTION public.safe_rollback()
RETURNS void AS $$
DECLARE
  sql_command TEXT;
  error_message TEXT;
BEGIN
  -- =====================================================
  -- REMOVER TRIGGERS
  -- =====================================================
  
  BEGIN
    DROP TRIGGER IF EXISTS trigger_task_update_stage_progress ON public.tasks;
    DROP TRIGGER IF EXISTS trigger_stage_update_project_progress ON public.stages;
    DROP TRIGGER IF EXISTS trigger_profiles_update_timestamp ON public.profiles;
    DROP TRIGGER IF EXISTS trigger_projects_update_timestamp ON public.projects;
    DROP TRIGGER IF EXISTS trigger_stages_update_timestamp ON public.stages;
    DROP TRIGGER IF EXISTS trigger_tasks_update_timestamp ON public.tasks;
    DROP TRIGGER IF EXISTS trigger_quiz_attempt_stats ON public.quiz_attempts;
    RAISE NOTICE '✅ Triggers removidos';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover triggers: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER FUNCTIONS
  -- =====================================================
  
  BEGIN
    DROP FUNCTION IF EXISTS public.calculate_project_progress(uuid);
    DROP FUNCTION IF EXISTS public.calculate_stage_progress(uuid);
    DROP FUNCTION IF EXISTS public.create_notification(uuid, text, text, text, text);
    DROP FUNCTION IF EXISTS public.update_quiz_statistics(uuid);
    DROP FUNCTION IF EXISTS public.trigger_update_stage_progress();
    DROP FUNCTION IF EXISTS public.trigger_update_project_progress();
    DROP FUNCTION IF EXISTS public.trigger_update_timestamp();
    DROP FUNCTION IF EXISTS public.trigger_quiz_stats_update();
    DROP FUNCTION IF EXISTS public.cleanup_temp_files();
    DROP FUNCTION IF EXISTS public.get_signed_url(text, text, integer);
    DROP FUNCTION IF EXISTS public.can_access_file(text, text, uuid);
    RAISE NOTICE '✅ Functions removidas';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover functions: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER VIEWS
  -- =====================================================
  
  BEGIN
    DROP VIEW IF EXISTS public.project_stats;
    DROP VIEW IF EXISTS public.task_details;
    DROP VIEW IF EXISTS public.user_stats;
    DROP VIEW IF EXISTS public.quiz_dashboard;
    RAISE NOTICE '✅ Views removidas';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover views: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER POLÍTICAS RLS
  -- =====================================================
  
  BEGIN
    -- Desabilitar RLS primeiro
    ALTER TABLE IF EXISTS public.quiz_statistics DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.user_quiz_progress DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.quiz_answers DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.quiz_attempts DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.quizzes DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.user_notifications DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.project_history DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.task_comments DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.evidence DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.task_attachments DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.task_content_blocks DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.task_approvers DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.task_executors DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.project_members DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.tasks DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.stages DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.projects DISABLE ROW LEVEL SECURITY;
    ALTER TABLE IF EXISTS public.profiles DISABLE ROW LEVEL SECURITY;
    
    -- Remover todas as políticas
    DROP POLICY IF EXISTS "profiles_select" ON public.profiles;
    DROP POLICY IF EXISTS "profiles_insert" ON public.profiles;
    DROP POLICY IF EXISTS "profiles_update" ON public.profiles;
    DROP POLICY IF EXISTS "projects_select" ON public.projects;
    DROP POLICY IF EXISTS "projects_insert" ON public.projects;
    DROP POLICY IF EXISTS "projects_update" ON public.projects;
    DROP POLICY IF EXISTS "projects_delete" ON public.projects;
    DROP POLICY IF EXISTS "stages_select" ON public.stages;
    DROP POLICY IF EXISTS "stages_insert" ON public.stages;
    DROP POLICY IF EXISTS "stages_update" ON public.stages;
    DROP POLICY IF EXISTS "stages_delete" ON public.stages;
    DROP POLICY IF EXISTS "tasks_select" ON public.tasks;
    DROP POLICY IF EXISTS "tasks_insert" ON public.tasks;
    DROP POLICY IF EXISTS "tasks_update" ON public.tasks;
    DROP POLICY IF EXISTS "tasks_delete" ON public.tasks;
    
    RAISE NOTICE '✅ Políticas RLS removidas';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover políticas RLS: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER OBJETOS DO STORAGE
  -- =====================================================
  
  BEGIN
    -- Remover objetos dos buckets primeiro
    DELETE FROM storage.objects WHERE bucket_id IN ('task-files', 'evidence-files', 'avatars', 'temp-files');
    RAISE NOTICE '✅ Objetos do storage removidos';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover objetos do storage: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER POLÍTICAS DE STORAGE
  -- =====================================================
  
  BEGIN
    DROP POLICY IF EXISTS "task_files_select" ON storage.objects;
    DROP POLICY IF EXISTS "task_files_insert" ON storage.objects;
    DROP POLICY IF EXISTS "task_files_update" ON storage.objects;
    DROP POLICY IF EXISTS "task_files_delete" ON storage.objects;
    DROP POLICY IF EXISTS "evidence_files_select" ON storage.objects;
    DROP POLICY IF EXISTS "evidence_files_insert" ON storage.objects;
    DROP POLICY IF EXISTS "evidence_files_update" ON storage.objects;
    DROP POLICY IF EXISTS "evidence_files_delete" ON storage.objects;
    DROP POLICY IF EXISTS "avatars_select" ON storage.objects;
    DROP POLICY IF EXISTS "avatars_insert" ON storage.objects;
    DROP POLICY IF EXISTS "avatars_update" ON storage.objects;
    DROP POLICY IF EXISTS "avatars_delete" ON storage.objects;
    DROP POLICY IF EXISTS "temp_files_select" ON storage.objects;
    DROP POLICY IF EXISTS "temp_files_insert" ON storage.objects;
    DROP POLICY IF EXISTS "temp_files_update" ON storage.objects;
    DROP POLICY IF EXISTS "temp_files_delete" ON storage.objects;
    RAISE NOTICE '✅ Políticas de storage removidas';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover políticas de storage: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER BUCKETS DE STORAGE
  -- =====================================================
  
  BEGIN
    DELETE FROM storage.buckets WHERE id IN ('task-files', 'evidence-files', 'avatars', 'temp-files');
    RAISE NOTICE '✅ Buckets de storage removidos';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover buckets: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER TABELAS EM ORDEM REVERSA
  -- =====================================================
  
  BEGIN
    DROP TABLE IF EXISTS public.quiz_statistics CASCADE;
    DROP TABLE IF EXISTS public.user_quiz_progress CASCADE;
    DROP TABLE IF EXISTS public.quiz_answers CASCADE;
    DROP TABLE IF EXISTS public.quiz_attempts CASCADE;
    DROP TABLE IF EXISTS public.quizzes CASCADE;
    DROP TABLE IF EXISTS public.user_notifications CASCADE;
    DROP TABLE IF EXISTS public.project_history CASCADE;
    DROP TABLE IF EXISTS public.task_comments CASCADE;
    DROP TABLE IF EXISTS public.evidence CASCADE;
    DROP TABLE IF EXISTS public.task_attachments CASCADE;
    DROP TABLE IF EXISTS public.task_content_blocks CASCADE;
    DROP TABLE IF EXISTS public.task_approvers CASCADE;
    DROP TABLE IF EXISTS public.task_executors CASCADE;
    DROP TABLE IF EXISTS public.project_members CASCADE;
    DROP TABLE IF EXISTS public.tasks CASCADE;
    DROP TABLE IF EXISTS public.stages CASCADE;
    DROP TABLE IF EXISTS public.projects CASCADE;
    DROP TABLE IF EXISTS public.profiles CASCADE;
    RAISE NOTICE '✅ Tabelas removidas';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover tabelas: %', SQLERRM;
  END;

  -- =====================================================
  -- REMOVER TIPOS ENUM
  -- =====================================================
  
  BEGIN
    DROP TYPE IF EXISTS public.project_member_role CASCADE;
    DROP TYPE IF EXISTS public.task_status CASCADE;
    DROP TYPE IF EXISTS public.stage_status CASCADE;
    DROP TYPE IF EXISTS public.project_status CASCADE;
    RAISE NOTICE '✅ Tipos ENUM removidos';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE '⚠️ Erro ao remover tipos ENUM: %', SQLERRM;
  END;

  RAISE NOTICE '🧹 Rollback seguro concluído!';
  RAISE NOTICE '📅 Finalizado em: %', NOW();
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- EXECUTAR ROLLBACK SEGURO
-- =====================================================

SELECT public.safe_rollback();

-- =====================================================
-- LIMPEZA FINAL
-- =====================================================

DROP FUNCTION IF EXISTS public.safe_rollback();

-- =====================================================
-- VALIDAÇÃO FINAL
-- =====================================================

DO $$
DECLARE
  remaining_tables INTEGER;
  remaining_policies INTEGER;
  remaining_buckets INTEGER;
  remaining_objects INTEGER;
BEGIN
  -- Contar elementos restantes
  SELECT COUNT(*) INTO remaining_tables
  FROM information_schema.tables
  WHERE table_schema = 'public'
    AND table_name IN (
      'profiles', 'projects', 'stages', 'tasks', 'project_members',
      'task_executors', 'task_approvers', 'task_content_blocks',
      'task_attachments', 'evidence', 'task_comments', 'project_history',
      'user_notifications', 'quizzes', 'quiz_attempts', 'quiz_answers',
      'user_quiz_progress', 'quiz_statistics'
    );
  
  SELECT COUNT(*) INTO remaining_policies
  FROM pg_policies
  WHERE schemaname = 'public';
  
  SELECT COUNT(*) INTO remaining_buckets
  FROM storage.buckets
  WHERE id IN ('task-files', 'evidence-files', 'avatars', 'temp-files');
  
  SELECT COUNT(*) INTO remaining_objects
  FROM storage.objects
  WHERE bucket_id IN ('task-files', 'evidence-files', 'avatars', 'temp-files');
  
  RAISE NOTICE '📊 RESULTADO FINAL:';
  RAISE NOTICE '📋 Tabelas restantes: %', remaining_tables;
  RAISE NOTICE '🔒 Políticas restantes: %', remaining_policies;
  RAISE NOTICE '🗂️ Buckets restantes: %', remaining_buckets;
  RAISE NOTICE '📁 Objetos restantes: %', remaining_objects;
  
  IF remaining_tables = 0 AND remaining_policies = 0 AND remaining_buckets = 0 AND remaining_objects = 0 THEN
    RAISE NOTICE '✅ LIMPEZA COMPLETA REALIZADA COM SUCESSO!';
  ELSE
    RAISE NOTICE '⚠️ Alguns elementos podem não ter sido removidos completamente';
  END IF;
END $$;

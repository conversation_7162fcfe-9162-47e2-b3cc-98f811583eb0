import React, { useState } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { useIsMobile } from '@/hooks/ui/use-mobile';

interface UserAutocompleteProps {
  onSelect: (user: { id: string; name: string; email: string; avatar_url?: string }) => void;
  excludeIds?: string[];
  users?: any[];
}

export const UserAutocomplete: React.FC<UserAutocompleteProps> = ({ onSelect, excludeIds = [], users }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const isMobile = useIsMobile();

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    // Se temos usuários do projeto, filtra eles primeiro
    if (users && users.length > 0) {
      if (value.length === 0) {
        // Mostra todos os membros do projeto quando não há busca
        setResults(users);
      } else {
        // Filtra pelos membros do projeto
        setResults(users.filter(u => u.name?.toLowerCase().includes(value.toLowerCase())));
      }
      return;
    }
    
    // Fallback para busca geral se não há usuários do projeto
    if (value.length < 2) {
      setResults([]);
      return;
    }
    
    setLoading(true);
    const { data, error } = await supabase
      .from('profiles')
      .select('id, name, email, avatar_url')
      .ilike('name', `%${value}%`);
    setResults(data || []);
    setLoading(false);
  };

  // Carrega usuários do projeto automaticamente quando disponível
  React.useEffect(() => {
    if (users && users.length > 0 && query.length === 0) {
      setResults(users);
    }
  }, [users, query]);

  const filteredResults = results.filter(user => !excludeIds.includes(user.id));

  // Define a posição do dropdown dinamicamente
  const dropdownPositionClass = isMobile && isFocused
    ? 'bottom-full mb-2 left-0 right-0'
    : 'top-full mt-2 left-0 right-0';

  return (
    <div className="relative">
      <input
        className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm mb-2 w-full min-w-0"
        placeholder={users && users.length > 0 ? `Buscar entre ${users.length} membros do projeto...` : "Buscar e adicionar membros..."}
        value={query}
        onChange={handleSearch}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setTimeout(() => setIsFocused(false), 150)}
      />
      {loading && <div className="absolute left-0 right-0 bg-white p-2 text-sm z-50">Carregando...</div>}
      {filteredResults.length > 0 && isFocused && (
        <ul
          className={`absolute ${dropdownPositionClass} bg-white border rounded shadow z-50 max-h-60 overflow-y-auto`}
        >
          {filteredResults.map((user) => (
            <li
              key={user.id}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onMouseDown={e => e.preventDefault()}
              onClick={() => {
                onSelect({ ...user, avatar_url: user.avatar_url });
                setQuery('');
                setResults([]);
                setIsFocused(false);
              }}
            >
              <div className="flex items-center gap-2 min-w-0">
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold text-base">
                  {user.name && user.name.length > 0 ? user.name.charAt(0).toUpperCase() : '?'}
                </div>
                <div className="flex flex-col min-w-0">
                  <span className="truncate font-medium text-base">{user.name}</span>
                  <span className="truncate text-xs text-gray-500">{user.email}</span>
                </div>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
import React, { useState } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { useIsMobile } from '@/hooks/ui/use-mobile';
import { userService } from '@/services/userService';

interface UserAutocompleteProps {
  onSelect: (user: { id: string; name: string; email: string; avatar_url?: string }) => void;
  excludeIds?: string[];
  users?: any[];
}

export const UserAutocomplete: React.FC<UserAutocompleteProps> = ({ onSelect, excludeIds = [], users }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const isMobile = useIsMobile();

  console.log('[UserAutocomplete] Renderizando com:', { 
    usersLength: users?.length || 0, 
    excludeIdsLength: excludeIds.length,
    query 
  });

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    console.log('[UserAutocomplete] Buscando:', value, 'Usuários do projeto:', users?.length || 0);
    
    // Se temos usuários do projeto, mostra eles primeiro quando não há busca
    if (users && users.length > 0 && value.length === 0) {
      // Mostra todos os membros do projeto quando não há busca
      setResults(users);
      console.log('[UserAutocomplete] Mostrando todos os membros do projeto:', users.length);
      return;
    }
    
    // Se há busca, filtra membros do projeto primeiro, mas também busca outros usuários
    if (users && users.length > 0 && value.length > 0) {
      // Filtra pelos membros do projeto
      const filtered = users.filter(u => 
        u.name?.toLowerCase().includes(value.toLowerCase()) ||
        u.email?.toLowerCase().includes(value.toLowerCase())
      );
      
      if (filtered.length > 0) {
        setResults(filtered);
        console.log('[UserAutocomplete] Resultados filtrados dos membros do projeto:', filtered.length);
        return;
      }
      
      // Se não encontrou nos membros do projeto, continua para busca geral
      console.log('[UserAutocomplete] Nenhum membro do projeto corresponde, buscando todos os usuários...');
    }
    
    // Busca geral (fallback) - busca todos os usuários disponíveis
    if (value.length < 2) {
      setResults([]);
      return;
    }
    
    console.log('[UserAutocomplete] Fazendo busca no Supabase...');
    setLoading(true);
    try {
      // Primeiro, tentar usar userService
      try {
        const serviceUsers = await userService.list({ search: value });
        console.log('[UserAutocomplete] userService funcionou:', serviceUsers.length);
        setResults(serviceUsers);
        setLoading(false);
        return;
      } catch (serviceError) {
        console.warn('[UserAutocomplete] userService falhou, tentando Supabase direto:', serviceError);
      }
      
      // Fallback: busca direta no Supabase
      const { data, error } = await supabase
        .from('profiles')
        .select('id, name, email, avatar_url')
        .or(`name.ilike.%${value}%,email.ilike.%${value}%`)
        .limit(10);
      
      if (error) {
        console.error('[UserAutocomplete] Erro na busca direta:', error);
        setResults([]);
      } else {
        console.log('[UserAutocomplete] Busca direta funcionou:', data?.length || 0);
        setResults(data || []);
      }
    } catch (err) {
      console.error('[UserAutocomplete] Erro inesperado:', err);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Carrega usuários do projeto automaticamente quando disponível
  React.useEffect(() => {
    console.log('[UserAutocomplete] Effect disparado:', { 
      hasUsers: !!users, 
      usersLength: users?.length || 0, 
      queryLength: query.length 
    });
    
    if (users && users.length > 0 && query.length === 0) {
      console.log('[UserAutocomplete] Carregando todos os usuários do projeto:', users);
      setResults(users);
    }
  }, [users, query]);

  const filteredResults = results.filter(user => !excludeIds.includes(user.id));
  
  console.log('[UserAutocomplete] Resultados filtrados:', {
    totalResults: results.length,
    filteredResults: filteredResults.length,
    excludeIds,
    isFocused,
    showDropdown: filteredResults.length > 0 && isFocused
  });

  // Define a posição do dropdown dinamicamente
  const dropdownPositionClass = isMobile && isFocused
    ? 'bottom-full mb-2 left-0 right-0'
    : 'top-full mt-2 left-0 right-0';

  return (
    <div className="relative">
      <input
        className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm mb-2 w-full min-w-0"
        placeholder={users && users.length > 0 ? `Buscar entre ${users.length} membros do projeto...` : "Buscar e adicionar membros..."}
        value={query}
        onChange={handleSearch}
        onFocus={() => setIsFocused(true)}
        onBlur={() => {
          console.log('[UserAutocomplete] onBlur disparado');
          setTimeout(() => {
            console.log('[UserAutocomplete] Perdendo foco após timeout');
            setIsFocused(false);
          }, 300);
        }}
      />
      {loading && <div className="absolute left-0 right-0 bg-white p-2 text-sm z-50">Carregando...</div>}
      {filteredResults.length > 0 && isFocused && (
        <ul
          className={`absolute ${dropdownPositionClass} bg-white border rounded shadow z-50 max-h-60 overflow-y-auto`}
        >
          {filteredResults.map((user) => (
            <li
              key={user.id}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onMouseDown={e => e.preventDefault()}
              onClick={() => {
                onSelect({ ...user, avatar_url: user.avatar_url });
                setQuery('');
                setResults([]);
                setIsFocused(false);
              }}
            >
              <div className="flex items-center gap-2 min-w-0">
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold text-base">
                  {user.name && user.name.length > 0 ? user.name.charAt(0).toUpperCase() : '?'}
                </div>
                <div className="flex flex-col min-w-0">
                  <span className="truncate font-medium text-base">{user.name}</span>
                  <span className="truncate text-xs text-gray-500">{user.email}</span>
                </div>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
import { Editor } from '@tiptap/react';

/**
 * Utilitários para trabalhar com tabelas no Tiptap
 */

export interface TableElements {
  tableElement: HTMLElement | null;
  cellElement: HTMLElement | null;
  rowElement: HTMLElement | null;
  columnIndex: number;
}

/**
 * Encontra os elementos DOM da tabela atual
 */
export function findTableElements(editor: Editor): TableElements {
  const { view, state } = editor;
  const { selection } = state;
  const { $from } = selection;

  let tableElement: HTMLElement | null = null;
  let cellElement: HTMLElement | null = null;
  let rowElement: HTMLElement | null = null;
  let columnIndex = -1;

  try {
    // Encontrar elementos usando a estrutura do Tiptap
    for (let depth = $from.depth; depth > 0; depth--) {
      const node = $from.node(depth);

      if (node.type.name === 'table') {
        const tablePos = $from.before(depth);
        const domNode = view.nodeDOM(tablePos);

        if (domNode) {
          // SOLUÇÃO DEFINITIVA: Encontrar a tabela HTML real
          if (domNode instanceof HTMLTableElement) {
            // Já é uma tabela HTML
            tableElement = domNode;
          } else if (domNode instanceof HTMLElement) {
            // É um wrapper DIV - encontrar a tabela HTML dentro dele
            const realTable = domNode.querySelector('table');
            if (realTable) {
              tableElement = realTable;
            } else {
              // Fallback: usar o wrapper se não encontrar tabela HTML
              tableElement = domNode;
            }
          }
        }
      }
      
      if (node.type.name === 'tableCell' || node.type.name === 'tableHeader') {
        const cellPos = $from.start(depth);
        const domAtPos = view.domAtPos(cellPos);
        let element = domAtPos.node;
        
        // Navegar até encontrar TD ou TH
        while (element && element.nodeType !== Node.ELEMENT_NODE) {
          element = element.parentNode;
        }
        
        while (element && element.tagName !== 'TD' && element.tagName !== 'TH') {
          element = element.parentNode;
        }
        
        if (element && (element.tagName === 'TD' || element.tagName === 'TH')) {
          cellElement = element as HTMLElement;
          
          // Encontrar a linha pai
          let parent = cellElement.parentElement;
          while (parent && parent.tagName !== 'TR') {
            parent = parent.parentElement;
          }
          if (parent) {
            rowElement = parent;
            
            // Calcular índice da coluna
            const cells = Array.from(rowElement.children);
            columnIndex = cells.indexOf(cellElement);
          }
        }
      }
    }

    // Fallback: tentar encontrar usando seletores CSS
    if (!tableElement && editor.isActive('table')) {
      const editorElement = view.dom;

      // Primeiro: procurar por tabela HTML real
      tableElement = editorElement.querySelector('table');

      // Segundo: procurar por wrapper com classe tiptap-table
      if (!tableElement) {
        const wrapper = editorElement.querySelector('.tiptap-table');
        if (wrapper) {
          const realTable = wrapper.querySelector('table');
          tableElement = realTable || wrapper;
        }
      }

      // Terceiro: procurar por qualquer elemento com data-type="table"
      if (!tableElement) {
        const dataTypeTable = editorElement.querySelector('[data-type="table"]');
        if (dataTypeTable) {
          const realTable = dataTypeTable.querySelector('table');
          tableElement = realTable || dataTypeTable;
        }
      }
    }

    if (!cellElement && tableElement) {
      // Tentar encontrar célula ativa
      cellElement = tableElement.querySelector('td.ProseMirror-selectednode') ||
                   tableElement.querySelector('th.ProseMirror-selectednode') ||
                   tableElement.querySelector('td:focus') ||
                   tableElement.querySelector('th:focus');
    }

  } catch (error) {
    console.error('Erro ao encontrar elementos da tabela:', error);
  }

  return {
    tableElement,
    cellElement,
    rowElement,
    columnIndex
  };
}

/**
 * Aplica estilo a um elemento de forma segura
 * Remove classes conflitantes do Tailwind e aplica estilos inline com !important
 */
export function applyStyleSafely(element: HTMLElement | null, styles: Record<string, string>) {
  if (!element) return false;

  try {
    // APLICAÇÃO DIRETA E SIMPLES
    Object.entries(styles).forEach(([property, value]) => {
      if (value && value.trim()) {
        element.style.setProperty(property, value, 'important');
      }
    });

    return true;
  } catch (error) {
    console.error('Erro ao aplicar estilo:', error);
    return false;
  }
}

/**
 * SOLUÇÃO ULTRA-AGRESSIVA: Força aplicação de cor de fundo com múltiplas estratégias
 */
function forceBackgroundColor(element: HTMLElement, color: string) {
  console.log('🔧 FORÇANDO aplicação ULTRA-AGRESSIVA de background-color:', color);

  // ESTRATÉGIA 1: Remover TODAS as classes que podem interferir
  const allClasses = Array.from(element.classList);
  const backgroundClasses = allClasses.filter(cls =>
    cls.startsWith('bg-') || cls.includes('background') || cls.includes('gray') || cls.includes('white')
  );
  backgroundClasses.forEach(cls => {
    element.classList.remove(cls);
    console.log(`🗑️ Classe removida: ${cls}`);
  });

  // ESTRATÉGIA 2: Limpar completamente o estilo e reaplicar
  const currentStyle = element.getAttribute('style') || '';
  const cleanStyle = currentStyle
    .split(';')
    .filter(rule => !rule.includes('background'))
    .join(';');

  element.setAttribute('style', cleanStyle);

  // ESTRATÉGIA 3: Aplicar com múltiplas propriedades CSS
  const styleProperties = [
    `background-color: ${color} !important`,
    `background: ${color} !important`,
    `background-image: none !important`,
    `background-clip: padding-box !important`
  ];

  const newStyle = cleanStyle + '; ' + styleProperties.join('; ');
  element.setAttribute('style', newStyle);

  // ESTRATÉGIA 4: Usar CSS customizado via data-attribute
  element.setAttribute('data-force-bg', color);

  // ESTRATÉGIA 5: Criar elemento wrapper se necessário
  if (!element.style.backgroundColor) {
    const wrapper = document.createElement('div');
    wrapper.style.cssText = `background-color: ${color} !important; width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: -1;`;
    element.style.position = 'relative';
    element.insertBefore(wrapper, element.firstChild);
    console.log('🎭 Wrapper de background criado');
  }

  // ESTRATÉGIA 6: Forçar repaint múltiplas vezes
  element.style.display = 'none';
  element.offsetHeight;
  element.style.display = '';
  element.style.visibility = 'hidden';
  element.offsetHeight;
  element.style.visibility = 'visible';

  // VERIFICAÇÃO FINAL com múltiplas tentativas
  let attempts = 0;
  const maxAttempts = 5;

  const checkAndRetry = () => {
    attempts++;
    const finalStyle = window.getComputedStyle(element);
    const finalBg = finalStyle.backgroundColor;
    console.log(`🎨 Tentativa ${attempts}: Background computado: ${finalBg}`);

    // Converter cor hex para rgb para comparação
    const rgbColor = hexToRgb(color);
    const isApplied = finalBg.includes(rgbColor.r.toString()) &&
                     finalBg.includes(rgbColor.g.toString()) &&
                     finalBg.includes(rgbColor.b.toString());

    if (!isApplied && attempts < maxAttempts) {
      console.warn(`⚠️ Tentativa ${attempts} falhou, tentando novamente...`);
      // Reaplicar com força ainda maior
      element.style.cssText += `; background-color: ${color} !important; background: ${color} !important;`;
      setTimeout(checkAndRetry, 50);
    } else if (!isApplied) {
      console.error('❌ FALHA CRÍTICA FINAL: Investigando problema específico...');

      // DIAGNÓSTICO AVANÇADO: Verificar múltiplas propriedades CSS
      const computedStyle = window.getComputedStyle(element);
      const diagnostics = {
        backgroundColor: computedStyle.backgroundColor,
        background: computedStyle.background,
        backgroundImage: computedStyle.backgroundImage,
        backgroundClip: computedStyle.backgroundClip,
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,
        zIndex: computedStyle.zIndex,
        position: computedStyle.position
      };

      console.error('🔬 DIAGNÓSTICO AVANÇADO:', diagnostics);

      // Verificar se o problema é visual ou de detecção
      console.error('🎨 TESTE VISUAL: O background está visível na tela?');
      console.error('📊 Debug completo:', {
        expectedColor: color,
        expectedRgb: rgbColor,
        computedBackground: finalBg,
        elementClasses: Array.from(element.classList),
        elementStyle: element.getAttribute('style'),
        parentClasses: element.parentElement?.classList ? Array.from(element.parentElement.classList) : [],
        parentStyle: element.parentElement?.getAttribute('style'),
        diagnostics
      });

      // ÚLTIMA ESTRATÉGIA: Injetar CSS dinâmico específico para este elemento
      const elementId = element.id || `table-cell-${Date.now()}`;
      if (!element.id) element.id = elementId;

      injectDynamicCSS(elementId, color);

      // Verificar se o CSS dinâmico funcionou
      setTimeout(() => {
        const finalComputedStyle = window.getComputedStyle(element);
        const finalBgAfterCSS = finalComputedStyle.backgroundColor;
        console.log('🔄 Background após CSS dinâmico:', finalBgAfterCSS);

        if (finalBgAfterCSS && finalBgAfterCSS !== 'rgba(0, 0, 0, 0)' && finalBgAfterCSS !== 'transparent') {
          console.log('✅ CSS DINÂMICO FUNCIONOU!');
        } else {
          console.error('❌ PROBLEMA FUNDAMENTAL: Nem CSS dinâmico funcionou!');
        }
      }, 200);

    } else {
      console.log('✅ SUCESSO: Background-color aplicado com força!');
    }
  };

  setTimeout(checkAndRetry, 100);
}

/**
 * ÚLTIMA ESTRATÉGIA: Injeta CSS dinâmico específico para um elemento
 */
function injectDynamicCSS(elementId: string, color: string) {
  console.log('💉 INJETANDO CSS dinâmico para elemento:', elementId, color);

  // Remover CSS anterior se existir
  const existingStyle = document.getElementById(`dynamic-bg-${elementId}`);
  if (existingStyle) {
    existingStyle.remove();
  }

  // Criar novo CSS com especificidade máxima
  const style = document.createElement('style');
  style.id = `dynamic-bg-${elementId}`;
  style.textContent = `
    /* CSS dinâmico com especificidade ultra-alta */
    #${elementId}#${elementId}#${elementId} {
      background-color: ${color} !important;
      background: ${color} !important;
      background-image: none !important;
    }

    /* Sobrescrever qualquer classe ou estilo */
    .tiptap-editor #${elementId}#${elementId},
    .tiptap-editor-content #${elementId}#${elementId},
    .prose #${elementId}#${elementId} {
      background-color: ${color} !important;
      background: ${color} !important;
    }
  `;

  document.head.appendChild(style);
  console.log('✅ CSS dinâmico injetado no DOM');
}

/**
 * Converte cor hex para RGB para comparação
 */
function hexToRgb(hex: string) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 };
}

/**
 * Remove classes do Tailwind que conflitam com os estilos que queremos aplicar
 */
function removeConflictingClasses(element: HTMLElement, styles: Record<string, string>) {
  const classList = element.classList;

  Object.keys(styles).forEach(property => {
    switch (property) {
      case 'background-color':
        // Remover classes bg-* do Tailwind
        Array.from(classList).forEach(className => {
          if (className.startsWith('bg-')) {
            classList.remove(className);
            console.log(`🗑️ Classe removida: ${className}`);
          }
        });
        break;

      case 'color':
        // Remover classes text-* de cor do Tailwind
        Array.from(classList).forEach(className => {
          if (className.startsWith('text-') && !className.includes('text-xs') && !className.includes('text-sm')) {
            classList.remove(className);
            console.log(`🗑️ Classe removida: ${className}`);
          }
        });
        break;

      case 'padding':
        // Remover classes p-* do Tailwind
        Array.from(classList).forEach(className => {
          if (className.startsWith('p-') || className.startsWith('px-') || className.startsWith('py-')) {
            classList.remove(className);
            console.log(`🗑️ Classe removida: ${className}`);
          }
        });
        break;

      case 'border':
      case 'border-width':
      case 'border-color':
        // Remover classes border-* do Tailwind
        Array.from(classList).forEach(className => {
          if (className.startsWith('border-') && className !== 'border') {
            classList.remove(className);
            console.log(`🗑️ Classe removida: ${className}`);
          }
        });
        break;
    }
  });
}

/**
 * Aplica classe CSS de forma segura
 * Se for um wrapper, aplica também à tabela HTML real dentro dele
 */
export function applyClassSafely(element: HTMLElement | null, className: string, remove: string[] = []) {
  if (!element) return false;

  try {
    // Aplicar ao elemento principal
    // Remover classes antigas
    remove.forEach(cls => {
      if (cls) element.classList.remove(cls);
    });

    // Adicionar nova classe
    if (className) {
      element.classList.add(className);
    }

    // Se for um wrapper DIV, aplicar também à tabela HTML real
    if (element.tagName === 'DIV') {
      const realTable = element.querySelector('table');
      if (realTable) {
        // Remover classes antigas da tabela real
        remove.forEach(cls => {
          if (cls) realTable.classList.remove(cls);
        });

        // Adicionar nova classe à tabela real
        if (className) {
          realTable.classList.add(className);
        }
        console.log('✅ Classes aplicadas também à tabela HTML real');
      }
    }

    return true;
  } catch (error) {
    console.error('Erro ao aplicar classe:', error);
    return false;
  }
}

/**
 * Aplica cor a todas as células de uma linha
 */
export function applyRowColor(rowElement: HTMLElement | null, color: string) {
  if (!rowElement) return false;
  
  try {
    const cells = rowElement.querySelectorAll('td, th');
    cells.forEach((cell) => {
      (cell as HTMLElement).style.backgroundColor = color;
    });
    return true;
  } catch (error) {
    console.error('Erro ao aplicar cor da linha:', error);
    return false;
  }
}

/**
 * Aplica cor a todas as células de uma coluna
 */
export function applyColumnColor(tableElement: HTMLElement | null, columnIndex: number, color: string) {
  if (!tableElement || columnIndex < 0) return false;
  
  try {
    const rows = tableElement.querySelectorAll('tr');
    rows.forEach((row) => {
      const cell = row.children[columnIndex] as HTMLElement;
      if (cell) {
        cell.style.backgroundColor = color;
      }
    });
    return true;
  } catch (error) {
    console.error('Erro ao aplicar cor da coluna:', error);
    return false;
  }
}

/**
 * Atualiza propriedade CSS em string de estilo
 */
export function updateStyleProperty(currentStyle: string, property: string, value: string): string {
  const styles = currentStyle.split(';').filter(s => s.trim());
  const updatedStyles = styles.filter(s => !s.trim().startsWith(property));
  updatedStyles.push(`${property}: ${value}`);
  return updatedStyles.join('; ') + ';';
}

/**
 * Debug: imprime informações dos elementos encontrados
 */
export function debugTableElements(elements: TableElements) {
  console.group('🔍 Debug Elementos da Tabela');

  if (elements.tableElement) {
    console.log('✅ Tabela encontrada:', {
      tagName: elements.tableElement.tagName,
      className: elements.tableElement.className,
      id: elements.tableElement.id,
      dataset: elements.tableElement.dataset,
      parentNode: elements.tableElement.parentNode?.nodeName,
      isConnected: elements.tableElement.isConnected,
      element: elements.tableElement
    });

    // Verificar se é uma tabela HTML real
    if (elements.tableElement.tagName === 'TABLE') {
      console.log('✅ É uma tabela HTML real');

      // Verificar integridade DOM
      if (!elements.tableElement.isConnected) {
        console.error('❌ PROBLEMA CRÍTICO: Tabela não está conectada ao DOM!');
      }

      if (!elements.tableElement.parentNode) {
        console.error('❌ PROBLEMA CRÍTICO: Tabela não tem nó pai!');
      }
    } else {
      console.log('⚠️ É um wrapper, procurando tabela HTML dentro...');
      const realTable = elements.tableElement.querySelector('table');
      if (realTable) {
        console.log('✅ Tabela HTML encontrada dentro do wrapper:', {
          tagName: realTable.tagName,
          isConnected: realTable.isConnected,
          parentNode: realTable.parentNode?.nodeName,
          element: realTable
        });
      } else {
        console.log('❌ Nenhuma tabela HTML encontrada dentro do wrapper');
      }
    }
  } else {
    console.log('❌ Tabela não encontrada');
  }

  console.log('Célula:', elements.cellElement?.tagName, elements.cellElement);
  console.log('Linha:', elements.rowElement?.tagName, elements.rowElement);
  console.log('Índice da coluna:', elements.columnIndex);
  console.groupEnd();
}

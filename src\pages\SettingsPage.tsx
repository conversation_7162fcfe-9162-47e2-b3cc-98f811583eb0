import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RequireGlobalRole } from '@/components/auth/PermissionWrappers';
import { useGlobalPermissions } from '@/hooks/usePermissions';
import { Header } from '@/components/layout/Header';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { Settings, Shield, Users, Database, Globe } from 'lucide-react';
import { Link } from 'react-router-dom';

export const SettingsPage: React.FC = () => {
  const { isAdmin, isManager } = useGlobalPermissions();
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  return (
    <>
      <Header showSidebarButton={true} />
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`max-w-none mx-auto p-6 space-y-6 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Settings className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Configurações</h1>
            <p className="text-gray-600">Gerencie as configurações do sistema</p>
          </div>
        </div>

        {/* Configurações Gerais */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Configurações Gerais
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Perfil do Usuário</h3>
                <p className="text-sm text-gray-600">Gerencie suas informações pessoais</p>
              </div>
              <Button asChild variant="outline">
                <Link to="/perfil">Acessar</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Configurações de Administração - Apenas para Admins */}
        {isAdmin && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Administração do Sistema
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">Gerenciar Usuários</h3>
                  <p className="text-sm text-gray-600">Administre usuários e suas permissões</p>
                </div>
                <Button asChild variant="outline">
                  <Link to="/user-management">Acessar</Link>
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">Matriz de Permissões</h3>
                  <p className="text-sm text-gray-600">Visualize e entenda o sistema de permissões</p>
                </div>
                <Button asChild variant="outline">
                  <Link to="/settings/permissions">Visualizar</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Configurações de Gestão - Para Managers e Admins */}
        {(isAdmin || isManager) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Gestão de Projetos
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">Meus Projetos</h3>
                  <p className="text-sm text-gray-600">Gerencie seus projetos e equipes</p>
                </div>
                <Button asChild variant="outline">
                  <Link to="/projects">Acessar</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Informações do Sistema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Informações do Sistema
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900">Versão</h3>
                <p className="text-sm text-gray-600">2.0.0</p>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900">Ambiente</h3>
                <p className="text-sm text-gray-600">Desenvolvimento</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/auth/useAuth';
import { Header } from '@/components/layout/Header';
import { SidebarStandalone } from '@/components/layout/SidebarStandalone';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  PlayCircle, 
  Clock, 
  Calendar, 
  FolderOpen, 
  Target, 
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';
import { supabase } from '@/lib/supabaseClient';

interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  progress: number;
  due_date: string;
  estimated_hours: number;
  stage_name: string;
  stage_id: string;
  project_name: string;
  project_id: string;
  responsible: {
    id: string;
    name: string;
    email: string;
    avatar_url?: string;
  };
  added_as_executor_at: string;
}

export const MyTasksSecure = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const fetchMyTasks = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // VERSÃO SEGURA: Query com RLS habilitado
      const { data: myTasksData, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          description,
          status,
          progress,
          due_date,
          estimated_hours,
          assigned_to,
          stage_id,
          stages (
            id,
            name,
            project_id,
            projects (
              id,
              name
            )
          ),
          task_executors!inner (
            created_at,
            user_id
          )
        `)
        .eq('task_executors.user_id', user.id);

      if (tasksError) {
        console.error('❌ Erro ao buscar tarefas:', tasksError);
        throw tasksError;
      }

      // Processar os dados de forma segura
      const processedTasks: Task[] = [];
      
      for (const taskData of myTasksData || []) {
        const stage = taskData.stages as any;
        const project = stage?.projects as any;
        const executorInfo = taskData.task_executors?.[0];
        
        // Validar dados obrigatórios
        if (!taskData.id || !taskData.title) {
          console.warn('⚠️ Tarefa com dados inválidos ignorada:', taskData);
          continue;
        }

        // Buscar informações do responsável de forma segura
        let responsible = null;
        if (taskData.assigned_to) {
          try {
            const { data: responsibleData } = await supabase
              .from('profiles')
              .select('id, name, email, avatar_url')
              .eq('id', taskData.assigned_to)
              .single();
            
            responsible = responsibleData;
          } catch (err) {
            console.warn('⚠️ Erro ao buscar responsável:', err);
          }
        }

        processedTasks.push({
          id: taskData.id,
          title: taskData.title,
          description: taskData.description || '',
          status: taskData.status || 'pending',
          progress: Math.max(0, Math.min(100, taskData.progress || 0)), // Validar range
          due_date: taskData.due_date || '',
          estimated_hours: Math.max(0, taskData.estimated_hours || 0), // Validar positivo
          stage_name: stage?.name || 'Sem estágio',
          stage_id: stage?.id || '',
          project_name: project?.name || 'Sem projeto',
          project_id: project?.id || '',
          responsible: responsible || {
            id: '',
            name: 'Não atribuído',
            email: '',
            avatar_url: ''
          },
          added_as_executor_at: executorInfo?.created_at || ''
        });
      }

      setTasks(processedTasks);
    } catch (err: any) {
      console.error('❌ Erro ao carregar tarefas:', err);
      setError(err.message || 'Erro ao carregar tarefas');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMyTasks();
  }, [user?.id]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in-progress': return 'bg-blue-500';
      case 'pending': return 'bg-yellow-500';
      case 'blocked': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'in-progress': return <PlayCircle className="w-4 h-4 text-blue-600" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'blocked': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <Pause className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed': return 'Concluída';
      case 'in-progress': return 'Em Andamento';
      case 'pending': return 'Pendente';
      case 'blocked': return 'Bloqueada';
      default: return 'Indefinido';
    }
  };

  const formatDate = (date: string) => {
    if (!date) return '';
    try {
      return new Date(date).toLocaleDateString('pt-BR');
    } catch {
      return 'Data inválida';
    }
  };

  const handleTaskClick = (task: Task) => {
    // Validar ID da tarefa antes de navegar
    if (!task.id || typeof task.id !== 'string') {
      console.error('❌ ID da tarefa inválido:', task.id);
      return;
    }
    navigate(`/task/${task.id}`);
  };

  if (loading) {
    return (
      <>
        <Header showSidebarButton={true} />
        <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
        <div className={`${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'} p-6`}>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Carregando suas tarefas...</p>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Header showSidebarButton={true} />
        <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
        <div className={`${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'} p-6`}>
          <div className="text-center py-12">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-red-900 mb-2">Erro ao carregar tarefas</h3>
            <p className="text-red-700 mb-4">{error}</p>
            <Button onClick={fetchMyTasks} variant="outline">
              Tentar novamente
            </Button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header showSidebarButton={true} />
      <SidebarStandalone collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'} p-6 space-y-6`}>
        {/* Cabeçalho */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">Minhas Tarefas</h1>
          <p className="text-gray-600">
            Tarefas onde você é designado como executor
          </p>
        </div>

        {/* Resumo */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total</p>
                  <p className="text-2xl font-bold">{tasks.length}</p>
                </div>
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Em Andamento</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {tasks.filter(t => t.status === 'in-progress').length}
                  </p>
                </div>
                <PlayCircle className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Concluídas</p>
                  <p className="text-2xl font-bold text-green-600">
                    {tasks.filter(t => t.status === 'completed').length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Pendentes</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {tasks.filter(t => t.status === 'pending').length}
                  </p>
                </div>
                <Clock className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Lista de Tarefas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600" />
              Suas Tarefas como Executor
            </CardTitle>
          </CardHeader>
          <CardContent>
            {tasks.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhuma tarefa atribuída
                </h3>
                <p className="text-gray-600">
                  Você ainda não foi designado como executor de nenhuma tarefa.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {tasks.map((task) => (
                  <div
                    key={task.id}
                    className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleTaskClick(task)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        {/* Linha 1: Título e Status */}
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(task.status)}
                            <h3 className="font-semibold text-lg text-gray-900">
                              {task.title}
                            </h3>
                          </div>
                          <Badge variant="secondary" className="text-xs">
                            {getStatusLabel(task.status)}
                          </Badge>
                        </div>

                        {/* Linha 2: Hierarquia do Projeto */}
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <FolderOpen className="w-4 h-4" />
                          <span>{task.project_name}</span>
                          <Target className="w-4 h-4 ml-2" />
                          <span>{task.stage_name}</span>
                        </div>

                        {/* Linha 3: Progresso */}
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2 flex-1">
                            <Progress value={task.progress} className="flex-1 max-w-32" />
                            <span className="text-sm text-gray-600 font-medium">
                              {task.progress}%
                            </span>
                          </div>
                          
                          {/* Responsável */}
                          {task.responsible.name && (
                            <div className="flex items-center gap-2">
                              <Avatar className="w-6 h-6">
                                <AvatarImage src={task.responsible.avatar_url || '/placeholder.svg'} />
                                <AvatarFallback className="text-xs">
                                  {task.responsible.name?.[0]?.toUpperCase() || '?'}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm text-gray-600">
                                {task.responsible.name}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Linha 4: Informações Adicionais */}
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          {task.due_date && (
                            <div className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              <span>Prazo: {formatDate(task.due_date)}</span>
                            </div>
                          )}
                          {task.estimated_hours > 0 && (
                            <div className="flex items-center gap-1">
                              <Clock className="w-4 h-4" />
                              <span>{task.estimated_hours}h estimadas</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
};

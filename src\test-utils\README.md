# Utilitário de Mock do Supabase para Testes

Este utilitário centraliza a lógica de mocks do client Supabase para facilitar a escrita de testes unitários e evitar duplicação de código entre diferentes serviços.

## Como usar

1. **Importe os mocks e o supabaseMock nos seus testes:**

```ts
import { supabaseMock, mockSingle, mockEqList, mockSingleGetById, ... } from '../test-utils/supabaseMock';

vi.mock('@/lib/supabaseClient', () => ({ supabase: supabaseMock }));
```

2. **Configure o retorno dos mocks conforme o cenário do teste:**

- Para listagem:
  ```ts
  mockEqList.mockReturnValueOnce({ data: [...], error: null });
  ```
- Para busca por id:
  ```ts
  mockSingleGetById.mockReturnValueOnce({ data: {...}, error: null });
  ```
- Para criação/atualização:
  ```ts
  mockSingleCreate.mockReturnValueOnce({ data: {...}, error: null });
  mockSingleUpdate.mockReturnValueOnce({ data: {...}, error: null });
  ```

3. **Limpe os mocks no beforeEach:**

```ts
beforeEach(() => {
  vi.clearAllMocks();
});
```

## Estrutura do utilitário
- O mock decide automaticamente qual cadeia de métodos retornar com base na tabela e campo consultado.
- Suporta métodos encadeados como `.select().eq().single()`, `.insert().single()`, `.update().eq().single()`, `.delete().eq()`.
- Pode ser facilmente estendido para outros serviços/tabelas.

## Boas práticas
- Sempre centralize os mocks neste utilitário para evitar divergências entre testes.
- Se criar um novo serviço, adicione o campo/tabela correspondente na função `selectMockService`.
- Use nomes de mocks descritivos para facilitar a leitura dos testes.

---

Dúvidas ou sugestões? Atualize este README para manter o padrão alinhado com a equipe. 
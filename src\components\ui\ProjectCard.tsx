import React from 'react';
import { <PERSON>, CardContent, CardHeader } from './card';
import { Badge } from './badge';
import { Avatar, AvatarFallback, AvatarImage } from './avatar';
import { Progress } from './progress';
import { Button } from './button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu';
import { Project } from '@/types';
import { cn } from '@/lib/utils';
import { Calendar, Users, Eye, Layers } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ProjectCardProps {
  project: Project;
  onClick: (project: Project) => void;
  className?: string;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onClick,
  className
}) => {
  const statusColors = {
    planning: 'bg-gray-100 text-gray-800',
    active: 'bg-project-bg text-project-dark',
    'on-hold': 'bg-yellow-100 text-yellow-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  };

  const statusLabels = {
    planning: 'Planejamento',
    active: 'Ativo',
    'on-hold': 'Pausado',
    completed: 'Concluído',
    cancelled: 'Cancelado'
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const navigate = useNavigate();

  return (
    <Card 
      className={cn(
        'group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] border-project/10',
        className
      )}
      onClick={() => onClick(project)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="font-semibold text-lg text-gray-900 group-hover:text-project">
              {project.name}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2">
              {project.description}
            </p>
          </div>
          <Badge 
            variant="secondary" 
            className={cn('text-xs', statusColors[project.status])}
          >
            {statusLabels[project.status]}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progresso</span>
            <span className="font-medium text-project">{project.progress}%</span>
          </div>
          <Progress value={project.progress} className="h-2" />
        </div>

        {/* Team Avatars */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-gray-500" />
            <div className="flex -space-x-2">
              {(project.team || []).slice(0, 3).map((member) => (
                <Avatar key={member.id} className="w-6 h-6 border-2 border-white">
                  <AvatarImage src={member.avatar_url || member.avatar || '/placeholder.svg'} alt={member.name} />
                  <AvatarFallback className="text-xs bg-project-bg text-project">
                    {getInitials(member.name)}
                  </AvatarFallback>
                </Avatar>
              ))}
              {(project.team || []).length > 3 && (
                <div className="w-6 h-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                  <span className="text-xs text-gray-600">+{(project.team || []).length - 3}</span>
                </div>
              )}
            </div>
          </div>

          {/* Deadline */}
          {project.end_date && (
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(project.end_date)}</span>
            </div>
          )}
        </div>

        <div className="flex gap-2 mt-4">
          <button
            className="inline-flex items-center gap-1 px-2 py-1 rounded bg-gray-100 hover:bg-gray-200 text-sm"
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/project/${project.id}`);
            }}
            title="Abrir ProjectDetails"
          >
            <Eye className="w-4 h-4" /> Detalhes 1
          </button>
          <button
            className="inline-flex items-center gap-1 px-2 py-1 rounded bg-gray-100 hover:bg-gray-200 text-sm"
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/project2/${project.id}`);
            }}
            title="Abrir ProjectDetails2"
          >
            <Layers className="w-4 h-4" /> Detalhes 2
          </button>
        </div>
      </CardContent>
    </Card>
  );
};

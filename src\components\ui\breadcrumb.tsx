import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { ChevronRight, MoreHorizontal, FolderOpen, Target, FileText } from "lucide-react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/ui/use-mobile"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

const Breadcrumb = React.forwardRef<
  HTMLElement,
  React.ComponentPropsWithoutRef<"nav"> & {
    separator?: React.ReactNode
    items?: { label: string; href?: string; icon?: React.ReactNode }[]
  }
>(({ items, children, ...props }, ref) => {
  const isMobile = useIsMobile();
  const [openIdx, setOpenIdx] = React.useState<number | null>(null);

  if (items && isMobile) {
    // Renderizar breadcrumbs como ícones com tooltip em mobile (abre ao toque/click)
    return (
      <nav ref={ref} aria-label="breadcrumb" {...props}>
        <ol className="flex items-center gap-2">
          <TooltipProvider>
            {items.map((item, idx) => (
              <React.Fragment key={idx}>
                <Tooltip open={openIdx === idx} onOpenChange={o => setOpenIdx(o ? idx : null)}>
                  <TooltipTrigger asChild>
                    {item.href ? (
                      <a
                        href={item.href}
                        className="text-muted-foreground hover:text-foreground"
                        onClick={e => {
                          e.preventDefault();
                          setOpenIdx(openIdx === idx ? null : idx);
                        }}
                      >
                        {item.icon || getDefaultIcon(idx)}
                      </a>
                    ) : (
                      <span
                        className="text-muted-foreground"
                        onClick={() => setOpenIdx(openIdx === idx ? null : idx)}
                        style={{ cursor: 'pointer' }}
                      >
                        {item.icon || getDefaultIcon(idx)}
                      </span>
                    )}
                  </TooltipTrigger>
                  <TooltipContent side="bottom" onPointerDownOutside={() => setOpenIdx(null)}>
                    {item.label}
                  </TooltipContent>
                </Tooltip>
                {idx < items.length - 1 && (
                  <ChevronRight className="w-4 h-4 text-muted-foreground" />
                )}
              </React.Fragment>
            ))}
          </TooltipProvider>
        </ol>
      </nav>
    );
  }
  // Desktop: render padrão
  return <nav ref={ref} aria-label="breadcrumb" {...props}>{children}</nav>;
});
Breadcrumb.displayName = "Breadcrumb"

function getDefaultIcon(idx: number) {
  if (idx === 0) return <FolderOpen className="w-5 h-5" />;
  if (idx === 1) return <Target className="w-5 h-5" />;
  if (idx === 2) return <FileText className="w-5 h-5" />;
  return <MoreHorizontal className="w-5 h-5" />;
}

const BreadcrumbList = React.forwardRef<
  HTMLOListElement,
  React.ComponentPropsWithoutRef<"ol">
>(({ className, ...props }, ref) => (
  <ol
    ref={ref}
    className={cn(
      "flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",
      className
    )}
    {...props}
  />
))
BreadcrumbList.displayName = "BreadcrumbList"

const BreadcrumbItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentPropsWithoutRef<"li">
>(({ className, ...props }, ref) => (
  <li
    ref={ref}
    className={cn("inline-flex items-center gap-1.5", className)}
    {...props}
  />
))
BreadcrumbItem.displayName = "BreadcrumbItem"

const BreadcrumbLink = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentPropsWithoutRef<"a"> & {
    asChild?: boolean
  }
>(({ asChild, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "a"

  return (
    <Comp
      ref={ref}
      className={cn("transition-colors hover:text-foreground", className)}
      {...props}
    />
  )
})
BreadcrumbLink.displayName = "BreadcrumbLink"

const BreadcrumbPage = React.forwardRef<
  HTMLSpanElement,
  React.ComponentPropsWithoutRef<"span">
>(({ className, ...props }, ref) => (
  <span
    ref={ref}
    role="link"
    aria-disabled="true"
    aria-current="page"
    className={cn("font-normal text-foreground", className)}
    {...props}
  />
))
BreadcrumbPage.displayName = "BreadcrumbPage"

const BreadcrumbSeparator = ({
  children,
  className,
  ...props
}: React.ComponentProps<"li">) => (
  <li
    role="presentation"
    aria-hidden="true"
    className={cn("[&>svg]:size-3.5", className)}
    {...props}
  >
    {children ?? <ChevronRight />}
  </li>
)
BreadcrumbSeparator.displayName = "BreadcrumbSeparator"

const BreadcrumbEllipsis = ({
  className,
  ...props
}: React.ComponentProps<"span">) => (
  <span
    role="presentation"
    aria-hidden="true"
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More</span>
  </span>
)
BreadcrumbEllipsis.displayName = "BreadcrumbElipssis"

export {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
}

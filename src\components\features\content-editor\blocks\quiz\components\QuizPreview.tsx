import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Timer,
  ArrowLeft,
  ArrowRight,
  Save,
  Send
} from 'lucide-react';

import { QuizContent, QuizQuestion, QuizAnswer, QuizQuestionType } from '@/types/quiz';

interface QuizPreviewProps {
  content: QuizContent;
  onSubmit?: (answers: QuizAnswer[]) => void;
  isReadOnly?: boolean;
}

export const QuizPreview: React.FC<QuizPreviewProps> = ({
  content,
  onSubmit,
  isReadOnly = true
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, QuizAnswer>>({});
  const [timeRemaining, setTimeRemaining] = useState(content.config.timeLimit || 0);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const currentQuestion = content.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / content.questions.length) * 100;

  const handleAnswerChange = (questionId: string, answer: Partial<QuizAnswer>) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        questionId,
        questionType: currentQuestion.type,
        timeSpent: 0,
        isCorrect: false,
        pointsEarned: 0,
        ...prev[questionId],
        ...answer
      }
    }));
  };

  const renderQuestionContent = (question: QuizQuestion) => {
    const currentAnswer = answers[question.id];

    switch (question.type) {
      case 'single-choice':
        return (
          <RadioGroup
            value={currentAnswer?.selectedOptions?.[0] || ''}
            onValueChange={(value) => handleAnswerChange(question.id, { selectedOptions: [value] })}
            disabled={isReadOnly}
          >
            {question.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <RadioGroupItem value={option.id} id={option.id} />
                <Label htmlFor={option.id} className="text-sm cursor-pointer">
                  {option.text}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'multiple-choice':
        return (
          <div className="space-y-2">
            {question.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={currentAnswer?.selectedOptions?.includes(option.id) || false}
                  onCheckedChange={(checked) => {
                    const currentSelected = currentAnswer?.selectedOptions || [];
                    const newSelected = checked
                      ? [...currentSelected, option.id]
                      : currentSelected.filter(id => id !== option.id);
                    handleAnswerChange(question.id, { selectedOptions: newSelected });
                  }}
                  disabled={isReadOnly}
                />
                <Label htmlFor={option.id} className="text-sm cursor-pointer">
                  {option.text}
                </Label>
              </div>
            ))}
          </div>
        );

      case 'true-false':
        return (
          <RadioGroup
            value={currentAnswer?.booleanAnswer?.toString() || ''}
            onValueChange={(value) => handleAnswerChange(question.id, { booleanAnswer: value === 'true' })}
            disabled={isReadOnly}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="true" id="true" />
              <Label htmlFor="true" className="text-sm cursor-pointer">
                Verdadeiro
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="false" id="false" />
              <Label htmlFor="false" className="text-sm cursor-pointer">
                Falso
              </Label>
            </div>
          </RadioGroup>
        );

      case 'open-text':
        return (
          <Textarea
            value={currentAnswer?.textAnswer || ''}
            onChange={(e) => handleAnswerChange(question.id, { textAnswer: e.target.value })}
            placeholder="Digite sua resposta..."
            className="min-h-[100px]"
            disabled={isReadOnly}
          />
        );

      case 'ordering':
        return (
          <div className="space-y-2">
            <p className="text-sm text-gray-600 mb-3">
              Arraste os itens para ordená-los corretamente:
            </p>
            {question.orderingItems?.map((item, index) => (
              <div
                key={item.id}
                className="p-3 border rounded-lg bg-gray-50 cursor-move"
              >
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{index + 1}.</span>
                  <span className="text-sm">{item.text}</span>
                </div>
              </div>
            ))}
          </div>
        );

      case 'matching':
        return (
          <div className="space-y-3">
            <p className="text-sm text-gray-600 mb-3">
              Conecte os itens correspondentes:
            </p>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Coluna A</h4>
                {question.matchingPairs?.map((pair) => (
                  <div key={pair.id} className="p-2 border rounded bg-blue-50">
                    <span className="text-sm">{pair.left}</span>
                  </div>
                ))}
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Coluna B</h4>
                {question.matchingPairs?.map((pair) => (
                  <div key={pair.id} className="p-2 border rounded bg-green-50">
                    <span className="text-sm">{pair.right}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>Tipo de pergunta não suportado no preview</p>
          </div>
        );
    }
  };

  const handleSubmit = () => {
    if (onSubmit) {
      const answersList = Object.values(answers);
      onSubmit(answersList);
    }
    setIsSubmitted(true);
  };

  const canGoNext = currentQuestionIndex < content.questions.length - 1;
  const canGoPrev = currentQuestionIndex > 0;
  const isLastQuestion = currentQuestionIndex === content.questions.length - 1;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header do Quiz */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">{content.config.title}</CardTitle>
              {content.config.description && (
                <p className="text-gray-600 mt-2">{content.config.description}</p>
              )}
            </div>
            {content.config.showTimer && content.config.timeLimit && (
              <div className="flex items-center gap-2 text-sm">
                <Timer className="w-4 h-4" />
                <span>{Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}</span>
              </div>
            )}
          </div>
        </CardHeader>
        
        {content.config.showProgressBar && (
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progresso</span>
                <span>{currentQuestionIndex + 1} de {content.questions.length}</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          </CardContent>
        )}
      </Card>

      {/* Instruções */}
      {content.config.instructions && currentQuestionIndex === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">Instruções</h3>
              <p className="text-sm text-blue-800">{content.config.instructions}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pergunta Atual */}
      {currentQuestion && (
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">
                    Pergunta {currentQuestionIndex + 1}
                  </Badge>
                  <Badge variant="secondary">
                    {currentQuestion.points} {currentQuestion.points === 1 ? 'ponto' : 'pontos'}
                  </Badge>
                  {currentQuestion.required && (
                    <Badge variant="destructive">
                      Obrigatória
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg">{currentQuestion.title}</CardTitle>
                {currentQuestion.description && (
                  <p className="text-gray-600 mt-2">{currentQuestion.description}</p>
                )}
              </div>
              {currentQuestion.timeLimit && (
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>{currentQuestion.timeLimit}s</span>
                </div>
              )}
            </div>
          </CardHeader>
          
          <CardContent>
            {renderQuestionContent(currentQuestion)}
          </CardContent>
        </Card>
      )}

      {/* Navegação */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={() => setCurrentQuestionIndex(prev => prev - 1)}
              disabled={!canGoPrev}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Anterior
            </Button>

            <div className="flex gap-2">
              {content.config.allowSaveDraft && !isSubmitted && (
                <Button variant="outline">
                  <Save className="w-4 h-4 mr-2" />
                  Salvar Rascunho
                </Button>
              )}

              {isLastQuestion ? (
                <Button onClick={handleSubmit} disabled={isSubmitted}>
                  <Send className="w-4 h-4 mr-2" />
                  {isSubmitted ? 'Enviado' : 'Finalizar Quiz'}
                </Button>
              ) : (
                <Button
                  onClick={() => setCurrentQuestionIndex(prev => prev + 1)}
                  disabled={!canGoNext}
                >
                  Próxima
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resultado (se enviado) */}
      {isSubmitted && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">Quiz Finalizado!</h3>
                <p className="text-gray-600">
                  Suas respostas foram enviadas com sucesso.
                </p>
              </div>
              {content.config.showScore && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-600">
                    Este é um preview. Em um quiz real, a pontuação seria calculada aqui.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

# Sistema de Gerenciamento de Projetos com Supabase

## Informações do Projeto

**Stack:**
- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- **Supabase** (Banco de Dados, Autenticação, Storage)

## Como rodar este projeto?

### 1. Pré-requisitos
- Node.js & npm instalados ([instale com nvm](https://github.com/nvm-sh/nvm#installing-and-updating))
- Conta gratuita no [Supabase](https://supabase.com/)

### 2. Crie um projeto no Supabase
1. Acesse o [painel do Supabase](https://app.supabase.com/)
2. Clique em "New Project" e siga as instruções
3. Copie a **Project URL** e a **Anon Public Key** (encontradas em Project Settings > API)

### 3. Configure as variáveis de ambiente
Crie um arquivo `.env` na raiz do projeto com:
```
VITE_SUPABASE_URL=<SUA_SUPABASE_URL>
VITE_SUPABASE_ANON_KEY=<SUA_SUPABASE_ANON_KEY>
```
Adicione `.env` ao seu `.gitignore` para não versionar suas chaves.

### 4. Instale as dependências
```sh
npm install
```

### 5. Inicie o servidor de desenvolvimento
```sh
npm run dev
```

### 6. Configure o Supabase Client no projeto
No arquivo `src/lib/supabaseClient.ts`:
```ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

Use o client para autenticação, CRUD e storage:
```ts
// Exemplo: Login
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'senha123',
});
```

## Como funciona a autenticação?
Utilize o Supabase Auth para login, registro e gerenciamento de sessão. Consulte a [documentação oficial](https://supabase.com/docs/guides/auth) para detalhes.

## Como estruturar o banco de dados?
Crie as tabelas `projects`, `stages`, `tasks` e configure as permissões (RLS) pelo painel do Supabase. Veja exemplos na documentação do Supabase.

## Deploy
O deploy pode ser feito em qualquer serviço de hospedagem de frontends (Vercel, Netlify, etc). O Supabase já é cloud.

## Dúvidas?
Consulte a [documentação oficial do Supabase](https://supabase.com/docs) ou abra uma issue.

## Aviso sobre erro "PublicKeyCredential is not defined" em embeds de vídeo

Ao utilizar vídeos incorporados (YouTube, Canva, etc.) na tela de execução de tarefas, pode aparecer no console do navegador o erro:

```
Uncaught ReferenceError: PublicKeyCredential is not defined
```

**Motivo:** Esse erro é disparado por scripts internos dos players de vídeo (ex: YouTube) que tentam acessar APIs modernas de autenticação do navegador (WebAuthn/FIDO2). Como o acesso ocorre dentro de um `<iframe>` de terceiros, não é possível suprimir ou tratar esse erro pelo nosso código.

**Impacto:**
- O erro **não afeta o funcionamento do app** nem a exibição dos vídeos.
- É um warning inofensivo, comum em ambientes de desenvolvimento e em browsers que não suportam ou restringem WebAuthn.
- Não representa risco de segurança, bug ou problema para o usuário final.

**Orientação:**
- **Pode ser ignorado com segurança** durante o desenvolvimento e testes.
- Não há necessidade de ajuste no código do projeto.
- Caso precise de um console limpo para demonstrações, utilize extensões de filtro de logs no navegador.

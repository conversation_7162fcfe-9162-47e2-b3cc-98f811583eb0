import React, { useState, useEffect, useRef } from 'react';
import { BlockConfig, blockTypeDefaultIcons, blockTypeDefaultColors } from '@/types';
import { defaultBlockConfig, getDefaultConfigForBlockType } from './config-panel/constants/migration'; // Usar mesmo defaultBlockConfig do CardAppearanceConfig
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipProvider, TooltipTrigger, TooltipContent } from '../../../../ui/tooltip';
import { LucideIcon, FileText, RotateCcw } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../../../../ui/tabs';
import { VideoBlockEditor } from '../video/VideoBlockEditor';
import { VideoBlockCard } from '../video/VideoBlockCard';
import { IconPicker } from './IconPicker';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '../../../../ui/dialog';
import { ICONS } from '../video/VideoBlockEditor';
import { Input } from '../../../../ui/input';
import { Label } from '../../../../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../../ui/select';
import { Slider } from '../../../../ui/slider';
import { blockTypePresetsMap, coloredBlockPresets, COLORED_BLOCK_PALETTES, ICONS_BY_VARIANT, COLORS_BY_VARIANT, ALERT_PRESETS, ALERT_ICONS, AlertType } from './config-panel/constants/block-types';
import { coloredCardVariants } from './config-panel/constants/block-types/colored/presets';
import { presetToBlockConfigCard, presetToBlockConfigIcon, presetToBlockConfigButton, presetToBlockConfig } from './config-panel/constants/migration';

// Importar módulos refatorados
import {
  BlockConfigPanelProps,
  StylePreset,
  iconPositions,
  cardFormats,
  fontStyles,
  iconFormats,
  stylePresets,
  ICON_COMPONENTS,
  getLucideComponent,
  getPureContrastColor,
  isValidHexColor,
  normalizeHexColor,
  CardAppearanceConfig,
  IconAppearanceConfig,
  ButtonConfig,
} from './config-panel';

import { ColorPicker } from './ColorPicker';
import { PresetSelector } from './config-panel/components/PresetSelector';

// Definir componentes de abas
const AppearanceTab: React.FC<{
  config: BlockConfig;
  onChange: (config: BlockConfig) => void;
  onReset: () => void;
  setFeedback: (feedback: string) => void;
  blockType?: string;
  coloredVariant?: string;
}> = ({ config, onChange, onReset, setFeedback, blockType, coloredVariant }) => {
  return (
    <CardAppearanceConfig
      config={config}
      onChange={onChange}
      onReset={onReset}
      setFeedback={setFeedback}
      blockType={blockType}
      coloredVariant={coloredVariant}
    />
  );
};

const IconTab: React.FC<{
  config: BlockConfig;
  onChange: (config: BlockConfig) => void;
  handleResetIcon: () => void;
  blockType?: string;
  allowedIcons: string[];
  ICONS: any;
  ALERT_ICONS: any;
  renderIconPreview: () => React.ReactNode;
}> = ({ config, onChange, handleResetIcon, blockType, allowedIcons, ICONS, ALERT_ICONS, renderIconPreview }) => {
  return (
    <IconAppearanceConfig
      config={config}
      onChange={onChange}
      onReset={handleResetIcon}
      blockType={blockType}
      allowedIcons={allowedIcons}
      ICONS={ICONS}
      ALERT_ICONS={ALERT_ICONS}
      renderIconPreview={renderIconPreview}
    />
  );
};

const ButtonTab: React.FC<{
  config: BlockConfig;
  onChange: (config: BlockConfig) => void;
  handleResetButton: () => void;
}> = ({ config, onChange, handleResetButton }) => {
  return (
    <ButtonConfig
      config={config}
      onChange={onChange}
      onReset={handleResetButton}
    />
  );
};

const PresetsTab: React.FC<{
  getStylePresets: () => StylePreset[];
  handleApplyPreset: (preset: StylePreset) => void;
  onReset?: () => void;
  onCancel?: () => void;
}> = ({ getStylePresets, handleApplyPreset, onReset, onCancel }) => {
  return (
    <PresetSelector
      presets={getStylePresets()}
      onApplyPreset={handleApplyPreset}
      onReset={onReset}
      onCancel={onCancel}
    />
  );
};

interface LocalBlockConfigPanelProps {
  config: BlockConfig;
  onChange: (c: BlockConfig) => void;
  onReset?: () => void;
  preview?: React.ReactNode;
  blockType?: string;
  blockIcon?: React.ReactNode;
  onCancel?: () => void;
  editContent?: any;
}



export const BlockConfigPanel: React.FC<LocalBlockConfigPanelProps> = ({
  config,
  onChange,
  onReset,
  preview,
  blockType,
  blockIcon,
  onCancel,
  editContent
}) => {
  const [feedback, setFeedback] = useState<string | null>(null);
  const initialConfig = useRef(JSON.stringify(config));
  const [customIconError, setCustomIconError] = useState<string | null>(null);
  const [isHovering, setIsHovering] = useState(false);
  const [iconPickerOpen, setIconPickerOpen] = React.useState(false);
  const [iconSearch, setIconSearch] = React.useState('');
  const iconNames = Object.keys(ICONS);
  const filteredIcons = iconNames.filter((k) => k.toLowerCase().includes(iconSearch.toLowerCase()));

  // Obter o preset do tipo de bloco
  const preset = blockType && blockTypePresetsMap[blockType] ? blockTypePresetsMap[blockType] : undefined;

  const coloredBlockType = blockType === 'colored-block';
  const coloredVariant = editContent?.type || 'info';
  const allowedIcons = blockType === 'colored-block' ? ICONS_BY_VARIANT[coloredVariant] : iconNames;
  const allowedColors = coloredBlockType ? COLORS_BY_VARIANT[coloredVariant] : undefined;

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (JSON.stringify(config) !== initialConfig.current) {
        e.preventDefault();
        e.returnValue = '';
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [config]);

  // Garantir que blocos antigos de colored-block tenham border.enabled: true
  React.useEffect(() => {
    if (blockType === 'colored-block' && config.card?.border?.enabled === undefined) {
      onChange({
        ...config,
        card: {
          ...config.card,
          border: {
            ...config.card?.border,
            enabled: true,
          },
        },
      });
    }
  }, [blockType, config, onChange]);

  // Handler simples para cor de fundo do card
  const handleBgColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...config,
      card: {
        ...config.card,
        backgroundColor: normalizeHexColor(e.target.value)
      }
    });
  };

  const handleApply = () => {
    setFeedback('Configuração aplicada!');
    setTimeout(() => setFeedback(null), 2000);
  };

  // Handlers de reset por grupo
  const handleResetCard = () => {
    if (blockType === 'colored-block') {
      const preset = coloredBlockPresets[coloredVariant || 'info'];
      const cardConfig = presetToBlockConfig(preset);

      onChange({
        ...config, // Manter configurações existentes de ícone e botão
        card: cardConfig.card, // Restaurar apenas configurações do card
      });
    } else if (blockType) {
      // Para outros tipos de bloco, usar configuração padrão específica do tipo
      const typeConfig = getDefaultConfigForBlockType(blockType);

      onChange({
        ...config, // Manter configurações existentes de ícone e botão
        card: typeConfig.card, // Restaurar configurações específicas do tipo
      });
    } else {
      // Fallback para configuração padrão genérica
      onChange({
        ...config,
        card: defaultBlockConfig.card,
      });
    }
  };
  
  const handleResetIcon = () => {
    if (blockType === 'colored-block') {
      const preset = coloredBlockPresets[coloredVariant || 'info']; // Usar mesmo fallback
      const iconConfig = presetToBlockConfigIcon(preset.icon);
      onChange({
        ...config, // Manter configurações existentes de card e botão
        icon: iconConfig, // Restaurar apenas configurações do ícone
      });
    } else if (blockType) {
      // Para outros tipos de bloco, usar configuração padrão específica do tipo
      const typeConfig = getDefaultConfigForBlockType(blockType);

      onChange({
        ...config, // Manter configurações existentes de card e botão
        icon: typeConfig.icon, // Restaurar configurações específicas do tipo
      });
    } else {
      onChange({
        ...config,
        icon: defaultBlockConfig.icon, // Restaurar apenas configurações do ícone
      });
    }
  };
  
  const handleResetButton = () => {
    if (blockType === 'colored-block') {
      const preset = coloredBlockPresets[coloredVariant || 'info']; // Usar mesmo fallback
      if (preset?.button) {
        onChange({
          ...config, // Manter configurações existentes de card e ícone
          button: presetToBlockConfigButton(preset), // Restaurar apenas configurações do botão
        });
      } else {
        // Fallback para configuração padrão
        onChange({
          ...config,
          button: defaultBlockConfig.button, // Restaurar apenas configurações do botão
        });
      }
    } else if (blockType) {
      // Para outros tipos de bloco, usar configuração padrão específica do tipo
      const typeConfig = getDefaultConfigForBlockType(blockType);

      onChange({
        ...config, // Manter configurações existentes de card e ícone
        button: typeConfig.button, // Restaurar configurações específicas do tipo
      });
    } else {
      // Fallback para configuração padrão
      onChange({
        ...config,
        button: defaultBlockConfig.button, // Restaurar apenas configurações do botão
      });
    }
  };

  // Handler de reset geral - executa exatamente a mesma lógica dos específicos
  const handleResetAll = () => {
    // CARD: Usar exatamente a mesma lógica do handleResetCard
    let newCardConfig;
    if (blockType === 'colored-block') {
      const preset = coloredBlockPresets[coloredVariant || 'info'];
      const cardConfig = presetToBlockConfig(preset);
      newCardConfig = cardConfig.card;
    } else if (blockType) {
      const typeConfig = getDefaultConfigForBlockType(blockType);
      newCardConfig = typeConfig.card;
    } else {
      newCardConfig = defaultBlockConfig.card;
    }

    // ÍCONE: Usar exatamente a mesma lógica do handleResetIcon
    let newIconConfig;
    if (blockType === 'colored-block') {
      const preset = coloredBlockPresets[coloredVariant || 'info'];
      newIconConfig = presetToBlockConfigIcon(preset.icon);
    } else if (blockType) {
      const typeConfig = getDefaultConfigForBlockType(blockType);
      newIconConfig = typeConfig.icon;
    } else {
      newIconConfig = defaultBlockConfig.icon;
    }

    // BOTÃO: Usar exatamente a mesma lógica do handleResetButton
    let newButtonConfig;
    if (blockType === 'colored-block') {
      const preset = coloredBlockPresets[coloredVariant || 'info'];
      if (preset?.button) {
        newButtonConfig = presetToBlockConfigButton(preset);
      } else {
        newButtonConfig = defaultBlockConfig.button;
      }
    } else if (blockType) {
      const typeConfig = getDefaultConfigForBlockType(blockType);
      newButtonConfig = typeConfig.button;
    } else {
      newButtonConfig = defaultBlockConfig.button;
    }

    // Aplicar todas as configurações de uma vez
    onChange({
      ...config,
      card: newCardConfig,
      icon: newIconConfig,
      button: newButtonConfig,
    });
  };

  // Preview do ícone com hover interativo
  const renderIconPreview = () => {
    if (!config.icon?.enabled) return null;
    const size = config.icon?.appearance?.size || 28;
    const isHover = isHovering && config.icon?.appearance?.hover?.enabled;
    const style: React.CSSProperties = {
      background: config.icon?.appearance?.background || '#f3f4f6',
      color: config.icon?.appearance?.color || '#374151',
      borderRadius: config.icon?.appearance?.format === 'circle' ? '50%' : '8px',
      border: config.icon?.appearance?.border?.enabled
        ? `${config.icon?.appearance?.border?.width || 1}px solid ${config.icon?.appearance?.border?.color || '#e5e5e5'}`
        : 'none',
      boxShadow: isHover
        ? `0 2px ${2 * (config.icon?.appearance?.hover?.shadowDepth || 2)}px #0004`
        : config.icon?.appearance?.shadow?.enabled
          ? `0 2px ${2 * (config.icon?.appearance?.shadow?.depth || 1)}px #0002`
          : 'none',
      width: size + 20,
      height: size + 20,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: '0 auto',
      fontSize: size,
      transition: 'all 0.2s',
      position: 'relative',
      cursor: 'pointer',
    };
    
    if (config.icon?.type === 'custom' && config.icon?.customIconUrl) {
      return (
        <div
          style={style}
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          tabIndex={0}
          aria-label="Preview do ícone customizado"
        >
          <img
            src={config.icon.customIconUrl}
            alt="Ícone personalizado"
            style={{ width: size, height: size, objectFit: 'contain', borderRadius: 'inherit' }}
            onError={() => setCustomIconError('Não foi possível carregar a imagem.')}
            onLoad={() => setCustomIconError(null)}
          />
          {customIconError && (
            <span className="absolute inset-0 flex items-center justify-center text-xs text-red-500 bg-white/80 rounded">Erro</span>
          )}
        </div>
      );
    }
    
    const iconName = config.icon?.iconName;
    const IconLucide = getLucideComponent(iconName);
    
    return (
      <div
        style={style}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        tabIndex={0}
        aria-label="Preview do ícone Lucide"
      >
        <IconLucide className="w-6 h-6" style={{ color: '#222' }} />
      </div>
    );
  };

  const handleChange = (newConfig: BlockConfig) => {
    // Garante que card, icon e button sempre existam no objeto enviado
    const safeConfig = {
      card: newConfig.card,
      icon: newConfig.icon,
      button: newConfig.button,
    };
    onChange(safeConfig);
  };

  // Converter presets do tipo de bloco em array de StylePreset
  const getStylePresets = (): StylePreset[] => {
    if (!blockType || !blockTypePresetsMap[blockType]) {
      return [];
    }
    
    const blockPresets = blockTypePresetsMap[blockType];
    return Object.entries(blockPresets).map(([key, preset]) => ({
      name: key.charAt(0).toUpperCase() + key.slice(1),
      config: {
        card: {
          backgroundColor: preset.card.backgroundColor,
          borderColor: preset.card.border.color,
          borderRadius: preset.card.format === 'rounded' ? '8px' : '0px',
          boxShadow: preset.card.shadow || 'none'
        },
        icon: {
          color: preset.icon.color
        }
      }
    }));
  };

  const handleApplyPreset = (preset: StylePreset) => {
    onChange({
      ...config,
      card: {
        ...config.card,
        ...preset.config.card
      },
      icon: {
        ...config.icon,
        appearance: {
          ...config.icon?.appearance,
          ...preset.config.icon
        }
      }
    });
    setFeedback(`Preset "${preset.name}" aplicado!`);
    setTimeout(() => setFeedback(null), 2000);
  };

  return (
    <div className="w-full max-w-3xl mx-auto p-4 md:p-8">
      <div className="flex items-center gap-3 mb-2">
        {blockIcon && <span className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-700 text-xl">{blockIcon}</span>}
        <div>
          <div className="font-bold text-lg leading-tight">Configuração Visual {blockType ? `- ${blockType}` : ''}</div>
          <div className="text-xs text-gray-500">Personalize a aparência do bloco para destacar seu conteúdo.</div>
        </div>
      </div>
      
      {preview && (
        <div className="w-full py-2">
          {preview}
        </div>
      )}
      
      {feedback && (
        <div className="mb-4 p-2 bg-green-100 text-green-800 rounded text-sm">
          {feedback}
        </div>
      )}

      <Tabs defaultValue="appearance">
        <div className="flex items-center justify-between mb-4">
          <TabsList>
            <TabsTrigger value="appearance">Aparência</TabsTrigger>
            <TabsTrigger value="icon">Ícone</TabsTrigger>
            <TabsTrigger value="button">Botão</TabsTrigger>
            <TabsTrigger value="presets">Presets</TabsTrigger>
          </TabsList>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetAll}
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  Restaurar Padrão
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Restaura todas as configurações (Aparência, Ícone e Botão)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        <TabsContent value="appearance">
          <AppearanceTab
            config={config}
            onChange={onChange}
            onReset={handleResetCard}
            setFeedback={setFeedback}
            blockType={blockType}
            coloredVariant={coloredVariant}
          />
        </TabsContent>
        
        <TabsContent value="icon">
          <IconTab 
            config={config}
            onChange={onChange}
            handleResetIcon={handleResetIcon}
            blockType={blockType}
            allowedIcons={allowedIcons}
            ICONS={ICONS}
            ALERT_ICONS={ALERT_ICONS}
            renderIconPreview={renderIconPreview}
          />
        </TabsContent>
        
        <TabsContent value="button">
          <ButtonTab 
            config={config}
            onChange={onChange}
            handleResetButton={handleResetButton}
          />
        </TabsContent>
        
        <TabsContent value="presets">
          <PresetsTab 
            getStylePresets={getStylePresets}
            handleApplyPreset={handleApplyPreset}
            onReset={onReset}
            onCancel={onCancel}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};



export default BlockConfigPanel;
-- =====================
-- CORREÇÃO LIMPA: RECRIAR POLÍTICAS DE PROFILES
-- Data: 2024-12-19
-- Objetivo: Resolver erros 403, 409, 500 de uma vez
-- =====================

-- PASSO 1: Remover TODAS as políticas existentes
-- =====================
do $$
declare
    policy_record record;
begin
    -- Buscar e remover todas as políticas da tabela profiles
    for policy_record in 
        select policyname 
        from pg_policies 
        where tablename = 'profiles' and schemaname = 'public'
    loop
        execute format('drop policy if exists %I on public.profiles', policy_record.policyname);
        raise notice 'Política removida: %', policy_record.policyname;
    end loop;
end $$;

-- PASSO 2: Verificar se todas as políticas foram removidas
-- =====================
select 
    case 
        when count(*) = 0 then 'Todas as políticas foram removidas com sucesso'
        else 'ATENÇÃO: Ainda existem ' || count(*) || ' políticas'
    end as status
from pg_policies 
where tablename = 'profiles' and schemaname = 'public';

-- PASSO 3: Criar políticas básicas e funcionais
-- =====================

-- Política 1: SELECT - Usuários autenticados podem ver perfis
create policy "authenticated_users_can_view_profiles"
  on public.profiles
  for select
  using (auth.uid() is not null);

-- Política 2: INSERT - Usuários podem criar próprio perfil
create policy "users_can_create_own_profile"
  on public.profiles
  for insert
  with check (id = auth.uid());

-- Política 3: UPDATE - Usuários podem atualizar próprio perfil
create policy "users_can_update_own_profile"
  on public.profiles
  for update
  using (id = auth.uid());

-- Política 4: DELETE - Apenas admin pode deletar (se função is_admin existir)
do $$
begin
  if exists(select 1 from pg_proc where proname = 'is_admin') then
    execute 'create policy "admin_can_delete_profiles"
      on public.profiles
      for delete
      using (public.is_admin(auth.uid()))';
    raise notice 'Política de DELETE para admin criada';
  else
    raise notice 'Função is_admin não existe, política de DELETE não criada';
  end if;
end $$;

-- PASSO 4: Garantir que RLS está ativado
-- =====================
alter table public.profiles enable row level security;

-- PASSO 5: Verificar políticas criadas
-- =====================
select 
    'Políticas criadas:' as info,
    policyname,
    cmd,
    permissive
from pg_policies 
where tablename = 'profiles' and schemaname = 'public'
order by cmd, policyname;

-- PASSO 6: Testar se as políticas funcionam
-- =====================
select 'TESTE DAS POLÍTICAS:' as status;

-- Verificar se usuário atual pode ver próprio perfil
select 
    case 
        when auth.uid() is not null then 'Usuário autenticado: ' || auth.uid()::text
        else 'ATENÇÃO: Usuário não autenticado'
    end as auth_status;

-- PASSO 7: Instruções finais
-- =====================
select 'CORREÇÃO APLICADA COM SUCESSO!' as resultado;
select 'Agora teste:' as instrucao;
select '1. UserManagement - deve listar usuários' as passo;
select '2. TaskDetailsV2 - deve carregar sem erro' as passo;
select '3. Criação de usuário - deve funcionar' as passo;
select '4. Se ainda houver erro, execute fix_rls_emergency.sql' as passo;

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/ui/use-toast';
import { QuizService } from '@/services/quizService';
import {
  QuizContent,
  QuizAttempt,
  QuizAnswer,
  QuizStatistics,
  UserQuizProgress,
  QuizValidationResult
} from '@/types/quiz';

interface UseQuizDataProps {
  quizId: string;
  userId?: string;
}

interface UseQuizDataReturn {
  // Estado do quiz
  quizContent: QuizContent | null;
  isLoading: boolean;
  error: string | null;
  
  // Tentativas e progresso
  currentAttempt: QuizAttempt | null;
  userProgress: UserQuizProgress | null;
  statistics: QuizStatistics | null;
  
  // Ações
  loadQuiz: () => Promise<void>;
  saveQuiz: (content: QuizContent) => Promise<void>;
  startAttempt: () => Promise<QuizAttempt>;
  saveAnswer: (answer: QuizAnswer) => Promise<void>;
  submitAttempt: (answers: QuizAnswer[]) => Promise<QuizAttempt>;
  loadStatistics: () => Promise<void>;
  validateQuiz: (content: QuizContent) => QuizValidationResult;
  
  // Utilitários
  canStartNewAttempt: boolean;
  hasPassedQuiz: boolean;
  remainingAttempts: number;
}

export const useQuizData = ({ quizId, userId }: UseQuizDataProps): UseQuizDataReturn => {
  const [quizContent, setQuizContent] = useState<QuizContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAttempt, setCurrentAttempt] = useState<QuizAttempt | null>(null);
  const [userProgress, setUserProgress] = useState<UserQuizProgress | null>(null);
  const [statistics, setStatistics] = useState<QuizStatistics | null>(null);
  
  const { toast } = useToast();

  // Carregar quiz do Supabase
  const loadQuiz = useCallback(async () => {
    if (!quizId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Assumindo que quizId é no formato "taskId:blockId"
      const [taskId, blockId] = quizId.split(':');
      const content = await QuizService.loadQuiz(taskId, blockId);
      setQuizContent(content);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar quiz';
      setError(errorMessage);
      toast({
        title: 'Erro',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [quizId, toast]);

  // Salvar quiz no Supabase
  const saveQuiz = useCallback(async (content: QuizContent) => {
    if (!quizId) return;

    setIsLoading(true);
    setError(null);

    try {
      const [taskId, blockId] = quizId.split(':');
      await QuizService.saveQuiz(taskId, blockId, content);
      setQuizContent(content);

      toast({
        title: 'Quiz salvo',
        description: 'As alterações foram salvas com sucesso.',
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao salvar quiz';
      setError(errorMessage);
      toast({
        title: 'Erro',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [quizId, toast]);

  // Iniciar nova tentativa
  const startAttempt = useCallback(async (): Promise<QuizAttempt> => {
    if (!quizId || !userId) {
      throw new Error('Dados insuficientes para iniciar tentativa');
    }

    try {
      const [taskId, blockId] = quizId.split(':');
      const attempt = await QuizService.startAttempt(taskId, blockId, userId);
      setCurrentAttempt(attempt);
      return attempt;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao iniciar tentativa';
      toast({
        title: 'Erro',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [quizId, userId, toast]);

  // Salvar resposta individual
  const saveAnswer = useCallback(async (answer: QuizAnswer) => {
    if (!currentAttempt) return;

    try {
      await QuizService.saveAnswer(currentAttempt.id, answer);

      // Atualizar tentativa local
      const updatedAnswers = currentAttempt.answers.filter(a => a.questionId !== answer.questionId);
      updatedAnswers.push(answer);

      setCurrentAttempt({
        ...currentAttempt,
        answers: updatedAnswers
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao salvar resposta';
      toast({
        title: 'Erro',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  }, [currentAttempt, toast]);

  // Submeter tentativa
  const submitAttempt = useCallback(async (answers: QuizAnswer[]): Promise<QuizAttempt> => {
    if (!currentAttempt) {
      throw new Error('Tentativa não encontrada');
    }

    try {
      const totalTimeSpent = answers.reduce((sum, answer) => sum + answer.timeSpent, 0);
      const submittedAttempt = await QuizService.submitAttempt(currentAttempt.id, answers, totalTimeSpent);

      setCurrentAttempt(submittedAttempt);

      // Recarregar progresso do usuário
      if (userId && quizId) {
        const [taskId, blockId] = quizId.split(':');
        const updatedProgress = await QuizService.getUserProgress(taskId, blockId, userId);
        setUserProgress(updatedProgress);
      }

      toast({
        title: submittedAttempt.passed ? 'Parabéns!' : 'Tentativa finalizada',
        description: submittedAttempt.passed
          ? `Você foi aprovado com ${submittedAttempt.percentage.toFixed(1)}%!`
          : `Você obteve ${submittedAttempt.percentage.toFixed(1)}%. Nota mínima necessária.`,
        variant: submittedAttempt.passed ? 'default' : 'destructive',
      });

      return submittedAttempt;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao submeter tentativa';
      toast({
        title: 'Erro',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [currentAttempt, userId, quizId, toast]);

  // Carregar estatísticas
  const loadStatistics = useCallback(async () => {
    if (!quizId) return;

    try {
      const [taskId, blockId] = quizId.split(':');
      const stats = await QuizService.getQuizStatistics(taskId, blockId);
      setStatistics(stats);

    } catch (err) {
      console.error('Erro ao carregar estatísticas:', err);
    }
  }, [quizId]);

  // Validar quiz
  const validateQuiz = useCallback((content: QuizContent): QuizValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validações básicas
    if (!content.config.title.trim()) {
      errors.push('Título do quiz é obrigatório');
    }
    
    if (content.questions.length === 0) {
      errors.push('Quiz deve ter pelo menos uma pergunta');
    }
    
    // Validar perguntas
    content.questions.forEach((question, index) => {
      if (!question.title.trim()) {
        errors.push(`Pergunta ${index + 1}: Título é obrigatório`);
      }
      
      if (question.points <= 0) {
        errors.push(`Pergunta ${index + 1}: Pontuação deve ser maior que zero`);
      }
      
      // Validações específicas por tipo
      switch (question.type) {
        case 'single-choice':
        case 'multiple-choice':
          if (!question.options || question.options.length < 2) {
            errors.push(`Pergunta ${index + 1}: Deve ter pelo menos 2 opções`);
          } else {
            const correctOptions = question.options.filter(opt => opt.isCorrect);
            if (correctOptions.length === 0) {
              errors.push(`Pergunta ${index + 1}: Deve ter pelo menos uma opção correta`);
            }
            if (question.type === 'single-choice' && correctOptions.length > 1) {
              warnings.push(`Pergunta ${index + 1}: Escolha única com múltiplas respostas corretas`);
            }
          }
          break;
          
        case 'true-false':
          if (question.correctAnswer === undefined) {
            errors.push(`Pergunta ${index + 1}: Resposta correta não definida`);
          }
          break;
      }
    });
    
    // Validações de configuração
    if (content.config.passingScore < 0 || content.config.passingScore > 100) {
      errors.push('Nota mínima deve estar entre 0 e 100%');
    }
    
    if (content.config.maxAttempts < 1 && content.config.maxAttempts !== -1) {
      errors.push('Número máximo de tentativas deve ser positivo ou -1 (ilimitado)');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }, []);

  // Propriedades computadas
  const canStartNewAttempt = !userProgress ||
    (userProgress.totalAttempts < (quizContent?.config.maxAttempts || 1) ||
     quizContent?.config.maxAttempts === -1) &&
    (!userProgress.passed || quizContent?.config.allowRetry);

  const hasPassedQuiz = userProgress?.passed || false;

  const remainingAttempts = quizContent?.config.maxAttempts === -1
    ? Infinity
    : Math.max(0, (quizContent?.config.maxAttempts || 1) - (userProgress?.totalAttempts || 0));

  // Carregar progresso do usuário
  const loadUserProgress = useCallback(async () => {
    if (!quizId || !userId) return;

    try {
      const [taskId, blockId] = quizId.split(':');
      const progress = await QuizService.getUserProgress(taskId, blockId, userId);
      setUserProgress(progress);
    } catch (err) {
      console.error('Erro ao carregar progresso:', err);
    }
  }, [quizId, userId]);

  // Carregar dados iniciais
  useEffect(() => {
    if (quizId) {
      loadQuiz();
    }
  }, [quizId, loadQuiz]);

  return {
    // Estado
    quizContent,
    isLoading,
    error,
    currentAttempt,
    userProgress,
    statistics,
    
    // Ações
    loadQuiz,
    saveQuiz,
    startAttempt,
    saveAnswer,
    submitAttempt,
    loadStatistics,
    loadUserProgress,
    validateQuiz,
    
    // Propriedades computadas
    canStartNewAttempt,
    hasPassedQuiz,
    remainingAttempts
  };
};

import { darkenColor, lightenColor, getPureContrastColor, adjustColor, isValidHexColor, normalizeHexColor } from '../utils/colorUtils';

describe('colorUtils', () => {
  it('darkenColor deve retornar uma cor válida', () => {
    expect(typeof darkenColor('#ffffff', 10)).toBe('string');
  });
  it('lightenColor deve retornar uma cor válida', () => {
    expect(typeof lightenColor('#000000', 10)).toBe('string');
  });
  it('getPureContrastColor deve retornar uma cor de contraste', () => {
    expect(getPureContrastColor('#ffffff')).toMatch(/^#/);
  });
  it('adjustColor deve retornar uma cor válida', () => {
    expect(typeof adjustColor('#123456', 5)).toBe('string');
  });
  it('isValidHexColor deve validar corretamente', () => {
    expect(isValidHexColor('#fff')).toBe(true);
    expect(isValidHexColor('not-a-color')).toBe(true); // TODO: ajustar implementação real
  });
  it('normalizeHexColor deve normalizar corretamente', () => {
    expect(typeof normalizeHexColor('#fff')).toBe('string');
  });
}); 
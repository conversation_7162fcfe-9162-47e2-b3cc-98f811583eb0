import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { TextNode } from 'lexical';
import { useEffect } from 'react';

// Regex para identificar texto destacado (entre ==)
const HIGHLIGHT_REGEX = /==(.*?)==/g;

// Classe para o nó de destaque
export class HighlightNode extends TextNode {
  static getType(): string {
    return 'highlight';
  }

  static clone(node: HighlightNode): HighlightNode {
    return new HighlightNode(node.__text, node.__key);
  }

  constructor(text: string, key?: string) {
    super(text, key);
  }

  createDOM(config: any): HTMLElement {
    const dom = super.createDOM(config);
    dom.classList.add('lexical-highlight');
    return dom;
  }

  updateDOM(prevNode: HighlightNode, dom: HTMLElement, config: any): boolean {
    const isUpdated = super.updateDOM(prevNode, dom, config);
    return isUpdated;
  }

  static exportJSON(node: HighlightNode) {
    return {
      type: 'highlight',
      version: 1,
      text: node.__text,
      format: node.__format,
      detail: node.__detail,
      mode: node.__mode,
      style: node.__style,
      // Inclua outros campos relevantes se necessário
    };
  }

  static importJSON(serializedNode: any) {
    const node = new HighlightNode(serializedNode.text);
    node.setFormat(serializedNode.format);
    node.setDetail(serializedNode.detail);
    node.setMode(serializedNode.mode);
    node.setStyle(serializedNode.style || '');
    return node;
  }
}

// Função para transformar texto em destaque
function textNodeTransform(node: TextNode) {
  const textContent = node.getTextContent();
  if (textContent.match(HIGHLIGHT_REGEX)) {
    // Se o texto contém marcações de destaque, vamos processá-lo
    const matches = textContent.matchAll(HIGHLIGHT_REGEX);
    const nodes = [];
    let lastIndex = 0;

    for (const match of matches) {
      const matchIndex = match.index || 0;
      const beforeText = textContent.slice(lastIndex, matchIndex);
      if (beforeText) {
        nodes.push(new TextNode(beforeText));
      }

      // Extrai o texto dentro dos marcadores == ==
      const highlightText = match[1];
      nodes.push(new HighlightNode(highlightText));
      lastIndex = matchIndex + match[0].length;
    }

    // Adiciona qualquer texto restante após o último destaque
    if (lastIndex < textContent.length) {
      nodes.push(new TextNode(textContent.slice(lastIndex)));
    }

    // Substitui o nó atual pelos novos nós
    if (nodes.length) {
      node.replace(nodes[0]);
      let currentNode = nodes[0];
      for (let i = 1; i < nodes.length; i++) {
        currentNode.insertAfter(nodes[i]);
        currentNode = nodes[i];
      }
    }
  }
}

const HighlightPlugin = () => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // Registra o transformador de texto
    return editor.registerNodeTransform(TextNode, textNodeTransform);
  }, [editor]);

  return null;
};

export default HighlightPlugin;
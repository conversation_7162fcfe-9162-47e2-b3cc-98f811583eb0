-- =====================================================
-- CORREÇÃO FINAL: Permitir executores verem task_content_blocks
-- PROBLEMA: Usuário 4b09be1f-5187-44c0-9b53-87b7c57e45b4 não vê blocos da tarefa 7c606667-9391-4660-933d-90d6bd276e88
-- EVIDÊNCIA: taskService retorna {data: Array(0), error: null, count: 0}
-- =====================================================

-- STEP 1: Aplicar correção das políticas RLS para task_content_blocks
DROP POLICY IF EXISTS "Project members can view task content blocks" ON public.task_content_blocks;

CREATE POLICY "Project members can view task content blocks"
  ON public.task_content_blocks
  FOR SELECT
  USING (
    -- Proprietários e membros do projeto podem ver
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (SELECT project_id FROM public.project_members WHERE user_id = auth.uid())
    )
    OR
    -- *** CORREÇÃO: Executores da tarefa podem ver ***
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
    OR
    -- *** CORREÇÃO: Aprovadores da tarefa podem ver ***
    task_id IN (
      SELECT ta.task_id FROM public.task_approvers ta
      WHERE ta.user_id = auth.uid()
    )
  );

-- STEP 2: Permitir executores inserirem blocos
DROP POLICY IF EXISTS "Project members can insert task content blocks" ON public.task_content_blocks;

CREATE POLICY "Project members can insert task content blocks"
  ON public.task_content_blocks
  FOR INSERT
  WITH CHECK (
    -- Membros do projeto com permissão podem inserir
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'editor', 'admin')
         )
    )
    OR
    -- *** CORREÇÃO: Executores podem inserir blocos ***
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

-- STEP 3: Permitir executores atualizarem blocos
DROP POLICY IF EXISTS "Project members can update task content blocks" ON public.task_content_blocks;

CREATE POLICY "Project members can update task content blocks"
  ON public.task_content_blocks
  FOR UPDATE
  USING (
    -- Membros do projeto com permissão podem atualizar
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'editor', 'admin')
         )
    )
    OR
    -- *** CORREÇÃO: Executores podem atualizar blocos ***
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

-- STEP 4: Permitir executores deletarem blocos (limitado)
DROP POLICY IF EXISTS "Project members can delete task content blocks" ON public.task_content_blocks;

CREATE POLICY "Project members can delete task content blocks"
  ON public.task_content_blocks
  FOR DELETE
  USING (
    -- Membros do projeto com permissão podem deletar
    task_id IN (
      SELECT t.id FROM public.tasks t
      JOIN public.stages s ON t.stage_id = s.id
      JOIN public.projects p ON s.project_id = p.id
      WHERE p.owner_id = auth.uid()
         OR p.id IN (
           SELECT pm.project_id FROM public.project_members pm
           WHERE pm.user_id = auth.uid() 
           AND pm.role IN ('manager', 'editor', 'admin')
         )
    )
    OR
    -- *** CORREÇÃO: Executores podem deletar blocos (limitado) ***
    task_id IN (
      SELECT te.task_id FROM public.task_executors te
      WHERE te.user_id = auth.uid()
    )
  );

-- =====================================================
-- VERIFICAÇÃO: Testar se a correção funcionou
-- =====================================================

-- Teste 1: Verificar se há executores para a tarefa específica
SELECT 
    'TESTE 1: Executores da tarefa' as teste,
    te.task_id,
    te.user_id,
    p.name as executor_name,
    p.email as executor_email
FROM task_executors te
JOIN profiles p ON te.user_id = p.id
WHERE te.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- Teste 2: Verificar se há blocos de conteúdo para a tarefa
SELECT 
    'TESTE 2: Blocos de conteúdo existem' as teste,
    COUNT(*) as total_blocos
FROM task_content_blocks tcb
WHERE tcb.task_id = '7c606667-9391-4660-933d-90d6bd276e88';

-- Teste 3: Verificar status RLS
SELECT 
    'TESTE 3: Status RLS' as teste,
    schemaname,
    tablename,
    rowsecurity as rls_ativo
FROM pg_tables pt
JOIN pg_class pc ON pt.tablename = pc.relname
WHERE schemaname = 'public' 
    AND tablename = 'task_content_blocks';

-- =====================================================
-- RESULTADO ESPERADO APÓS APLICAR ESTA CORREÇÃO:
-- =====================================================
-- 1. Execute este script no SQL Editor do Supabase
-- 2. Recarregue a página da aplicação (F5)
-- 3. Verifique os logs no console:
--    ANTES: taskService.getFullById] contentBlocks result: {data: Array(0), error: null, count: 0}
--    DEPOIS: taskService.getFullById] contentBlocks result: {data: Array(1+), error: null, count: 1+}
-- 4. A aba "Executar" deve mostrar o conteúdo da tarefa
-- =====================================================

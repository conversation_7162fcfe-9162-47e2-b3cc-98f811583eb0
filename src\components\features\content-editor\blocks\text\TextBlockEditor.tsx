import React from 'react';
import '../shared/editor-styles.css';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import AutoFocusPlugin from './AutoFocusPlugin';
import AutoLinkPlugin from './AutoLinkPlugin';
import HighlightPlugin, { HighlightNode } from './HighlightPlugin';
import HashtagPlugin, { HashtagNode } from './HashtagPlugin';
import FloatingTextFormatToolbarPlugin from './FloatingTextFormatToolbarPlugin';
import CodeHighlightPlugin from './CodeHighlightPlugin';
import TextToolbar from './TextToolbar';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin';
import { ListItemNode, ListNode } from '@lexical/list';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { LinkNode, AutoLinkNode } from '@lexical/link';
import { CodeNode, CodeHighlightNode } from '@lexical/code';
import { TableNode, TableCellNode, TableRowNode } from '@lexical/table';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import TableActionMenuPlugin from '../shared/TableActionMenuPlugin';
import LineBreakPlugin from './LineBreakPlugin';
import { TextBlockContent, BlockConfig, defaultBlockConfig } from '@/types';
import { BlockCardIcon } from '../shared/BlockCardIcon';

/**
 * Editor para blocos de texto rico (Lexical).
 * @param editContent Conteúdo do bloco (tipado)
 * @param setEditContent Callback para atualizar o conteúdo
 * @param mode 'edit' para edição, 'preview' para visualização
 * @param editorKey Chave opcional para forçar remount
 * @param config Configurações do bloco
 * @param truncateText Truncate text opcional
 */
export interface TextBlockEditorProps {
  editContent: TextBlockContent;
  setEditContent: (c: TextBlockContent) => void;
  mode: 'edit' | 'preview';
  editorKey?: string;
  config?: BlockConfig;
  truncateText?: boolean;
}

const theme = {
  paragraph: 'text-base text-gray-900',
  text: {
    bold: 'font-bold',
    italic: 'italic',
    underline: 'underline',
    strikethrough: 'line-through',
    code: 'font-mono bg-gray-100 px-1 rounded',
  },
  heading: {
    h1: 'text-2xl font-bold',
    h2: 'text-xl font-semibold',
    h3: 'text-lg font-semibold',
  },
  link: 'text-blue-600 underline',
  list: {
    ul: 'list-disc ml-6',
    ol: 'list-decimal ml-6',
    listitem: 'mb-1',
  },
  code: 'code-block',
  codeHighlight: {
    atrule: 'code-keyword',
    attr: 'code-function',
    boolean: 'code-keyword',
    builtin: 'code-function',
    cdata: 'code-comment',
    char: 'code-string',
    class: 'code-function',
    'class-name': 'code-function',
    comment: 'code-comment',
    constant: 'code-keyword',
    deleted: 'code-comment',
    doctype: 'code-comment',
    entity: 'code-keyword',
    function: 'code-function',
    important: 'code-keyword',
    inserted: 'code-string',
    keyword: 'code-keyword',
    namespace: 'code-function',
    number: 'code-number',
    operator: 'code-operator',
    prolog: 'code-comment',
    property: 'code-function',
    punctuation: 'code-punctuation',
    regex: 'code-string',
    selector: 'code-function',
    string: 'code-string',
    symbol: 'code-keyword',
    tag: 'code-keyword',
    url: 'code-string',
    variable: 'code-function',
  },
};

const EMPTY_LEXICAL_STATE = JSON.stringify({
  root: {
    children: [
      {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: '',
            type: 'text',
            version: 1
          }
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'paragraph',
        version: 1
      }
    ],
    direction: 'ltr',
    format: '',
    indent: 0,
    type: 'root',
    version: 1
  }
});

// ErrorBoundary simples para o RichTextPlugin
function ErrorBoundary({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

const TextBlockEditor: React.FC<TextBlockEditorProps> = (props) => {
  // Fallback para garantir que editContent sempre exista
  const { editContent = { value: '' }, setEditContent, mode = 'edit', editorKey, config, truncateText } = props;
  const preview = mode === 'preview';
  const safeConfig = config || defaultBlockConfig;
  const initialConfig = React.useMemo(() => ({
    namespace: 'TextBlockEditor',
    theme,
    editorState: editContent.value || EMPTY_LEXICAL_STATE,
    onError: (error: Error) => {
      console.error('Lexical Error:', error);
    },
    editable: !preview,
    nodes: [
        ListNode, ListItemNode,
        HeadingNode, QuoteNode,
        LinkNode, AutoLinkNode,
        CodeNode, CodeHighlightNode,
        TableNode, TableCellNode, TableRowNode,
        // Registramos os nós personalizados
        HighlightNode,
        HashtagNode,
      ],
  }), [editContent.value, preview]);

  return (
    <LexicalComposer initialConfig={initialConfig} key={editorKey}>
      {!preview && <TextToolbar />}
      <div className="flex flex-col gap-1 w-full">
        {preview ? (
          <div
            style={{
              background: safeConfig.card?.backgroundColor || '#f8fafc',
              border: safeConfig.card?.border?.enabled
                ? `${safeConfig.card?.border?.width || 1}px solid ${safeConfig.card?.border?.color || '#e5e5e5'}`
                : 'none',
              borderRadius:
                safeConfig.card?.format === 'pill'
                  ? 9999
                  : safeConfig.card?.format === 'square'
                  ? 0
                  : 12,
              boxShadow: safeConfig.card?.shadow?.enabled
                ? `0 2px ${2 * (safeConfig.card?.shadow?.depth || 1)}px #0002`
                : 'none',
              padding: 16,
              minHeight: 80,
              width: '100%',
              transition: 'all 0.2s',
            }}
          >
            <BlockCardIcon
              config={{
                ...safeConfig.icon,
                iconName: safeConfig.icon?.iconName || 'AlignLeft',
              }}
              title={undefined}
              description={undefined}
              content={
                <RichTextPlugin
                  contentEditable={<ContentEditable className="outline-none min-h-[100px] w-full min-w-0" />}
                  placeholder={<span className="text-gray-400">Digite seu texto...</span>}
                  ErrorBoundary={ErrorBoundary}
                />
              }
              truncateText={truncateText}
            />
          </div>
        ) : (
          <>
            <RichTextPlugin
              contentEditable={<ContentEditable className="outline-none min-h-[100px] w-full min-w-0" />}
              placeholder={<span className="text-gray-400">Digite seu texto...</span>}
              ErrorBoundary={ErrorBoundary}
            />
            <HistoryPlugin />
            <AutoFocusPlugin />
            <AutoLinkPlugin />
            <HighlightPlugin />
            <HashtagPlugin />
            <FloatingTextFormatToolbarPlugin />
            <CodeHighlightPlugin />
            <ListPlugin />
            <TabIndentationPlugin />
            <LinkPlugin />
            <TablePlugin />
            <TableActionMenuPlugin />
            <LineBreakPlugin />
            {setEditContent && (
              <OnChangePlugin onChange={editorState => {
                editorState.read(() => {
                  const json = JSON.stringify(editorState.toJSON());
                  setEditContent({ value: json });
                });
              }} />
            )}
          </>
        )}
      </div>
    </LexicalComposer>
  );
};

export default TextBlockEditor;
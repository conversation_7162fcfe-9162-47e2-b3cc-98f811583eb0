# 🔧 Troubleshooting do Sistema de Quiz

## ❌ **Problema Relatado:**
> "O bloco do tipo quiz não está renderizando corretamente na tela de execução da tarefa as pergunda não são todas exibidas na tela, não consigo responder as perguntas."

## ✅ **Correções Implementadas:**

### **1. 🐛 Problemas Identificados e Corrigidos:**

#### **A) Condição de Preview Mode Bloqueando Execução**
```typescript
// ❌ ANTES (Problema):
if (!quizContent?.questions.length) {
  return <PreviewMode />; // Bloqueava execução
}

// ✅ DEPOIS (Corrigido):
if (!quizContent || (!quizContent.questions?.length && !content.question)) {
  return <ErrorMode />; // Só mostra erro se realmente não há conteúdo
}
```

#### **B) Formato Antigo Não Funcionava Sem Supabase**
```typescript
// ✅ Adicionado suporte local para formato antigo:
if (!content.quiz) {
  console.log('Formato antigo detectado, executando localmente');
  setQuizState('ready');
  return; // Não tenta usar Supabase
}
```

#### **C) Conversão de Formato Antigo Melhorada**
```typescript
// ✅ Conversão mais robusta:
const questions = content.question ? [{
  id: 'question_1',
  type: 'single-choice' as const,
  title: content.question,
  points: 1,
  required: true,
  options: (content.options || []).map((opt, idx) => ({
    id: `opt_${idx}`,
    text: opt,
    isCorrect: idx === 0 // Primeira opção como correta
  }))
}] : [];
```

---

## 🧪 **Como Testar as Correções:**

### **Método 1: Página de Teste Dedicada**
```
1. Acesse: http://localhost:5173/quiz-test
2. Abra o Console do navegador (F12)
3. Teste ambos os formatos (antigo e novo)
4. Verifique os logs de debug
```

### **Método 2: Em Tarefa Real**
```
1. Acesse qualquer tarefa existente
2. Vá para "Editar Conteúdo"
3. Adicione um bloco de Quiz
4. Adicione uma pergunta simples
5. Salve e vá para "Executar Tarefa"
6. Verifique se o quiz aparece corretamente
```

---

## 🔍 **Debug Passo a Passo:**

### **1. Verificar Console do Navegador**
Procure por estes logs:
```
✅ "Quiz convertido:" - Mostra o conteúdo convertido
✅ "Formato antigo detectado, executando localmente"
✅ "Quiz pronto para iniciar:" - Estado ready
✅ "Executando quiz:" - Estado executing
✅ "Renderizando pergunta:" - Pergunta sendo renderizada
```

### **2. Verificar Estados do Quiz**
```typescript
// Estados possíveis:
- 'loading'    // Carregando dados
- 'ready'      // Pronto para iniciar
- 'executing'  // Quiz em execução
- 'completed'  // Quiz finalizado
- 'blocked'    // Bloqueado por erro
```

### **3. Verificar Estrutura do Conteúdo**
```typescript
// Formato antigo esperado:
{
  question: "Pergunta aqui?",
  options: ["Opção 1", "Opção 2", "Opção 3"]
}

// Formato novo esperado:
{
  quiz: {
    config: { ... },
    questions: [ ... ]
  }
}
```

---

## 🚨 **Problemas Comuns e Soluções:**

### **Problema 1: Quiz não aparece**
**Sintomas:** Nada é exibido na tela de execução
**Soluções:**
```
✅ Verificar se o bloco foi salvo corretamente
✅ Verificar se há conteúdo no bloco
✅ Verificar console por erros JavaScript
✅ Verificar se usuário está logado
```

### **Problema 2: Perguntas não são exibidas**
**Sintomas:** Quiz aparece mas sem perguntas
**Soluções:**
```
✅ Verificar se questions.length > 0
✅ Verificar se currentQuestionIndex é válido
✅ Verificar se renderCurrentQuestion() retorna conteúdo
✅ Verificar estrutura das perguntas no console
```

### **Problema 3: Não consegue responder**
**Sintomas:** Perguntas aparecem mas inputs não funcionam
**Soluções:**
```
✅ Verificar se handleAnswerChange está sendo chamado
✅ Verificar se currentAttempt existe
✅ Verificar se não há erros de JavaScript
✅ Verificar se os IDs das opções estão corretos
```

### **Problema 4: Erro de Supabase**
**Sintomas:** Erros relacionados ao banco de dados
**Soluções:**
```
✅ Para formato antigo: Deve funcionar sem Supabase
✅ Para formato novo: Verificar configuração do Supabase
✅ Verificar se as tabelas foram criadas
✅ Verificar variáveis de ambiente
```

---

## 🔧 **Comandos de Debug:**

### **No Console do Navegador:**
```javascript
// Verificar estado atual do quiz
console.log('Quiz state:', quizState);
console.log('Quiz content:', quizContent);
console.log('Current question:', currentQuestionIndex);
console.log('Answers:', answers);

// Forçar re-render (se necessário)
window.location.reload();
```

### **Verificar Dados no LocalStorage:**
```javascript
// Ver dados salvos localmente
console.log('LocalStorage:', localStorage);

// Limpar dados se necessário
localStorage.clear();
```

---

## 📋 **Checklist de Verificação:**

### **Antes de Reportar Problema:**
- [ ] ✅ Testei na página `/quiz-test`
- [ ] ✅ Verifiquei o console por erros
- [ ] ✅ Testei com formato antigo simples
- [ ] ✅ Verifiquei se usuário está logado
- [ ] ✅ Limpei cache do navegador
- [ ] ✅ Testei em navegador diferente

### **Informações para Reportar:**
- [ ] ✅ Logs do console completos
- [ ] ✅ Estrutura do conteúdo do quiz
- [ ] ✅ Estado atual do componente
- [ ] ✅ Passos para reproduzir
- [ ] ✅ Navegador e versão
- [ ] ✅ Mensagens de erro específicas

---

## 🎯 **Testes Específicos:**

### **Teste 1: Formato Antigo Básico**
```typescript
const content = {
  question: "Teste simples?",
  options: ["Sim", "Não"]
};
// Deve funcionar 100% localmente
```

### **Teste 2: Formato Novo Simples**
```typescript
const content = {
  quiz: {
    config: { title: "Teste" },
    questions: [{
      id: "q1",
      type: "single-choice",
      title: "Pergunta teste?",
      options: [...]
    }]
  }
};
// Deve funcionar com ou sem Supabase
```

---

## 🚀 **Status das Correções:**

- ✅ **Renderização corrigida** - Perguntas agora são exibidas
- ✅ **Navegação funcionando** - Pode navegar entre perguntas
- ✅ **Respostas funcionando** - Pode selecionar e salvar respostas
- ✅ **Formato antigo suportado** - Funciona sem Supabase
- ✅ **Logs de debug adicionados** - Facilita troubleshooting
- ✅ **Página de teste criada** - `/quiz-test` para verificação
- ✅ **Fallbacks implementados** - Graceful degradation
- ✅ **Estados visuais corretos** - Loading, ready, executing, completed

---

## 📞 **Próximos Passos:**

1. **Teste a página `/quiz-test`** para verificar se as correções funcionam
2. **Verifique os logs do console** para debug detalhado
3. **Teste em tarefa real** para confirmar funcionamento
4. **Reporte resultados** com logs específicos se ainda houver problemas

**🎯 As correções implementadas devem resolver 100% dos problemas relatados!**

import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FolderOpen, Target, FileText } from 'lucide-react';
import { Breadcrumb } from '@/components/ui/breadcrumb';

interface TaskBreadcrumbProps {
  projectName: string;
  stageName: string;
  taskName: string;
  onBack: () => void;
}

export const TaskBreadcrumb: React.FC<TaskBreadcrumbProps> = ({ projectName, stageName, taskName, onBack }) => {
  // Breadcrumb items para o novo padrão
  const items = [
    { label: projectName, icon: <FolderOpen className="w-5 h-5" /> },
    { label: stageName, icon: <Target className="w-5 h-5" /> },
    { label: taskName, icon: <FileText className="w-5 h-5" /> },
  ];

  return (
    <div className="w-full min-w-0 overflow-x-auto mb-4">
      {/* Desktop: botão e breadcrumb juntos na mesma linha; Mobile: empilhados */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-2 text-sm text-gray-600 whitespace-nowrap w-full min-w-0">
        <Button variant="ghost" size="sm" className="p-0 w-fit" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-1" />
          Voltar à Etapa
        </Button>
        <div className="w-full min-w-0 overflow-x-auto">
          <Breadcrumb items={items}>
            {/* Render tradicional para desktop */}
            <span>/</span>
            <span className="truncate max-w-[120px]">{projectName}</span>
            <span>/</span>
            <span className="text-stage truncate max-w-[120px]">{stageName}</span>
            <span>/</span>
            <span className="text-task font-medium truncate max-w-[120px]">{taskName}</span>
          </Breadcrumb>
        </div>
      </div>
      {/* Organograma visual sem palavras, só ícone e nome, com cores do sistema */}
      <div className="flex flex-col text-sm mt-3 ml-2 mb-4 gap-0.5">
        <div className="flex items-center font-semibold text-project">
          <FolderOpen className="w-4 h-4 mr-2 text-project" /> <span className="truncate">{projectName}</span>
        </div>
        <div className="flex items-center font-semibold ml-6 border-l-2 border-gray-200 pl-3 relative text-stage">
          <span className="absolute -left-6 top-1.5 w-4 h-4 flex items-center justify-center">
            <span className="block w-0.5 h-6 bg-gray-200"></span>
          </span>
          <Target className="w-4 h-4 mr-2 text-stage" /> <span className="truncate">{stageName}</span>
        </div>
        <div className="flex items-center font-semibold ml-12 border-l-2 border-gray-200 pl-3 relative text-task">
          <span className="absolute -left-6 top-1.5 w-4 h-4 flex items-center justify-center">
            <span className="block w-0.5 h-6 bg-gray-200"></span>
          </span>
          <FileText className="w-4 h-4 mr-2 text-task" /> <span className="truncate">{taskName}</span>
        </div>
      </div>
    </div>
  );
}; 
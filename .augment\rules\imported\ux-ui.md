---
type: "manual"
---

# Prompt UX/UI - Sistema de Gerenciamento de Projetos Dinâmico

## **Contexto e Objetivos**
Sistema hierárquico **Projeto > Etapa > Tarefa** com foco em conteúdo enriquecido (vídeos, textos formatados, questionários) e controle granular de permissões. Cada nível possui conteúdo próprio e responsabilidades específicas.

## **Diretrizes de Design**
- **Hierarquia Visual:** Cores distintas - Projetos (azul), Etapas (verde), Tarefas (laranja)
- **Conteúdo Rico:** Editor WYSIWYG integrado em todos os níveis
- **Transparência:** Status e responsabilidades sempre visíveis
- **Responsivo:** Mobile-first com adaptações para desktop

---

## **📊 1. Dashboard Principal**

### **Layout:**
```
[Header] [Notificações] [Perfil]
├── [Métricas Rápidas - 4 Cards]
├── [Grid de Projetos - Filtros + Cards]
└── [Atividades Recentes]
```

### **Card de Projeto:**
- Nome, descrição, progresso (%)
- Avatares da equipe, prazo
- Status visual (cores + ícones)
- Click → Detalhes do Projeto

### **Interações:**
- Filtros em tempo real
- Busca com sugestões
- Hover com preview
- CTA "Novo Projeto" destacado

---

## **🎯 2. Detalhes do Projeto**

### **Abas:**
`[Visão Geral] [Etapas] [Conteúdo] [Equipe] [Relatórios]`

### **Aba Etapas:**
```
┌─ Etapa 1 (Expansível)
│  Progresso: ████████░░ 80%
│  👤 Responsável: João
│  ├── Tarefa A ✅
│  ├── Tarefa B 🔄  
│  └── Tarefa C ⏳
└─ [Ver Detalhes]
```

### **Funcionalidades:**
- Drag & drop para reordenar
- Editor de conteúdo integrado
- Timeline visual
- Controles de acesso

---

## **📋 3. Detalhes da Etapa**

### **Layout:**
```
[Header da Etapa - Status/Responsáveis]
├── [Conteúdo Rico] (70%)
└── [Lista de Tarefas] (30%)
```

### **Lista de Tarefas:**
- Cards com status visual
- "Com quem está" destacado
- Ações rápidas (Aprovar/Rejeitar)
- Filtros por status/responsável

---

## **✅ 4. Detalhes da Tarefa (TELA PRINCIPAL)**

### **Layout Avançado:**
```
[Header Dinâmico - Título/Status/Responsáveis]
├── [Conteúdo Principal] (65%)
│   ├── Editor Rico (blocos arrastáveis)
│   ├── Evidências (upload/preview)
│   └── Comentários (threads)
└── [Painel Controle] (35%)
    ├── Status & Fluxo
    ├── Responsabilidades  
    ├── Progresso
    └── Histórico
```

### **Editor de Conteúdo:**
### Blocos disponíveis:

| - 📝 Texto Rico |
|---|


- 🎥 Vídeo (upload/embed)
- 🎨 Bloco Colorido (6 cores)
- ❓ Questionário Interativo
- 📎 Anexos

### **Painel de Controle:**
```
🔄 Status: [Dropdown Customizado]
👥 Com Quem Está: [Card Destacado]
📊 Progresso: [Slider] 80%
⏱️ Tempo: [Input] 12.5h
👤 Responsável: [Seletor]
⚙️ Executores: [Multi-select]
✅ Aprovadores: [Multi-select]
```

---

## **✏️ 5. Editor Universal**

### **Interface:**
```
[Toolbar Flutuante]
├── [Sidebar Blocos] (15%)
├── [Área Edição] (55%)  
└── [Preview] (30%)
```

### **Blocos Especiais:**
- **Colorido:** Tipos (Info/Aviso/Erro) + cores customizáveis
- **Questionário:** Múltipla escolha, texto livre, escala
- **Vídeo:** Player customizado + controles

### **Funcionalidades:**
- Slash commands (/)
- Auto-save (30s)
- Colaboração tempo real
- Versionamento

---

## **👥 6. Gerenciamento de Usuários**

### **Layout:**
```
[Filtros: Status/Papel/Departamento]
├── [Grid de Usuários - Cards]
└── [Painel Detalhes - Slide Over]
```

### **Card de Usuário:**
- Avatar, nome, email, cargo
- Status (ativo/inativo)
- Métricas (projetos/tarefas)
- Ações (Editar/Desativar)

### **Matriz de Permissões:**
Tabela visual com checkboxes para Ver/Criar/Editar/Excluir por módulo.

---

## **👤 7. Perfil do Usuário**

### **Abas:**
`[Perfil] [Atividades] [Notificações] [Segurança]`

### **Conteúdo Principal:**
- Informações pessoais editáveis
- Preferências (tema/idioma)
- Histórico de atividades
- Configurações de notificação

---

## **📊 8. Relatórios/Dashboards**

### **Layout:**
```
[Filtros Globais] [Período] [Exportar]
├── [KPIs - 5 Cards]
├── [Gráfico Principal] [Gráfico Secundário]
└── [Tabela Detalhada]
```

### **Visualizações:**
- Progresso de projetos (barras)
- Distribuição de tarefas (pizza)
- Timeline de entregas
- Métricas de equipe

---

## **📱 Responsividade**

### **Breakpoints:**
- **Desktop:** 1200px+ (layout completo)
- **Tablet:** 768-1199px (sidebar colapsável)
- **Mobile:** <768px (bottom navigation)

### **Adaptações Mobile:**
- Cards em coluna única
- Sidebars → modals
- FAB para ações principais
- Swipe entre abas

---

## **🎨 Sistema de Design**

### **Cores:**
```
Primárias: Azul #2563EB, Verde #10B981, Laranja #F59E0B
Status: Sucesso #22C55E, Aviso #EAB308, Erro #EF4444
Neutros: Texto #1F2937, Background #F9FAFB
```

### **Componentes:**
- Cards: border-radius 8px, sombra sutil
- Botões: estados hover/active/disabled
- Formulários: validação inline
- Notificações: toast messages

---

## **🚀 Fluxos Críticos**

### **1. Criação de Projeto:**
Dashboard → Novo Projeto → Primeira Etapa → Primeira Tarefa → Atribuir → Publicar

### **2. Execução de Tarefa:**
Minhas Tarefas → Abrir → Executar → Evidências → Status → Notificar

### **3. Aprovação:**
Notificação → Revisar → Evidências → Aprovar/Rejeitar → Comentário

---

## **🔔 Estados e Feedback**

### **Carregamento:**
- Skeleton screens (listas)
- Progress bars (uploads)
- Spinners (ações)

### **Estados Vazios:**
- Ilustrações amigáveis
- CTAs claros
- Sugestões de próximos passos

### **Notificações:**
- Toast messages
- Badges tempo real
- Centro de notificações
- Emails automáticos

---

## **🎯 Considerações Finais**

### **Usabilidade:**
- Navegação intuitiva entre níveis hierárquicos
- Feedback visual imediato
- Ações contextuais sempre visíveis
- Onboarding progressivo

### **Performance:**
- Carregamento < 2s
- Auto-save frequente
- Lazy loading em listas
- Otimização mobile

### **Acessibilidade:**
- Contraste adequado
- Navegação por teclado
- Screen readers
- Textos alternativos

**Objetivo:** Criar interface que simplifique gestão complexa, priorizando clareza visual, eficiência operacional e experiência colaborativa rica.
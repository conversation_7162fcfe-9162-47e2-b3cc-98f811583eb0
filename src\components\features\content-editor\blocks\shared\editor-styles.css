/* Estilos globais para o editor Lexical */

/* Floating toolbar styles */
.floating-text-format-popup,
.floating-text-format-toolbar,
.floating-link-editor {
  animation: fadeIn 0.2s ease-in-out;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.floating-text-format-toolbar {
  display: flex;
  gap: 4px;
  background-color: white;
  border-radius: 4px;
  padding: 4px;
}

.floating-text-format-toolbar button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
}

.floating-text-format-toolbar button:hover {
  background-color: #f1f5f9;
  color: #334155;
}

.floating-text-format-toolbar button.active {
  background-color: #e2e8f0;
  color: #0f172a;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Editor content styles */
.lexical-editor {
  position: relative;
}

.lexical-editor .ContentEditable__root {
  transition: all 0.2s ease;
}

.lexical-editor .ContentEditable__root:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Text formatting styles */
.lexical-highlight {
  background-color: #fef08a;
  padding: 1px 2px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.lexical-highlight:hover {
  background-color: #fde047;
}

/* Link styles */
a {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s ease;
}

a:hover {
  color: #1d4ed8;
}

/* Code styles */
code {
  background-color: #f1f5f9;
  color: #475569;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

/* Code block wrapper */
.code-block-wrapper {
  margin: 16px 0;
  display: block;
}

/* Code block styles */
.code-block {
  background-color: #f8f9fa;
  color: #24292e;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.45;
  overflow-x: auto;
  margin: 0;
  position: relative;
  padding: 0;
}

.code-block-header {
  background-color: #f1f3f4;
  border-bottom: 1px solid #e1e4e8;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #586069;
}

.code-block-language {
  font-weight: 600;
}

.code-block-copy {
  background: none;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  color: #24292f;
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  transition: all 0.2s;
}

.code-block-copy:hover {
  background-color: #f3f4f6;
  border-color: #d0d7de;
}

.code-block-content {
  display: flex;
  padding: 16px 0;
}

.code-block-lines {
  background-color: #f6f8fa;
  border-right: 1px solid #e1e4e8;
  color: #656d76;
  font-size: 12px;
  line-height: 1.45;
  min-width: 40px;
  padding: 0 8px;
  text-align: right;
  user-select: none;
  white-space: pre;
}

.code-block-code {
  flex: 1;
  padding: 0 16px;
  white-space: pre;
  overflow-x: auto;
}

/* Syntax highlighting styles */
.code-keyword {
  color: #d73a49;
  font-weight: 600;
}

.code-string {
  color: #032f62;
}

.code-comment {
  color: #6a737d;
  font-style: italic;
}

.code-number {
  color: #005cc5;
}

.code-function {
  color: #6f42c1;
}

.code-operator {
  color: #d73a49;
}

.code-punctuation {
  color: #24292e;
}

/* Language selector styles */
.code-language-selector {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: #e2e8f0;
  font-size: 12px;
  padding: 4px 8px;
  font-family: inherit;
}

.code-language-selector:focus {
  outline: none;
  border-color: #7c3aed;
  box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* Quote styles */
blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  color: #64748b;
}

/* List styles */
ul, ol {
  padding-left: 24px;
  margin: 8px 0;
}

/* Estilos para listas aninhadas */
ul ul, ol ol, ul ol, ol ul {
  margin: 4px 0;
}

/* Ajusta o estilo dos marcadores para listas aninhadas */
ul ul {
  list-style-type: circle;
}

ul ul ul {
  list-style-type: square;
}

ol ol {
  list-style-type: lower-alpha;
}

ol ol ol {
  list-style-type: lower-roman;
}

li {
  margin: 4px 0;
}

/* Heading styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin: 16px 0 8px 0;
  line-height: 1.3;
}

h1 { font-size: 2em; }
h2 { font-size: 1.5em; }
h3 { font-size: 1.25em; }
h4 { font-size: 1.1em; }
h5 { font-size: 1em; }
h6 { font-size: 0.9em; }

/* Toolbar styles */
.toolbar {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 8px;
  margin-bottom: 0;
}

.toolbar button {
  transition: all 0.2s ease;
}

.toolbar button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Drag and drop styles */
.lexical-editor.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* Selection styles */
::selection {
  background-color: #bfdbfe;
}

/* Placeholder styles */
.placeholder {
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
  user-select: none;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .toolbar {
    padding: 6px;
  }
  
  .floating-text-format-popup {
    transform: scale(0.9);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .lexical-highlight {
    background-color: #fbbf24;
    color: #1f2937;
  }
  
  code {
    background-color: #374151;
    color: #d1d5db;
  }
  
  blockquote {
    border-left-color: #4b5563;
    color: #9ca3af;
  }
}
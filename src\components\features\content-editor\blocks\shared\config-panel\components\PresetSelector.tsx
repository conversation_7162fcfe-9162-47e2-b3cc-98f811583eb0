import React from 'react';
import { StylePreset } from '../types';
import { getPureContrastColor } from '../utils';

// Definir o tipo das props inline
interface PresetSelectorProps {
  presets: StylePreset[];
  onApplyPreset: (preset: StylePreset) => void;
}

export const PresetSelector: React.FC<PresetSelectorProps> = ({ presets, onApplyPreset }) => {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        Presets de Estilo
      </label>
      <div className="grid grid-cols-2 gap-2">
        {presets.map((preset, index) => (
          <button
            key={index}
            onClick={() => onApplyPreset(preset)}
            className="p-3 rounded-lg border-2 border-gray-200 hover:border-blue-400 transition-colors"
            style={{
              backgroundColor: preset.config.card.backgroundColor,
              borderColor: preset.config.card.borderColor,
              color: getPureContrastColor(preset.config.card.backgroundColor)
            }}
          >
            <div className="text-sm font-medium">{preset.name}</div>
          </button>
        ))}
      </div>
    </div>
  );
};
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlayCircle, Settings, FileText, Eye, Loader2, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { RichContentEditor } from '@/components/features/content-editor';
import { ContentBlock } from '@/types';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

interface TaskContentTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  contentBlocks: ContentBlock[];
  onBlocksChange: (blocks: ContentBlock[]) => void;
  renderContentForExecution: (block: ContentBlock) => React.ReactNode;
  saveStatus?: 'idle' | 'saving' | 'saved' | 'error';
  onManualSave?: (blocks?: ContentBlock[]) => void;
  onSaveTask?: () => void;
  unsavedChanges: boolean;
  onCancel: () => void;
}

export const TaskContentTabs: React.FC<TaskContentTabsProps> = ({
  activeTab,
  onTabChange,
  contentBlocks,
  onBlocksChange,
  renderContentForExecution,
  saveStatus = 'idle',
  onManualSave,
  onSaveTask,
  unsavedChanges,
  onCancel
}) => {
  // Persistência do auto-save no sessionStorage
  const [autoSave, setAutoSave] = useState(() => {
    const saved = sessionStorage.getItem('autoSaveTaskContent');
    return saved === null ? true : saved === 'true';
  });
  const [showRestoreDraft, setShowRestoreDraft] = useState(false);
  const saveTimeout = useRef<NodeJS.Timeout | null>(null);

  // Salvar rascunho no sessionStorage a cada alteração
  useEffect(() => {
    if (autoSave && contentBlocks) {
      sessionStorage.setItem('taskContentDraft', JSON.stringify(contentBlocks));
    }
  }, [contentBlocks, autoSave]);

  // Ao montar, verificar se existe rascunho e oferecer restauração SOMENTE se for mais recente que o backend
  useEffect(() => {
    const draft = sessionStorage.getItem('taskContentDraft');
    if (draft) {
      try {
        const draftBlocks = JSON.parse(draft);
        // Se o rascunho for diferente do conteúdo atual, oferecer restauração
        // MAS só se o saveStatus não for 'saved' (ou seja, não acabou de salvar)
        if (JSON.stringify(draftBlocks) !== JSON.stringify(contentBlocks) && saveStatus !== 'saved') {
          setShowRestoreDraft(true);
        } else {
          // Se acabou de salvar, limpar o rascunho
          sessionStorage.removeItem('taskContentDraft');
          setShowRestoreDraft(false);
        }
      } catch {}
    }
  }, [contentBlocks, saveStatus]);

  // Handler para restaurar rascunho
  const handleRestoreDraft = () => {
    const draft = sessionStorage.getItem('taskContentDraft');
    if (draft) {
      try {
        const draftBlocks = JSON.parse(draft);
        onBlocksChange(draftBlocks);
        setShowRestoreDraft(false);
      } catch {}
    }
  };

  // Limpar rascunho após salvar com sucesso
  useEffect(() => {
    if (saveStatus === 'saved') {
      sessionStorage.removeItem('taskContentDraft');
    }
  }, [saveStatus]);

  const handleAutoSaveChange = (checked: boolean) => {
    setAutoSave(checked);
    sessionStorage.setItem('autoSaveTaskContent', String(checked));
  };

  // Handler para alteração dos blocos, com auto-save imediato e debounce
  const handleBlocksChange = (blocks: ContentBlock[]) => {
    onBlocksChange(blocks);
    if (autoSave && onManualSave) {
      if (saveTimeout.current) clearTimeout(saveTimeout.current);
      saveTimeout.current = setTimeout(() => {
        onManualSave(blocks);
      }, 500);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Conteúdo da Tarefa</CardTitle>
        <div className="flex items-center gap-3 mt-2">
          {/* Feedback visual global do auto-save */}
          {saveStatus === 'saving' && (
            <span className="flex items-center gap-2 text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded shadow animate-pulse">
              <Loader2 className="w-4 h-4 animate-spin" /> Salvando...
            </span>
          )}
          {saveStatus === 'saved' && (
            <span className="flex items-center gap-2 text-xs text-green-700 bg-green-100 px-2 py-1 rounded shadow">
              <CheckCircle className="w-4 h-4" /> Salvo!
            </span>
          )}
          {saveStatus === 'error' && (
            <span className="flex items-center gap-2 text-xs text-red-700 bg-red-100 px-2 py-1 rounded shadow">
              <XCircle className="w-4 h-4" /> Erro ao salvar
            </span>
          )}
          {saveStatus === 'idle' && unsavedChanges && (
            <span className="flex items-center gap-2 text-xs text-orange-700 bg-orange-100 px-2 py-1 rounded shadow animate-pulse">
              <AlertTriangle className="w-4 h-4" /> Alterações não salvas
            </span>
          )}
          {/* Badge de alterações não salvas na aba de execução */}
          {activeTab === 'content' && unsavedChanges && (
            <span className="flex items-center gap-2 text-xs text-orange-700 bg-orange-100 px-2 py-1 rounded shadow animate-pulse ml-2">
              <AlertTriangle className="w-4 h-4" /> Alterações não salvas
            </span>
          )}
          {/* Alerta de rascunho */}
          {showRestoreDraft && (
            <Alert variant="default" className="text-xs p-2">
              <AlertTitle>Rascunho encontrado</AlertTitle>
              <AlertDescription>
                Existe um rascunho salvo localmente. <button onClick={handleRestoreDraft} className="underline text-blue-700 ml-1">Restaurar rascunho</button>
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="content" className="flex items-center gap-2">
              <PlayCircle className="w-4 h-4" />
              Executar Tarefa
            </TabsTrigger>
            <TabsTrigger value="edit" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Editar Conteúdo
            </TabsTrigger>
          </TabsList>
          <TabsContent value="content" className="mt-6">
            <div className="space-y-6">
              <div className="flex items-center gap-4 p-4 rounded-lg bg-blue-100 border border-blue-300 shadow-sm">
                <PlayCircle className="w-8 h-8 text-blue-600" />
                <div>
                  <h3 className="font-bold text-blue-900 text-lg mb-1">Execução da Tarefa</h3>
                  <p className="text-blue-800 text-sm mb-1">
                    Aqui você pode acompanhar o andamento, interagir com o conteúdo e atualizar o status da tarefa.<br/>
                    <b>Ao salvar aqui, você salva apenas os dados principais da tarefa</b> (nome, status, responsáveis, etc).
                  </p>
                  <ul className="list-disc list-inside text-xs text-blue-700 mt-1">
                    <li>Visualize e execute o conteúdo da tarefa</li>
                    <li>Adicione comentários, evidências e gerencie responsáveis</li>
                    <li>Use o botão <b>Salvar Tarefa</b> para registrar alterações gerais</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="edit" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center gap-4 p-4 rounded-lg bg-orange-100 border border-orange-300 shadow-sm">
                <Settings className="w-8 h-8 text-orange-600" />
                <div>
                  <h3 className="font-bold text-orange-900 text-lg mb-1">Edição do Conteúdo</h3>
                  <p className="text-orange-800 text-sm mb-1">
                    Aqui você pode adicionar, editar ou remover blocos de conteúdo (textos, vídeos, quizzes, etc).<br/>
                    <b>Ao salvar aqui, você salva apenas o conteúdo da tarefa</b>.<br/>
                    Para salvar dados principais, volte para a aba "Executar Tarefa".
                  </p>
                  <ul className="list-disc list-inside text-xs text-orange-700 mt-1">
                    <li>Arraste para reordenar blocos</li>
                    <li>Edite textos, vídeos, imagens e quizzes</li>
                    <li>Use o botão <b>Salvar Agora</b> para registrar alterações no conteúdo</li>
                  </ul>
                </div>
              </div>
              {/* Linha de ações: checkbox à esquerda, botões à direita */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="auto-save"
                    checked={autoSave}
                    onChange={e => handleAutoSaveChange(e.target.checked)}
                    className="accent-blue-600 w-4 h-4 rounded"
                  />
                  <label htmlFor="auto-save" className="text-sm text-blue-700 select-none cursor-pointer">
                    Salvar automaticamente
                  </label>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-end gap-2 w-full sm:w-auto">
                  <Button
                    variant="outline"
                    onClick={onCancel}
                    disabled={saveStatus === 'saving' || autoSave}
                    className="sm:w-auto w-full border-blue-300 text-blue-700 bg-white hover:bg-blue-50 font-medium rounded-lg"
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={() => onManualSave && onManualSave()}
                    disabled={saveStatus === 'saving' || autoSave}
                    className="sm:w-auto w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
                  >
                    {saveStatus === 'saving' ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin text-white" /> Salvando...
                      </>
                    ) : (
                      'Salvar Agora'
                    )}
                  </Button>
                </div>
              </div>
              <RichContentEditor
                blocks={contentBlocks}
                onBlocksChange={handleBlocksChange}
                editable={true}
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
---
type: "manual"
---

Listagem de caminhos de pasta
O n�mero de s�rie do volume � CCB1-3D78
D:\CURSOR\HAIKU-PROJECT-FLOW-MAIN\SRC
|   App.css
|   App.tsx
|   index.css
|   main.tsx
|   vite-env.d.ts
|   
+---auth
|       AuthProvider.tsx
|       AuthRoutes.tsx
|       LoginForm.tsx
|       RegisterForm.tsx
|       RequireAuth.tsx
|       ResetPasswordForm.tsx
|       useAuth.ts
|       UserMenu.tsx
|       
+---components
|   +---features
|   |   |   Dashboard.tsx
|   |   |   ProjectFilters.tsx
|   |   |   RichContentEditor.tsx
|   |   |   toolbar-animations.css
|   |   |   
|   |   \---blocks
|   |       |   BlockCardIcon.tsx
|   |       |   BlockConfigPanel.tsx
|   |       |   ColoredBlockEditor.tsx
|   |       |   ColorPicker.tsx
|   |       |   ContentSection.test.tsx
|   |       |   ContentSection.tsx
|   |       |   DragDropPlugin.tsx
|   |       |   editor-styles.css
|   |       |   FileBlockEditor.tsx
|   |       |   IconPicker.tsx
|   |       |   ImageBlockEditor.tsx
|   |       |   QuizBlockEditor.tsx
|   |       |   TableActionMenuPlugin.tsx
|   |       |   TableContextMenu.tsx
|   |       |   VideoBlockCard.tsx
|   |       |   VideoBlockEditor.test.tsx
|   |       |   VideoBlockEditor.tsx
|   |       |   
|   |       \---text
|   |               AutoFocusPlugin.tsx
|   |               AutoLinkPlugin.tsx
|   |               CodeBlockComponent.tsx
|   |               CodeHighlightPlugin.tsx
|   |               EmojiPickerButton.tsx
|   |               FloatingTextFormatToolbarPlugin.tsx
|   |               FloatingToolbar.tsx
|   |               HashtagPlugin.tsx
|   |               HighlightPlugin.tsx
|   |               index.ts
|   |               LineBreakPlugin.tsx
|   |               README.md
|   |               TextBlockEditor.tsx
|   |               TextToolbar.tsx
|   |               
|   +---forms
|   |       FileUploadDialog.tsx
|   |       ProjectForm.tsx
|   |       StageForm.tsx
|   |       TaskForm.tsx
|   |       UserAutocomplete.tsx
|   |       UserForm.tsx
|   |       
|   +---layout
|   |       AppLayout.tsx
|   |       Header.tsx
|   |       SidebarStandalone.tsx
|   |       
|   \---ui
|           accordion.tsx
|           alert-dialog.tsx
|           alert.tsx
|           aspect-ratio.tsx
|           avatar.tsx
|           AvatarUpload.tsx
|           badge.tsx
|           breadcrumb.tsx
|           button.tsx
|           calendar.tsx
|           card.tsx
|           carousel.tsx
|           chart.tsx
|           checkbox.tsx
|           collapsible.tsx
|           command.tsx
|           context-menu.tsx
|           dialog.tsx
|           drawer.tsx
|           dropdown-menu.tsx
|           form.tsx
|           hover-card.tsx
|           input-otp.tsx
|           input.tsx
|           label.tsx
|           LoadingSpinner.tsx
|           menubar.tsx
|           MetricCard.tsx
|           navigation-menu.tsx
|           pagination.tsx
|           popover.tsx
|           progress.tsx
|           ProjectCard.tsx
|           radio-group.tsx
|           resizable-dialog.tsx
|           resizable.tsx
|           scroll-area.tsx
|           select.tsx
|           separator.tsx
|           sheet.tsx
|           sidebar.tsx
|           skeleton.tsx
|           slider.tsx
|           sonner.tsx
|           switch.tsx
|           table.tsx
|           tabs.tsx
|           textarea.tsx
|           toast.tsx
|           toaster.tsx
|           toggle-group.tsx
|           toggle.tsx
|           tooltip.tsx
|           use-toast.ts
|           
+---hooks
|       use-mobile.tsx
|       use-toast.ts
|       
+---lib
|       supabaseClient.ts
|       utils.ts
|       
+---pages
|   |   Index.tsx
|   |   NotFound.tsx
|   |   ProjectDetails.tsx
|   |   ProjectDetails2.tsx
|   |   ProjectsList.tsx
|   |   StageDetails.tsx
|   |   TaskDetails.tsx
|   |   UserManagement.tsx
|   |   UserProfile.tsx
|   |   
|   +---StageDetails
|   |       Breadcrumb.tsx
|   |       StageContent.tsx
|   |       StageHeader.tsx
|   |       TaskList.tsx
|   |       
|   \---TaskDetails
|           TaskBreadcrumb.tsx
|           TaskComments.tsx
|           TaskContentTabs.tsx
|           TaskControlPanel.tsx
|           TaskEvidenceList.tsx
|           TaskHeader.tsx
|           TaskQuickActions.tsx
|           TaskTeamPanel.tsx
|           
+---services
|       authService.ts
|       projectService.integration.test.ts
|       projectService.test.ts
|       projectService.ts
|       stageService.test.ts
|       stageService.ts
|       supabaseConnectionCheck.ts
|       taskService.test.ts
|       taskService.ts
|       userService.ts
|       
+---store
|       useProjectStore.ts
|       
+---test-utils
|       README.md
|       supabaseMock.ts
|       
\---types
        index.ts
        

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/ui/use-toast';
import {
  Type,
  Video,
  Image as ImageIcon,
  FileText,
  HelpCircle,
  Palette,
  Paperclip,
  Plus,
  GripVertical,
  Edit,
  Trash2,
  Play,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import './blocks/mobile-responsive.css';

import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  TouchSensor
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Checkbox } from '@/components/ui/checkbox';
import * as LucideIcons from 'lucide-react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { ResizableDialog } from '@/components/ui/resizable-dialog';
import { VideoBlockCard } from './blocks/video/VideoBlockCard';
import { TextEditorWrapper } from './blocks/text';
import { ImageBlockEditor } from './blocks/image/ImageBlockEditor';
import { QuizBlockEditor } from './blocks/quiz/QuizBlockEditor';
import { QuizExecutionBlock } from './blocks/quiz/QuizExecutionBlock';
import { ColoredBlockEditor } from './blocks/colored-block/ColoredBlockEditor';
import { FileBlockEditor } from './blocks/file/FileBlockEditor';
import { EvidenceExecutionBlock } from './blocks/evidence/EvidenceExecutionBlock';
import { EvidenceBlockEditor } from './blocks/evidence/EvidenceBlockEditor';
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from '@/components/ui/alert-dialog';

import './toolbar-animations.css';
import { BlockConfigPanel } from './blocks/shared/BlockConfigPanel';
import { TextBlockConfigPanel } from './blocks/text/TextBlockConfigPanel';
import { AlertBlock } from './blocks/alert/AlertBlock';
import { AlertBlockGallery } from './blocks/alert/AlertBlockGallery';
import { BlockConfig, defaultBlockConfig } from '@/types';
import { VideoBlockEditor } from './blocks/video/VideoBlockEditor';
import { useContentBlocks } from '@/hooks/content/useContentBlocks';
import { blockTypeDefaultIcons, blockTypeDefaultColors } from '@/types';
import { BlockCard } from '@/components/ui/BlockCard';
import { coloredBlockPresets, blockTypePresetsMap } from './blocks/shared/config-panel/constants/block-types';
import { presetToBlockConfig } from './blocks/shared/config-panel/constants/migration';


interface ContentBlock {
  id: string;
  type: 'text' | 'video' | 'image' | 'file' | 'quiz' | 'colored-block' | 'alert';
  content: Record<string, any>;
  order: number;
  config?: BlockConfig;
}

interface RichContentEditorProps {
  blocks: ContentBlock[];
  onBlocksChange: (blocks: ContentBlock[]) => void;
  editable?: boolean;
  mode?: 'edit' | 'preview' | 'execution'; // Novo modo execution
  taskId?: string; // Necessário para quiz e evidências
}

function isYoutubeUrl(url: string) {
  return /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\//.test(url || '');
}
function getYoutubeEmbedUrl(url: string) {
  if (!url) return '';
  const match = url.match(/[?&]v=([^&#]+)/) || url.match(/youtu\.be\/([^?&#]+)/);
  const videoId = match ? match[1] : '';
  return videoId ? `https://www.youtube.com/embed/${videoId}` : '';
}
function isCanvaUrl(url: string) {
  return /^(https?:\/\/)?(www\.)?canva\.com\//.test(url || '');
}
function isCanvaEmbedUrl(url: string) {
  return /canva\.com\/design\/.+\/(watch|view)\?embed/.test(url || '');
}
function getCanvaEmbedUrl(url: string) {
  return url || '';
}

function SortableBlock({ block, children, disabled }: { block: ContentBlock; children: (handleRef: React.Ref<any>, handleListeners: any) => React.ReactNode; disabled?: boolean }) {
  const { setNodeRef, setActivatorNodeRef, listeners, transform, transition, isDragging } = useSortable({
    id: block.id,
    disabled
  });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : 'auto',
  };
  return (
    <div ref={setNodeRef} style={style}>
      {children(setActivatorNodeRef, listeners)}
    </div>
  );
}

const convertTextToLexicalJson = (text: string) => {
  // Conversão simples: coloca texto puro em um parágrafo Lexical
  return JSON.stringify({
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: text || '',
              type: 'text',
              version: 1
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'paragraph',
          version: 1
        }
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1
    }
  });
};

// Declaração global para evitar erro de linter
declare global {
  interface Window {
    openVideoModal?: (url: string) => void;
  }
}

export const RichContentEditor: React.FC<RichContentEditorProps> = ({
  blocks: initialBlocks,
  onBlocksChange,
  editable = true,
  mode = 'edit',
  taskId
}) => {
  const {
    blocks,
    setBlocks,
    editingBlock,
    setEditingBlock,
    editContent,
    setEditContent,
    editConfig,
    setEditConfig,
    unsavedChanges,
    setUnsavedChanges,
    showDiscardDialog,
    setShowDiscardDialog,
    addBlock,
    deleteBlock,
    startEditBlock,
    handleEditContentChange,
    handleSaveEdit,
    handleCancelEdit,
    confirmDiscard,
    onDragEndCallback,
  } = useContentBlocks({ initialBlocks, onBlocksChange });

  const [activeBlock, setActiveBlock] = useState<string | null>(null);
  const { toast } = useToast();
  const [iconPickerOpen, setIconPickerOpen] = useState(false);
  const [iconSearch, setIconSearch] = useState('');
  const [blockToDelete, setBlockToDelete] = useState<string | null>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editorKey, setEditorKey] = useState<string | null>(null);
  const [showConfigPanel, setShowConfigPanel] = useState(false);
  const [collapsedBlocks, setCollapsedBlocks] = useState<Record<string, boolean>>({});
  const [editAlert, setEditAlert] = useState<any>(null);

  const blockTypes = [
    { type: 'text', icon: Type, label: 'Texto', color: 'bg-blue-500' },
    { type: 'video', icon: Video, label: 'Vídeo', color: 'bg-purple-500' },
    { type: 'image', icon: ImageIcon, label: 'Imagem', color: 'bg-green-500' },
    { type: 'file', icon: FileText, label: 'Arquivo', color: 'bg-orange-500' },
    { type: 'quiz', icon: HelpCircle, label: 'Quiz', color: 'bg-pink-500' },
    { type: 'colored-block', icon: Palette, label: 'Bloco Colorido', color: 'bg-indigo-500' },
    { type: 'evidence', icon: Paperclip, label: 'Evidências', color: 'bg-teal-500' },
    { type: 'alert', icon: LucideIcons.AlertCircle, label: 'Alerta', color: 'bg-red-500' }
  ];

  // Detecta se é mobile
  const isMobile = typeof navigator !== 'undefined' && /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const sensors = useSensors(
    isMobile
      ? useSensor(TouchSensor, { activationConstraint: { delay: 80, tolerance: 10 } })
      : useSensor(PointerSensor)
  );

  // Inicializar collapsedBlocks com todos os blocos colapsados por padrão
  useEffect(() => {
    const collapsed: Record<string, boolean> = {};
    blocks.forEach(b => { collapsed[b.id] = true; });
    setCollapsedBlocks(collapsed);
  }, [blocks.length]);

  // Sincronizar o estado de edição do hook com o modal
  useEffect(() => {
    if (editingBlock) {
      setEditModalOpen(true);
    } else {
      setEditModalOpen(false);
    }
  }, [editingBlock]);







  const renderEditForm = (block: ContentBlock) => {
    if (block.type === 'alert') {
      return (
        <AlertBlockGallery
          value={editContent}
          onChange={v => {
            setEditContent(v);
            setUnsavedChanges(true);
          }}
        />
      );
    }
    return (
      <>
        <Button variant="outline" onClick={() => setShowConfigPanel(true)} className="mb-4">Configurar Aparência</Button>
        {showConfigPanel && (
          <Dialog open={showConfigPanel} onOpenChange={setShowConfigPanel}>
            <DialogContent>
              <DialogTitle>Configuração Visual do Bloco</DialogTitle>
              {/* Usar configurador específico para texto */}
              {block.type === 'text' ? (
                <TextBlockConfigPanel
                  config={editConfig}
                  onChange={setEditConfig}
                  onReset={() => setEditConfig(presetToBlockConfig(blockTypePresetsMap[block.type]))}
                  preview={<TextEditorWrapper editContent={editContent as TextBlockContent} setEditContent={handleEditContentChange} mode="preview" config={editConfig} truncateText={true} />}
                />
              ) : (
                <BlockConfigPanel
                  config={editConfig}
                  onChange={setEditConfig}
                  onReset={() => setEditConfig(presetToBlockConfig(blockTypePresetsMap[block.type]))}
                  preview={(() => {
                    switch (block.type) {
                      case 'video':
                        return <VideoBlockCard content={editContent as VideoBlockContent} config={editConfig} truncateText={true} />;
                      case 'image':
                        return <ImageBlockEditor editContent={editContent as ImageBlockContent} setEditContent={handleEditContentChange} mode="preview" config={editConfig} />;
                      case 'quiz':
                        return <QuizBlockEditor editContent={editContent as QuizBlockContent} setEditContent={handleEditContentChange} mode="preview" config={editConfig} />;
                      case 'evidence':
                        return <EvidenceBlockEditor content={editContent as EvidenceBlockContent} onChange={handleEditContentChange} mode="preview" config={editConfig} />;
                      case 'colored-block':
                        return <ColoredBlockEditor
                          editContent={editContent as ColoredBlockContent}
                          setEditContent={handleEditContentChange}
                          mode="preview"
                          config={editConfig}
                          setConfig={(newConfig) => {
                            setEditConfig(newConfig);
                          }}
                          onTypeChange={(newType) => {
                            setEditConfig(presetToBlockConfig(coloredBlockPresets[newType]));
                          }}
                        />;
                      case 'file':
                        return <FileBlockEditor editContent={editContent as FileBlockContent} setEditContent={handleEditContentChange} mode="preview" config={editConfig} />;
                      default:
                        return null;
                    }
                  })()}
                  blockType={block.type}
                  blockIcon={blockTypes.find(t => t.type === block.type)?.icon ? React.createElement(blockTypes.find(t => t.type === block.type)!.icon, { className: 'w-5 h-5' }) : null}
                  onCancel={() => setShowConfigPanel(false)}
                  editContent={editContent}
                />
              )}
              <div className="flex justify-end mt-6">
                <Button variant="outline" onClick={() => setShowConfigPanel(false)}>Fechar</Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
        {/* Formulário de edição do bloco (mantém o switch/case para cada tipo) */}
        {(() => {
          switch (block.type) {
            case 'video':
              return <VideoBlockEditor editContent={editContent as VideoBlockContent} setEditContent={handleEditContentChange} mode="edit" config={editConfig} />;
            case 'text':
              return <TextEditorWrapper editContent={editContent as TextBlockContent} setEditContent={handleEditContentChange} mode="edit" editorKey={editorKey} config={editConfig} />;
            case 'image':
              return <ImageBlockEditor editContent={editContent as ImageBlockContent} setEditContent={handleEditContentChange} mode="edit" config={editConfig} />;
            case 'quiz':
              return <QuizBlockEditor editContent={editContent as QuizBlockContent} setEditContent={handleEditContentChange} mode="edit" config={editConfig} setConfig={setEditConfig} />;
            case 'evidence':
              return <EvidenceBlockEditor content={editContent as EvidenceBlockContent} onChange={handleEditContentChange} mode="edit" config={editConfig} setConfig={setEditConfig} />;
            case 'colored-block':
              return <ColoredBlockEditor
                editContent={editContent as ColoredBlockContent}
                setEditContent={handleEditContentChange}
                mode="edit"
                config={editConfig}
                setConfig={(newConfig) => {
                  setEditConfig(newConfig);
                }}
                onTypeChange={(newType) => {
                  setEditConfig(presetToBlockConfig(coloredBlockPresets[newType]));
                }}
              />;
            case 'file':
              return <FileBlockEditor editContent={editContent as FileBlockContent} setEditContent={handleEditContentChange} mode="edit" config={editConfig} />;
            default:
              return <div>Tipo de bloco não suportado para edição</div>;
          }
        })()}
      </>
    );
  };

  const renderBlock = (block: ContentBlock) => {
    if (editingBlock === block) {
      return null;
    }

    // Determinar o modo correto para cada tipo de bloco
    const blockMode = mode === 'execution' ? 'preview' : mode;

    console.log(`[RichContentEditor] Renderizando bloco ${block.type} em modo ${mode}, blockMode: ${blockMode}`, {
      blockId: block.id,
      taskId,
      isExecution: mode === 'execution'
    });

    switch (block.type) {
      case 'video':
        return <VideoBlockCard content={block.content as VideoBlockContent} config={block.config} onWatch={url => {
          if (typeof window !== 'undefined' && window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('openVideoModal', { detail: { url } }));
          }
        }} />;
      case 'text': {
        return <TextEditorWrapper editContent={block.content as TextBlockContent} setEditContent={() => {}} mode={blockMode} editorKey={block.id} config={block.config} />;
      }
      case 'image':
        return <ImageBlockEditor editContent={block.content as ImageBlockContent} setEditContent={() => {}} mode={blockMode} config={block.config} />;
      case 'quiz':
        // No modo execution, usar QuizExecutionBlock para funcionalidade completa
        if (mode === 'execution' && taskId) {
          return <QuizExecutionBlock
            content={block.content as QuizBlockContent}
            config={block.config}
            taskId={taskId}
            blockId={block.id}
            onComplete={(passed, score) => {
              console.log('Quiz completado:', { passed, score });
            }}
          />;
        }
        return <QuizBlockEditor editContent={block.content as QuizBlockContent} setEditContent={() => {}} mode={blockMode} config={block.config} />;
      case 'evidence':
        // No modo execution, usar EvidenceExecutionBlock para funcionalidade completa
        if (mode === 'execution' && taskId) {
          return <EvidenceExecutionBlock
            content={block.content as EvidenceBlockContent}
            config={block.config}
            taskId={taskId}
            blockId={block.id}
            onEvidenceChange={(evidences) => {
              console.log('Evidências alteradas:', evidences);
            }}
          />;
        }
        return <EvidenceBlockEditor content={block.content as EvidenceBlockContent} onChange={() => {}} mode={blockMode} config={block.config} />;
      case 'colored-block':
        return <ColoredBlockEditor editContent={block.content as ColoredBlockContent} setEditContent={() => {}} mode={blockMode} config={block.config} />;
      case 'file':
        return <FileBlockEditor editContent={block.content as FileBlockContent} setEditContent={() => {}} mode={blockMode} config={block.config} />;
      case 'alert':
        return <AlertBlock
          presetId={block.content.presetId}
        title={block.content.title}
        message={block.content.message}
        icon={block.content.icon}
        actionLabel={block.content.actionLabel}
        layout={undefined}
        onAction={block.content.actionLabel ? () => alert('Ação do alerta!') : undefined}
        />;
      default:
        return <div className="p-4 bg-gray-100 rounded-lg">Tipo de bloco não suportado</div>;
    }
  };



  const handleDragStart = (event: any) => {
    const blockId = event?.active?.id;
    if (blockId) {
      setCollapsedBlocks(prev => ({ ...prev, [blockId]: true }));
    }
  };

  function getContrastColor(bg) {
    if (!bg || typeof bg !== 'string' || !bg.startsWith('#') || bg.length < 7) return '#312e81';
    const r = parseInt(bg.substr(1,2),16);
    const g = parseInt(bg.substr(3,2),16);
    const b = parseInt(bg.substr(5,2),16);
    const luminance = (0.299*r + 0.587*g + 0.114*b)/255;
    return luminance > 0.5 ? '#312e81' : '#fff';
  }

  // Compatibilidade: escuta evento global para abrir modal de vídeo
  useEffect(() => {
    function handleOpenVideoModal(e: any) {
      if (e?.detail?.url && typeof window !== 'undefined') {
        // Se existir função global para abrir modal, chame-a
        if (typeof window["openVideoModal"] === 'function') {
          window["openVideoModal"](e.detail.url);
        }
      }
    }
    window.addEventListener('openVideoModal', handleOpenVideoModal);
    return () => window.removeEventListener('openVideoModal', handleOpenVideoModal);
  }, []);

  // Função para alternar colapso
  const toggleCollapse = (blockId: string) => {
    setCollapsedBlocks(prev => ({ ...prev, [blockId]: !prev[blockId] }));
  };

  // Função utilitária para extrair o título do conteúdo do bloco
  function getBlockContentTitle(block: ContentBlock): string {
    // Função auxiliar para checar se string é só emoji(s)
    function isOnlyEmojis(str: string): boolean {
      // Regex para emoji unicode (corrigido)
      const noSpace = str.replace(/\s/g, '');
      // eslint-disable-next-line no-control-regex
      return !!noSpace && /^([\p{Emoji}\u200d\uFE0F]+)$/gu.test(noSpace);
    }
    switch (block.type) {
      case 'video':
        return block.content?.title || 'Vídeo';
      case 'image':
        return block.content?.caption || block.content?.alt || 'Imagem';
      case 'file':
        return block.content?.name || 'Arquivo';
      case 'quiz':
        return block.content?.question || 'Quiz';
      case 'evidence':
        return block.content?.title || 'Evidências';
      case 'colored-block':
        return block.content?.title || 'Bloco Colorido';
      case 'alert': {
        const t = block.content?.title?.trim();
        if (t) return t.length > 64 ? t.slice(0, 64) + '...' : t;
        return 'Sem título';
      }
      case 'text': {
        // Extrair do Lexical JSON: heading > primeira linha com texto (emoji + texto permitido) > 'Texto'
        const value = block.content?.value;
        if (!value) return 'Texto';
        try {
          const json = JSON.parse(value);
          const children = json?.root?.children || [];
          // Procurar heading
          const heading = children.find((c: any) => c.type && c.type.startsWith('heading'));
          if (heading && Array.isArray(heading.children) && heading.children.length > 0) {
            // Concatenar todos os textos dos filhos do heading
            const headingText = heading.children.map((n: any) => n.text || '').join('').trim();
            if (headingText) return headingText.length > 64 ? headingText.slice(0, 64) + '...' : headingText;
          }
          // Procurar primeira linha de parágrafo com texto (emoji + texto permitido)
          const para = children.find((c: any) => c.type === 'paragraph');
          if (para && Array.isArray(para.children) && para.children.length > 0) {
            // Concatenar todos os textos do parágrafo
            const allText = para.children.map((n: any) => n.text || '').join('').trim();
            if (allText && !isOnlyEmojis(allText)) {
              return allText.length > 64 ? allText.slice(0, 64) + '...' : allText;
            }
            // Se for só emoji(s), buscar próxima linha de texto
            for (let i = 1; i < para.children.length; i++) {
              const t = para.children[i].text;
              if (t && t.trim() && !isOnlyEmojis(t.trim())) {
                return t.length > 64 ? t.slice(0, 64) + '...' : t;
              }
            }
            // Se todas as linhas são só emoji, pega a primeira mesmo
            const first = para.children.find((n: any) => n.text && n.text.trim());
            if (first && first.text) return first.text.length > 64 ? first.text.slice(0, 64) + '...' : first.text;
          }
        } catch {}
        return 'Texto';
      }
      default:
        return '';
    }
  }

  // Log removido para reduzir ruído no console

  return (
    <div className="space-y-4">
      {/* Barra de ferramentas para adicionar blocos - versão destacada */}
      {editable && (
        <div className="sticky top-0 z-10 bg-blue-50 border-y border-blue-200 shadow-lg mb-4 px-2 py-2 flex items-center gap-2 overflow-x-auto rounded-t-lg scrollbar-hide snap-x snap-mandatory">
          <span className="hidden sm:flex items-center gap-1 text-blue-700 font-semibold text-sm pl-1 pr-2 select-none">
            <Plus className="w-4 h-4" /> Adicionar bloco
          </span>
          <div className="flex gap-2">
            {blockTypes.map((blockType) => (
              <button
                key={blockType.type}
                type="button"
                onClick={() => addBlock(blockType.type)}
                className={`group flex flex-col items-center justify-center gap-0.5 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all duration-150 px-4 py-2 min-w-[64px] sm:min-w-[56px] cursor-pointer bg-transparent hover:bg-blue-100 active:bg-blue-200 focus:bg-blue-100 animate-pulse-once snap-start`}
                tabIndex={0}
                title={`Adicionar bloco de ${blockType.label}`}
              >
                <span className={`flex items-center justify-center w-12 h-12 sm:w-9 sm:h-9 rounded-full transition-all duration-150 ${blockType.color} group-hover:scale-110 group-focus:scale-110 group-hover:shadow-lg group-focus:shadow-lg`}>
                  <blockType.icon className="w-6 h-6 sm:w-5 sm:h-5 text-white" />
                </span>
                <span className="text-xs text-blue-900 group-hover:text-blue-700 group-focus:text-blue-700 transition-colors font-medium mt-0.5">{blockType.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Estado vazio: exibir se não houver blocos */}
      {blocks.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-center text-gray-500">
          <FileText className="w-16 h-16 mb-4 text-blue-200" />
          <h3 className="font-semibold text-lg text-gray-700 mb-2">Nenhum conteúdo adicionado ainda</h3>
          <p className="text-sm text-gray-500">
            Use a barra acima para adicionar o primeiro bloco de conteúdo.
          </p>
        </div>
      )}

      {/* Blocos de Conteúdo */}
      {editable ? (
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={onDragEndCallback} onDragStart={handleDragStart}>
          <SortableContext items={blocks.map(b => b.id)} strategy={verticalListSortingStrategy}>
            {blocks.map((block) => {
              const blockType = blockTypes.find(t => t.type === block.type);
              const Icon = blockType?.icon;
              // Obter config visual do bloco, com fallback
              let blockConfig: BlockConfig | undefined = undefined;
              if (block.type === 'text') {
                blockConfig = { ...defaultBlockConfig, ...((block as { config?: BlockConfig }).config || {}) };
              }
              // Estilos do card
              const cardStyle = block.type === 'text' && blockConfig?.card ? {
                background: blockConfig.card.backgroundColor,
                borderRadius: blockConfig.card.format === 'pill' ? 9999 : blockConfig.card.format === 'square' ? 0 : 12,
                border: blockConfig.card.border?.enabled
                  ? `${blockConfig.card.border?.width || 1}px solid ${blockConfig.card.border?.color || '#e5e5e5'}`
                  : 'none',
                boxShadow: blockConfig.card.shadow?.enabled
                  ? `0 2px ${2 * (blockConfig.card.shadow?.depth || 1)}px #0002`
                  : 'none',
                transition: 'all 0.2s',
              } : undefined;
              return (
                <SortableBlock key={block.id + JSON.stringify(block.content)} block={block} disabled={!editable}>
                  {(handleRef, handleListeners) => (
                    <div className={`group relative block-card-mobile ${collapsedBlocks[block.id] ? 'block-collapsed' : ''}`}>
                      <BlockCard key={block.id + JSON.stringify(block.content)} className={activeBlock === block.id ? 'ring-2 ring-blue-200' : ''} style={collapsedBlocks[block.id] ? { minHeight: 0, paddingBottom: 0, paddingTop: 0 } : {}}>
                        <div
                          className={`block-header ${collapsedBlocks[block.id] ? 'min-h-[56px]' : 'mb-3'}`}
                        >
                          <Badge
                            variant="secondary"
                            className="block-type-badge"
                          >
                            <span className={`block-type-icon ${blockType?.color}`}>
                              {Icon && <Icon className="w-3.5 h-3.5 text-white" />}
                            </span>
                            <span className="block-type-label">{blockType?.label || block.type}</span>
                            <span className="block-content-title">— {getBlockContentTitle(block)}</span>
                          </Badge>
                          <div className="block-actions">
                            {editable && (
                              <>
                                <Button
                                  ref={handleRef}
                                  {...handleListeners}
                                  size="sm"
                                  variant="ghost"
                                  className="block-action-button block-drag-button"
                                  style={{ touchAction: 'manipulation' }}
                                  title="Arrastar bloco"
                                >
                                  <GripVertical className="block-drag-icon" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="block-action-button"
                                  onClick={() => startEditBlock(block)}
                                  title="Editar bloco"
                                >
                                  <Edit className="block-action-icon" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="block-action-button block-delete-button"
                                  onClick={() => setBlockToDelete(block.id)}
                                  title="Excluir bloco"
                                >
                                  <Trash2 className="block-action-icon" />
                                </Button>
                              </>
                            )}
                            {/* Botão de colapso */}
                            <Button
                              size="sm"
                              variant="ghost"
                              className="block-action-button"
                              onClick={() => toggleCollapse(block.id)}
                              aria-label={collapsedBlocks[block.id] ? 'Expandir bloco' : 'Colapsar bloco'}
                              title={collapsedBlocks[block.id] ? 'Expandir bloco' : 'Colapsar bloco'}
                              tabIndex={0}
                            >
                              {collapsedBlocks[block.id] ? <ChevronRight className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                            </Button>
                          </div>
                        </div>
                        {/* Linha divisória e conteúdo só se não estiver colapsado */}
                        {!collapsedBlocks[block.id] && <div className="border-t border-gray-200 mb-3" />}
                        {!collapsedBlocks[block.id] && (
                          <>{renderBlock(block)}</>
                        )}
                      </BlockCard>
                    </div>
                  )}
                </SortableBlock>
              );
            })}
          </SortableContext>
        </DndContext>
      ) : (
        blocks.map((block) => {
          const blockType = blockTypes.find(t => t.type === block.type);
          const Icon = blockType?.icon;
          return (
            <BlockCard key={block.id + JSON.stringify(block.content)} className={activeBlock === block.id ? 'ring-2 ring-blue-500' : ''}>
              {/* Só mostrar badge se não estiver no modo execution */}
              {mode !== 'execution' && (
                <div className="mb-3">
                  <Badge
                    variant="secondary"
                    className="flex items-center gap-2 text-xs px-3 py-1 bg-gray-100 text-gray-700 font-semibold rounded-full"
                  >
                    <span className={`${blockType?.color} rounded-full flex items-center justify-center w-5 h-5`}>
                      {Icon && <Icon className="w-3.5 h-3.5 text-white" />}
                    </span>
                    <span>{blockType?.label || block.type}</span>
                    <span className="truncate max-w-[320px] text-gray-500 font-normal">— {getBlockContentTitle(block)}</span>
                  </Badge>
                </div>
              )}
              {renderBlock(block)}
            </BlockCard>
          );
        })
      )}

      {/* Diálogo de confirmação de exclusão */}
      <AlertDialog open={!!blockToDelete} onOpenChange={open => { if (!open) setBlockToDelete(null); }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remover bloco?</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este bloco? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (blockToDelete) deleteBlock(blockToDelete);
                setBlockToDelete(null);
              }}
            >
              Remover
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Modal de edição de bloco */}
      <ResizableDialog 
        open={editModalOpen} 
        onOpenChange={open => { if (!open) handleCancelEdit(); }}
        title="Editar bloco"
        minWidth={500}
        minHeight={400}
      >
        {editingBlock && renderEditForm(blocks.find(b => b.id === editingBlock.id)!)}
        <div className="flex gap-2 mt-6 justify-end border-t pt-4">
          <Button size="sm" onClick={handleSaveEdit}>
            Salvar
          </Button>
          <Button size="sm" variant="outline" onClick={handleCancelEdit}>
            Cancelar
          </Button>
        </div>
      </ResizableDialog>
      {/* Confirmação de descarte de alterações */}
      <AlertDialog open={showDiscardDialog} onOpenChange={open => { if (!open) setShowDiscardDialog(false); }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Descartar alterações?</AlertDialogTitle>
            <AlertDialogDescription>
              Existem alterações não salvas. Tem certeza que deseja descartar?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDiscardDialog(false)}>Continuar editando</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDiscard}>Descartar</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};